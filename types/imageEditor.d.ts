import type Konva from 'konva'
import MainPanel from '~/components/source/image-editor/MainPanel.vue'
import type { AiImage } from './savePostSettings'

type ImageEditorMainPanel = InstanceType<typeof MainPanel>

interface NodeRef<T extends Konva.Node> {
  getNode: () => T
}

interface VImageConfig extends Konva.ImageConfig {
  src?: string
}

interface MediaImage {
  id: number
  name: string
  url: string
}
interface ImageEditor {
  isOpen?: boolean
  image?: AiImage | null
}
interface StageSize {
  width: number
  height: number
}
interface ImageState {
  groupX: number
  groupY: number
  groupRotation: number
  groupScaleX: number
  groupScaleY: number
  groupOffsetX?: number
  groupOffsetY?: number
  imageX: number
  imageY: number
  imageScaleX: number
  imageScaleY: number
  groupWidth: number
  groupHeight: number
  imageWidth: number
  imageHeight: number
  originalWidth: number
  originalHeight: number
  filter: {
    name: string
    percentage: number
    adjustments?: Record<string, number>
  }
  textItems?: Konva.TextConfig[]
  clip: ClipConfig | null
  isManualCrop: boolean
  elements?: Konva.PathConfig[]
  stickers?: VImageConfig[]
}
interface HistoryState {
  past: ImageState[]
  present: ImageState | null
  future: ImageState[]
}
interface GeneratedImageData {
  dataURL: string
}
interface SizeOption {
  id: number
  name: string
  width?: number
  height?: number
  ratio: string
}

interface ClipConfig {
  x: number
  y: number
  width: number
  height: number
}

interface TextObject {
  id: string
  text: string
  x: number
  y: number
  fontSize: number
  fontFamily: string
  fill: string
  align: string
  verticalAlign: string
  width: number
  height: number
}

export type {
  ClipConfig,
  GeneratedImageData,
  HistoryState,
  ImageEditor,
  ImageEditorMainPanel,
  ImageState,
  MediaImage,
  NodeRef,
  SizeOption,
  StageSize,
  TextObject,
  VImageConfig,
}
