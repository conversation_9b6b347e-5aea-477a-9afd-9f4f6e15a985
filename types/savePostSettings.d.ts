interface Tab {
  label: string
  route: string
}
interface TargetWordCountOption {
  id: number
  range: string
}
interface TargetLengthOption {
  id: number
  range: string
}
interface ContentStyleOption {
  id: number
  label: string
}
interface AutoContentOption {
  id: number
  label: string
}
interface AccountOption {
  id: number
  name: string
  provider: string
  profilePic: string
  username: string
}
interface PostSettings {
  id?: number
  name?: string
  postToAccounts?: AccountOption[]
  postType?: string
  autoImage?: boolean
  teleprompter?: boolean
  autoHashtags?: boolean
  autoUsernames?: boolean
  autoShortenLinks?: boolean
  autoGenerateContent?: boolean
  wordCountRange?: string | null
  targetLengthRange?: string | null
  contentStyle?: string | null
  autoContent?: string | null
  prompt?: string
}
interface AiImage {
  id: number
  url: string
}
interface Hashtag {
  id: number
  name: string
}
interface Tag {
  id: number
  username: string
  name: string
}
interface GeneratedContent {
  text: string
  images: AiImage[]
  hashtags: Hashtag[]
  tags: Tag[]
}
interface PreviewContent {
  text?: string
  images?: AiImage[]
  hashtags?: Hashtag[]
  tags?: Tag[]
  video?: CapturedVideo | null
}
interface CapturedVideo {
  videoUrl: string
  thumbnailUrl: string
}

export type {
  AccountOption,
  AiImage,
  AutoContentOption,
  CapturedVideo,
  ContentStyleOption,
  GeneratedContent,
  Hashtag,
  PostSettings,
  PreviewContent,
  Tab,
  Tag,
  TargetLengthOption,
  TargetWordCountOption,
}
