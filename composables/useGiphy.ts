// ~/composables/useGiphy.ts
const gifs = ref<any[]>([])
// const gifPreview = ref('')

export const useGiphy = () => {
  const apiKey = 'SonM72WRO7i88nTFRf2JFU9UBqRjCBIm'
  const pending = ref(false)
  const loadingMore = ref(false)
  const offset = ref(0)
  const limit = 20
  const hasMore = ref(true)
  const gifPreview = ref('')

  const query = ref('')

  const search = async (query: string) => {
    const res = await $fetch(`https://api.giphy.com/v1/gifs/search`, {
      params: {
        api_key: apiKey,
        q: query,
        limit: 20,
      },
    })
    gifs.value = res.data
  }

  const loadGifs = async (reset = false) => {
    if (!hasMore.value || loadingMore.value) return

    if (reset) {
      gifs.value = []
      offset.value = 0
      hasMore.value = true
    }

    if (offset.value === 0) pending.value = true
    loadingMore.value = true

    const { data, error } = await useFetch(
      'https://api.giphy.com/v1/gifs/trending',
      {
        query: {
          api_key: 'SonM72WRO7i88nTFRf2JFU9UBqRjCBIm',
          q: query.value || undefined,
          limit,
          offset: offset.value,
          rating: 'g',
        },
      },
    )

    if (!error.value) {
      const newGifs = data.value?.data || []
      gifs.value.push(...newGifs)

      // Check if more gifs are available
      hasMore.value = newGifs.length === limit
      offset.value += limit
    }

    pending.value = false
    loadingMore.value = false
  }

  const selectGif = (gif: any) => {
    if (gif) {
      gifPreview.value = gif.images.original.url
    } else {
      gifPreview.value = gif
    }
    // Emit the gif, insert into input/editor, etc.
  }

  return {
    search,
    query,
    pending,
    loadingMore,
    gifs,
    loadGifs,
    gifPreview,
    selectGif,
  }
}
