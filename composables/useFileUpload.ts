export const useFileUpload = () => {
  const avatarPreview = ref<string>('')
  const multipleAvatarPreview = ref<string[]>([])
  const fileSelect = ref<boolean>(false)
  const onFileSelected = (event: Event) => {
    const target = event.target as HTMLInputElement
    // selectFile.value = target.files?.[0]
    let reader
    const files = target.files
    if (files && files.length > 0) {
      reader = new FileReader()
      reader.onload = (event) => {
        avatarPreview.value = event.target?.result as string
        fileSelect.value = true
        // profileUserInfo.value.avatar = selectFile.value
      }
      reader.readAsDataURL(files[0])
    }
    target.value = ''
  }

  const onMultipleFileSelected = (event: Event) => {
    const target = event.target as HTMLInputElement
    const files = target.files
    fileSelect.value = true
    if (files && files.length > 0) {
      Array.from(files).forEach((file) => {
        const reader = new FileReader()

        reader.onload = (e) => {
          if (e.target?.result) {
            multipleAvatarPreview.value.push(e.target.result as string)
          }
        }

        reader.readAsDataURL(file)
        // selectedFiles.value.push(file);
      })

      fileSelect.value = true
    }

    // Reset input to allow reselecting the same files
    target.value = ''
  }

  const removeFile = (index: number) => {
    multipleAvatarPreview.value.splice(index, 1)
    // selectedFiles.value.splice(index, 1)

    // If all files removed, reset fileSelect
    if (multipleAvatarPreview.value.length === 0) {
      fileSelect.value = false
    }
  }

  const resertFile = () => {
    avatarPreview.value = ''
    fileSelect.value = false
    multipleAvatarPreview.value = []
  }

  return {
    avatarPreview,
    multipleAvatarPreview,
    onFileSelected,
    onMultipleFileSelected,
    resertFile,
    fileSelect,
    removeFile
  }
}
