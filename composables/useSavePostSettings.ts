import { ref } from 'vue'
import { useStore } from 'vuex'
import facebookIcon from '~/assets/img/icon/FacebookIcon/<EMAIL>'
import instagramIcon from '~/assets/img/icon/instagram.webp'
import linkedinIcon from '~/assets/img/icon/LinkedInIcon/<EMAIL>'
import twitterIcon from '~/assets/img/icon/TwitterIcon/twitter.svg'
import type {
  AccountOption,
  AutoContentOption,
  ContentStyleOption,
  PostSettings,
  Tab,
  TargetLengthOption,
  TargetWordCountOption,
} from '~/types/savePostSettings'

export function useSavePostSettings() {
  const providerDataMap: Record<string, { icon: string; bgColor: string }> = {
    Facebook: {
      icon: facebookIcon,
      bgColor: '#E5F0FF',
    },
    Instagram: {
      icon: instagramIcon,
      bgColor: '#FFE9F1',
    },
    Twitter: {
      icon: twitterIcon,
      bgColor: '#E6F4FF',
    },
    LinkedIn: {
      icon: linkedinIcon,
      bgColor: '#C2EBFF',
    },
  }
  const tabs: Tab[] = [
    { label: 'Text', route: '' },
    { label: 'Video', route: '' },
  ]
  const accountOptions: AccountOption[] = [
    {
      id: 1,
      profilePic: `/social/profile-picture.png`,
      provider: 'Facebook',
      name: 'Sharparchive',
      username: '@official.sharparchive',
    },
    {
      id: 2,
      profilePic: `/social/profile-picture.png`,
      provider: 'Instagram',
      name: 'Sharparchive',
      username: '@sharparchive',
    },
    {
      id: 3,
      profilePic: `/social/profile-picture.png`,
      provider: 'Twitter',
      name: 'Sharparchive',
      username: '@sharp_archive',
    },
    {
      id: 4,
      profilePic: `/social/profile-picture.png`,
      provider: 'Facebook',
      name: 'Sharparchive',
      username: '@sharparchive1',
    },
    {
      id: 5,
      profilePic: `/social/chad-profile.png`,
      provider: 'Facebook',
      name: 'Chad Gordon',
      username: '@sharparchive2',
    },
    {
      id: 6,
      profilePic: `/social/chad-profile.png`,
      provider: 'LinkedIn',
      name: 'Greenstar Advisors',
      username: '@sharparchive3',
    },
  ]
  const targetWordCountOptions: TargetWordCountOption[] = [
    { id: 2, range: '100 - 250' },
    { id: 3, range: '250 - 350' },
    { id: 4, range: '350 - 500' },
    { id: 5, range: '500 - 1000' },
  ]
  const targetLengthOptions: TargetLengthOption[] = [
    { id: 2, range: '0:15 - 0:30' },
    { id: 3, range: '0:31 - 0:45' },
    { id: 4, range: '0:46 - 0:60' },
    { id: 5, range: '0:61 - 0:90' },
  ]
  const autoContentOptions: AutoContentOption[] = [
    { id: 2, label: 'Current Events' },
    { id: 3, label: 'Trending' },
    { id: 4, label: 'Most Popular' },
  ]
  const contentStyleOptions: ContentStyleOption[] = [
    { id: 2, label: 'Professional' },
    { id: 3, label: 'Formal' },
    { id: 4, label: 'Casual' },
    { id: 5, label: 'Friendly' },
    { id: 6, label: 'Hot Take' },
  ]

  const store = useStore()
  const currentTab = ref('Text')
  const selectedAccounts = ref<AccountOption[]>([])
  const name = ref('')
  const autoImage = ref(true)
  const teleprompter = ref(true)
  const autoHashtags = ref(true)
  const autoUsernames = ref(true)
  const autoShortenLinks = ref(true)
  const autoGenerateContent = ref(true)
  const wordCountRange = ref<TargetWordCountOption | null>(null)
  const targetLengthRange = ref<TargetLengthOption | null>(null)
  const contentStyle = ref<ContentStyleOption | null>(null)
  const autoContent = ref<AutoContentOption | null>(null)
  const prompt = ref('')

  const accountByProvider = (item: AccountOption) =>
    providerDataMap[item.provider] || { icon: '', bgColor: '#FFFFFF' }

  const removeAccount = (account: AccountOption) => {
    selectedAccounts.value = selectedAccounts.value.filter(
      (item) => item.id !== account.id,
    )
  }

  const handleToggleAutoImage = () => {
    autoImage.value = !autoImage.value
  }
  const handleToggleTeleprompter = () => {
    teleprompter.value = !teleprompter.value
  }

  const handleToggleAutoHashtags = () => {
    autoHashtags.value = !autoHashtags.value
  }

  const handleToggleAutoUsernames = () => {
    autoUsernames.value = !autoUsernames.value
  }

  const handleToggleAutoShortenLinks = () => {
    autoShortenLinks.value = !autoShortenLinks.value
  }
  const handleToggleAutoGenerateContent = () => {
    autoGenerateContent.value = !autoGenerateContent.value
  }

  const setCurrentSettings = (post: PostSettings) => {
    currentTab.value = post?.postType || 'Text'
    name.value = post.name || ''
    selectedAccounts.value = post.postToAccounts || []
    autoImage.value = post.autoImage || false
    teleprompter.value = post.teleprompter || false
    autoHashtags.value = post.autoHashtags || false
    autoUsernames.value = post.autoUsernames || false
    autoShortenLinks.value = post.autoShortenLinks || false
    autoGenerateContent.value = post.autoGenerateContent || false
    prompt.value = post.prompt || ''
    wordCountRange.value =
      targetWordCountOptions.find(
        (item) => item.range === post.wordCountRange,
      ) ?? null
    targetLengthRange.value =
      targetLengthOptions.find(
        (item) => item.range === post.targetLengthRange,
      ) ?? null
    contentStyle.value =
      contentStyleOptions.find((item) => item.label === post.contentStyle) ??
      null
    autoContent.value =
      autoContentOptions.find((item) => item.label === post.autoContent) ?? null
  }

  const updateRunningSettings = (settings: PostSettings) => {
    store.commit('createPost/UPDATE_RUNNING_POST_SETTINGS', settings)
  }

  return {
    tabs,
    targetWordCountOptions,
    targetLengthOptions,
    autoContentOptions,
    contentStyleOptions,
    accountOptions,
    currentTab,
    name,
    selectedAccounts,
    autoImage,
    teleprompter,
    autoHashtags,
    autoUsernames,
    autoShortenLinks,
    autoGenerateContent,
    wordCountRange,
    targetLengthRange,
    contentStyle,
    autoContent,
    prompt,
    setCurrentSettings,
    updateRunningSettings,
    removeAccount,
    accountByProvider,
    handleToggleAutoImage,
    handleToggleTeleprompter,
    handleToggleAutoHashtags,
    handleToggleAutoUsernames,
    handleToggleAutoShortenLinks,
    handleToggleAutoGenerateContent,
  }
}
