import { createSharedComposable, useResizeObserver } from '@vueuse/core'
import Konva from 'konva'
import { useImage } from 'vue-konva'
import { useStore } from 'vuex'
import type {
  ClipConfig,
  GeneratedImageData,
  HistoryState,
  ImageState,
  NodeRef,
  SizeOption,
  StageSize,
} from '~/types/imageEditor'
import type { AiImage } from '~/types/savePostSettings'
export const useImageEditor = () => {
  const { addOrUpdateSelectedImage } = useGeneratePost()
  const store = useStore()
  const currentEditorimage = computed<AiImage | null>(
    () => store.state.createPost.imageEditor.image,
  )
  const image = computed<string>(() => {
    const url = store.state.createPost.imageEditor.image?.url
    if (url) {
      return url
    }
    return ''
  })
  const [editorImage, imageStatus]: readonly [
    Ref<HTMLImageElement | null>,
    Ref<'error' | 'loading' | 'loaded'>,
  ] = useImage(image, 'Anonymous')
  const clipEnabled = ref(true)
  const clipConfig = ref<ClipConfig>({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  })
  const MIN_ZOOM = 10
  const MAX_ZOOM = 200
  const zoomPercentage = ref(100)
  const isUndoDisabled = ref(true)
  const isRedoDisabled = ref(true)
  const isCroping = ref(false)
  const isManualCrop = ref(false)
  const imageIsLoaded = ref(true)
  const currentFilter = ref('none')
  const filterPercentage = ref(50)
  const canvasContainer = ref<HTMLElement | null>(null)
  const stage = ref<NodeRef<Konva.Stage> | null>(null)
  const layer = ref<NodeRef<Konva.Layer> | null>(null)
  const group = ref<NodeRef<Konva.Group> | null>(null)
  const konvaImage = ref<NodeRef<Konva.Image> | null>(null)
  const cropRectRef = ref<NodeRef<Konva.Rect> | null>(null)
  const transformerCropRef = ref<NodeRef<Konva.Transformer> | null>(null)
  const transformerTextRef = ref<NodeRef<Konva.Transformer> | null>(null)
  const transformerElementRef = ref<NodeRef<Konva.Transformer> | null>(null)
  const transformerStickerRef = ref<NodeRef<Konva.Transformer> | null>(null)
  const selectedText = ref<Konva.TextConfig | null>(null)
  const textItems = ref<Konva.TextConfig[]>([])
  const elements = ref<Konva.PathConfig[]>([])
  const selectedElement = ref<Konva.PathConfig | null>(null)
  const stickers = ref<Konva.ImageConfig[]>([])
  const selectedSticker = ref<Konva.ImageConfig | null>(null)
  const transformerCropConfig = computed<Konva.TransformerConfig>(() => ({
    enabledAnchors: isFlexibleCrop.value
      ? [
          'top-left',
          'top-center',
          'top-right',
          'middle-left',
          'middle-right',
          'bottom-left',
          'bottom-center',
          'bottom-right',
        ]
      : ['top-left', 'top-right', 'bottom-left', 'bottom-right'],
    rotateEnabled: false,
    borderStroke: '#4A71D4',
    borderStrokeWidth: 2,
    anchorFill: '#fff',
    anchorStroke: '#4A71D4',
    anchorStrokeWidth: 2,
    anchorSize: 12,
    anchorCornerRadius: 20,
    keepRatio: !isFlexibleCrop.value,
    boundBoxFunc: !isFlexibleCrop.value
      ? (oldBox, newBox) => {
          const ratio = cropAspectRatio.value
          let { width, height } = newBox
          if (width / height > ratio) {
            width = height * ratio
          } else {
            height = width / ratio
          }
          return {
            ...newBox,
            width: Math.max(50, width),
            height: Math.max(50, height),
          }
        }
      : (oldBox, newBox) => {
          return {
            ...newBox,
            width: Math.max(50, newBox.width),
            height: Math.max(50, newBox.height),
          }
        },
  }))
  const transformerElementConfig = ref<Konva.TransformerConfig>({
    enabledAnchors: [
      'top-left',
      'top-center',
      'top-right',
      'middle-left',
      'middle-right',
      'bottom-left',
      'bottom-center',
      'bottom-right',
    ],
    rotationSnaps: [0, 90, 180, 270],
    borderStroke: '#4A71D4',
    borderStrokeWidth: 2,
    anchorFill: '#fff',
    anchorStroke: '#4A71D4',
    anchorStrokeWidth: 2,
    anchorSize: 12,
    anchorCornerRadius: 20,
    boundBoxFunc: (oldBox, newBox) => {
      return newBox.width < 10 ? oldBox : newBox
    },
  })
  const transformerStickersConfig = ref<Konva.TransformerConfig>({
    enabledAnchors: [
      'top-left',
      'top-center',
      'top-right',
      'middle-left',
      'middle-right',
      'bottom-left',
      'bottom-center',
      'bottom-right',
    ],
    rotationSnaps: [0, 90, 180, 270],
    borderStroke: '#4A71D4',
    borderStrokeWidth: 2,
    anchorFill: '#fff',
    anchorStroke: '#4A71D4',
    anchorStrokeWidth: 2,
    anchorSize: 12,
    anchorCornerRadius: 20,
    boundBoxFunc: (oldBox, newBox) => {
      return newBox.width < 10 ? oldBox : newBox
    },
  })
  const cropRectConfig = ref<Konva.RectConfig>({
    x: 100,
    y: 50,
    width: 200,
    height: 150,
    strokeWidth: 0,
    draggable: true,
    name: 'crop-rect',
  })
  const groupConfig = ref<Konva.GroupConfig>({
    x: 0,
    y: 0,
    draggable: true,
  })
  const transformerTextConfig = ref<Konva.TransformerConfig>({
    enabledAnchors: ['top-left', 'top-right', 'bottom-left', 'bottom-right'],
    rotationSnaps: [0, 90, 180, 270],
    borderStroke: '#4A71D4',
    borderStrokeWidth: 2,
    anchorFill: '#fff',
    anchorStroke: '#4A71D4',
    anchorStrokeWidth: 2,
    anchorSize: 12,
    anchorCornerRadius: 20,
    boundBoxFunc: (oldBox, newBox) => {
      return newBox.width < 10 ? oldBox : newBox
    },
  })
  const imageConfig = ref<Konva.ImageConfig>({
    image: editorImage.value || undefined,
  })
  const selectedSize = ref<SizeOption>({
    id: 0,
    name: 'Original Ratio',
    ratio: 'x:y',
  })
  const stageSize = ref<StageSize>({
    width: 100,
    height: 100,
  })
  const history = ref<HistoryState>({
    past: [],
    present: null,
    future: [],
  })
  const lastAppliedFilter = ref<{
    name: string
    percentage: number
    adjustments?: Record<string, number>
  }>({ name: 'none', percentage: 50 })
  const adjustmentSliders = ref([
    {
      id: 1,
      label: 'Brighten',
      name: 'brighten',
      lavel: 50,
    },
    {
      id: 2,
      label: 'Contrast',
      name: 'contrast',
      lavel: 50,
    },
    {
      id: 3,
      label: 'Saturation',
      name: 'saturation',
      lavel: 50,
    },
    {
      id: 4,
      label: 'Blur',
      name: 'blur',
      lavel: 0,
    },
  ])

  const cropAspectRatio = computed(() => {
    if (selectedSize.value.ratio === 'x:y') {
      if (editorImage.value) {
        return editorImage.value.naturalWidth / editorImage.value.naturalHeight
      }
      return 1
    } else {
      const [width, height] = selectedSize.value.ratio.split(':').map(Number)
      return width / height
    }
  })
  const isFlexibleCrop = computed(() => selectedSize.value.ratio === 'x:y')

  const updateCanvas = () => {
    layer.value?.getNode().batchDraw()
  }
  const updateCropTransformer = () => {
    if (!cropRectRef.value || !transformerCropRef.value) return
    const cropRect = cropRectRef.value.getNode()
    const transformer = transformerCropRef.value.getNode()
    if (!isFlexibleCrop.value) {
      const currentWidth = cropRect.width() * cropRect.scaleX()
      const currentHeight = cropRect.height() * cropRect.scaleY()
      const targetRatio = cropAspectRatio.value
      let newWidth = currentWidth
      let newHeight = currentHeight
      if (currentWidth / currentHeight !== targetRatio) {
        if (currentWidth / currentHeight > targetRatio) {
          newWidth = currentHeight * targetRatio
        } else {
          newHeight = currentWidth / targetRatio
        }
        cropRect.width(newWidth)
        cropRect.height(newHeight)
        cropRect.scaleX(1)
        cropRect.scaleY(1)
      }
    }
    transformer.nodes([cropRect])
    transformer.getLayer()?.batchDraw()
  }
  const startCrop = () => {
    if (!group.value || !konvaImage.value || !stage.value) return
    const groupNode = group.value.getNode()
    const groupBox = groupNode.getClientRect({ skipTransform: true })
    let cropWidth, cropHeight
    if (isFlexibleCrop.value) {
      const imageRatio = cropAspectRatio.value
      const maxWidth = groupBox.width * 0.6
      const maxHeight = groupBox.height * 0.6
      if (maxWidth / imageRatio <= maxHeight) {
        cropWidth = maxWidth
        cropHeight = cropWidth / imageRatio
      } else {
        cropHeight = maxHeight
        cropWidth = cropHeight * imageRatio
      }
    } else {
      const targetRatio = cropAspectRatio.value
      const maxWidth = groupBox.width * 0.6
      const maxHeight = groupBox.height * 0.6
      if (maxWidth / targetRatio <= maxHeight) {
        cropWidth = maxWidth
        cropHeight = cropWidth / targetRatio
      } else {
        cropHeight = maxHeight
        cropWidth = cropHeight * targetRatio
      }
    }
    const cropX = (groupBox.width - cropWidth) / 2
    const cropY = (groupBox.height - cropHeight) / 2
    cropRectConfig.value = {
      ...cropRectConfig.value,
      x: cropX,
      y: cropY,
      width: cropWidth,
      height: cropHeight,
      visible: true,
      strokeWidth: 2,
    }
    nextTick(() => {
      updateCropTransformer()
    })
  }
  const finishCrop = () => {
    if (!cropRectRef.value || !group.value) return
    const cropRect = cropRectRef.value.getNode()
    const groupNode = group.value.getNode()
    const cropBox = cropRect.getClientRect()
    const groupTransform = groupNode.getTransform().copy().invert()
    const topLeft = groupTransform.point({ x: cropBox.x, y: cropBox.y })
    const bottomRight = groupTransform.point({
      x: cropBox.x + cropBox.width,
      y: cropBox.y + cropBox.height,
    })
    const localX = topLeft.x
    const localY = topLeft.y
    const localWidth = bottomRight.x - topLeft.x
    const localHeight = bottomRight.y - topLeft.y
    groupNode.clipX(localX)
    groupNode.clipY(localY)
    groupNode.clipWidth(localWidth)
    groupNode.clipHeight(localHeight)
    clipConfig.value = {
      x: localX,
      y: localY,
      width: localWidth,
      height: localHeight,
    }
    clipEnabled.value = true
    isManualCrop.value = true
    cropRectConfig.value = {
      ...cropRectConfig.value,
      visible: false,
    }
    saveToHistory()
    updateCanvas()
  }
  const handleStageClick = (e: any) => {
    const clickedOnEmpty = e.target === e.currentTarget
    if (clickedOnEmpty || e.target.className === 'Rect') {
      if (selectedText.value) {
        selectedText.value = null
        if (transformerTextRef.value) {
          transformerTextRef.value.getNode().nodes([])
          updateCanvas()
        }
      }
      if (selectedElement.value) {
        selectedElement.value = null
        if (transformerElementRef.value) {
          transformerElementRef.value.getNode().nodes([])
          updateCanvas()
        }
      }
      if (selectedSticker.value) {
        selectedSticker.value = null
        if (transformerStickerRef.value) {
          transformerStickerRef.value.getNode().nodes([])
          updateCanvas()
        }
      }
    }
  }
  const handleTextClick = (text: Konva.TextConfig) => {
    selectedText.value = text
    nextTick(() => {
      const textNode = getNodeById(text.id)
      if (textNode && transformerTextRef.value) {
        transformerTextRef.value.getNode().nodes([textNode])
        transformerTextRef.value.getNode().moveToTop()
        updateCanvas()
      }
    })
  }
  const handleElementClick = (element: Konva.PathConfig) => {
    selectedElement.value = element
    nextTick(() => {
      const elementNode = getNodeById(element.id)
      if (elementNode && transformerElementRef.value) {
        transformerElementRef.value.getNode().nodes([elementNode])
        transformerElementRef.value.getNode().moveToTop()
        updateCanvas()
      }
    })
  }
  const handleStickerClick = (sticker: Konva.ImageConfig) => {
    selectedSticker.value = sticker
    nextTick(() => {
      const stickerNode = getNodeById(sticker.id)
      if (stickerNode && transformerStickerRef.value) {
        transformerStickerRef.value.getNode().nodes([stickerNode])
        transformerStickerRef.value.getNode().moveToTop()
        updateCanvas()
      }
    })
  }
  const getNodeById = (id: string | undefined): Konva.Text | null => {
    if (!layer.value || !id) return null
    const layerNode = layer.value.getNode()
    const node = layerNode.findOne(
      (node: Konva.Node) => node.attrs.id === id,
    ) as Konva.Text | undefined
    return node || null
  }
  const handleTextDragEnd = (id: string | undefined) => {
    const index = textItems.value.findIndex((item) => item.id === id)
    if (index !== -1) {
      const textNode = getNodeById(id)
      if (textNode) {
        textItems.value[index].x = textNode.x()
        textItems.value[index].y = textNode.y()
      }
    }
  }
  const handleTextTransformEnd = (id: string | undefined) => {
    const index = textItems.value.findIndex((item) => item.id === id)
    if (index !== -1) {
      const textNode = getNodeById(id)
      if (textNode) {
        const newRotation = textNode.rotation()
        const newFontSize =
          textNode.fontSize() * ((textNode.scaleX() + textNode.scaleY()) / 2)
        textItems.value[index].rotation = newRotation
        textItems.value[index].fontSize = newFontSize
        textNode.scaleX(1)
        textNode.scaleY(1)
      }
    }
  }
  const handleElementDragEnd = (id: string | undefined) => {
    const index = elements.value.findIndex((item) => item.id === id)
    if (index !== -1) {
      const elementNode = getNodeById(id)
      if (elementNode) {
        elements.value[index].x = elementNode.x()
        elements.value[index].y = elementNode.y()
      }
    }
  }
  const handleElementTransformEnd = (id: string | undefined) => {
    const index = elements.value.findIndex((item) => item.id === id)
    if (index !== -1) {
      const elementNode = getNodeById(id)
      if (elementNode) {
        elements.value[index].rotation = elementNode.rotation()
        elements.value[index].scaleX = elementNode.scaleX()
        elements.value[index].scaleY = elementNode.scaleY()
        elements.value[index].x = elementNode.x()
        elements.value[index].y = elementNode.y()
      }
    }
  }
  const handleStickerDragEnd = (id: string | undefined) => {
    const index = stickers.value.findIndex((item) => item.id === id)
    if (index !== -1) {
      const stickerNode = getNodeById(id)
      if (stickerNode) {
        stickers.value[index].x = stickerNode.x()
        stickers.value[index].y = stickerNode.y()
      }
    }
  }
  const handleStickerTransformEnd = (id: string | undefined) => {
    const index = stickers.value.findIndex((item) => item.id === id)
    if (index !== -1) {
      const stickerNode = getNodeById(id)
      if (stickerNode) {
        stickers.value[index].rotation = stickerNode.rotation()
        stickers.value[index].scaleX = stickerNode.scaleX()
        stickers.value[index].scaleY = stickerNode.scaleY()
        stickers.value[index].x = stickerNode.x()
        stickers.value[index].y = stickerNode.y()
      }
    }
  }
  useResizeObserver(canvasContainer, (entries) => {
    const entry = entries[0]
    const { width, height } = entry.contentRect
    const prevWidth = stageSize.value.width
    const prevHeight = stageSize.value.height
    stageSize.value.width = width
    stageSize.value.height = height
    if (prevWidth > 0 && prevHeight > 0) {
      centerAndFitGroupWithRatio()
    }
  })
  const centerAndFitGroupWithRatio = () => {
    if (!group.value || !history.value.present) return
    const groupNode = group.value.getNode()
    if (isManualCrop.value && clipEnabled.value) {
      const cropCenterX = clipConfig.value.x + clipConfig.value.width / 2
      const cropCenterY = clipConfig.value.y + clipConfig.value.height / 2
      groupNode.position({
        x: stageSize.value.width / 2,
        y: stageSize.value.height / 2,
      })
      groupNode.offset({
        x: cropCenterX,
        y: cropCenterY,
      })
    } else {
      groupNode.position({
        x: stageSize.value.width / 2,
        y: stageSize.value.height / 2,
      })
      const box = groupNode.getClientRect({ skipTransform: true })
      groupNode.offset({
        x: box.width / 2,
        y: box.height / 2,
      })
    }
    updateCanvas()
    updateCurrentState()
  }
  const getCurrentState = (): ImageState => {
    if (!group.value || !konvaImage.value) {
      const originalState = history.value.present || {
        originalWidth: editorImage.value?.width || 0,
        originalHeight: editorImage.value?.height || 0,
      }
      return originalState as ImageState
    }
    const groupNode = group.value.getNode()
    const imageNode = konvaImage.value.getNode()
    const originalState = history.value.present || {
      originalWidth: editorImage.value?.width || 0,
      originalHeight: editorImage.value?.height || 0,
    }
    const adjustments: Record<string, number> = adjustmentSliders.value.reduce<
      Record<string, number>
    >((acc, slider) => {
      acc[slider.name] = slider.lavel
      return acc
    }, {})
    return {
      groupX: groupNode.x(),
      groupY: groupNode.y(),
      groupRotation: groupNode.rotation(),
      groupScaleX: groupNode.scaleX(),
      groupScaleY: groupNode.scaleY(),
      groupOffsetX: groupNode.offsetX(),
      groupOffsetY: groupNode.offsetY(),
      imageX: imageNode.x(),
      imageY: imageNode.y(),
      imageScaleX: imageNode.scaleX(),
      imageScaleY: imageNode.scaleY(),
      groupWidth: groupNode.width(),
      groupHeight: groupNode.height(),
      imageWidth: imageNode.width(),
      imageHeight: imageNode.height(),
      originalWidth: originalState.originalWidth,
      originalHeight: originalState.originalHeight,
      clip: clipEnabled.value ? { ...clipConfig.value } : null,
      filter: {
        name: lastAppliedFilter.value.name,
        percentage: lastAppliedFilter.value.percentage,
        adjustments: {
          ...adjustments,
        },
      },
      textItems: JSON.parse(JSON.stringify(textItems.value)),
      elements: JSON.parse(JSON.stringify(elements.value)),
      stickers: JSON.parse(JSON.stringify(stickers.value)),
      isManualCrop: isManualCrop.value,
    }
  }
  const updateCurrentState = () => {
    if (!group.value || !konvaImage.value) return
    const currentState = getCurrentState()
    history.value.present = currentState
  }
  const updateUndoRedoState = () => {
    isUndoDisabled.value = history.value.past.length === 0
    isRedoDisabled.value = history.value.future.length === 0
  }
  const saveToHistory = () => {
    if (!group.value || !konvaImage.value) return
    const currentState = getCurrentState()
    const present = history.value.present
    history.value = {
      past: [...history.value.past, present!].filter(Boolean),
      present: currentState,
      future: [],
    }
    updateUndoRedoState()
  }
  const calculateRectDimensions = () => {
    let ratioWidth = 500
    let ratioHeight = 500
    if (imageStatus.value === 'loaded' && editorImage.value) {
      ratioWidth = editorImage.value.naturalWidth
      ratioHeight = editorImage.value.naturalHeight
    }
    const aspectRatio = ratioWidth / ratioHeight
    const maxWidth = stageSize.value.width * 0.9
    const maxHeight = stageSize.value.height * 0.9
    let width: number, height: number
    if (maxWidth / aspectRatio <= maxHeight) {
      width = maxWidth
      height = width / aspectRatio
    } else {
      height = maxHeight
      width = height * aspectRatio
    }
    return { width, height }
  }
  const initializeGroupOffset = () => {
    if (!group.value) return
    const groupNode = group.value.getNode()
    groupNode.position({
      x: stageSize.value.width / 2,
      y: stageSize.value.height / 2,
    })
    const box = groupNode.getClientRect()
    groupNode.offset({
      x: box.width / 2,
      y: box.height / 2,
    })
  }
  const updateClipping = () => {
    if (!group.value || !konvaImage.value) return
    const groupNode = group.value.getNode()
    const imageNode = konvaImage.value.getNode()

    if (isManualCrop.value && selectedSize.value.ratio !== 'x:y') {
      isManualCrop.value = false
    }
    if (!isManualCrop.value || !history.value.present) {
      clipEnabled.value = true
      const [widthRatio, heightRatio] =
        selectedSize.value.ratio === 'x:y'
          ? [imageNode.width(), imageNode.height()]
          : selectedSize.value.ratio.split(':').map(Number)

      const imageWidth = imageNode.width() * Math.abs(imageNode.scaleX())
      const imageHeight = imageNode.height() * Math.abs(imageNode.scaleY())
      let clipWidth, clipHeight
      const targetRatio = widthRatio / heightRatio
      const currentRatio = imageWidth / imageHeight
      if (targetRatio > currentRatio) {
        clipWidth = imageWidth
        clipHeight = clipWidth / targetRatio
      } else {
        clipHeight = imageHeight
        clipWidth = clipHeight * targetRatio
      }
      const offsetX = (imageWidth - clipWidth) / 2
      const offsetY = (imageHeight - clipHeight) / 2
      groupNode.clipX(offsetX)
      groupNode.clipY(offsetY)
      groupNode.clipWidth(clipWidth)
      groupNode.clipHeight(clipHeight)
      clipConfig.value = {
        x: offsetX,
        y: offsetY,
        width: clipWidth,
        height: clipHeight,
      }
    }

    updateCanvas()
    updateCurrentState()
  }
  const initializeImage = async () => {
    await nextTick()
    if (!editorImage.value || !konvaImage.value || !group.value) return
    const imageWidth = editorImage.value.width
    const imageHeight = editorImage.value.height
    const { width, height } = calculateRectDimensions()
    const groupNode = group.value.getNode()
    const imageNode = konvaImage.value.getNode()
    groupNode.scale({ x: 1, y: 1 })
    groupNode.rotation(0)
    groupNode.position({
      x: stageSize.value.width / 2,
      y: stageSize.value.height / 2,
    })
    groupNode.offset({
      x: width / 2,
      y: height / 2,
    })
    imageConfig.value = {
      image: editorImage.value,
      width: imageWidth,
      height: imageHeight,
    }
    const scaleX = width / imageWidth
    const scaleY = height / imageHeight
    const scale = Math.min(scaleX, scaleY)
    const scaledWidth = imageWidth * scale
    const scaledHeight = imageHeight * scale
    imageNode.position({
      x: (width - scaledWidth) / 2,
      y: (height - scaledHeight) / 2,
    })
    imageNode.scale({
      x: scale,
      y: scale,
    })
    imageNode.cache()
    // textItems.value = []
    selectedText.value = null
    const initialState: ImageState = {
      groupX: groupNode.x(),
      groupY: groupNode.y(),
      groupRotation: 0,
      groupScaleX: 1,
      groupScaleY: 1,
      imageX: (width - scaledWidth) / 2,
      imageY: (height - scaledHeight) / 2,
      imageScaleX: scale,
      imageScaleY: scale,
      groupWidth: width,
      groupHeight: height,
      imageWidth: imageWidth,
      imageHeight: imageHeight,
      originalWidth: imageWidth,
      originalHeight: imageHeight,
      filter: { name: 'none', percentage: 50 },
      // textItems: [],
      clip: null,
      isManualCrop: false,
    }
    updateClipping()
    updateCanvas()
    history.value.present = initialState
  }
  const initialize = () => {
    if (!image.value) {
      imageIsLoaded.value = false
      return
    }
    textItems.value = []
    selectedText.value = null
    const initializeAll = () => {
      if (editorImage.value && editorImage.value.complete && stage.value) {
        imageConfig.value = {
          image: editorImage.value,
        }
        nextTick(() => {
          initializeImage()
          initializeGroupOffset()
          stage.value?.getNode().on('wheel', handleWheel)
        })
      }
    }
    if (editorImage.value) {
      if (editorImage.value.complete) {
        initializeAll()
        imageIsLoaded.value = true
      } else {
        editorImage.value.addEventListener('load', () => {
          initializeAll()
          imageIsLoaded.value = true
        })
      }
    }
    const checkStage = setInterval(() => {
      if (stage.value && editorImage.value && editorImage.value.complete) {
        clearInterval(checkStage)
        initializeAll()
      }
    }, 100)
  }
  const applyZoom = () => {
    if (!group.value || !history.value.present) return
    saveToHistory()
    const groupNode = group.value.getNode()
    const zoomScale = zoomPercentage.value / 100
    const flipX = groupNode.scaleX() < 0 ? -1 : 1
    if (isCroping.value) {
      groupNode.scale({
        x: zoomScale * flipX,
        y: zoomScale,
      })
      groupNode.position({
        x: stageSize.value.width / 2,
        y: stageSize.value.height / 2,
      })
      const box = groupNode.getClientRect({ skipTransform: true })
      groupNode.offset({
        x: box.width / 2,
        y: box.height / 2,
      })
    } else {
      const wasManualCrop = isManualCrop.value
      const currentClipConfig = wasManualCrop ? { ...clipConfig.value } : null
      groupNode.scale({
        x: zoomScale * flipX,
        y: zoomScale,
      })
      if (wasManualCrop && currentClipConfig) {
        clipEnabled.value = true
        clipConfig.value = currentClipConfig
        groupNode.clipX(currentClipConfig.x)
        groupNode.clipY(currentClipConfig.y)
        groupNode.clipWidth(currentClipConfig.width)
        groupNode.clipHeight(currentClipConfig.height)
        const cropCenterX = currentClipConfig.x + currentClipConfig.width / 2
        const cropCenterY = currentClipConfig.y + currentClipConfig.height / 2
        groupNode.position({
          x: stageSize.value.width / 2,
          y: stageSize.value.height / 2,
        })
        groupNode.offset({
          x: cropCenterX,
          y: cropCenterY,
        })
      } else {
        groupNode.position({
          x: stageSize.value.width / 2,
          y: stageSize.value.height / 2,
        })
        const box = groupNode.getClientRect({ skipTransform: true })
        groupNode.offset({
          x: box.width / 2,
          y: box.height / 2,
        })
        if (!isManualCrop.value || selectedSize.value.ratio === 'x:y') {
          updateClipping()
        }
      }
    }
    updateCanvas()
    updateCurrentState()
  }
  const handleWheel = (e: Konva.KonvaEventObject<WheelEvent>) => {
    e.evt.preventDefault()
    const delta = e.evt.deltaY
    if (delta > 0) {
      if (zoomPercentage.value > MIN_ZOOM) {
        zoomPercentage.value -= 5
        applyZoom()
      }
    } else {
      if (zoomPercentage.value < MAX_ZOOM) {
        zoomPercentage.value += 5
        applyZoom()
      }
    }
  }
  const handleZoomIn = () => {
    if (zoomPercentage.value < MAX_ZOOM) {
      zoomPercentage.value += 10
      applyZoom()
    }
  }
  const handleZoomOut = () => {
    if (zoomPercentage.value > MIN_ZOOM) {
      zoomPercentage.value -= 10
      applyZoom()
    }
  }
  const applyState = (state: ImageState) => {
    if (!group.value || !konvaImage.value) return
    const groupNode = group.value.getNode()
    const imageNode = konvaImage.value.getNode()
    isManualCrop.value = state.isManualCrop || false
    groupNode.position({
      x: state.groupX !== undefined ? state.groupX : stageSize.value.width / 2,
      y: state.groupY !== undefined ? state.groupY : stageSize.value.height / 2,
    })
    if (state.groupOffsetX !== undefined && state.groupOffsetY !== undefined) {
      groupNode.offset({
        x: state.groupOffsetX,
        y: state.groupOffsetY,
      })
    } else if (state.groupWidth && state.groupHeight) {
      groupNode.offset({
        x: state.groupWidth / 2,
        y: state.groupHeight / 2,
      })
    }
    groupNode.rotation(
      state.groupRotation !== undefined ? state.groupRotation : 0,
    )
    groupNode.scale({
      x: state.groupScaleX !== undefined ? state.groupScaleX : 1,
      y: state.groupScaleY !== undefined ? state.groupScaleY : 1,
    })
    imageNode.position({
      x: state.imageX !== undefined ? state.imageX : 0,
      y: state.imageY !== undefined ? state.imageY : 0,
    })
    imageNode.scale({
      x: state.imageScaleX !== undefined ? state.imageScaleX : 1,
      y: state.imageScaleY !== undefined ? state.imageScaleY : 1,
    })
    zoomPercentage.value = Math.round(Math.abs(state.groupScaleY || 1) * 100)
    if (state.clip) {
      clipEnabled.value = true
      clipConfig.value = { ...state.clip }
      groupNode.clipX(state.clip.x)
      groupNode.clipY(state.clip.y)
      groupNode.clipWidth(state.clip.width)
      groupNode.clipHeight(state.clip.height)
    } else {
      clipEnabled.value = false
      isManualCrop.value = false
      clipConfig.value = {
        x: 0,
        y: 0,
        width: imageNode.width() * Math.abs(imageNode.scaleX()) * 2,
        height: imageNode.height() * Math.abs(imageNode.scaleY()) * 2,
      }
      groupNode.clipX(0)
      groupNode.clipY(0)
      groupNode.clipWidth(imageNode.width() * Math.abs(imageNode.scaleX()) * 2)
      groupNode.clipHeight(
        imageNode.height() * Math.abs(imageNode.scaleY()) * 2,
      )
    }
    if (state.filter) {
      imageNode.clearCache()
      imageNode.filters([])
      currentFilter.value = state.filter.name
      filterPercentage.value = state.filter.percentage
      lastAppliedFilter.value = {
        name: state.filter.name,
        percentage: state.filter.percentage,
        adjustments: state.filter.adjustments || {},
      }
      if (state.filter.adjustments) {
        adjustmentSliders.value.forEach((slider) => {
          if (
            state.filter.adjustments &&
            state.filter.adjustments[slider.name] !== undefined
          ) {
            slider.lavel = state.filter.adjustments[slider.name]
          }
        })
        adjustmentSliders.value.forEach((slider) => {
          if (
            (slider.name === 'blur' && slider.lavel !== 0) ||
            (slider.name !== 'blur' && slider.lavel !== 50)
          ) {
            applyFilterToNode(imageNode, slider.name, slider.lavel, true)
          }
        })
      } else if (state.filter.name !== 'none') {
        applyFilterToNode(imageNode, state.filter.name, state.filter.percentage)
      }
    }
    if (state.textItems) {
      textItems.value = JSON.parse(JSON.stringify(state.textItems))
    }
    selectedText.value = null
    if (state.elements) {
      elements.value = JSON.parse(JSON.stringify(state.elements))
    }
    selectedElement.value = null
    if (state.stickers) {
      stickers.value = JSON.parse(JSON.stringify(state.stickers))
    }
    selectedSticker.value = null
    if (transformerTextRef.value) {
      transformerTextRef.value.getNode().nodes([])
    }
    if (transformerElementRef.value) {
      transformerElementRef.value.getNode().nodes([])
    }
    if (transformerStickerRef.value) {
      transformerStickerRef.value.getNode().nodes([])
    }
    updateCanvas()
  }
  const handleUndo = () => {
    if (history.value.past.length > 0) {
      const previous = history.value.past.pop()!
      history.value.future = [
        history.value.present!,
        ...history.value.future,
      ].filter(Boolean)
      history.value.present = previous
      applyState(previous)
      updateUndoRedoState()
    }
  }
  const handleRedo = () => {
    if (history.value.future.length > 0) {
      const next = history.value.future.shift()!
      history.value.past = [
        ...history.value.past,
        history.value.present!,
      ].filter(Boolean)
      history.value.present = next
      applyState(next)
      updateUndoRedoState()
    }
  }
  const resetZoom = () => {
    zoomPercentage.value = 100
  }
  const resetCrop = () => {
    isManualCrop.value = false
    isCroping.value = false
    groupConfig.value.draggable = true
    cropRectConfig.value = {
      ...cropRectConfig.value,
      visible: false,
    }
  }
  const resetGroupPositionAndScale = () => {
    if (!group.value) return
    const groupNode = group.value.getNode()
    const { width, height } = calculateRectDimensions()
    groupNode.position({
      x: stageSize.value.width / 2,
      y: stageSize.value.height / 2,
    })
    groupNode.offset({ x: width / 2, y: height / 2 })
    groupNode.rotation(0)
    groupNode.scale({ x: 1, y: 1 })
  }
  const resetImagePositionAndScale = () => {
    if (!konvaImage.value || !history.value.present) return
    const imageNode = konvaImage.value.getNode()
    const { width, height } = calculateRectDimensions()
    const { originalWidth, originalHeight } = history.value.present
    const scaleX = width / originalWidth
    const scaleY = height / originalHeight
    const scale = Math.min(scaleX, scaleY)
    const scaledWidth = originalWidth * scale
    const scaledHeight = originalHeight * scale
    imageNode.position({
      x: (width - scaledWidth) / 2,
      y: (height - scaledHeight) / 2,
    })
    imageNode.rotation(0)
    imageNode.scale({ x: scale, y: scale })
  }
  const resetFilters = () => {
    currentFilter.value = 'none'
    filterPercentage.value = 50
    lastAppliedFilter.value = {
      name: 'none',
      percentage: 50,
      adjustments: {},
    }
  }
  const resetAdjustments = () => {
    if (!konvaImage.value) return
    const imageNode = konvaImage.value.getNode()
    adjustmentSliders.value.forEach((slider) => {
      slider.lavel = slider.name === 'blur' ? 0 : 50
    })
    imageNode.clearCache()
    imageNode.filters([])
    imageNode.brightness(0)
    imageNode.contrast(0)
    imageNode.saturation(0)
    if (typeof imageNode.blurRadius === 'function') imageNode.blurRadius(0)
    imageNode.cache()
  }
  const resetText = () => {
    textItems.value = []
    selectedText.value = null
    transformerTextRef.value?.getNode().nodes([])
  }
  const resetElements = () => {
    elements.value = []
    selectedElement.value = null
    transformerElementRef.value?.getNode().nodes([])
  }
  const resetStickers = () => {
    stickers.value = []
    selectedSticker.value = null
    transformerStickerRef.value?.getNode().nodes([])
  }
  const resetClip = () => {
    if (!group.value || !konvaImage.value) return
    const imageNode = konvaImage.value.getNode()
    const groupNode = group.value.getNode()
    const scale = imageNode.scaleX()
    clipEnabled.value = true
    clipConfig.value = {
      x: 0,
      y: 0,
      width: imageNode.width() * Math.abs(scale) * 2,
      height: imageNode.height() * Math.abs(scale) * 2,
    }
    groupNode.clipX(0)
    groupNode.clipY(0)
    groupNode.clipWidth(clipConfig.value.width)
    groupNode.clipHeight(clipConfig.value.height)
  }
  const resetHistory = () => {
    if (!history.value.present) return
    const currentState = getCurrentState()
    history.value = {
      past: [],
      present: currentState,
      future: [],
    }
    updateUndoRedoState()
  }
  const handleReset = () => {
    resetZoom()
    resetCrop()
    resetGroupPositionAndScale()
    resetImagePositionAndScale()
    resetFilters()
    resetAdjustments()
    resetText()
    resetElements()
    resetStickers()
    resetClip()
    updateCanvas()
    resetHistory()
    selectedSize.value = {
      id: 0,
      name: 'Original Ratio',
      ratio: 'x:y',
    }
  }
  const applyTransformWithCropHandling = (
    transformFn: (groupNode: Konva.Group) => void,
  ) => {
    if (!group.value || !konvaImage.value || !history.value.present) return
    saveToHistory()
    const groupNode = group.value.getNode()
    const wasManualCrop = isManualCrop.value
    const currentClipConfig = wasManualCrop ? { ...clipConfig.value } : null
    transformFn(groupNode)
    if (wasManualCrop && currentClipConfig) {
      clipEnabled.value = true
      clipConfig.value = currentClipConfig
      groupNode.clipX(currentClipConfig.x)
      groupNode.clipY(currentClipConfig.y)
      groupNode.clipWidth(currentClipConfig.width)
      groupNode.clipHeight(currentClipConfig.height)
      const cropCenterX = currentClipConfig.x + currentClipConfig.width / 2
      const cropCenterY = currentClipConfig.y + currentClipConfig.height / 2
      groupNode.position({
        x: stageSize.value.width / 2,
        y: stageSize.value.height / 2,
      })
      groupNode.offset({
        x: cropCenterX,
        y: cropCenterY,
      })
    } else {
      const boxBounds = groupNode.getClientRect({
        skipTransform: true,
        skipStroke: true,
        skipShadow: true,
      })
      groupNode.offset({
        x: boxBounds.width / 2,
        y: boxBounds.height / 2,
      })

      // Remove the condition to always update clipping
      // if (!isManualCrop.value || selectedSize.value.ratio === 'x:y') {
      updateClipping()
      // }
    }
    updateCanvas()
    updateCurrentState()
  }
  const handleRotate = () => {
    applyTransformWithCropHandling((groupNode) => {
      const currentRotation = groupNode.rotation()
      groupNode.rotation((currentRotation + 90) % 360)
    })
  }
  const handleFlip = () => {
    applyTransformWithCropHandling((groupNode) => {
      groupNode.scaleX(-groupNode.scaleX())
    })
  }
  const applyFilterToNode = (
    node: any,
    filterName: string,
    percentage: number,
    isAdjustment = false,
  ): void => {
    node.clearCache()
    let currentFilters = [...(node.filters() || [])]
    if (
      !isAdjustment ||
      !['brighten', 'contrast', 'saturation', 'blur'].includes(filterName)
    ) {
      node.filters([])
      currentFilters = []
      node.brightness(0)
      node.contrast(0)
      node.saturation(0)
      node.hue(0)
      if (typeof node.blurRadius === 'function') node.blurRadius(0)
      if (typeof node.pixelSize === 'function') node.pixelSize(1)
      if (typeof node.threshold === 'function') node.threshold(0.5)
      if (typeof node.noise === 'function') node.noise(0)
      if (typeof node.kaleidoscopePower === 'function')
        node.kaleidoscopePower(2)
      if (typeof node.embossStrength === 'function') node.embossStrength(0.5)
    }
    if (filterName === 'none') {
      node.cache()
      return
    }
    const norm = percentage / 100
    const addFilterIfNeeded = (filterType: any) => {
      if (!currentFilters.includes(filterType)) {
        currentFilters.push(filterType)
      }
    }
    switch (filterName) {
      case 'blur':
        addFilterIfNeeded(Konva.Filters.Blur)
        if (typeof node.blurRadius === 'function') node.blurRadius(norm * 20)
        break
      case 'brighten':
        addFilterIfNeeded(Konva.Filters.Brighten)
        node.brightness(norm * 2 - 1)
        break
      case 'contrast':
        addFilterIfNeeded(Konva.Filters.Contrast)
        node.contrast(norm * 200 - 100)
        break
      case 'hue':
        addFilterIfNeeded(Konva.Filters.HSV)
        node.hue(norm * 360 - 180)
        break
      case 'pixelate':
        addFilterIfNeeded(Konva.Filters.Pixelate)
        if (typeof node.pixelSize === 'function') node.pixelSize(norm * 40)
        break
      case 'grayscale':
        addFilterIfNeeded(Konva.Filters.Grayscale)
        break
      case 'sepia':
        addFilterIfNeeded(Konva.Filters.Sepia)
        break
      case 'invert':
        addFilterIfNeeded(Konva.Filters.Invert)
        break
      case 'solarize':
        addFilterIfNeeded(Konva.Filters.Solarize)
        if (typeof node.threshold === 'function') node.threshold(norm)
        break
      case 'saturation':
        addFilterIfNeeded(Konva.Filters.HSV)
        node.saturation(norm * 2 - 1)
        break
      case 'threshold':
        addFilterIfNeeded(Konva.Filters.Threshold)
        if (typeof node.threshold === 'function') node.threshold(norm)
        break
      case 'noise':
        addFilterIfNeeded(Konva.Filters.Noise)
        if (typeof node.noise === 'function') node.noise(norm)
        break
      case 'emboss':
        addFilterIfNeeded(Konva.Filters.Emboss)
        if (typeof node.embossStrength === 'function') node.embossStrength(norm)
        break
      case 'kaleidoscope':
        addFilterIfNeeded(Konva.Filters.Kaleidoscope)
        if (typeof node.kaleidoscopePower === 'function')
          node.kaleidoscopePower(Math.max(2, Math.round(norm * 6)))
        break
      case 'vintage':
        currentFilters = [
          Konva.Filters.Grayscale,
          Konva.Filters.Brighten,
          Konva.Filters.HSV,
        ]
        node.brightness(norm * 0.3)
        node.saturation(norm * 0.5)
        node.hue(norm * 20 - 10)
        break
      case 'gloomy':
        currentFilters = [Konva.Filters.Grayscale, Konva.Filters.Brighten]
        node.brightness(-0.4 * norm)
        break
      case 'cool':
        currentFilters = [Konva.Filters.HSV]
        node.hue(-40 * norm)
        node.saturation(0.3 + norm * 0.5)
        break
      case 'warm':
        currentFilters = [Konva.Filters.HSV]
        node.hue(40 * norm)
        node.saturation(0.3 + norm * 0.5)
        break
    }
    node.filters(currentFilters)
    node.cache()
  }
  const applyAdjustment = (slide: {
    id: number
    label: string
    name: string
    lavel: number
  }) => {
    if (!konvaImage.value) return
    const node = konvaImage.value.getNode()
    if (!node) return
    const previousFilter = { ...lastAppliedFilter.value }
    const adjustmentMap: Record<string, number> =
      adjustmentSliders.value.reduce<Record<string, number>>((acc, slider) => {
        acc[slider.name] = slider.lavel
        return acc
      }, {})
    adjustmentMap[slide.name] = slide.lavel
    lastAppliedFilter.value = {
      name: 'adjustment',
      percentage: 50,
      adjustments: { ...adjustmentMap },
    }
    applyFilterToNode(node, slide.name, slide.lavel, true)
    node.getLayer()?.batchDraw()
    saveToHistory()
  }
  const applyFilter = () => {
    if (!konvaImage.value) return
    const node = konvaImage.value.getNode()
    if (!node) return
    const previousFilter = { ...lastAppliedFilter.value }
    lastAppliedFilter.value = {
      name: currentFilter.value,
      percentage: filterPercentage.value,
    }
    applyFilterToNode(node, currentFilter.value, filterPercentage.value)
    node.getLayer()?.batchDraw()
    if (
      previousFilter.name !== currentFilter.value ||
      previousFilter.percentage !== filterPercentage.value
    ) {
      saveToHistory()
    }
  }
  const cancelCrop = () => {
    isCroping.value = false
    groupConfig.value.draggable = true
    cropRectConfig.value = {
      ...cropRectConfig.value,
      visible: false,
    }
    updateClipping()
    updateCanvas()
  }
  const handleCropDone = () => {
    finishCrop()
    isCroping.value = false
    groupConfig.value.draggable = true
  }
  const handleCrop = () => {
    if (!group.value || !konvaImage.value) return
    saveToHistory()
    isCroping.value = true
    groupConfig.value.draggable = false
    const groupNode = group.value.getNode()
    const imageNode = konvaImage.value.getNode()
    const imageScale = Math.abs(imageNode.scaleX())
    const fullWidth = imageNode.width() * imageScale
    const fullHeight = imageNode.height() * imageScale
    groupNode.clipX(0)
    groupNode.clipY(0)
    groupNode.clipWidth(fullWidth * 2)
    groupNode.clipHeight(fullHeight * 2)
    clipEnabled.value = false
    isManualCrop.value = false
    const currentZoom = zoomPercentage.value
    startCrop()
    if (currentZoom !== 100) {
      const zoomScale = currentZoom / 100
      const flipX = groupNode.scaleX() < 0 ? -1 : 1
      groupNode.scale({
        x: zoomScale * flipX,
        y: zoomScale,
      })
      groupNode.position({
        x: stageSize.value.width / 2,
        y: stageSize.value.height / 2,
      })
      const box = groupNode.getClientRect({ skipTransform: true })
      groupNode.offset({
        x: box.width / 2,
        y: box.height / 2,
      })
    }
    updateCanvas()
  }
  const handlePreview = () => {}
  const generatedImageData = (): GeneratedImageData | null => {
    if (!group.value || !history.value.present || !stage.value) return null
    const groupNode = group.value.getNode()
    const stageNode = stage.value.getNode()

    const applyFiltersToGroup = (targetGroup: any, sourceGroup: any) => {
      const sourceImages = sourceGroup.find('Image')
      if (sourceImages.length === 0) return

      const originalImage = sourceImages[0]
      const filterProperties = [
        'filters',
        'brightness',
        'contrast',
        'saturation',
        'hue',
        'blurRadius',
        'pixelSize',
        'threshold',
        'noise',
        'kaleidoscopePower',
        'embossStrength',
      ]

      targetGroup.find('Image').forEach((imageNode: any) => {
        filterProperties.forEach((prop) => {
          if (
            typeof imageNode[prop] === 'function' &&
            typeof originalImage[prop] === 'function'
          ) {
            imageNode[prop](originalImage[prop]())
          }
        })
        imageNode.cache()
      })
    }

    // Helper function to create export stage
    const createExportStage = (
      width: number,
      height: number,
      clonedGroup: any,
    ) => {
      const exportStage = new Konva.Stage({
        container: document.createElement('div'),
        width,
        height,
      })
      const exportLayer = new Konva.Layer()
      exportStage.add(exportLayer)

      const exportGroup = clonedGroup.clone()
      applyFiltersToGroup(exportGroup, groupNode)

      return { exportStage, exportLayer, exportGroup }
    }

    // Create temporary stage once
    const tempStage = new Konva.Stage({
      container: document.createElement('div'),
      width: stageNode.width(),
      height: stageNode.height(),
    })
    const tempLayer = new Konva.Layer()
    tempStage.add(tempLayer)

    const clonedGroup = groupNode.clone()
    applyFiltersToGroup(clonedGroup, groupNode)
    tempLayer.add(clonedGroup)
    tempLayer.draw()

    let dataURL: string

    try {
      if (clipEnabled.value) {
        const clipBounds = clipConfig.value
        const groupTransform = clonedGroup.getTransform()

        const corners = [
          { x: clipBounds.x, y: clipBounds.y },
          { x: clipBounds.x + clipBounds.width, y: clipBounds.y },
          { x: clipBounds.x, y: clipBounds.y + clipBounds.height },
          {
            x: clipBounds.x + clipBounds.width,
            y: clipBounds.y + clipBounds.height,
          },
        ].map((point) => groupTransform.point(point))

        const minX = Math.min(...corners.map((p) => p.x))
        const maxX = Math.max(...corners.map((p) => p.x))
        const minY = Math.min(...corners.map((p) => p.y))
        const maxY = Math.max(...corners.map((p) => p.y))

        const { exportStage, exportLayer, exportGroup } = createExportStage(
          maxX - minX,
          maxY - minY,
          clonedGroup,
        )

        exportGroup.position({
          x: exportGroup.x() - minX,
          y: exportGroup.y() - minY,
        })
        exportLayer.add(exportGroup)
        exportLayer.draw()

        dataURL = exportStage.toDataURL({
          pixelRatio: 2,
          mimeType: 'image/png',
          quality: 1,
        })
        exportStage.destroy()
      } else {
        const groupBounds = clonedGroup.getClientRect({
          skipTransform: false,
          skipShadow: true,
          skipStroke: true,
        })

        const { exportStage, exportLayer, exportGroup } = createExportStage(
          groupBounds.width,
          groupBounds.height,
          clonedGroup,
        )

        const originalBounds = groupNode.getClientRect({ skipTransform: true })
        exportGroup.position({
          x: groupBounds.width / 2,
          y: groupBounds.height / 2,
        })
        exportGroup.offset({
          x: originalBounds.width / 2,
          y: originalBounds.height / 2,
        })

        exportLayer.add(exportGroup)
        exportLayer.draw()

        dataURL = exportStage.toDataURL({
          pixelRatio: 2,
          mimeType: 'image/png',
          quality: 1,
        })
        exportStage.destroy()
      }
    } finally {
      tempStage.destroy()
    }

    return { dataURL }
  }
  const base64ToBlob = (base64Str: string): Blob | null => {
    const [header, data] = base64Str.split(',')
    const match = header?.match(/:(.*?);/)
    if (!match || !match[1]) {
      console.error('Invalid base64 header format')
      return null
    }
    const mime = match[1]
    const binary = atob(data)
    const array = new Uint8Array(binary.length)
    for (let i = 0; i < binary.length; i++) {
      array[i] = binary.charCodeAt(i)
    }
    const blob = new Blob([array], { type: mime })
    return blob
  }
  const imageEditingDone = () => {
    const result = generatedImageData()
    if (!result) return
    const { dataURL } = result
    const blob = base64ToBlob(dataURL)
    if (!blob) return
    const url = URL.createObjectURL(blob)
    store.commit('createPost/SET_OBJECT_URLS', url)
    if (currentEditorimage.value) {
      addOrUpdateSelectedImage({
        url: url,
        id: currentEditorimage.value.id,
      })
    }
  }
  watch(
    () => editorImage.value,
    (newVal, oldVal) => {
      if (newVal && newVal !== oldVal) {
        handleReset()
        const handleNewImage = () => {
          imageConfig.value = {
            image: newVal,
          }
          nextTick(() => {
            initializeImage()
            updateCanvas()
            updateUndoRedoState()
          })
        }
        if (newVal.complete) {
          handleNewImage()
        } else {
          newVal.onload = () => {
            handleNewImage()
          }
        }
      }
    },
    { deep: true },
  )
  watch(
    () => konvaImage.value,
    (newVal) => {
      if (newVal) {
        const imageNode = newVal.getNode()
        imageNode.cache()
      }
    },
    { deep: true },
  )
  watch(
    () => group.value,
    (newVal) => {
      if (newVal) {
        const groupNode = newVal.getNode()
        groupNode.on('dragend', saveToHistory)
        groupNode.on('transformend', saveToHistory)
      }
    },
    { deep: true },
  )
  watch(
    [currentFilter],
    ([newFilter]) => {
      if (newFilter) {
        filterPercentage.value = 50
        applyFilter()
      }
    },
    { deep: true },
  )
  watch(
    () => selectedSize.value,
    (newSize, oldSize) => {
      if (newSize.ratio !== oldSize?.ratio) {
        resetZoom()
        resetCrop()
        resetGroupPositionAndScale()
        resetImagePositionAndScale()
        // resetClip()
        saveToHistory()
        if (isCroping.value) {
          startCrop()
        } else {
          updateClipping()
        }
        updateCanvas()
      }
    },
    { deep: true },
  )
  watch(
    [textItems, elements, stickers],
    () => {
      const present = history.value.present
      if (present) {
        const currentState = getCurrentState()
        if (
          present &&
          JSON.stringify(present) === JSON.stringify(currentState)
        ) {
          return
        }
        saveToHistory()
      }
    },
    { deep: true },
  )
  onMounted(() => {
    initialize()
  })
  return {
    MIN_ZOOM,
    MAX_ZOOM,
    imageConfig,
    groupConfig,
    canvasContainer,
    history,
    group,
    stage,
    layer,
    elements,
    transformerTextRef,
    konvaImage,
    cropRectRef,
    transformerCropRef,
    transformerElementRef,
    zoomPercentage,
    isUndoDisabled,
    isRedoDisabled,
    isCroping,
    imageIsLoaded,
    editorImage,
    stageSize,
    currentFilter,
    adjustmentSliders,
    filterPercentage,
    selectedText,
    selectedElement,
    textItems,
    selectedSize,
    cropRectConfig,
    transformerTextConfig,
    transformerCropConfig,
    transformerElementConfig,
    saveToHistory,
    handleElementClick,
    handleZoomIn,
    handleZoomOut,
    applyZoom,
    handleUndo,
    handleRedo,
    handleReset,
    handleRotate,
    handleFlip,
    handleCrop,
    handleCropDone,
    handlePreview,
    applyFilter,
    generatedImageData,
    base64ToBlob,
    applyAdjustment,
    handleTextClick,
    handleTextDragEnd,
    handleTextTransformEnd,
    handleElementDragEnd,
    handleElementTransformEnd,
    getNodeById,
    handleStageClick,
    imageEditingDone,
    updateCropTransformer,
    cancelCrop,
    stickers,
    selectedSticker,
    transformerStickerRef,
    transformerStickersConfig,
    handleStickerClick,
    handleStickerDragEnd,
    handleStickerTransformEnd,
  }
}
export const useSharedImageEditor = createSharedComposable(useImageEditor)
