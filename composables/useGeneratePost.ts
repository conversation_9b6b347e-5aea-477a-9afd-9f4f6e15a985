import { useStore } from 'vuex'
import type { ImageEditor } from '~/types/imageEditor'
import type {
  AiImage,
  CapturedVideo,
  Hashtag,
  PreviewContent,
  Tag,
} from '~/types/savePostSettings'

export const useGeneratePost = () => {
  const store = useStore()

  const text = ref('')
  const selectedHashtags = ref<Hashtag[]>([])
  const selectedTags = ref<Tag[]>([])

  const selectedImages = computed<AiImage[]>(
    () => store.state.createPost.selectedImages,
  )
  const previewContent = computed<PreviewContent | null>(
    () => store.state.createPost.previewContent,
  )
  const updatePreviewContent = (content: PreviewContent) => {
    store.commit('createPost/UPDATE_PREVIEW_CONTENT', content)
  }
  const capturedVideo = computed<CapturedVideo | null>(
    () => store.state.createPost.capturedVideo,
  )
  const addOrUpdateSelectedImage = (image: AiImage) => {
    store.commit('createPost/ADD_OR_UPDATE_SELECTED_IMAGE', image)
  }
  const setSelectedImages = (images: AiImage[]) => {
    store.commit('createPost/SET_SELECTED_IMAGES', images)
  }
  const removeSelectedImage = (id: number) => {
    store.commit('createPost/REMOVE_SELECTED_IMAGE', id)
  }
  const setImageEditor = (imageEditor: ImageEditor) => {
    store.commit('createPost/SET_IMAGE_EDITOR', imageEditor)
  }

  const revokeVideoAndThumbnailURL = () => {
    if (capturedVideo.value) {
      URL.revokeObjectURL(capturedVideo.value?.thumbnailUrl)
      URL.revokeObjectURL(capturedVideo.value?.videoUrl)
    }
  }

  const getNumericId = () => {
    const timestamp = Date.now()
    const highRes = Math.floor(performance.now() * 1000) % 1000000
    const random = Math.floor(Math.random() * 100000)

    let idStr = `${timestamp}${highRes}${random}`.slice(0, 16)
    let id = Number(idStr)

    if (!Number.isSafeInteger(id)) {
      id = Number(`${timestamp}${random}`)
    }

    return id
  }

  watch(text, (newValue) => {
    updatePreviewContent({ text: newValue })
  })

  watch(
    () => previewContent.value,
    (newContent) => {
      if (newContent) {
        if (newContent.text !== undefined) {
          text.value = newContent.text
        }
        if (newContent.images !== undefined) {
          setSelectedImages(newContent.images)
        }
        if (newContent.hashtags !== undefined) {
          selectedHashtags.value = newContent.hashtags
        }
        if (newContent.tags !== undefined) {
          selectedTags.value = newContent.tags
        }
      }
    },
    { immediate: true, deep: true },
  )

  return {
    text,
    selectedImages,
    selectedHashtags,
    selectedTags,
    updatePreviewContent,
    revokeVideoAndThumbnailURL,
    setSelectedImages,
    removeSelectedImage,
    getNumericId,
    setImageEditor,
    addOrUpdateSelectedImage,
  }
}
