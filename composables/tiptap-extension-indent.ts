// import { Extension } from '@tiptap/core'
// import { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'

// export interface IndentOptions {
//   types: string[]
//   minIndent: number
//   maxIndent: number
//   indentLevel: number
// }

// export const CustomIndent = Extension.create<IndentOptions>({
//   name: 'indent',

//   addOptions() {
//     return {
//       types: ['paragraph', 'heading'],
//       minIndent: 0,
//       maxIndent: 1000,
//       indentLevel: 20,
//     }
//   },

//   addGlobalAttributes() {
//     return [
//       {
//         types: this.options.types,
//         attributes: {
//           indent: {
//             default: 0,
//             parseHTML: element => {
//               return parseInt(element.style.marginLeft) || 0
//             },
//             renderHTML: attributes => {
//               if (!attributes.indent) return {}
//               return {
//                 style: `margin-left: ${attributes.indent}px`,
//               }
//             },
//           },
//         },
//       },
//     ]
//   },

//   addProseMirrorPlugins() {
//     return [
//       new Plugin({
//         key: new PluginKey('indent'),
//         props: {
//           handleKeyDown: (view, event) => {
//             if (event.key === 'Tab') {
//               if (event.shiftKey) {
//                 this.editor.commands.decreaseIndent()
//               } else {
//                 this.editor.commands.increaseIndent()
//               }
//               return true
//             }
//             return false
//           },
//         },
//       }),
//     ]
//   },

//   addCommands() {
//     return {
//       increaseIndent:
//         () =>
//         ({ tr, state, dispatch }) => {
//           const { selection } = state
//           const { from, to } = selection

//           let hasChanged = false
//           state.doc.nodesBetween(from, to, (node, pos) => {
//             const nodeType = node.type.name
//             if (this.options.types.includes(nodeType)) {
//               const indent = node.attrs.indent || 0
//               if (indent < this.options.maxIndent) {
//                 const newIndent = Math.min(
//                   indent + this.options.indentLevel,
//                   this.options.maxIndent
//                 )
//                 if (dispatch) {
//                   tr.setNodeMarkup(pos, undefined, {
//                     ...node.attrs,
//                     indent: newIndent,
//                   })
//                 }
//                 hasChanged = true
//               }
//             }
//           })

//           if (dispatch && hasChanged) {
//             dispatch(tr)
//           }
//           return hasChanged
//         },

//       decreaseIndent:
//         () =>
//         ({ tr, state, dispatch }) => {
//           const { selection } = state
//           const { from, to } = selection

//           let hasChanged = false
//           state.doc.nodesBetween(from, to, (node, pos) => {
//             const nodeType = node.type.name
//             if (this.options.types.includes(nodeType)) {
//               const indent = node.attrs.indent || 0
//               if (indent > this.options.minIndent) {
//                 const newIndent = Math.max(
//                   indent - this.options.indentLevel,
//                   this.options.minIndent
//                 )
//                 if (dispatch) {
//                   tr.setNodeMarkup(pos, undefined, {
//                     ...node.attrs,
//                     indent: newIndent,
//                   })
//                 }
//                 hasChanged = true
//               }
//             }
//           })

//           if (dispatch && hasChanged) {
//             dispatch(tr)
//           }
//           return hasChanged
//         },
//     }
//   },
// })

import { Extension } from '@tiptap/core'
import { Plugin, PluginKey } from '@tiptap/pm/state'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    indent: {
      /**
       * Increase indent
       */
      increaseIndent: () => ReturnType
      /**
       * Decrease indent
       */
      decreaseIndent: () => ReturnType
    }
  }
}

export interface IndentOptions {
  types: string[]
  minIndent: number
  maxIndent: number
  indentLevel: number
}

export const CustomIndent = Extension.create<IndentOptions>({
  name: 'indent',

  addOptions() {
    return {
      types: ['paragraph', 'heading'],
      minIndent: 0,
      maxIndent: 1000,
      indentLevel: 20,
    }
  },

  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          indent: {
            default: 0,
            parseHTML: (element) => {
              return parseInt(element.style.marginLeft) || 0
            },
            renderHTML: (attributes) => {
              if (!attributes.indent) return {}
              return {
                style: `margin-left: ${attributes.indent}px`,
              }
            },
          },
        },
      },
    ]
  },

  addCommands() {
    return {
      increaseIndent:
        () =>
          ({ tr, state, dispatch }) => {
          console.log('hello wporld',tr, state, dispatch)
          const { selection } = state
          const { from, to } = selection

          let hasChanged = false
          state.doc.nodesBetween(from, to, (node, pos) => {
            const nodeType = node.type.name
            if (this.options.types.includes(nodeType)) {
              const indent = node.attrs.indent || 0
              if (indent < this.options.maxIndent) {
                const newIndent = Math.min(
                  indent + this.options.indentLevel,
                  this.options.maxIndent,
                )
                console.log(newIndent, 'newIndent', tr.setNodeMarkup(pos, undefined, {
                  ...node.attrs,
                  indent: newIndent,
                }))
                if (dispatch) {
                  tr.setNodeMarkup(pos, undefined, {
                    ...node.attrs,
                    indent: newIndent,
                  })
                }
                hasChanged = true
              }
            }
          })

          if (dispatch && hasChanged) {
            dispatch(tr)
          }
          return hasChanged
        },

      decreaseIndent:
        () =>
        ({ tr, state, dispatch }) => {
          const { selection } = state
          const { from, to } = selection

          let hasChanged = false
          state.doc.nodesBetween(from, to, (node, pos) => {
            const nodeType = node.type.name
            if (this.options.types.includes(nodeType)) {
              const indent = node.attrs.indent || 0
              if (indent > this.options.minIndent) {
                const newIndent = Math.max(
                  indent - this.options.indentLevel,
                  this.options.minIndent,
                )
                if (dispatch) {
                  tr.setNodeMarkup(pos, undefined, {
                    ...node.attrs,
                    indent: newIndent,
                  })
                }
                hasChanged = true
              }
            }
          })

          if (dispatch && hasChanged) {
            dispatch(tr)
          }
          return hasChanged
        },
    }
  },
})
