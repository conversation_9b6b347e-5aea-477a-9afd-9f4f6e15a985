import { useVuelidate } from '@vuelidate/core'
import { email, maxLength, required } from '@vuelidate/validators'
import { useStore } from 'vuex'
import { useInputValidations } from '~/composables/useValidations'
import { CHECK_CODE, USER_LOGOUT, USER_PROFILE } from '~/constants/urls'

export const useAuth = () => {
  const addressError = ref(false)
  const store = useStore()
  const config = useRuntimeConfig()
  const nuxtApp = useNuxtApp()
  const router = useRouter()
  const processing = ref(false)
  const tokenCookie = useCookie('token', {
    // domain: '.sharparchive.com',
    domain: 'localhost',
    path: '/',
    maxAge: 3600,
  })
  const refreshTokenCookie = useCookie('refresh', {
    maxAge: 3600,
  })
  const userInfo = useCookie('userInfo', {
    // domain: '.sharparchive.com',
    domain: 'localhost',
    path: '/',
    maxAge: 3600,
  })

  const loggedIn = useCookie('loggedIn', {
    maxAge: 3600,
  })

  const setCodeInfo = useCookie('codeInfo')

  const notifications = useCookie('notifications')
  const showErrorModal = useCookie('showErrorModal')

  const user = computed(() => {
    userInfo.value = store.state.auth.user
    return store.state.auth.user
  })

  // watch(
  //   () => userInfo.value,
  //   (data) => {
  //     if (data && data.userPermission === 'Partner') {
  //       loggedIn.value = null
  //       store.commit('auth/SET_LOGGEDIN')
  //       window.location.reload()
  //     }
  //   }
  // )

  const logout = async () => {
    return new Promise((resolve, reject) => {
      try {
        $fetch(USER_LOGOUT, {
          body: {
            refresh: refreshTokenCookie.value,
          },
          method: 'POST',
          headers: {
            Authorization: `Bearer ${tokenCookie.value}`,
            'X-CSRF-Token': tokenCookie.value,
          },
        })
          .then((response) => {
            if (response.detail === 'Successfully logged out.') {
              // tokenCookie.value = null
              // refreshTokenCookie.value = null
              // loggedIn.value = null
              // setTimeout(() => {
              //   store.dispatch('auth/setReset', {
              //     token: '',
              //     refresh: '',
              //     user: null,
              //     loggedIn: false,
              //   })
              // }, 500)
              resetAllValue()
              resolve(response)
            } else {
              reject(response)
            }
          })
          .catch((error) => {
            reject(error)
          })
      } catch (error) {
        reject(error)
      }
    })
  }
  const resetAllValue = () => {
    tokenCookie.value = null
    refreshTokenCookie.value = null
    loggedIn.value = null
    userInfo.value = null
    setTimeout(() => {
      store.dispatch('auth/setReset', {
        token: '',
        refresh: '',
        user: null,
        loggedIn: false,
      })
    }, 500)
  }
  const fetchUser = async () => {
    if (
      tokenCookie.value &&
      (!user.value || (user.value && user.value.paymentStatus === ''))
    ) {
      let status
      const data = await $fetch(USER_PROFILE, {
        headers: {
          Authorization: `Bearer ${tokenCookie.value}`,
          'X-CSRF-Token': tokenCookie.value,
        },
        onResponse({ request, response, options }) {
          status = response.status
          if (status === 401) {
            resetAllValue()
            setTimeout(() => {
              userInfo.value = null
              router.go(0)
            })
          }
        },
      })
      if (data) {
        userInfo.value = data.data
        store.dispatch('auth/setUser', { user: userInfo.value })
        if (
          config.public.workflow === 'dev' ||
          config.public.workflow === 'live'
        ) {
          if (userInfo.value && userInfo.value.isTrial) {
            store.commit(
              'SET_CURRENT_PAYMENT_SYSTEM_METHOD',
              'SetupAccountPaymentOption',
            )
          }
        }
      }
    }
  }
  const fetchAuthUser = async () => {
    if (tokenCookie.value) {
      let status
      const data = await $fetch(USER_PROFILE, {
        headers: {
          Authorization: `Bearer ${tokenCookie.value}`,
          'X-CSRF-Token': tokenCookie.value,
        },
        onResponse({ request, response, options }) {
          status = response.status
          if (status === 401) {
            resetAllValue()
            setTimeout(() => {
              userInfo.value = null
              router.go(0)
            })
          }
        },
      })
      if (data) {
        userInfo.value = data.data
        store.dispatch('auth/setUser', { user: userInfo.value })
        if (
          config.public.workflow === 'dev' ||
          config.public.workflow === 'live'
        ) {
          if (userInfo.value && userInfo.value.isTrial) {
            store.commit(
              'SET_CURRENT_PAYMENT_SYSTEM_METHOD',
              'SetupAccountPaymentOption',
            )
          }
        }
      }
    }
  }

  const updateProcess = ref(false)
  const updateUserProfile = async (profileUserInfo) => {
    const formData = new FormData()
    Object.keys(profileUserInfo).forEach((key) => {
      formData.append(key, profileUserInfo[key])
    })
    updateProcess.value = true
    try {
      const res = await $fetch(USER_PROFILE, {
        headers: {
          Authorization: `Bearer ${tokenCookie.value}`,
          'X-CSRF-Token': tokenCookie.value,
        },
        method: 'PUT',
        body: formData,
      })
      if (res && res.success) {
        nuxtApp.$toast('success', {
          message: res.message,
          className: 'toasted-bg-archive',
        })
        addressError.value = false
        store.dispatch('auth/setUser', { user: res.data })
      } else {
        if (res.message !== 'Address is invalid') {
          nuxtApp.$toast('error', {
            message: res.message,
            className: 'toasted-bg-alert',
          })
        } else if (res.message === 'Address is invalid') {
          addressError.value = true
        }
      }
    } catch (error) {
      nuxtApp.$toast('error', {
        message: error.message,
        className: 'toasted-bg-alert',
      })
      addressError.value = false
    } finally {
      updateProcess.value = false
    }
  }

  const setAuthCookies = async (token, refresh, userLoggedIn) => {
    tokenCookie.value = token ? token : tokenCookie.value
    refreshTokenCookie.value = refresh ? refresh : refreshTokenCookie.value
    loggedIn.value = userLoggedIn ? userLoggedIn : loggedIn.value
    await fetchUser()
    store.dispatch('auth/setToken', {
      token: tokenCookie.value,
      refresh: refreshTokenCookie.value,
      loggedIn: loggedIn.value,
    })
  }
  const setAccountErrorsCookies = (value) => {
    notifications.value = value
  }
  const account = ref({
    firstName: '',
    lastName: '',
    email: '',
  })
  const { checkEmail } = useInputValidations()
  // validations
  const rules = {
    firstName: { required, maxLength: maxLength(20) },
    lastName: { required, maxLength: maxLength(20) },
    email: {
      required,
      email,
    },
  }
  const v$ = useVuelidate(rules, account)
  const getSignupCode = async (value) => {
    try {
      processing.value = true
      const response = await $fetch(`${CHECK_CODE}?${value}`)
      if (response.success) {
        nuxtApp.$toast('success', {
          message: response.message,
          className: 'toasted-bg-archive',
        })
        account.value.signupCode = response.code
        store.commit('auth/SET_SIGNUP_CODE_INFO', account.value)
        router.push('/signup')
        processing.value = false
      } else {
        nuxtApp.$toast('error', {
          message: response.message,
          className: 'toasted-bg-alert',
        })
        processing.value = false
      }
    } catch (error) {
      nuxtApp.$toast('error', {
        message: error,
        className: 'toasted-bg-alert',
      })
      processing.value = false
    }
  }

  return {
    logout,
    fetchUser,
    setAuthCookies,
    updateUserProfile,
    resetAllValue,
    tokenCookie,
    refreshTokenCookie,
    userInfo,
    loggedIn,
    updateProcess,
    setCodeInfo,
    addressError,
    fetchAuthUser,
    getSignupCode,
    checkEmail,
    processing,
    v$,
    notifications,
    setAccountErrorsCookies,
    showErrorModal,
  }
}
