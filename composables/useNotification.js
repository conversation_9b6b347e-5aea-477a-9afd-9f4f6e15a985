import { useStore } from 'vuex'

const useNotification = () => {
  const $config = useRuntimeConfig()
  const { dispatch, commit, state } = useStore()

  const socket = ref()
  const heartbeatInterval = ref(null)

  const websocket = computed(() => state.profile.websocket)

  const joinNotification = (token) => {
    if (process.client) {
      socket.value = new WebSocket(
        `${$config.public.wsHost}/ws/notification/?token=${token}`,
      )
      commit('profile/SET_WEB_SOCKET', socket.value)
      sendNotification()
      receiveNotification()
      closeNotification()
    }
  }

  const sendNotification = () => {
    const heartbeat = 'PING'
    // let missedHeartbeats = 0
    if (socket.value) {
      socket.value.onopen = () => {
        if (heartbeatInterval.value === null) {
          // missedHeartbeats = 0;
          heartbeatInterval.value = setInterval(() => {
            if (socket.value.readyState === WebSocket.OPEN) {
              try {
                // missedHeartbeats++;
                // if (missedHeartbeats >= 3) {
                //   throw new Error("Too many missed heartbeats.");
                // }
                socket.value.send(heartbeat)
              } catch (e) {
                clearInterval(heartbeatInterval.value)
                heartbeatInterval.value = null
                console.warn('Closing connection. Reason: ' + e.message)
                closeSocket()
              }
            }
          }, 5000) // Here I increase the time from 5s to 2mins 120000
        }
      }
    }
  }

  const receiveNotification = () => {
    if (socket.value) {
      socket.value.onmessage = (e) => {
        const data = JSON.parse(e.data)
        if (data) {
          if (data.kind === 'notification') {
            commit('notifications/SET_OFFSET', 0)
            commit('notifications/SET_LIMIT', 25)
            commit('notifications/SET_NOTIFICATION', [])
            commit('notifications/SET_OLD_NOTIFICATION', [])
            dispatch('notifications/getAllNotifications')
          }
          if (data.kind === 'logout') {
            dispatch('profile/profileLogout')
          }
        }
      }
    }
  }

  const closeNotification = () => {
    if (socket.value) {
      socket.value.onerror = (e) => {
        clearInterval(heartbeatInterval.value)
        heartbeatInterval.value = null
        console.warn('Closing connection. Reason: ' + e.message)
        closeSocket()
      }
    }
  }

  const closeSocket = () => {
    if (socket.value) {
      socket.value.close()
      clearInterval(heartbeatInterval.value)
      heartbeatInterval.value = null
    } else if (websocket.value) {
      websocket.value.close()
      clearInterval(heartbeatInterval.value)
      heartbeatInterval.value = null
    }
  }

  return {
    joinNotification,
    closeSocket,
    closeNotification,
  }
}

export default useNotification