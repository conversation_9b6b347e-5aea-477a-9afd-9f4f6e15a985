// ~/composables/CustomImage.ts
import Image from '@tiptap/extension-image'

export const CustomImage = Image.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      size: {
        default: 'bestfit',
        parseHTML: (element) => element.getAttribute('data-size') || 'bestfit',
        renderHTML: (attributes) => {
          return {
            'data-size': attributes.size,
          }
        },
      },
    }
  },

  addCommands() {
    return {
      ...this.parent?.(),
    }
  },

  addNodeView() {
    return ({ node, getPos, editor }) => {
      const img = document.createElement('img')
      img.src = node.attrs.src
      img.alt = node.attrs.alt || ''
      img.title = node.attrs.title || ''

      // Apply size styling
      if (node.attrs.width) img.style.width = node.attrs.width
      if (node.attrs.height) img.style.height = node.attrs.height

      // Apply size class for styling
      img.className = `image-${node.attrs.size || 'bestfit'}`

      // Add data attribute to identify as custom image
      img.setAttribute('data-custom-image', 'true')

      return { dom: img }
    }
  },
})
