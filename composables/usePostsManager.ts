import { compareAsc, format, isSameDay, parseISO, startOfDay } from 'date-fns'
import { useStore } from 'vuex'

export interface Status {
  id: number
  status: string
}

export function usePostsManager() {
  const { selectedDate, currentTab } = useCalendarManager()
  const store = useStore()
  const searchInput = ref<HTMLInputElement | null>(null)
  const selectedStatus = ref<Status | null>(null)

  // Computed properties
  const posts = computed(() => store.state.post.posts)
  const showSearch = computed(() => store.state.post.isSearchExpanded)

  // Data transformation methods
  const transformDataByHour = (filteredData: any[]) => {
    const groupedData: Record<string, any[]> = {}

    const sortedData = filteredData.sort((a, b) =>
      compareAsc(parseISO(a.date), parseISO(b.date)),
    )
    sortedData.forEach((item) => {
      const itemDate = parseISO(item.date)
      const timeKey = format(itemDate, 'hh:00 aaa')

      if (!groupedData[timeKey]) {
        groupedData[timeKey] = []
      }
      groupedData[timeKey].push(item)
    })

    return Object.entries(groupedData).map(([time, data], index) => ({
      id: index + 1,
      time,
      data,
    }))
  }

  const transformPostsToEvents = (posts: any[]) => {
    return posts.map((post) => ({
      id: post.id.toString(),
      title: post.title,
      start: post.date,
      allDay: false,
      extendedProps: {
        id: post.id.toString(),
        socialPlatform: post.socialPlatform,
        profileImage: post.profileImage,
        image: post.image,
        name: post.name,
        status: post.status,
        content: post.content,
        date: post.date,
      },
    }))
  }

  // Methods
  const toggleSearch = () => {
    store.commit('post/SET_IS_SEARCH_EXPANDED', true)
  }

  const handleSelectStatus = (option: Status | null) => {
    selectedStatus.value = option
    if (!option) {
      if (currentTab.value === 'List' && selectedDate.value) {
        filterPostsByDate(selectedDate.value)
      } else {
        initializePostEvents()
      }
      return
    }
    const filteredPosts = posts.value.filter(
      (post: any) => post.status === option?.status,
    )

    if (currentTab.value === 'List') {
      const transformedData = transformDataByHour(filteredPosts)
      store.commit('post/SET_POST_LIST', transformedData)
    } else {
      const transformedEventsData = transformPostsToEvents(filteredPosts)
      store.commit('post/SET_POST_EVENTS', transformedEventsData)
    }
  }

  const filterPostsByDate = (date: Date) => {
    if (!date || posts.value.length === 0) {
      store.commit('post/SET_POST_LIST', [])
      return
    }

    const normalizedSelectedDate = startOfDay(new Date(date))

    const filteredData = posts.value.filter((item: any) => {
      const itemDate = parseISO(item.date)
      return isSameDay(itemDate, normalizedSelectedDate)
    })

    const transformedData = transformDataByHour(filteredData)
    store.commit('post/SET_POST_LIST', transformedData)
  }

  const initializePostEvents = () => {
    if (posts.value.length === 0) {
      store.commit('post/SET_POST_EVENTS', [])
      return
    }
    const transformedData = transformPostsToEvents(posts.value)
    store.commit('post/SET_POST_EVENTS', transformedData)
  }

  // Watchers
  watch(showSearch, (newVal) => {
    if (newVal) {
      nextTick(() => {
        searchInput.value?.focus()
      })
    }
  })

  return {
    // State
    searchInput,
    selectedStatus,
    // Computed
    posts,
    showSearch,
    // Methods
    transformDataByHour,
    transformPostsToEvents,
    toggleSearch,
    handleSelectStatus,
    filterPostsByDate,
    initializePostEvents,
  }
}
