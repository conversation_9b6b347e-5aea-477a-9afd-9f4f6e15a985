export const useFetched = () => {
  const fetch = async (request, options, accessToken) => {
    const tokenCookie = useCookie('token')

    // Step 1: Get the IP first
    let clientIp = '0.0.0.0'
    try {
      const ipData = await $fetch('/api/get-ip')
      clientIp = ipData.ip
      console.log(clientIp, 'clientIp')
    } catch (error) {
      console.warn('Could not fetch client IP:', error)
    }

    return await $fetch(request, {
      ...options,
      headers: {
        'Cache-Control': 'no-cache',
        Authorization:
          tokenCookie.value || accessToken
            ? `Bearer ${tokenCookie.value || accessToken}`
            : '',
        'X-Forwarded-For': clientIp, // ✅ use here
        'X-CSRF-Token': tokenCookie.value,
      },
    })
  }

  return {
    fetch,
  }
}
