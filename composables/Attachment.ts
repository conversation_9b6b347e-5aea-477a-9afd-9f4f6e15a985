// plugins/tiptap/Attachment.ts
import { Node, mergeAttributes } from '@tiptap/core'

export const Attachment = Node.create({
  name: 'attachment',
  group: 'block',
  atom: true,
  selectable: true,

  addAttributes() {
    return {
      fileName: { default: null },
      fileUrl: { default: null },
    }
  },

  parseHTML() {
    return [{ tag: 'attachment-block' }]
  },

  renderHTML({ HTMLAttributes }) {
    return ['attachment-block', mergeAttributes(HTMLAttributes), `${HTMLAttributes.fileName}`]
  },

  addNodeView() {
    return ({ node }) => {
      const el = document.createElement('div')
      el.className = 'attachment-preview'
      el.innerHTML = `
        <div class="bg-gray-100 p-2 rounded shadow-sm flex justify-between items-center">
          <span class="font-medium">${node.attrs.fileName}</span>
          <a href="${node.attrs.fileUrl}" target="_blank" class="text-blue-500 underline text-sm">Download</a>
        </div>
      `
      return {
        dom: el,
      }
    }
  },
})
