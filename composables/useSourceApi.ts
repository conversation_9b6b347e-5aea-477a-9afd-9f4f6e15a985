import { useStore } from 'vuex'
interface SubAccount {
  id: number
  text: string
  value: number
  select: boolean
}
interface Account {
  id: number
  text: string
  value: number
  select: boolean
  subAccounts: SubAccount[]
}

const accounts = ref<Account[]>([
  {
    id: 1,
    text: 'Social Media',
    value: 1,
    select: false,
    subAccounts: [
      {
        id: 1,
        text: 'Facebook',
        value: 1,
        select: true,
      },
      {
        id: 2,
        text: 'Instagram',
        value: 2,
        select: true,
      },
      {
        id: 3,
        text: 'Twitter',
        value: 3,
        select: true,
      },
      {
        id: 4,
        text: 'LinkedIn',
        value: 4,
        select: true,
      },
      {
        id: 5,
        text: 'Tiktok',
        value: 5,
        select: false,
      },
    ],
  },
  {
    id: 2,
    text: 'Emails',
    value: 2,
    select: false,
    subAccounts: [
      {
        id: 1,
        text: 'Gmail',
        value: 1,
        select: false,
      },
      {
        id: 2,
        text: 'Microsoft',
        value: 2,
        select: false,
      },
    ],
  },
  {
    id: 3,
    text: 'Text and Chat',
    value: 3,
    select: false,
    subAccounts: [
      {
        id: 1,
        text: 'T-Mobile (<PERSON>)',
        value: 1,
        select: false,
      },
      {
        id: 2,
        text: 'WhatsApp (<PERSON>)',
        value: 2,
        select: false,
      },
    ],
  },
])
const selectedSubAccounts = ref<SubAccount[]>([])

export const useSourceApi = () => {
  const store = useStore()
  const router = useRouter()

  const addSubAccount = (subAccount: SubAccount) => {
    const isAlreadySelected = selectedSubAccounts.value.some(
      (selected) =>
        selected.id === subAccount.id && selected.text === subAccount.text,
    )

    if (!isAlreadySelected) {
      selectedSubAccounts.value.push(subAccount)
    }
  }

  const removeSubAccount = (subAccount: SubAccount) => {
    selectedSubAccounts.value = selectedSubAccounts.value.filter(
      (selected) =>
        !(selected.id === subAccount.id && selected.text === subAccount.text),
    )
  }

  const updateSelectedSubAccounts = () => {
    const selected: SubAccount[] = []
    accounts.value.forEach((account) => {
      account.subAccounts.forEach((subAccount) => {
        if (subAccount.select) {
          selected.push(subAccount)
        }
      })
    })
    selectedSubAccounts.value = selected
  }

  onMounted(() => {
    updateSelectedSubAccounts()
  })

  const toggleCheckAll = (id: string, items: Account[]) => {
    items.forEach((item: Account) => {
      if (`${item.id}_${item.text}` === id) {
        item.select = !item.select
        if (item.select) {
          item.subAccounts.forEach((subAccount: SubAccount) => {
            subAccount.select = true
            addSubAccount(subAccount)
          })
        } else if (!item.select) {
          item.subAccounts.forEach((subAccount: SubAccount) => {
            subAccount.select = false
            removeSubAccount(subAccount)
          })
        }
      }
    })
  }
  const checkUncheckSubAccounts = (id: string, items: Account[]) => {
    items.forEach((item: Account) => {
      item.subAccounts.forEach((subAccount) => {
        if (`${subAccount.id}_${subAccount.text}` === id) {
          subAccount.select = !subAccount.select

          if (subAccount.select) {
            addSubAccount(subAccount)
          } else {
            removeSubAccount(subAccount)
          }
          const allSelected = item.subAccounts.every((sub) => sub.select)
          const anySelected = item.subAccounts.some((sub) => sub.select)
          item.select = allSelected ? true : anySelected ? false : item.select
        }
      })
    })
  }
  const getAllMessages = (chatId: number, messageId: number) => {
    store.commit('social/SET_SHOW_ALL_MESSAGES', true)
    changePersonListSelectValue(chatId)
    store.commit('social/SET_SPECIFIC_ID', messageId)
    store.commit('social/SET_SINGLE_CONVERTIONS')
  }
  const changePersonListSelectValue = (chatId: number) => {
    store.commit('social/CHANGE_PERSON_LIST_SELECT_VALUE', chatId)
  }
  const getSinglePost = (
    item: any,
    type: string,
    postId: number,
    commentId: number,
  ) => {
    switch (type) {
      case 'comments':
        store.commit('social/SET_SHOW_SINGLE_POST', true)
        store.commit('social/SET_SINGLE_POST', {
          mentionText: item.mentionText,
          profileImageUrl: item.img,
          profilename: item.name,
          text: item.description,
        })
        store.commit('social/SET_SPECIFIC_ID', commentId)
        break
      case 'tagged':
        store.commit('social/SET_SHOW_SINGLE_POST', true)
        store.commit('social/SET_SINGLE_POST', {
          mentionText: item.mentionText,
          profileImageUrl: item.img,
          profilename: item.name,
          text: item.description,
        })
        break
      case 'mentions':
        store.commit('social/SET_SHOW_SINGLE_POST', false)
        setTimeout(() => {
          store.commit('social/SET_SHOW_SINGLE_POST', true)
        }, 100)

        store.commit('social/SET_SINGLE_POST', {
          mentionText: item.mentionText,
          profileImageUrl: item.img,
          profilename: item.name,
          text: item.description,
        })
        store.commit('social/SET_SPECIFIC_ID', commentId)
        break
    }
  }
  const getRouteOnProvider = (
    provider: string,
    accountType: string,
    username: string,
  ) => {
    if (
      provider === 'RingCentral' ||
      provider === 'WhatsApp' ||
      provider === 'Telegram'
    ) {
      router.push(`/source/hub/${accountType}/${username}/chats`)
    } else if (provider === 'Google') {
      router.push(`/source/hub/${accountType}/${username}/primary/inbox`)
    } else if (provider === 'Microsoft') {
      router.push(`/source/hub/${accountType}/${username}/focused/inbox`)
    } else {
      router.push(`/source/hub/${accountType}/${username}/all`)
    }
  }

  return {
    accounts,
    selectedSubAccounts,
    toggleCheckAll,
    checkUncheckSubAccounts,
    getAllMessages,
    changePersonListSelectValue,
    getSinglePost,
    getRouteOnProvider,
  }
}
