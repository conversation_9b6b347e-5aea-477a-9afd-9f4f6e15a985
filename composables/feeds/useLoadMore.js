// import { ref, useStore, getCurrentInstance } from '@nuxtjs/composition-api'
import { useStore } from 'vuex'

export function useLoadMore() {
  const nuxtApp = useNuxtApp()
  
  const instance = getCurrentInstance()
  const disableLoadMoreProcess = ref(false)
  const store = useStore()
  const loadMore = async () => {
    if (store.state.home.loadMoreArticles) {
      disableLoadMoreProcess.value = true
      try {
        if (
          store.state.home.startDate === '' &&
          store.state.home.endDate === ''
        ) {
          await store.dispatch('home/loadMoreArticles')
        } else {
          await store.dispatch('home/loadMoreEmailArticles')
        }
      } catch (error) {
        console.log(error)
      } finally {
        disableLoadMoreProcess.value = false
      }
    } else {
      store.dispatch('home/getAllSocialArticle', store.state.home.articles.id)
    }
  }

  const loadMorePlaylistItem = async () => {
    disableLoadMoreProcess.value = true
    try {
      await store.dispatch('home/loadMorePlaylistItems')
    } catch (error) {
      console.log(error)
    } finally {
      disableLoadMoreProcess.value = false
    }
  }

  const showEndMessage = () => {
    const currentProvider =
      instance.proxy.$store.state.home.currentSocialComponent.provider
    nuxtApp.$toast('clear')
    if (
      currentProvider === 'Facebook' ||
      currentProvider === 'LinkedIn' ||
      currentProvider === 'Instagram' ||
      currentProvider === 'Twitter'
    ) {
      nuxtApp.$toast('success', {
        message: 'No More Posts',
        className: 'toasted-bg-archive',
      })
    } else if (
      currentProvider === 'Google' ||
      currentProvider === 'Microsoft'
    ) {
      nuxtApp.$toast('success', {
        message: 'No More Emails',
        className: 'toasted-bg-archive',
      })
    } else if (currentProvider === 'YouTube') {
      nuxtApp.$toast('success', {
        message: 'No More Videos',
        className: 'toasted-bg-archive',
      })
    } else {
      nuxtApp.$toast('success', {
        message: 'No More Data',
        className: 'toasted-bg-archive',
      })
    }
  }

  return {
    loadMore,
    showEndMessage,
    loadMorePlaylistItem,
    disableLoadMoreProcess,
  }
}