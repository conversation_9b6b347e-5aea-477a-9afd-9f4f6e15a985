<template>
  <client-only>
    <section
      class="overflow-hidden"
      :style="{
        '--color': globalColorPanel.backgroundColor,
        '--width': `${windowWidth}px`,
      }"
    >
      <div
        v-if="isDesktop"
        class="fixed hidden w-full h-full overflow-hidden md:block bg-ash-default"
        :class="
          showPasswordConfirmModal || showForgetPasswordModal ? 'bg-blur' : ''
        "
      >
        <the-sidebar @squeeze="doSqueeze" @notsqueeze="notSqueeze" />
        <div class="h-full overflow-hidden md:ml-26">
          <DesktopHeader
            :global-color-panel="globalColorPanel"
            @closePopup="startArchivePopup = false"
          />
          <div
            id="desktop"
            class="hidden md:block relative"
            :class="[
              squeeze ? 'blog' : 'blog1',
              showBlurActiveModal ? 'bg-blur' : '',
            ]"
          >
            <NuxtPage
              :squeeze="squeeze"
              @set-selected-user-info="setSelectedUserInfo($emit)"
            />
            <Transition name="page" mode="out-in">
              <div v-if="showTextOnBoarding">
                <TextAccountOnboarding />
                <div
                  class="fixed z-[10] inset-0 transition-opacity"
                  @click="
                    store.commit('header/SET_TEXT_ACCOUNT_ONBOARDING', false)
                  "
                >
                  <div
                    data-v-c9d2025d=""
                    class="absolute inset-0 bg-gray-600 opacity-75"
                  ></div>
                </div>
              </div>
            </Transition>
          </div>
        </div>
        <div v-if="showBlurActiveModal" class="web-overlay"></div>
      </div>
      <!-- Small Device  -->
      <div v-else class="relative w-full h-full overflow-hidden bg-ash-default">
        <div class="relative h-full overflow-hidden md:ml-26">
          <mobile-header
            v-if="hideMobileHeader === false"
            class="md:hidden"
            :window-height="windowHeight"
            :source="'search'"
            :expand-exist="expand"
            :global-color-panel="globalColorPanel"
            @show-footer="showFooter"
            @blur="blurBackground"
            @showProfile="profile"
          ></mobile-header>
          <div
            v-if="
              showMobileSearch ||
              showMobileFilter ||
              showMobileAddAlert ||
              showPricingHeader ||
              showProfile ||
              showChangeBilling
            "
            class="md:hidden overlay"
            @click="removeOverlay"
          ></div>
          <div
            id="smallDevice"
            class="md:hidden mobile_blog bg-ash-default relative"
            :class="
              showBlurActiveModal ||
              showPasswordConfirmModal ||
              showForgetPasswordModal
                ? 'bg-blur'
                : ''
            "
            @click="removeExpand"
          >
            <NuxtPage @set-selected-user-info="setSelectedUserInfo($emit)" />
            <Transition name="page" mode="out-in">
              <div v-if="showTextOnBoarding">
                <TextAccountOnboarding />
                <div
                  class="fixed z-[10] inset-0 transition-opacity"
                  @click.stop="
                    store.commit('header/SET_TEXT_ACCOUNT_ONBOARDING', false)
                  "
                >
                  <div
                    data-v-c9d2025d=""
                    class="absolute inset-0 bg-gray-600 opacity-75"
                  ></div>
                </div>
              </div>
            </Transition>
          </div>
          <mobile-footer
            class="md:hidden"
            :class="hideMobileHeader === false ? '' : 'hidden'"
            :show-footer="expand"
          ></mobile-footer>
          <div v-if="showBlurActiveModal" class="web-overlay"></div>
        </div>
        <mobile-filter :filter="showMobileFilter"></mobile-filter>
        <MobilePricingHeader
          class="md:hidden"
          :show-pricing-header="showPricingHeader"
        ></MobilePricingHeader>
        <!-- start search page component -->
        <mobile-search class="md:hidden"></mobile-search>
        <mobile-save-search class="md:hidden"></mobile-save-search>
        <!-- 
      <create-new-group
        :color="
          $route.name === 'search'
            ? '#7D80BD'
            : $route.name === 'archive'
            ? '#8DB230'
            : '#E0AD1F'
        "
      ></create-new-group> -->
      </div>
      <Loading v-if="downloadLoader"></Loading>
      <notification :global-color-panel="globalColorPanel"></notification>
      <SavePostSettingsModal />
      <social-auth-feeds
        :global-color-panel="globalColorPanel"
      ></social-auth-feeds>
      <profile
        :show-profile="show"
        :global-color-panel="globalColorPanel"
      ></profile>
      <update-add-feeds></update-add-feeds>
      <Transition name="globalFadeInFadeOut">
        <LazyConfirmationModal v-if="confirmModal" />
      </Transition>
      <transition name="slideInOut">
        <AchBankVerification
          v-if="showAchBankVerification"
          :class="user.paymentStatus === 'pending' ? '!z-[***********]' : ''"
          @showPopup="startArchivePopup = true"
        />
      </transition>
      <add-new-card></add-new-card>
      <LazySettingsAccountAchMandataModal v-if="showACHMandata" />
      <ReFundModal
        v-if="showRefundModal && user.isOwner === true"
      ></ReFundModal>
      <transition name="my-layouts">
        <message-read-modal
          v-if="notificationMessageModal.show"
          :message="notificationMessageModal"
        />
      </transition>
      <Transition name="globalFadeInFadeOut">
        <LazyForgetPasswordModal v-if="showForgetPasswordModal" />
      </Transition>
      <transition name="my-layouts">
        <showInvoicePdf v-if="showQuotaInvoicePdf || showEmailInvoicePdf" />
      </transition>
      <AgentBookUserInfo :selected-user-info="selectedUserInfo" />
      <AgentBookShowHide />
      <start-archiving-modal
        v-if="startArchivePopup && showBlurActiveModal"
        @close-popup="startArchivePopup = false"
      ></start-archiving-modal>
      <Transition name="page">
        <ErrorModal v-if="showErrorModal"></ErrorModal>
      </Transition>
      <Transition name="globalFadeInFadeOut">
        <LazyPdfDownloadPupUp v-if="showPdfPupUp" />
      </Transition>
      <Transition name="globalFadeInFadeOut">
        <div v-if="showWhatsappOnboarding">
          <LazyHomeRealtimeFeedRssTextsWhatsAppOnboarding />
          <div
            @click.stop="
              store.commit('home/SET_SHOW_WHATSAPP_ONBOARDING', false)
            "
            class="fixed inset-0 transition-opacity"
          >
            <div
              data-v-c9d2025d=""
              class="absolute inset-0 bg-gray-600 opacity-75"
            ></div>
          </div>
        </div>
      </Transition>
      <Transition name="globalFadeInFadeOut">
        <div v-if="showEmailPercentageModal">
          <LazyHomeRealtimeFeedRssMessagePercentage />
          <div
            @click.stop="
              store.commit('home/SET_SHOW_EMAIL_PERCENTAGE_MODAL', false)
            "
            class="fixed inset-0 transition-opacity"
          >
            <div
              data-v-c9d2025d=""
              class="absolute inset-0 bg-gray-600 opacity-75"
            ></div>
          </div>
        </div>
      </Transition>
      <new-alert></new-alert>
    </section>
  </client-only>
</template>

<script>
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core'
import { mapActions, mapState, useStore } from 'vuex'
import MobileFilter from '~/components/archive/FilterModal.vue'
import ConfirmationModal from '~/components/ConfirmationModal.vue'
import MobileHeader from '~/components/home/<USER>/Header.vue'
import MobileFooter from '~/components/layout-element/Footer.vue'
import Profile from '~/components/settings/account/Profile'
import SocialAuthFeeds from '~/components/socialAuthFeeds'
import TheSidebar from '~/components/TheSidebar.vue'
import DesktopHeader from '../components/home/<USER>/Header.vue'
// import MobileAddAlert from '~/components/alert/MobileAddAlert.vue'
import NewAlert from '~/components/alert/common-component/NewAlert.vue'
import ForgetPasswordModal from '~/components/ForgetPasswordModal.vue'
import Loading from '~/components/Loading.vue'
import Notification from '~/components/Notification.vue'
import MobilePricingHeader from '~/components/pricing-calculate/MobilePricingHeader.vue'
import ReFundModal from '~/components/ReFundModal.vue'
import MobileSaveSearch from '~/components/search/MobileSaveSearch'
import MobileSearch from '~/components/search/MobileSearch'
import SearchAlert from '~/components/search/SearchAlert'
import AchBankVerification from '~/components/settings/account/AchBankVerification.vue'
import AddNewCard from '~/components/settings/account/AddNewCard.vue'
import CreateNewGroup from '~/components/settings/organize/CreateNewGroup.vue'
import messageReadModal from '~/components/settings/system/messageReadModal.vue'
import showInvoicePdf from '~/components/showInvoicePdf.vue'
import SavePostSettingsModal from '~/components/source/create-post/SavePostSettingsModal.vue'
import StartArchivingModal from '~/components/startArchivingModal'
import UpdateAddFeeds from '~/components/UpdateAddFeeds.vue'
import { useAuth } from '~/composables/useAuth'
import { useFetched } from '~/composables/useFetched'
import useNotification from '~/composables/useNotification'
import { FEEDS } from '~/constants/urls'
import { set } from 'date-fns'

export default defineComponent({
  name: 'HomeLayout',
  components: {
    AddNewCard,
    SocialAuthFeeds,
    DesktopHeader,
    Profile,
    TheSidebar,
    MobileHeader,
    MobileFooter,
    ConfirmationModal,
    MobileSearch,
    MobileSaveSearch,
    SearchAlert,
    MobileFilter,
    // MobileAddAlert,
    MobilePricingHeader,
    UpdateAddFeeds,
    ReFundModal,
    Notification,
    messageReadModal,
    ForgetPasswordModal,
    Loading,
    CreateNewGroup,
    showInvoicePdf,
    AchBankVerification,
    StartArchivingModal,
    SavePostSettingsModal,
    NewAlert,
  },
  setup() {
    const store = useStore()
    const { fetch } = useFetched()
    const {
      setAuthCookies,
      loggedIn,
      tokenCookie,
      userInfo,
      notifications,
      setAccountErrorsCookies,
      showErrorModal,
    } = useAuth()
    const $config = useRuntimeConfig()
    const nuxtApp = useNuxtApp()
    const breakpoints = useBreakpoints(breakpointsTailwind)
    if ($config.public.workflow === 'live') {
      const notification = useNotification()
      if (loggedIn.value) {
        let token = tokenCookie.value
        notification.joinNotification(token)
      }
    }
    const { $initMap } = useNuxtApp()
    useHead(() => ({
      bodyAttrs: {
        class: 'overflow-hidden',
      },
      // meta: [
      //   {
      //     hid: 'robots',
      //     name: 'robots',
      //     content: 'noindex, nofollow',
      //   },
      // ],
      script: [
        {
          type: 'text/javascript',
          id: 'hs-script-loader',
          async: true,
          defer: true,
          src: `//js-na1.hs-scripts.com/********.js`,
          body: true,
        },
      ],
    }))

    return {
      isDesktop: breakpoints.greaterOrEqual('md'),
      loggedIn,
      nuxtApp,
      setAuthCookies,
      fetch,
      userInfo,
      notifications,
      showErrorModal,
      setAccountErrorsCookies,
      showPdfPupUp: computed(() => store.state.home.showPdfPupUp),
      showTextOnBoarding: computed(() => store.state.header.showTextOnBoarding),
      store,
    }
  },
  data() {
    return {
      squeeze: false,
      showProfile: false,
      expand: false,
      blur: false,
      current: 'home',
      windowHeight: 0,
      windowWidth: 0,
      selectedUserInfo: null,
      startArchivePopup: false,
    }
  },
  computed: {
    ...mapState('archive', ['downloadLoader']),
    showMobileSearch() {
      return this.$store.getters['header/getShowSearchBar']
    },
    showMobileFilter() {
      return this.$store.getters['header/getShowFilter']
    },
    showMobileAddAlert() {
      return this.$store.getters['header/getShowAddAlert']
    },
    showPricingHeader() {
      return this.$store.getters['header/getShowPricingHeader']
    },
    showOverlay() {
      return this.$store.getters['header/getOverlay']
    },
    hideMobileHeader() {
      return this.$store.getters['header/getHideMobileHeader']
    },
    showChangeBilling() {
      return this.$store.getters['header/getPayment']
    },
    showBlurActiveModal() {
      return this.$store.getters['header/showBlurActiveModal']
    },
    ...mapState('socialFeed', ['showRefundModal']),
    ...mapState('notifications', ['notificationMessageModal']),
    ...mapState('profile', ['show']),
    ...mapState('confirm', ['showPasswordConfirmModal', 'confirmModal']),
    ...mapState('system', ['allDate', 'allTimesZone', 'timeFormats']),
    ...mapState(['globalColorPanel', 'showForgetPasswordModal']),
    ...mapState('auth', ['tokenCookie']),
    ...mapState('setting', [
      'showQuotaInvoicePdf',
      'showEmailInvoicePdf',
      'showACHMandata',
    ]),
    ...mapState('account', ['showAchBankVerification']),
    ...mapState('home', ['showWhatsappOnboarding', 'showEmailPercentageModal']),
    user() {
      return this.$store.state.auth.user
    },
  },
  watch: {
    $route(to, from) {
      if (from.name === 'search') {
        this.$store.commit('search/SET_HOME_SEARCH', '')
      }
    },
  },
  mounted() {
    setTimeout(() => {
      const chatWidget = document.getElementById(
        'hubspot-messages-iframe-container',
      )
      if (chatWidget) {
        chatWidget.style.setProperty('display', 'none', 'important')
      }
    }, 500)
    if (
      this.userInfo &&
      this.userInfo.paymentStatus === '' &&
      this.userInfo.isOwner === true &&
      this.userInfo.userPermission !== 'Partner'
    ) {
      this.$router.replace('/payment')
    }
    if (
      this.userInfo &&
      this.userInfo.paymentStatus === '' &&
      this.userInfo.isOwner === true &&
      this.userInfo.userPermission === 'Partner'
    ) {
      window.location.href = 'https://partner.sharparchive.com/partners-portal'
    }
    if (this.$route.fullPath.includes('/home?success=true')) {
      setTimeout(() => {
        if (this.userInfo && this.userInfo.paymentStatus !== 'trial') {
          this.nuxtApp.$toast('success', {
            message: 'Payment Successful',
            className: 'toasted-bg-archive',
          })
        }
      }, 500)

      setTimeout(() => {
        this.$router.replace('/home')
      }, 1000)
    }
    this.login_button_transition(true)
    this.submit_button_transition(true)
    this.login_form_transition(true)
    this.successfully(true)
    this.notsuccessfully(false)
    this.loading_text(true)
    this.loading_text(false)
    this.after_loading(true)
    this.home_header(true)
    this.home_sidebar(true)
    this.home_modal(true)
    this.width_increase(true)
    this.header_text(true)
    this.home_circle(true)
    this.login_circle(false)
    this.slide_left(true)
    this.full_width(true)
    this.sidebar_menu(true)
    this.sidebar_circle(true)
    this.home_circle(false)
    this.show_logo(true)
    this.home_side_menu(true)
    this.home_menu_text(true)
    this.all_side_menu(true)
    this.show_home(true)
    this.show_logo_text(false)
    this.home_menu_text(false)
    this.home_wrapper(false)
    this.show_home_content(false)
    // this.show_login(true)
    window.addEventListener('resize', this.screenSize)
    this.screenSize()
    if (this.loggedIn) {
      document.addEventListener('mousemove', this.resetInactivity)
      document.addEventListener('keydown', this.resetInactivity)
      setInterval(this.checkInactivity, 60000)
      this.getAllProviders()
      setTimeout(() => {
        this.show_login(false)
      }, 2000)
      this.fatchSocialFeeds().then((res) => {
        if (this.notifications) {
          const tempaccountErrors = JSON.parse(
            JSON.stringify(this.notifications),
          )

          res.data.forEach((item) => {
            if (
              item.status &&
              !tempaccountErrors.find((x) => x.id === item.id)
            ) {
              tempaccountErrors.push({
                id: item.id,
              })
            }
          })
          res.data.forEach((item) => {
            if (
              !item.status &&
              tempaccountErrors.find((x) => x.id === item.id)
            ) {
              const index = tempaccountErrors.findIndex((x) => x.id === item.id)
              tempaccountErrors.splice(index, 1)
            }
          })
          // this.accountErrors = tempaccountErrors
          if (this.notifications.length !== tempaccountErrors.length) {
            this.showErrorModal = false
            this.setAccountErrorsCookies(JSON.stringify(tempaccountErrors))
          }
        } else {
          const tempaccountErrors = []
          res.data.forEach((item) => {
            if (item.status) {
              tempaccountErrors.push({
                id: item.id,
              })
            }
          })
          // this.accountErrors = tempaccountErrors
          if (tempaccountErrors && tempaccountErrors.length > 0) {
            this.showErrorModal = true
            this.setAccountErrorsCookies(JSON.stringify(tempaccountErrors))
          }
        }
        if (this.user.paymentStatus === 'pending') {
          this.$store.commit('account/SET_ACH_BANK_VERIFICATION', true)
          this.$store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false)
          this.$store.commit('header/BLUR_ACTIVE_FEED_MODAL', true)
        } else if (res.data.length === 0) {
          this.$store.commit('header/BLUR_ACTIVE_FEED_MODAL', true)
          this.$store.commit('socialFeed/SHOW_ADD_FEED_MODAL', true)
        }
      })
      this.getAllLatestData()
      this.getAllTimeZones().then((res) => {
        if (res.success) {
          this.getAllTimes()
        }
      })
      this.getAllNotifications()
      this.getOldestJoiningDateTime()
      this.getUserIPAndTimezone()
    }
  },
  destroyed() {
    setTimeout(() => {
      window.removeEventListener('resize', this.screenSize)
      document.removeEventListener('mousemove', this.resetInactivity)
      document.removeEventListener('keydown', this.resetInactivity)
      clearInterval(this.checkInactivity)
    }, 2000)
  },
  methods: {
    resetInactivity() {
      this.$store.commit('user/RESET_INACTIVITY_TIME')
    },
    checkInactivity() {
      this.$store.commit('user/SET_INACTIVITY_TIME')
    },
    setSelectedUserInfo($emit) {
      this.selectedUserInfo = $emit
    },
    async getAllProviders() {
      try {
        const res = await this.fetch(FEEDS)
        if (res.success) {
          this.setAllSocialProviders(res.data)
        }
      } catch (error) {
        console.log(error)
      }
    },
    screenSize() {
      setTimeout(() => {
        if (document.getElementById('smallDevice')) {
          this.windowHeight =
            document.getElementById('smallDevice').offsetHeight - 149
          this.windowWidth =
            document.getElementById('smallDevice').offsetWidth - 32
        }
      })
    },
    // closeSidebar() {
    //   this.$store.commit('archive/SET_BULK_DOWNLOAD_SIDEBAR', false)
    //   this.$store.commit('profile/SET_PROFILE_MODAL', false)
    //   this.$store.commit('search/SET_SAVE_SAERCH_MODAL', false)
    //   this.$store.commit('socialFeed/SHOW_SOCIAL_EDIT_FEED_MODAL', {
    //     open: false,
    //     data: {},
    //   })
    //   this.$store.commit('SET_SHOW_SIDE_BAR', false)
    // },
    ...mapActions({
      setAllSocialProviders: 'setAllSocialProviders',
      setPastMonths: 'setPastMonths',
    }),
    ...mapActions('home', [
      'getAllLatestData',
      'getAllSocialArticle',
      'getOldestJoiningDateTime',
    ]),
    ...mapActions('system', [
      'getAllTimeZones',
      'getTimeZoneWiseDate',
      'getAllTimeFormats',
      'getAllTimes',
      'getUserIPAndTimezone',
    ]),
    ...mapActions('socialFeed', ['fatchSocialFeeds']),
    ...mapActions('notifications', ['getAllNotifications']),

    doSqueeze() {
      this.squeeze = true
      this.$store.dispatch('header/setSqueeze')
    },
    notSqueeze() {
      setTimeout(() => {
        this.squeeze = false
      }, 50)
      this.$store.dispatch('header/removeSqueeze')
    },
    profile() {
      this.showProfile = true
    },
    closeProfile(data) {
      this.showProfile = data
    },
    showFooter(data) {
      this.expand = data
    },
    blurBackground(data) {
      this.blur = data
    },
    removeOverlay() {
      this.nuxtApp.$bus.$emit('hideVCalender')
      this.$store.dispatch('header/removeOverlay')
      this.$store.dispatch('header/removePayment')
      this.expand = false
      this.showProfile = false
    },
    removeExpand() {
      this.expand = false
      this.showProfile = false
      this.$store.commit('notifications/SHOW_NOTIFICATION_MODAL', false)
    },
    ...mapActions({
      login_button_transition: 'loginAnimation/login_button_transition',
      submit_button_transition: 'loginAnimation/submit_button_transition',
      login_form_transition: 'loginAnimation/login_form_transition',
      successfully: 'loginAnimation/successfully',
      notsuccessfully: 'loginAnimation/notsuccessfully',
      after_loading: 'loginAnimation/after_loading',
      home_modal: 'loginAnimation/home',
      sidebar_menu: 'loginAnimation/sidebar_menu',
      sidebar_circle: 'loginAnimation/circle',
      home_sidebar: 'loginAnimation/sidebar',
      home_circle: 'loginAnimation/home_circle',
      login_circle: 'loginAnimation/login_circle',
      slide_left: 'loginAnimation/slide_left',
      show_logo: 'loginAnimation/show_logo',
      home_header: 'loginAnimation/header',
      width_increase: 'loginAnimation/width_increase',
      full_width: 'loginAnimation/full_width',
      home_side_menu: 'loginAnimation/home_side_menu',
      all_side_menu: 'loginAnimation/all_side_menu',
      show_home: 'loginAnimation/show_home',
      home_menu_text: 'loginAnimation/home_menu_text',
      show_home_content: 'loginAnimation/show_home_content',
      home_wrapper: 'loginAnimation/home_wrapper',
      show_logo_text: 'loginAnimation/show_logo_text',
      header_text: 'loginAnimation/header_text',
      loading_text: 'loginAnimation/loading_text',

      show_login: 'loginAnimation/show_login',
      landing_content: 'loginAnimation/landing_content',
    }),
  },
})
</script>

<style lang="scss">
:root {
  color-scheme: light light !important;
  supported-color-schemes: light light !important;
}
.toasted-bg-archive {
  background-color: #8db230 !important;
}

.toasted-bg-search {
  background-color: #7d80bd !important;
}

.toasted-bg-alert {
  background-color: #a22a2a !important;
}

.toasted-bg-setting {
  background-color: #e0ad1f !important;
}

.toasted-container {
  top: 0 !important;
  z-index: 9999999999 !important;
}

html {
  font-size: 16px;
  word-spacing: 1px;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  -moz-tap-highlight-color: transparent;
  overflow-x: hidden;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
}

.button--green {
  display: inline-block;
  border-radius: 4px;
  border: 1px solid #3b8070;
  color: #3b8070;
  text-decoration: none;
  padding: 10px 30px;
}

.button--green:hover {
  color: #fff;
  background-color: #3b8070;
}

.button--grey {
  display: inline-block;
  border-radius: 4px;
  border: 1px solid #35495e;
  color: #35495e;
  text-decoration: none;
  padding: 10px 30px;
  margin-left: 15px;
}

.button--grey:hover {
  color: #fff;
  background-color: #35495e;
}

.blog {
  height: calc(100% - 3.75rem);
  @apply pl-4;
  margin-left: 90px;
  transition: all 0.5s;
  transition-delay: 0.5s;
  // animation: squeez 0.2s linear;
}

.blog1 {
  height: calc(100% - 3.75rem);
  margin-left: 0px;
  transition: margin 0.5s;
  // animation: squeez1 0.2s linear;
}

.content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-x: hidden;
  position: relative;
  -ms-overflow-style: none;
  scrollbar-width: thin;
  scrollbar-color: #177294 #ececec;
}

@mixin page($size, $name) {
  @if $name == bulkDownload {
    width: $size !important;
  }
}

@mixin page2($size, $name) {
  @if $name == archive {
    width: $size !important;
  }
}

.mx-datepicker {
  height: 47px;
  width: 100% !important;
  border-radius: 20px;

  &::placeholder {
    border-color: #519fc5;
    color: #519fc5;
  }

  .mx-input {
    display: inline-block;
    box-sizing: border-box;
    width: 100%;
    height: 34px;
    padding: 6px 30px;
    padding-left: 10px;
    font-size: 18px;
    font-weight: bold;
    line-height: 1.4;
    color: #f8f8f8 !important;
    background-color: #8db230 !important;
    border: none;
    border-radius: 20px !important;
    box-shadow: none;
    outline: none;
    cursor: pointer;
  }

  .mx-input::placeholder {
    color: #f8f8f8;
  }

  .alert-date-input {
    display: inline-block;
    box-sizing: border-box;
    width: 100%;
    height: 34px;
    padding: 6px 30px;
    padding-left: 10px;
    font-size: 18px;
    font-weight: bold;
    line-height: 1.4;
    color: #f8f8f8 !important;
    background-color: #e05252 !important;
    border: none;
    border-radius: 20px !important;
    box-shadow: none;
    outline: none;
  }

  .alert-date-input::placeholder {
    color: #f8f8f8;
  }
}

.mx-icon-calendar,
.mx-icon-clear {
  display: none !important;
}

//Small Device
.overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9;
  background-color: rgba(255, 255, 255, 0.1);
  opacity: 1;
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  pointer-events: all;
}

.overlay-web {
  position: fixed;
  top: 60px;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9;
  background-color: rgba(255, 255, 255, 0.1);
  opacity: 1;
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  pointer-events: all;
}

.mobile_blog {
  height: 100%;
  margin-left: 0px;
  position: fixed;
  width: 100%;
  // z-index: 999;
  // top: 60px;
  // padding-bottom: 60px;
}

.scroll {
  overflow-y: auto;
  overflow-x: auto;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-width: thin;
  &::-webkit-scrollbar {
    width: 6px;
    height: 10px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    // box-shadow: inset 0 0 5px #ECECEC;
    border-radius: 3px;
    background: #ececec;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
  }
}
.custom-scroll {
  overflow-y: auto;
  overflow-x: auto;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-color: #a1cdff #a1cdff50;
  &::-webkit-scrollbar {
    width: 4px !important;
    height: 4px !important;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    // box-shadow: inset 0 0 5px #ECECEC;
    border-radius: 3px;
    background: #a1cdff50 !important;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: #a1cdff !important;
  }
}
.custom-whatsapp-scroll {
  overflow-y: auto;
  overflow-x: auto;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-color: #c1e9bb #c8eec280;
  &::-webkit-scrollbar {
    width: 4px !important;
    height: 4px !important;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    // box-shadow: inset 0 0 5px #ECECEC;
    border-radius: 3px;
    background: #c8eec280 !important;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: #c1e9bb !important;
  }
}
.custom-telegram-scroll {
  overflow-y: auto;
  overflow-x: auto;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-color: #b2e5ff #b2e5ff80;
  &::-webkit-scrollbar {
    width: 4px !important;
    height: 4px !important;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    // box-shadow: inset 0 0 5px #ECECEC;
    border-radius: 3px;
    background: #b2e5ff80 !important;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: #b2e5ff !important;
  }
}
.no-scrollbar::-webkit-scrollbar {
  display: none;
}
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

[data-title]:hover:after {
  opacity: 1;
  transition: all 0.1s ease 0.5s;
  visibility: visible;
}

[data-title]:after {
  content: attr(data-title);
  position: absolute;
  bottom: -9px;
  padding: 2px;
  white-space: nowrap;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  -moz-box-shadow: 0px 0px 4px #666;
  -webkit-box-shadow: 0px 0px 4px #666;
  box-shadow: 0px 0px 4px #666;
  background-color: white;
  background-image: -moz-linear-gradient(top, #dbc9c9, #958f8f);
  background-image: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0, #dbc9c9),
    color-stop(1, #958f8f)
  );
  background-image: -webkit-linear-gradient(top, #dbc9c9, #958f8f);
  background-image: -moz-linear-gradient(top, #dbc9c9, #958f8f);
  background-image: -ms-linear-gradient(top, #dbc9c9, #958f8f);
  background-image: -o-linear-gradient(top, #dbc9c9, #958f8f);
  opacity: 0;
  z-index: **********************9;
  visibility: hidden;
  font-size: 12px;
}

[data-title] {
  position: relative;
}

.fadeIn-enter-active,
.fadeIn-leave-active {
  transition: opacity 0.5s ease-in-out;
}

.fadeIn-enter,
.fadeIn-leave-to {
  opacity: 0;
}

.fadeIn-enter-t0,
.fadeIn-leave-from {
  opacity: 1;
}

@media (max-width: 767px) {
  .scroll {
    &::-webkit-scrollbar {
      width: 3px;
      height: 3px;
    }
  }
  .custom-scroll {
    &::-webkit-scrollbar {
      width: 3px;
      height: 3px;
    }
  }
  [data-title]:after {
    z-index: 0;
    display: none;
  }
}

.StripeElement {
  border-radius: 25px !important;
}

.web-overlay {
  position: fixed;
  top: 60px;
  bottom: 0;
  left: 100px;
  right: 0;
  z-index: 101;
  opacity: 1;
  pointer-events: all;
}

@media (max-width: 767px) {
  .web-overlay {
    left: 0 !important;
    height: 100%;
  }
}

.VueCarousel-wrapper {
  height: 100% !important;
}

.VueCarousel-inner {
  height: 100% !important;
}
.bg-blur {
  filter: blur(4px);
}
.VueCarousel-navigation-next {
  border: none !important;
  outline: none !important;
  right: 62px !important;
}
.VueCarousel-navigation-prev {
  border: none !important;
  outline: none !important;
  left: 62px !important;
}
.modal-view {
  display: block;
}

table {
  border-collapse: collapse !important;
}
table,
tr,
th,
td {
  border: none !important;
}

canvas {
  @apply mx-auto;
}
.pac-container {
  z-index: ***********;
}
.content p {
  margin: 0px;
  // display: inline-block !important;
}
.ProseMirror p {
  // display: inline-block !important;
  display: block !important;
}
.ProseMirror {
  h1 {
    font-size: 22px;
    line-height: 29px;
  }
  h2 {
    font-size: 18px;
    line-height: 24px;
  }
  h3 {
    font-size: 16px;
    line-height: 22px;
  }
  h4 {
    font-size: 14px;
    line-height: 19px;
  }
}
.ProseMirror {
  display: inline-block !important;
}
.ProseMirror a {
  display: inline !important;
}
.ProseMirror img {
  display: block;
  height: auto;
  margin: 1.5rem 0;
  max-width: 100%;
  &.ProseMirror-selectednode {
    outline: 2px solid #4a71d4;
  }
}

.comment-text-editor p.is-editor-empty:first-child::before,
.message-text-editor p.is-editor-empty:first-child::before {
  color: #707070;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}
.text-editor p.is-editor-empty:first-child::before {
  color: #333333;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

.mainRedditText a {
  color: #0079d3;
}
.text-color {
  color: #f2f2f2 !important;
}
.name-color {
  color: #ffffff !important;
  font-size: 1.25rem !important;
  line-height: 1.75rem !important;
  font-weight: bold !important;
  font-family: 'Roboto', sans-serif !important;
}
.button-font {
  font-family: 'Roboto', sans-serif !important;
}
#Signature table {
  max-width: 100% !important;
  width: 100% !important;
}
.text-normal {
  font-weight: normal !important;
}
.text-bold {
  font-weight: bold !important;
}
ol {
  list-style: decimal;
}
.redditText,
.mainRedditText {
  ol {
    @apply ml-5;
  }
}
.VueCarousel-dot-container {
  padding-top: 30px !important;
}
.mail-carousal .VueCarousel-dot-container {
  padding-top: 0px !important;
}
code {
  white-space: break-spaces;
}
// update style for calendar style
.vc-home {
  --vc-accent-50: #f0f9ff;
  --vc-accent-100: #ddccbb;
  --vc-accent-200: #dcc3ab;
  --vc-accent-300: #d7b99c;
  --vc-accent-400: #cb9d6f;
  --vc-accent-500: #e1a365;
  --vc-accent-600: #e4801d;
  --vc-accent-700: #e4801d;
  --vc-accent-800: #e4801d;
  --vc-accent-900: #e4801d;
}
.vc-archive {
  --vc-accent-50: #f0f9ff;
  --vc-accent-100: #b9bdaf;
  --vc-accent-200: #dce0d2;
  --vc-accent-300: #b8c1a1;
  --vc-accent-400: #aabc7d;
  --vc-accent-500: #9dbd4d;
  --vc-accent-600: #8db230;
  --vc-accent-700: #8db230;
  --vc-accent-800: #8db230;
  --vc-accent-900: #8db230;
}
.vc-search {
  --vc-accent-50: #f0f9ff;
  --vc-accent-100: #e0f2fe;
  --vc-accent-200: #e1e1e8;
  --vc-accent-300: #acadc5;
  --vc-accent-400: #b0b1c6;
  --vc-accent-500: #9193ba;
  --vc-accent-600: #7d80bd;
  --vc-accent-700: #7d80bd;
  --vc-accent-800: #7d80bd;
  --vc-accent-900: #7d80bd;
}
.carousel__viewport {
  height: 100%;
}
.carousel__track {
  height: 100%;
}
.carousel__pagination {
  margin: 20px 0 0;
  position: absolute;
  bottom: 0px;
  width: 100%;
}
.carousel__pagination-button--active::after {
  background-color: #e4801d;
}
.carousel__pagination-button::after {
  width: 10px;
  height: 10px;
  border-radius: 10px;
}
.carousel__next,
.carousel__prev {
  background-color: #fff;
  @apply rounded-full;
}
.carousel__icon {
  fill: #222831;
}
.all_dates[data-title]:after {
  color: var(--color);
  font-size: 16px;
  right: 0% !important;
  top: 40px;
  height: 36px;
  padding: 12px;
  padding-top: 13px;
  display: flex;
  align-items: center;
  z-index: 99999999;
  font-weight: normal;
}
.hidden_tooltip[data-title]:after {
  display: none;
}
@media (max-width: 767px) {
  .all_dates[data-title]:after {
    display: none;
  }
  .hidden_tooltip[data-title]:after {
    display: none;
  }
}
#zoomContainer iframe {
  min-height: 400px;
  height: 100% !important;
  width: 100% !important;
}

.vue-tel-input {
  border-radius: 9999px;
  border: none;
  background-color: #ffffff;
  // box-shadow: 0 1px 3px #8e5200b3;
}
.vue-tel-input:focus-within {
  border: none;
  box-shadow: none;
}

.telInput {
  border-radius: 0 9999px 9999px 0;
}
.vti__input {
  border: none;
  // border-radius: 0 9999px 9999px 0;
  width: 100%;
  outline: none;
  padding-left: 7px;
  color: var(--color);
  &::placeholder {
    color: var(--color);
    opacity: 0.5;
  }
}

.vti__dropdown .vti__selection .vti__country-code,
.vti__dropdown-arrow {
  color: var(--color);
  font-weight: bold;
  font-size: 16px;
}
.vti__dropdown:hover {
  border-radius: 9999px 0px 0px 9999px;
}
.vti__dropdown.open {
  border-radius: 9999px 0px 0px 9999px;
}
.vti__dropdown-list {
  max-width: 490px;
  width: 490px;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-color: var(--color) #ececec; /* Firefox 64 */
  scrollbar-width: thin;
  &::-webkit-scrollbar {
    width: 10px;
    height: 6px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    // box-shadow: inset 0 0 5px #ECECEC;
    background: var(--color);
    border-radius: 3px;
    background: #ececec;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: var(--color);
    border-radius: 3px;
  }
}
.vti__dropdown-item[aria-selected='true'] {
  background-color: dodgerblue;
  color: #ffffff;
}
@media (max-width: 767px) {
  .vti__dropdown-list {
    max-width: var(--width);
    width: var(--width);
  }
}
/* for slideInOut */
.slideInOut-enter-active,
.slideInOut-leave-active {
  transition: all 0.8s ease-in-out;
}
.slideInOut-enter-from,
.slideInOut-leave-to {
  right: -100%;
}
chat-widget {
  display: none !important;
}
mark {
  display: inline-block;
}
s {
  display: inline-flex;
}
.underline {
  text-decoration: underline;
}
.line-through {
  text-decoration: line-through;
}
</style>
