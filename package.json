{"name": "sharparchive", "version": "2.0.0", "description": "Sharp archive provides ringtail ediscovery services, archive softwares, social media archive, web archiving and instant message archiving service in USA.", "private": true, "scripts": {"dev": "NODE_TLS_REJECT_UNAUTHORIZED=0 nuxt dev --https --ssl-cert localhost.pem --ssl-key localhost-key.pem", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint:js": "eslint --ext \".js,.vue\" --ignore-path .gitignore .", "lint:js-fix": "eslint --fix --ext \".js,.vue\" --ignore-path .gitignore .", "prepare": "husky install", "lint": "yarn lint:js", "start-dev": "concurrently -k \"npm run dev\" \"npm run server\"", "seed-data": "node server/data/seeds", "server": "npx nodemon server/server"}, "lint-staged": {"*.{js,vue}": "eslint"}, "type": "module", "dependencies": {"@dargmuesli/nuxt-cookie-control": "^8.4.5", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-brands-svg-icons": "^6.5.2", "@fortawesome/free-regular-svg-icons": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/vue-fontawesome": "^3.0.6", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/vue3": "^6.1.15", "@googlemaps/js-api-loader": "^1.16.6", "@nuxt/fonts": "^0.7.2", "@nuxt/image": "^1.8.0", "@pinia/nuxt": "^0.5.5", "@popperjs/core": "^2.11.8", "@sentry/browser": "^7.111.0", "@sentry/core": "^7.111.0", "@sentry/replay": "^7.111.0", "@sentry/tracing": "^7.111.0", "@sentry/vue": "^7.111.0", "@svgmoji/noto": "^3.2.0", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-font-family": "^2.11.5", "@tiptap/extension-highlight": "^2.11.5", "@tiptap/extension-image": "^2.11.7", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/extension-table": "^2.24.0", "@tiptap/extension-table-cell": "^2.24.0", "@tiptap/extension-table-header": "^2.24.0", "@tiptap/extension-table-row": "^2.24.0", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@tiptap/vue-3": "^2.11.5", "@vuelidate/core": "^2.0.2", "@vuelidate/validators": "^2.0.2", "@vueuse/integrations": "^13.2.0", "apexcharts": "4.5.0", "class-transformer": "^0.5.1", "core-js": "^3.37.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "eslint-plugin-jest": "^28.2.0", "express-form-data": "^2.0.23", "flatpickr": "^4.6.13", "fs": "0.0.2", "konva": "^9.3.20", "lodash.debounce": "^4.0.8", "maska": "^2.1.11", "masonry-layout": "^4.2.2", "mitt": "^3.0.0", "net": "^1.0.2", "nuxt": "^3.11.2", "nuxt-emoji-picker": "1.1.0", "nuxt-meta-pixel": "^2.0.2", "nuxt-security": "^2.3.0", "nuxt-seo-experiments": "^4.0.0", "nuxt-simple-robots": "^4.0.0-rc.17", "pinia": "^2.3.1", "playwright": "^1.48.0", "recordrtc": "^5.6.2", "sortablejs": "^1.15.6", "svgmoji": "^3.2.0", "v-calendar": "^3.1.2", "v-lazy-image": "^2.1.1", "video.js": "^8.10.0", "vue": "^3.4.21", "vue-dompurify-html": "^5.0.1", "vue-konva": "^3.2.1", "vue-pdf-embed": "^2.1.1", "vue-router": "^4.3.0", "vue-tel-input": "^8.3.1", "vue-toastification": "^2.0.0-rc.5", "vue2-datepicker": "^3.11.1", "vue3-apexcharts": "^1.8.0", "vue3-carousel": "^0.3.1", "vue3-emoji-picker": "^1.1.8", "vuedraggable": "^4.1.0", "vuelidate-messages": "^0.1.2", "vuex": "^4.0.2"}, "devDependencies": {"@babel/eslint-parser": "^7.24.1", "@nuxt/devtools": "^1.2.0", "@nuxt/test-utils": "^3.14.2", "@nuxt/types": "^2.18.1", "@nuxtjs/eslint-config": "^12.0.0", "@nuxtjs/eslint-module": "^4.1.0", "@nuxtjs/sitemap": "^5.1.4", "@nuxtjs/tailwindcss": "^6.12.0", "@stripe/stripe-js": "^4.7.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@types/lodash.debounce": "^4.0.9", "@types/node": "^18", "@types/recordrtc": "^5", "@types/sortablejs": "^1", "@vue/test-utils": "^2.4.6", "@vueuse/core": "^10.9.0", "@vueuse/nuxt": "^10.9.0", "@zadigetvoltaire/nuxt-gtm": "^0.0.13", "casual": "^1.6.2", "eslint": "^9.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-nuxt": "^4.0.0", "eslint-plugin-vue": "^9.25.0", "happy-dom": "^15.7.4", "husky": "^9.0.11", "lint-staged": "^15.2.2", "nuxt-lodash": "^2.5.3", "path": "^0.12.7", "playwright-core": "^1.47.1", "prettier": "^3.2.5", "sass": "^1.75.0", "vitest": "^2.1.1", "vue-stripe-js": "^1.0.2"}, "packageManager": "yarn@4.2.2"}