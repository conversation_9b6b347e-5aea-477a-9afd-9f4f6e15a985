import { config, library } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
// This is important, we are going to let <PERSON>uxt worry about the CSS
config.autoAddCss = false

/* import specific icons */
import {
  faAngleLeft,
  faAngleRight,
  faArrowLeft,
  faArrowsAltV,
  faBars,
  faBell,
  faBroom,
  faCalendarDays,
  faCaretDown,
  faCaretUp,
  faCheck,
  faCheckCircle,
  faChevronDown,
  faChevronLeft,
  faChevronRight,
  faChevronUp,
  faCircleInfo,
  faCog,
  faDownload,
  faEarthAmericas,
  faEllipsis,
  faEllipsisH,
  faEllipsisV,
  faExclamationCircle,
  faExpand,
  faEye,
  faEyeSlash,
  faFileInvoice,
  faGear,
  faHeart,
  faLock,
  faLongArrowRight,
  faMagnifyingGlass,
  faMinus,
  faPaperclip,
  faPaperPlane,
  faPencilAlt,
  faPenNib,
  faPlus,
  faQuestionCircle,
  faRepeat,
  faSave,
  faSearch,
  faSearchMinus,
  faSearchPlus,
  faSitemap,
  faSortDown,
  faSpinner,
  faStar,
  faStarHalfAlt,
  faTag,
  faTimes,
  faTimesCircle,
  faTrash,
  faUser,
  faUserCircle,
  faUserGroup,
  faUserSecret,
  faWandMagicSparkles,
} from '@fortawesome/free-solid-svg-icons'

import { faFloppyDisk } from '@fortawesome/free-regular-svg-icons'

/* add icons to the library */
library.add(
  faHeart,
  faUserSecret,
  faAngleRight,
  faCalendarDays,
  faChevronRight,
  faLongArrowRight,
  faArrowLeft,
  faCaretUp,
  faCaretDown,
  faSpinner,
  faTimes,
  faChevronLeft,
  faChevronUp,
  faChevronDown,
  faPaperPlane,
  faPlus,
  faSave,
  faSearchPlus,
  faSearchMinus,
  faSearch,
  faMinus,
  faStar,
  faStarHalfAlt,
  faArrowsAltV,
  faBroom,
  faDownload,
  faFileInvoice,
  faTimesCircle,
  faPencilAlt,
  faCheck,
  faUserCircle,
  faEllipsisV,
  faEye,
  faEyeSlash,
  faExpand,
  faEllipsis,
  faEarthAmericas,
  faEllipsisH,
  faUserGroup,
  faLock,
  faGear,
  faBell,
  faQuestionCircle,
  faCog,
  faBars,
  faSortDown,
  faUser,
  faExclamationCircle,
  faAngleLeft,
  faPenNib,
  faTrash,
  faMagnifyingGlass,
  faSitemap,
  faWandMagicSparkles,
  faCheckCircle,
  faFloppyDisk,
  faRepeat,
  faTag,
  faCircleInfo,
  faPaperclip,
)

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.component('fa', FontAwesomeIcon)
})
