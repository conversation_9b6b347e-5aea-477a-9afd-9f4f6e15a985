import {
  findItemAndImmediateParent,
  flattenLeaves,
  getMaxId,
  getNextId,
  // moveToRoot,
  // getUpdatedLabelsArray,
  // findNestedItemAndRootParent,
  removeLabelById,
  toggleSelectedById,
} from '@/utils/labelHelpers'
import ProductIcon from '~/components/shared/icon/hub/emails/sidebar/labels/Product.vue'

const emailsStore = {
  namespaced: true,
  state() {
    return {
      emailMessages: [
        {
          id: 1,
          profileUrl: '/social/<EMAIL>',
          from: 'Game On Pro',
          subject: `<p><span>"Level Up: Must-Play Games of the Month"</span></p>`,
          snippet: `<p class="text-[#707070]"> - Dear <PERSON>, explore these exciting games to take your gaming experience to</p>`,
          description: `<p class="text-[#707070]">Dear <PERSON>, explore these exciting games to take your gaming experience to</p>`,
          createdAt: '2025-07-16T06:30:00.000Z',
          read: false,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
          fileName: 'Level Up: Must-Play Games of the Month.eml',
          size: '1200',
        },
        {
          id: 2,
          profileUrl: '/social/outlook-mail-1.png',
          from: 'Travel Visionary',
          subject: `<p><span>Plan Your Dream Getaway ✈️</span></p>`,
          snippet: `<p class="text-[#707070]"> - Here’s how to turn your travel dreams into reality.</p>`,
          description: `<p class="text-[#707070]">Here’s how to turn your travel dreams into reality.</p>`,
          createdAt: '2025-07-16T05:15:00.000Z',
          read: false,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
          fileName: 'Plan Your Dream Getaway.eml',
          size: '2500',
        },
        {
          id: 3,
          profileUrl: '/social/outlook-mail-2.png',
          from: 'Foodie Adventures',
          subject: `<p><span>Delicious Recipes to Try Tonight 🍴</span></p>`,
          snippet: `<p class="text-[#707070]"> - Treat yourself to these mouth-watering dishes!</p>`,
          description: `<p class="text-[#707070]">Treat yourself to these mouth-watering dishes!</p>`,
          createdAt: '2025-07-16T04:00:00.000Z',
          read: true,
          checked: false,
          favourite: false,
          starred: true,
          selected: false,
          fileName: 'Delicious Recipes to Try Tonight.eml',
          size: '3100',
        },
        {
          id: 4,
          profileUrl: '/social/outlook-mail-3.png',
          from: 'Book HavenX',
          subject: `<p><span>5 Books That Will Change Your Perspective</span></p>`,
          snippet: `<p class="text-[#707070]"> - These books will inspire you and open your mind to new ideas.</p>`,
          description: `<p class="text-[#707070]">These books will inspire you and open your mind to new ideas.</p>`,
          createdAt: '2025-07-16T06:30:00.000Z',
          read: true,
          checked: false,
          favourite: false,
          starred: true,
          selected: false,
          fileName: '5 Books That Will Change Your Perspective.eml',
          size: '1800',
        },
        {
          id: 5,
          profileUrl: '/social/<EMAIL>',
          from: 'Photo Enthusiast',
          subject: `<p><span>Capture the Moment: Photography Tips</span></p>`,
          snippet: `<p class="text-[#707070]"> - Enhance your skills and take stunning shots with these techniques.</p>`,
          description: `<p class="text-[#707070]">Enhance your skills and take stunning shots with these techniques.</p>`,
          createdAt: '2025-07-16T05:15:00.000Z',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
          fileName: 'Capture the Moment: Photography Tips.eml',
          size: '2000',
        },
        {
          id: 6,
          profileUrl: '/social/<EMAIL>',
          from: 'Success Navigator',
          subject: `<p><span>Navigate Your Way to Success</span></p>`,
          snippet: `<p class="text-[#707070]"> - Map out a plan that ensures you achieve your goals.</p>`,
          description: `<p class="text-[#707070]">Map out a plan that ensures you achieve your goals.</p>`,
          createdAt: '2025-07-16T04:00:00.000Z',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
          fileName: 'Navigate Your Way to Success.eml',
          size: '1500',
        },
        {
          id: 7,
          profileUrl: '/social/<EMAIL>',
          from: 'InspiredJourneyHQ',
          subject: `<p><span>Inspiration Awaits: Take the First Step</span></p>`,
          snippet: `<p class="text-[#707070]"> - Hello Larry, everything great starts with one small step—make yours today.</p>`,
          description: `<p class="text-[#707070]">Hello Larry, everything great starts with one small step—make yours today.</p>`,
          createdAt: '2025-07-16T01:00:00.000Z',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          fileName: 'Inspiration Awaits: Take the First Step.eml',
          size: '2300',
        },
        {
          id: 8,
          profileUrl: '/social/<EMAIL>',
          from: 'Global Forum',
          subject: `<p><span>2024 Global Forum: Registration Open Now</span></p>`,
          snippet: `<p class="text-[#707070]"> - Don’t miss out on a chance to engage with world leaders and innovators.</p>`,
          description: `<p class="text-[#707070]">Don’t miss out on a chance to engage with world leaders and innovators.</p>`,
          createdAt: '2025-07-16T11:45:00.000Z',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
          fileName: '2024 Global Forum: Registration Open Now.eml',
          size: '2800',
        },
        {
          id: 9,
          profileUrl: '/social/<EMAIL>',
          from: 'Workshop Wizard',
          subject: `<p><span>Hands-On Workshop: Learn from the Best</span></p>`,
          snippet: `<p class="text-[#707070]"> - Gain practical insights and skills at our interactive session.</p>`,
          description: `<p class="text-[#707070]">Gain practical insights and skills at our interactive session.</p>`,
          createdAt: '2025-07-16T10:21:00.000Z',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
          fileName: 'Hands-On Workshop: Learn from the Best.eml',
          size: '1900',
        },
        {
          id: 10,
          profileUrl: '/social/<EMAIL>',
          from: 'Launch Party Pro',
          subject: `<p><span>You're Invited to Our Product Launch 🎉</span></p>`,
          snippet: `<p class="text-[#707070]"> - Hello Larry, its a good news for you. be among the first to witness our newest</p>`,
          description: `<p class="text-[#707070]">Hello Larry, its a good news for you. be among the first to witness our newest...</p>`,
          createdAt: '2025-07-16T08:25:00.000Z',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
          fileName: "You're Invited to Our Product Launch.eml",
          size: '3400',
        },
        {
          id: 11,
          profileUrl: '/social/<EMAIL>',
          from: 'Tech Summit',
          subject: `<p><span>Tech Innovations Conference 2024: Register Now</span></p>`,
          snippet: `<p class="text-[#707070]"> - Join industry leaders for groundbreaking discussions and networking.</p>`,
          description: `<p class="text-[#707070]">Join industry leaders for groundbreaking discussions and networking.</p>`,
          createdAt: '2025-07-16T08:25:00.000Z',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
          fileName: 'Tech Innovations Conference 2024: Register Now.eml',
          size: '2200',
        },
        {
          id: 12,
          profileUrl: '/social/<EMAIL>',
          from: 'Shop Smart Now',
          subject: `<p><span>Shop Smarter, Save More 2024: Register Now</span></p>`,
          snippet: `<p class="text-[#707070]"> - Here’s how you can get the most value out of your purchases.</p>`,
          description: `<p class="text-[#707070]">Here’s how you can get the most value out of your purchases.</p>`,
          createdAt: '2024-11-22T10:00:00.000Z',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
          fileName: 'Shop Smarter, Save More 2024: Register Now.eml',
          size: '2600',
        },
        {
          id: 13,
          profileUrl: '/social/<EMAIL>',
          from: 'Exclusive Deals',
          subject: `<p><span>An Exclusive Offer for Our Valued Partners</span></p>`,
          snippet: `<p class="text-[#707070]"> - As a token of appreciation, we’re offering you first access to this deal.</p>`,
          description: `<p class="text-[#707070]">As a token of appreciation, we’re offering you first access to this deal.</p>`,
          createdAt: '2024-11-22T10:00:00.000Z',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
          fileName: 'An Exclusive Offer for Our Valued Partners.eml',
          size: '1700',
        },
        {
          id: 14,
          profileUrl: '/social/<EMAIL>',
          from: 'Meeting Master',
          subject: `<p><span>Meeting Follow-Up: Key Takeaways</span></p>`,
          snippet: `<p class="text-[#707070]"> - Thank you for your time earlier—here are the highlights of our discussion.</p>`,
          description: `<p class="text-[#707070]">Thank you for your time earlier—here are the highlights of our discussion.</p>`,
          createdAt: '2024-11-22T10:00:00.000Z',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
          fileName: 'Meeting Follow-Up: Key Takeaways.eml',
          size: '2100',
        },
        {
          id: 15,
          profileUrl: '/social/<EMAIL>',
          from: 'Time Optimizer',
          subject: `<p><span>Maximize Your Productivity Today</span></p>`,
          snippet: `<p class="text-[#707070]"> - Here are a few simple yet effective strategies to optimize your workflow.</p>`,
          description: `<p class="text-[#707070]">Here are a few simple yet effective strategies to optimize your workflow.</p>`,
          createdAt: '2024-11-22T10:00:00.000Z',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
          fileName: 'Maximize Your Productivity Today.eml',
          size: '1400',
        },
      ],
      selectedEmailMessage: null,
      showComposeSection: false,
      showReplyBox: false,
      composeArray: [],
      numberOfCompose: 0,
      showConfidentialModeModal: false,
      showDriveInsertFileModal: false,
      showInsertPhotoFileModal: false,
      showAddRecipients: false,
      selectedColor: '',
      fileChanged: false,
      fileUrl: '',
      closeAllMenus: false,
      dynamicEmailModal: false,
      createdSignature: [{ id: 1, text: 'No signature' }],
      insertSignature: false,
      showSignatureModal: false,
      editSignatureName: false,
      signatureName: {},
      menuItems: null,
      reportSpamModal: false,
      isGmailMessageRefreshing: false,
      snoozeDateTimeModal: false,
      showGmailComposeModal: false,
      gmailComposeMoreOption: {
        defaultFullScreen: false,
        plainTextMode: false,
        checkSpelling: false,
      },
      gmailGeneral: {
        vacationResponder: {
          status: 'off',
          firstDay: '2025-08-01T00:00:00.000Z',
          lastDay: '2025-08-10T00:00:00.000Z',
          enableLastDay: false,
          subject: '',
          message: '',
          myContent: false,
        },
      },
      gmailInbox: {
        checkedCategories: [
          'Primary',
          'Promotions',
          'Social',
          'Updates',
          'Forums',
        ],
      },
      systemLabelsOptions: [
        {
          id: 1,
          text: 'Inbox',
        },
        {
          id: 2,
          text: 'Starred',
          selected: 'show',
          showText: 'show',
          hideText: 'hide',
        },
        {
          id: 3,
          text: 'Snoozed',
          selected: 'hide',
          showText: 'show',
          hideText: 'hide',
        },
        {
          id: 4,
          text: 'Important',
          selected: 'show',
          showText: 'show',
          hideText: 'hide',
        },
        {
          id: 5,
          text: 'Chats',
          selected: 'hide',
          showText: 'show',
          hideText: 'hide',
        },
        {
          id: 6,
          text: 'Sent',
          selected: 'hide',
          showText: 'show',
          hideText: 'hide',
        },
        {
          id: 7,
          text: 'Scheduled',
          selected: 'show if unread',
          showText: 'show',
          hideText: 'hide',
          unreadText: 'show if unread',
        },
        {
          id: 8,
          text: 'Drafts',
          selected: 'show',
          showText: 'show',
          hideText: 'hide',
          unreadText: 'show if unread',
        },
        {
          id: 9,
          text: 'All Mail',
          selected: 'hide',
          showText: 'show',
          hideText: 'hide',
        },
        {
          id: 10,
          text: 'Spam',
          selected: 'hide',
          showText: 'show',
          hideText: 'hide',
          unreadText: 'show if unread',
        },
        {
          id: 11,
          text: 'Trash',
          selected: 'hide',
          showText: 'show',
          hideText: 'hide',
        },
      ],
      categoriesLabelsOptions: [
        {
          id: 1,
          text: 'Categories',
          selected: 'show',
          showText: 'show',
          hideText: 'hide',
        },
        {
          id: 2,
          text: 'Social',
          selected: 'show',
          showText: 'show',
          hideText: 'hide',
        },
        {
          id: 3,
          text: 'Updates',
          selected: 'show',
          showText: 'show',
          hideText: 'hide',
        },
        {
          id: 4,
          text: 'Forums',
          selected: 'show',
          showText: 'show',
          hideText: 'hide',
        },
        {
          id: 5,
          text: 'Promotions',
          selected: 'show',
          showText: 'show',
          hideText: 'hide',
        },
      ],
      categoriesMessageOptions: [
        {
          id: 1,
          text: 'Categories',
        },
        {
          id: 2,
          text: 'Social',
          selected: 'show',
          showText: 'show',
          hideText: 'hide',
        },
        {
          id: 3,
          text: 'Updates',
          selected: 'show',
          showText: 'show',
          hideText: 'hide',
        },
        {
          id: 4,
          text: 'Forums',
          selected: 'show',
          showText: 'show',
          hideText: 'hide',
        },
        {
          id: 5,
          text: 'Promotions',
          selected: 'show',
          showText: 'show',
          hideText: 'hide',
        },
      ],
      newLabel: {},
      labelsItems: [
        {
          id: 1,
          name: 'dad',
          image: markRaw(ProductIcon),
          nested: false,
          selected: true,
          showInLabelList: true,
          showInMessageList: true,
          children: [],
        },
        {
          id: 2,
          name: 'yyy',
          image: markRaw(ProductIcon),
          nested: false,
          selected: true,
          showInLabelList: true,
          showInMessageList: true,
          children: [],
        },
        {
          id: 3,
          name: 'ete',
          image: markRaw(ProductIcon),
          nested: false,
          selected: true,
          showInLabelList: true,
          showInMessageList: true,
          children: [],
        },
        {
          id: 4,
          name: 'ppp',
          image: markRaw(ProductIcon),
          nested: false,
          selected: true,
          showInLabelList: true,
          showInMessageList: true,
          children: [
            {
              id: 5,
              name: 'ttt12',
              image: markRaw(ProductIcon),
              nested: true,
              selected: true,
              showInLabelList: true,
              showInMessageList: true,
              children: [],
            },
          ],
        },
        {
          id: 6,
          name: 'iii',
          image: markRaw(ProductIcon),
          nested: false,
          selected: true,
          showInLabelList: true,
          showInMessageList: true,
          children: [],
        },
      ],
      createdLabelOptions: [],
      createdLabelMessageOptions: [],
      createdLabelActionOptions: [],
      showNewLabelModal: false,
      showEditLabelModal: false,
      showRemoveConfirmationModal: false,
      filters: [],
      selectedFilter: null,
      tempFilters: null,
      gmailSearchInput: '',
      createNewFilterModal: {
        isOpen: false,
        type: '',
        for: '',
      },
      deleteFilterModal: {
        isOpen: false,
        type: '',
      },
      blockedAddresses: [],
      unblockAddressModal: {
        isOpen: false,
        type: '',
      },
      forwardingAddresses: [],
      forwardingAddressModal: false,
      removeEmailModal: {
        type: '',
        isOpen: false,
        email: null,
      },
      forwardingAndPopImap: {
        forwarding: {
          status: 'disabled',
          forwardingEmail: null,
          forwardingAction: {
            label: "Keep Gmail's copy in the Inbox",
            value: 'keep-in-inbox',
          },
        },
        imapAccess: {
          expunge: 'on',
          deleteAction: 'archive',
          folderSizeLimit: 'unlimited_size',
          currentFolderSize: {
            label: '1,000',
            value: 1000,
          },
        },
        pop: {
          status: 'disabled',
          enabledSince: '4:21 PM',
          popAction: {
            label: "Keep Gmail's copy in the Inbox",
            value: 'keep-in-inbox',
          },
        },
      },
      showImportMailModal: false,
      importMail: [],
      forwardingEmails: [],
      unverifiedEmails: [],
      showAddAccountModal: false,
      accountList: [],
      showAnotherEmailModal: false,
      emailAddressList: [],
      showGrantAccountModal: false,
      showGrantAccountDeleteModal: false,
      grantAccountLists: [],
      selectedGrantAccount: null,
      showNewRule: false,
      generatedRules: [],
      finalMessageFormat: {
        selectedMessageOption: [],
        selectedMessageFormat: 'HTML',
        selectedFontFamily: 'Aptos',
        selectedHeaderSize: 8,
        bold: false,
        italic: false,
        underline: false,
        colorText: '#000000',
      },
      finalCopyPasteFormat: {
        selectedPastingFromEmailsOption: 'Keep source formatting',
        selectedPastingFromAppsOption: 'Merge formatting',
      },
      newRuleData: {
        ruleName: '',
        newconditionsList: [
          {
            id: Date.now(),
            condition: null,
            allContext: [],
            inputValue: '',
            error: false,
            activeItem: false,
          },
        ],
        newactionList: [
          {
            id: Date.now(),
            action: null,
            allContext: [],
            inputValue: '',
            error: false,
            activeItem: false,
          },
        ],
        newexceptionList: [
          {
            id: Date.now(),
            exception: null,
            allContext: [],
            inputValue: '',
            error: false,
            activeItem: false,
          },
        ],
      },
      junkMail: {
        incomingMailHandleType: 'standard',
        blockAttachments: true,
        trustEmailFromContacts: false,
      },
      senderList: {
        safeSenders: [],
        blockedSenders: [],
        safeMailingLists: [],
      },
      messageHandling: {
        emptyDeletedItemsFolder: false,
        markAsRead: 'selection_changes',
        alwaysKeepUnread: true,
        readReceipt: 'ask_me',
        showShoppingLinks: false,
        confirmAction: true,
        translateMessage: 'ask_me_before_translating',
        translateLanguage: 'English',
        noTranslateLanguages: ['English'],
      },
      showNewCategoryModal: false,
      actionCategoryOptions: [
        {
          id: 1,
          title: 'Blue category',
          color: '#d4f4fd',
        },
        {
          id: 2,
          title: 'Green category',
          color: '#cef0cd',
        },
        {
          id: 3,
          title: 'Orange category',
          color: '#f1d9cc',
        },
        {
          id: 4,
          title: 'Red category',
          color: '#f6d1d5',
        },
        {
          id: 5,
          title: 'Yellow category',
          color: '#fcefd3',
        },
      ],
      showEditRule: false,
      labelAsOptions: [
        {
          id: 1,
          title: 'Updates',
          checked: true,
        },
        {
          id: 2,
          title: 'Social',
          checked: false,
        },
        {
          id: 3,
          title: 'Forums',
          checked: false,
        },
        {
          id: 4,
          title: 'Promotions',
          checked: false,
        },
      ],
      readUnreadAll: false,
      showhideAdvanceMenu: false,
      recipientsSearch: '',
      recipientsSuggestionOptions: [
        {
          id: 1,
          name: 'Sharp Archive',
          text: '<EMAIL>',
          subtext: '<EMAIL>',
          image: '/images/microsoft-mail-settings/profile_1.png',
          selected: false,
        },
        {
          id: 2,
          name: 'Sam Wilson',
          text: '<EMAIL>',
          subtext: '<EMAIL>',
          image: '/images/microsoft-mail-settings/profile_2.png',
          selected: false,
        },
        {
          id: 3,
          name: 'Jane Doe',
          text: '<EMAIL>',
          subtext: '<EMAIL>',
          image: '/images/microsoft-mail-settings/profile_3.png',
          selected: false,
        },
      ],
      uploadedFiles: [],
      driveAttachmentFiles: [],
      driveLinkFiles: [],
      photoAttachmentFiles: [],
      showImageAltTextModal: false,
      imageAltTextModalData: {
        currentAlt: '',
        node: null,
        getPos: 0,
      },
      previousPath: null,
    }
  },

  getters: {},

  mutations: {
    SET_PREVIOUS_PATH(state, payload) {
      state.previousPath = payload
    },
    SET_DRIVE_ATTACHMENT_FILES(state, payload) {
      state.driveAttachmentFiles = payload
      console.log(state.driveAttachmentFiles, 'state.driveAttachmentFiles')
    },
    RESET_DRIVE_ATTACHMENT_FILES(state, payload) {
      state.driveAttachmentFiles = []
      console.log(state.driveAttachmentFiles, 'state.driveAttachmentFiles')
    },
    SET_PHOTO_ATTACHMENT_FILES(state, payload) {
      state.photoAttachmentFiles = payload
      console.log(state.photoAttachmentFiles, 'state.driveAttachmentFiles')
    },
    RESET_PHOTO_ATTACHMENT_FILES(state, payload) {
      state.photoAttachmentFiles = []
      console.log(state.photoAttachmentFiles, 'state.driveAttachmentFiles')
    },
    SET_DRIVE_LINK_FILES(state, payload) {
      payload.forEach((item) => {
        if (!item.id) {
          item.id = getNextId(state.driveLinkFiles)
          state.driveLinkFiles.push(item)
        }
      })

      console.log(state.driveLinkFiles, 'state.driveLinkFiles')
    },
    RESET_DRIVE_LINK_FILES(state, payload) {
      state.driveLinkFiles = []
      console.log(state.driveLinkFiles, 'state.driveLinkFiles')
    },
    REMOVE_SPECIFIC_LINK_FILE(state, payload) {
      state.driveLinkFiles.forEach((item, index) => {
        if (item.id === payload) {
          state.driveLinkFiles.splice(index, 1)
        }
      })
    },
    SET_DRIVE_LINK_COMPOSE_SECTION(state, payload) {
      state.composeArray.forEach((item) => {
        if (item.active) {
          if (!item.linkFiles || item.linkFiles.length === 0) {
            item.linkFiles = []
            payload.forEach((value) => {
              if (!value.id) {
                value.id = getNextId(item.linkFiles)
                item.linkFiles.push(value)
              }
            })
          } else if (item.linkFiles && item.linkFiles.length > 0) {
            payload.forEach((value) => {
              if (!value.id) {
                value.id = getNextId(item.linkFiles)
                item.linkFiles.push(value)
              }
            })
          }
        }
      })
    },
    REMOVE_SPECIFIC_LINK_FILE_FROM_SPECIFIC_COMPOSE(state, payload) {
      state.composeArray.forEach((item) => {
        if (item.active && item.linkFiles && item.linkFiles.length > 0) {
          item.linkFiles.forEach((linkfile, index) => {
            if (linkfile.id === payload) {
              item.linkFiles.splice(index, 1)
            }
          })
        }
      })
    },
    SET_SHOW_HIDE_ADVANCE_MENU(state, payload) {
      state.showhideAdvanceMenu = payload
    },
    SET_UPDATED_LABEL_AS_MENU_OPTIONS(state, payload) {
      state.labelAsOptions = payload
    },
    SET_REPORT_SPAM_MODAL(state, payload) {
      state.reportSpamModal = payload
    },
    SET_GMAIL_MESSAGE_REFRESHING(state, payload) {
      state.isGmailMessageRefreshing = payload
    },
    SET_SNOOZE_DATE_TIME_MODAL(state, payload) {
      state.snoozeDateTimeModal = payload
    },
    SET_SHOW_EDIT_RULE(state, payload) {
      state.showEditRule = payload
    },
    SET_SELECTED_UPDATED_GENERATED_RULE(state, payload) {
      state.generatedRules.forEach((generatedRule) => {
        if (generatedRule.id === payload.id) {
          generatedRule.ruleName = payload.ruleName
          generatedRule.newconditionsList = payload.newconditionsList
          generatedRule.newactionList = payload.newactionList
          generatedRule.newexceptionList = payload.newexceptionList
        }
      })
    },
    SET_NEW_RULE_DATA_ON_EDIT(state, payload) {
      state.generatedRules.forEach((generatedRule) => {
        if (generatedRule.id === payload) {
          state.newRuleData = generatedRule
        }
      })
    },
    DELETE_NEW_RULE_FROM_OPTIONS(state, payload) {
      state.generatedRules.forEach((generatedRule, index) => {
        if (generatedRule.id === payload) {
          state.generatedRules.splice(index, 1)
        }
      })
    },
    SET_NEW_RULE_DATA(state, payload) {
      state.newRuleData = payload
    },
    SET_SHOW_NEW_CATEGORY_MODAL(state, payload) {
      state.showNewCategoryModal = payload
    },
    SET_NEW_ACTION_CATEGORY_OPTION(state, payload) {
      state.actionCategoryOptions.push({
        id: getNextId(state.actionCategoryOptions),
        title: payload.name,
        color: payload.color,
      })
    },
    SET_FINAL_COPY_PASTE_FORMAT(state, payload) {
      state.finalCopyPasteFormat = JSON.parse(JSON.stringify(payload))
    },
    SET_FINAL_MESSAGE_FORMAT(state, payload) {
      state.finalMessageFormat = JSON.parse(JSON.stringify(payload))
    },
    SHOW_NEW_RULE(state, payload) {
      state.showNewRule = payload
    },
    SET_GENERATED_RULES(state, payload) {
      state.generatedRules.push({
        id: getNextId(state.generatedRules),
        ruleName: payload.ruleName,
        newconditionsList: payload.newconditionsList,
        newactionList: payload.newactionList,
        newexceptionList: payload.newexceptionList,
        description: `If the message was received from '<EMAIL>', forward the message to '<EMAIL>' and stop processing more rules on this message.`,
        expand: false,
        toggleButton: true,
      })
    },
    SET_GENERATED_RULES_EXPAND_STATE(state, payload) {
      state.generatedRules.forEach((generatedRule) => {
        if (generatedRule.id === payload) {
          generatedRule.expand = !generatedRule.expand
        }
      })
    },
    SET_GENERATED_RULES_ALL_EXPAND_STATE(state, payload) {
      state.generatedRules.forEach((generatedRule) => {
        generatedRule.expand = payload
      })
    },
    SET_ORDERED_GENERATED_RULES(state, payload) {
      state.generatedRules = payload
    },
    MOVE_RULE_UP(state, payload) {
      const temp = state.generatedRules[payload - 1]
      state.generatedRules[payload - 1] = state.generatedRules[payload]
      state.generatedRules[payload] = temp
    },
    MOVE_RULE_DOWN(state, payload) {
      const temp = state.generatedRules[payload + 1]
      state.generatedRules[payload + 1] = state.generatedRules[payload]
      state.generatedRules[payload] = temp
    },
    SHOW_GRANT_ACCOUNT_MODAL(state, payload) {
      state.showGrantAccountModal = payload.show
      if (payload.email) {
        state.grantAccountLists.push({
          id: state.grantAccountLists.length + 1,
          email: payload.email,
        })
      }
    },
    SHOW_GRANT_ACCOUNT_DELETE_MODAL(state, payload) {
      state.showGrantAccountDeleteModal = payload.show
      if (payload.grantAccount) {
        state.selectedGrantAccount = payload.grantAccount
      }
      if (payload.id) {
        state.grantAccountLists.forEach((grantAccountList, index) => {
          if (grantAccountList.id === payload.id) {
            state.grantAccountLists.splice(index, 1)
          }
        })
      }
    },
    SET_SHOW_ANOTHER_EMAIL_MODAL(state, payload) {
      state.showAnotherEmailModal = payload.show
      if (payload.email) {
        state.emailAddressList.push(payload.email)
      }
    },
    SET_SHOW_IMPORT_MAIL_MODAL(state, payload) {
      state.showImportMailModal = payload.show
      if (payload.email) {
        state.importMail.push(payload.email)
      }
    },
    SET_SHOW_ADD_ACCOUNT_MODAL(state, payload) {
      state.showAddAccountModal = payload.show
      if (payload.email) {
        state.accountList.push(payload.email)
      }
    },
    SET_MENU_ITEMS(state, payload) {
      state.menuItems = payload
    },
    SET_LABEL_ITEMS(state, payload) {
      state.labelsItems = payload
    },
    SET_MENU_ITEMS_SHOW_HIDE(state, payload) {
      state.menuItems.forEach((item) => {
        if (
          item.name === payload.text &&
          (payload.selected === 'show' || payload.selected === 'show if unread')
        ) {
          item.selected = true
        } else if (item.name === payload.text && payload.selected === 'hide') {
          item.selected = false
        }
      })
    },
    SET_CHANGE_SELECTED_TEXT(state, payload) {
      state.systemLabelsOptions.forEach((systemLabelsOption) => {
        if (systemLabelsOption.id === payload.id) {
          systemLabelsOption.selected = payload.selectedText
        }
      })
    },
    SET_SHOW_GMAIL_COMPOSE_MODAL(state, payload) {
      state.showGmailComposeModal = payload
    },
    SET_GMAIL_COMPOSE_MORE_OPTION(state, payload) {
      state.gmailComposeMoreOption = {
        ...state.gmailComposeMoreOption,
        ...payload,
      }
    },
    SET_GMAIL_GENERAL(state, payload) {
      state.gmailGeneral = payload
    },
    SET_VACATION_END(state) {
      state.gmailGeneral.vacationResponder.status = 'off'
    },
    SET_CHECKED_INBOX_CATEGORIES(state, payload) {
      state.gmailInbox.checkedCategories = payload
    },
    SET_CATEGORY_LABEL_SELECTED_TEXT(state, payload) {
      state.categoriesMessageOptions.forEach((categoriesMessageOption) => {
        if (categoriesMessageOption.id === payload.id) {
          categoriesMessageOption.selected = payload.selectedText
        }
      })
    },
    SET_CATEGORY_MESSAGE_SELECTED_TEXT(state, payload) {
      state.categoriesLabelsOptions.forEach((categoriesLabelsOption) => {
        if (categoriesLabelsOption.id === payload.id) {
          categoriesLabelsOption.selected = payload.selectedText
        }
      })
    },
    SET_NEW_LABEL_MODAL(state, payload) {
      state.showNewLabelModal = payload
    },
    SET_EDIT_LABEL_MODAL(state, payload) {
      state.showEditLabelModal = payload
      // if (!state.showEditLabelModal) {
      //   state.newLabel = {}
      //   state.createdLabelActionOptions.forEach((createdLabelActionOption) => {
      //     createdLabelActionOption.selected = ''
      //   })
      // }
    },
    SET_REMOVE_CONFIRMATION_MODAL(state, payload) {
      state.showRemoveConfirmationModal = payload
      if (!state.showRemoveConfirmationModal) {
        state.newLabel = {}
        state.createdLabelActionOptions.forEach((createdLabelActionOption) => {
          createdLabelActionOption.selected = ''
        })
      }
    },
    SET_NEW_LABEL(state, payload) {
      state.newLabel = payload
      // state.newLabel.id = payload.id
      // state.newLabel.text = payload.name
      // state.newLabel.nested = payload.nested
      // const result = findNestedItemAndRootParent(state.labelsItems, payload.id)
      const result = findItemAndImmediateParent(state.labelsItems, payload.id)

      if (result && result.parent) {
        state.newLabel.parent = result.parent
      } else {
        state.newLabel.parent = null
      }
    },
    SET_CREATED_LABEL_OPTIONS(state, payload) {
      const maxId = getMaxId(state.labelsItems)
      const tempLabelItems = {
        id: maxId + 1,
        name: payload.text,
        image: markRaw(ProductIcon),
        nested: payload.nested.name ? true : false,
        selected: true,
        showInLabelList: true,
        showInMessageList: true,
        children: [],
      }
      if (payload.nested.name) {
        // state.labelsItems.forEach((item) => {
        //   if (item.id === payload.nested.id) {
        //     item.children.push(tempLabelItems)
        //   }
        // })
        this.commit('emails/SET_NEST_LABEL', {
          labelsItems: state.labelsItems,
          nested: payload.nested,
          tempLabelItems: tempLabelItems,
        })
        // Find the last used id
        const lastId = state.labelAsOptions.length
          ? Math.max(...state.labelAsOptions.map((i) => i.id))
          : 0

        // Flatten new leaves starting from lastId + 1
        const { leaves } = flattenLeaves(
          JSON.parse(JSON.stringify(state.labelsItems)),
          lastId + 1,
        )

        // Filter out leaves that already exist in state.labelAsOptions
        const uniqueLeaves = leaves.filter(
          (leaf) =>
            !state.labelAsOptions.some((opt) => opt.title === leaf.title),
        )

        // Merge only unique ones
        state.labelAsOptions = [...state.labelAsOptions, ...uniqueLeaves]
      } else {
        state.labelsItems.push(tempLabelItems)
        // Find the last used id
        const lastId = state.labelAsOptions.length
          ? Math.max(...state.labelAsOptions.map((i) => i.id))
          : 0

        // Flatten new leaves starting from lastId + 1
        const { leaves } = flattenLeaves(
          JSON.parse(JSON.stringify(state.labelsItems)),
          lastId + 1,
        )

        // Filter out leaves that already exist in state.labelAsOptions
        const uniqueLeaves = leaves.filter(
          (leaf) =>
            !state.labelAsOptions.some((opt) => opt.title === leaf.title),
        )

        // Merge only unique ones
        state.labelAsOptions = [...state.labelAsOptions, ...uniqueLeaves]
      }
    },
    SET_CREATED_LABEL_EDIT_OPTIONS(state, payload) {
      const { updatedTree, removedItem } = removeLabelById(
        state.labelsItems,
        payload.targetId,
      )
      state.labelsItems = updatedTree
      const maxId = getMaxId(state.labelsItems)
      const tempLabelItems = {
        id: removedItem.id || maxId + 1,
        name: payload.text,
        image: markRaw(ProductIcon),
        nested: payload.nested ? true : false,
        selected: removedItem.selected,
        showInLabelList: removedItem.showInLabelList,
        showInMessageList: removedItem.showInMessageList,
        children: removedItem.children || [],
      }
      if (payload.nested) {
        // state.labelsItems.forEach((item) => {
        //   if (item.id === payload.nested.id) {
        //     item.children.push(tempLabelItems)
        //   }
        // })
        this.commit('emails/SET_NEST_LABEL', {
          labelsItems: state.labelsItems,
          nested: payload.nested,
          tempLabelItems: tempLabelItems,
        })
      } else {
        state.labelsItems.push(tempLabelItems)
      }
    },
    SHOW_HIDE_SELECTED_LABEL(state, payload) {
      state.labelsItems = toggleSelectedById(state.labelsItems, payload.id)
    },
    SHOW_HIDE_NESTED_CHILDREN(state, payload) {
      state.labelsItems.forEach((item) => {
        if (item.id === payload.id) {
          item.showInLabelList = !item.showInLabelList
          item.showInMessageList = !item.showInMessageList
        }
      })
    },
    SET_LABEL_SELECTED_TEXT(state, payload) {
      state.createdLabelOptions.forEach((createdLabelOption) => {
        if (createdLabelOption.id === payload.id) {
          createdLabelOption.selected = payload.selectedText
        }
      })
    },
    SET__MESSAGE_LABEL_SELECTED_TEXT(state, payload) {
      state.createdLabelMessageOptions.forEach((createdLabelMessageOption) => {
        if (createdLabelMessageOption.id === payload.id) {
          createdLabelMessageOption.selected = payload.selectedText
        }
      })
    },
    SET_ACTION_LABEL_SELECTED_TEXT(state, payload) {
      state.createdLabelActionOptions.forEach((createdLabelActionOption) => {
        if (createdLabelActionOption.id === payload.id) {
          createdLabelActionOption.selected = payload.selectedText
        }
      })
    },
    SET_LABEL_ITEMS_SHOW_HIDE(state, payload) {
      state.labelsItems.forEach((item) => {
        if (item.id === payload.id) {
          item.showInLabelList = payload.showInLabelList
        }
      })
    },
    SET_REMOVE_EXITING_LABEL(state, payload) {
      state.createdLabelOptions.forEach((createdLabelOption, index) => {
        if (createdLabelOption.id === payload) {
          state.createdLabelOptions.splice(index, 1)
        }
      })
      state.createdLabelMessageOptions.forEach(
        (createdLabelMessageOption, index) => {
          if (createdLabelMessageOption.id === payload) {
            state.createdLabelMessageOptions.splice(index, 1)
          }
        },
      )
      state.createdLabelActionOptions.forEach(
        (createdLabelActionOption, index) => {
          if (createdLabelActionOption.id === payload) {
            state.createdLabelActionOptions.splice(index, 1)
          }
        },
      )
      state.labelsItems.forEach((labelsItem, index) => {
        if (labelsItem.id === payload) {
          state.labelsItems.splice(index, 1)
        }
      })
    },
    SET_NEST_LABEL(state, payload) {
      payload.labelsItems.forEach((item) => {
        if (item.id === payload.nested.id) {
          // payload.tempLabelItems.nested = true
          item.children.push(payload.tempLabelItems)
        } else if (item.children && item.children.length > 0) {
          this.commit('emails/SET_NEST_LABEL', {
            labelsItems: item.children,
            nested: payload.nested,
            tempLabelItems: payload.tempLabelItems,
          })
        }
      })
      payload.labelsItems.forEach((labelsItem, index) => {
        if (labelsItem.id === payload.tempLabelItems.id) {
          payload.labelsItems.splice(index, 1)
        }
      })
    },
    SET_INSERT_SIGNATURE(state, payload) {
      state.insertSignature = payload
    },
    SET_DESCRIPTION_TO_SIGNATURE(state, payload) {
      state.createdSignature.forEach((createdSignature) => {
        payload.forEach((item) => {
          if (item.id === createdSignature.id) {
            createdSignature.description = item.description
          }
        })
      })
    },
    SET_NEW_EMAIL_SIGNATURE(state, payload) {
      const firstElement = state.createdSignature[0]

      // Remove the first element from the array
      const rest = state.createdSignature.slice(1)

      // Add the new element at the start
      rest.unshift({
        id: state.createdSignature.length + 1,
        text: payload,
        selected: true,
      })

      // Re-add the first element at the beginning
      state.createdSignature = [firstElement, ...rest]
      state.createdSignature.forEach((signature, index) => {
        if (index !== 1) {
          signature.selected = false
        }
      })
    },
    REMOVE_EMAIL_SIGNATURE(state, payload) {
      state.createdSignature.forEach((signature, index) => {
        if (signature.id === payload) {
          state.createdSignature.splice(index, 1)
        }
      })
      state.createdSignature.forEach((signature, index) => {
        if (index === 1) {
          signature.selected = true
        }
      })
    },
    SET_EDIT_SIGNATURE_NAME(state, payload) {
      if (payload.show) {
        state.editSignatureName = payload.show
        state.signatureName.text = payload.name
        state.signatureName.id = payload.id
      } else {
        state.editSignatureName = payload.show
        state.signatureName = {}
      }
    },
    SET_UPDATED_EMAIL_SIGNATURE_NAME(state, payload) {
      state.createdSignature.forEach((signature) => {
        if (signature.id === payload.id) {
          signature.text = payload.name
        }
      })

      this.commit('emails/SET_EDIT_SIGNATURE_NAME', {
        show: false,
      })
    },
    SET_SELECTED_EMAIL_SIGNATURE(state, payload) {
      state.createdSignature.forEach((signature) => {
        if (signature.id === payload) {
          signature.selected = true
        } else {
          signature.selected = false
        }
      })
    },
    SHOW_HIDE_EMAIL_SIGNATURE(state, payload) {
      state.showSignatureModal = payload
    },
    SET_CHECKED_ALL_EMAIL_MESSAGES(state, payload) {
      state.emailMessages.forEach((emailMessage) => {
        emailMessage.checked = !emailMessage.checked
      })
    },
    SET_UNCHECKED_ALL_EMAIL_MESSAGES(state, payload) {
      state.emailMessages.forEach((emailMessage) => {
        emailMessage.checked = false
      })
    },
    SET_FAVOURITE_MESSAGE(state, payload) {
      const seletedOne = state.emailMessages.find(
        (emailMessage) => emailMessage.id === payload,
      )
      if (seletedOne) {
        seletedOne.favourite = !seletedOne.favourite
      }
    },
    READ_A_SPECIFIC_MESSAGE(state, payload) {
      const selectedOne = state.emailMessages.find(
        (emailMessage) => emailMessage.id === payload,
      )
      if (selectedOne) {
        selectedOne.read = true
      }
      state.emailMessages.forEach((emailMessage) => {
        if (emailMessage.id === payload) {
          emailMessage.selected = true
        } else {
          emailMessage.selected = false
        }
      })
    },
    READ_UNREAD_A_SPECIFIC_MESSAGE(state, payload) {
      const seletedOne = state.emailMessages.find(
        (emailMessage) => emailMessage.id === payload,
      )
      if (seletedOne) {
        seletedOne.read = !seletedOne.read
      }
    },
    READ_ALL_MESSAGE_WITH_ALL_UNCHECK(state, payload) {
      state.emailMessages.forEach((emailMessage) => {
        emailMessage.read = true
      })
    },
    UNREAD_ALL_MESSAGE_WITH_ALL_UNCHECK(state, payload) {
      state.emailMessages.forEach((emailMessage) => {
        emailMessage.read = false
      })
    },
    READ_ALL_MESSAGE(state, payload) {
      state.emailMessages.forEach((emailMessage) => {
        if (emailMessage.checked) {
          emailMessage.read = true
        }
      })
      const unCheckAllItem = state.emailMessages.every(
        (emailMessage) => !emailMessage.checked,
      )
      if (unCheckAllItem) {
        state.emailMessages.forEach((emailMessage) => {
          emailMessage.read = true
        })
      }
      state.readUnreadAll = true
    },
    UNREAD_ALL_MESSAGE(state, payload) {
      state.emailMessages.forEach((emailMessage) => {
        if (emailMessage.checked) {
          emailMessage.read = false
        }
      })
      const unCheckAllItem = state.emailMessages.every(
        (emailMessage) => !emailMessage.checked,
      )
      if (unCheckAllItem) {
        state.emailMessages.forEach((emailMessage) => {
          emailMessage.read = false
        })
      }
      state.readUnreadAll = false
    },
    SET_CHECKED_READ_EMAIL_MESSAGES(state, payload) {
      switch (payload.title) {
        case 'All':
          state.emailMessages.forEach((emailMessage) => {
            emailMessage.checked = true
          })
          break
        case 'None':
          state.emailMessages.forEach((emailMessage) => {
            emailMessage.checked = false
          })
          break
        case 'Read':
          state.emailMessages.forEach((emailMessage) => {
            emailMessage.checked = emailMessage.read
          })
          break
        case 'Unread':
          state.emailMessages.forEach((emailMessage) => {
            emailMessage.checked = !emailMessage.read
          })
          break
        case 'Starred':
          state.emailMessages.forEach((emailMessage) => {
            emailMessage.checked = emailMessage.starred
          })
          break
        case 'Unstarred':
          state.emailMessages.forEach((emailMessage) => {
            emailMessage.checked = !emailMessage.starred
          })
          break
      }
      // state.emailMessages.forEach((emailMessage) => {
      //   emailMessage.checked = emailMessage.read
      // })
    },
    SET_CHECK_SINGLE_MESSAGE(state, payload) {
      state.emailMessages.forEach((emailMessage) => {
        if (emailMessage.id === payload) {
          emailMessage.checked = true
        } else {
          emailMessage.checked = false
        }
      })
    },
    SET_SELECTED_EMAIL_MESSAGE(state, payload) {
      state.selectedEmailMessage = payload
    },
    SET_SHOW_SPELLING_CHECK_MODAL(state, payload) {
      const activeItem = state.composeArray.find((item) => item?.active)
      if (activeItem) {
        activeItem.spellingCheck = !activeItem?.spellingCheck
      }
    },
    SET_SHOW_COMPOSE_SECTION(state, payload) {
      if (
        payload?.provider === 'Google' &&
        state.gmailComposeMoreOption.defaultFullScreen
      ) {
        if (state.composeArray.length === 0) {
          state.numberOfCompose = state.numberOfCompose + 1
          state.composeArray.push({
            id: state.numberOfCompose,
            active: true,
            recipientInfo: [],
          })
        }
        state.showGmailComposeModal = true
      } else {
        state.numberOfCompose = state.numberOfCompose + 1
        state.composeArray.push({
          id: state.numberOfCompose,
          active: true,
          recipientInfo: [],
        })
      }
      state.showComposeSection = true
      state.composeArray.forEach((item) => {
        if (state.numberOfCompose !== item.id) {
          item.active = false
        }
      })
    },
    SET_FORWARD_MESSAGE_EVENTS(state, payload) {
      setTimeout(() => {
        state.composeArray.forEach((item) => {
          if (item.active) {
            item.forwardMessageEvents = payload
          }
        })
      }, 100)
    },
    SET_ATTACHMENT_FROM_DRIVE(state, payload) {
      console.log(
        state.composeArray,
        'state.composeArray',
        // payload.uploadProgress,
      )
      setTimeout(() => {
        if (state.composeArray && state.composeArray.length > 0) {
          state.composeArray.forEach((item) => {
            if (item.active) {
              if (
                item.attachmentFromDrive &&
                item.attachmentFromDrive.attachedFiles &&
                item.attachmentFromDrive.attachedFiles.length > 0
              ) {
                console.log(item.attachmentFromDrive)
                item.attachmentFromDrive.attachedFiles.push(
                  ...payload.attachedFiles,
                )
                item.attachmentFromDrive.fileUrls.push(...payload.fileUrls)
                // console.log(Array.isArray(payload.uploadProgress), 'payload.uploadProgress')
                // item.attachmentFromDrive.uploadProgress.push(
                //   ...payload.uploadProgress,
                // )
                // console.log(
                //   item.attachmentFromDrive.uploadProgress,
                //   'item.attachmentFromDrive.uploadProgress',
                // )
              } else {
                console.log(payload.attachedFiles, 'attachedFiles')
                item.attachmentFromDrive = payload
              }
            }
          })
        }
      }, 100)
    },
    REMOVE_ATTACHMENT_FROM_DRIVE(state, payload) {
      setTimeout(() => {
        if (state.composeArray && state.composeArray.length > 0) {
          state.composeArray.forEach((item) => {
            if (item.active) {
              if (
                item.attachmentFromDrive.attachedFiles &&
                item.attachmentFromDrive.attachedFiles.length > 0
              ) {
                console.log(item.attachmentFromDrive, payload, 'attachedFiles')
                item.attachmentFromDrive.attachedFiles.splice(payload, 1)
                item.attachmentFromDrive.fileUrls.splice(payload, 1)
                // item.attachmentFromDrive.uploadProgress.splice(payload, 1)
              }
            }
          })
        }
      }, 100)
    },
    RESET_SHOW_COMPOSE_SECTION(state, payload) {
      state.showGmailComposeModal = false
      state.showComposeSection = false
      state.composeArray = []
      state.numberOfCompose = 0
    },
    SET_RECIPIENTS_NAME(state, payload) {
      state.recipientsSuggestionOptions.forEach((option) => {
        if (option.id === payload.recipientInfo.id) {
          option.selected = true
        }
      })
      state.composeArray.forEach((item) => {
        if (item.active && item.id === payload.id) {
          if (item.recipientInfo && item.recipientInfo.length > 0) {
            const exist = item.recipientInfo.some(
              (recipientInfo) => recipientInfo.id === payload.recipientInfo.id,
            )
            if (!exist) {
              item.recipientInfo.push(payload.recipientInfo)
            }
          } else {
            item.recipientInfo.push(payload.recipientInfo)
          }
        }
      })
    },
    SET_REMOVE_SPECIFIC_RECIPIENTS_NAME(state, payload) {
      state.composeArray.forEach((item) => {
        if (item.active && item.id === payload.id) {
          if (item.recipientInfo && item.recipientInfo.length > 0) {
            item.recipientInfo.forEach((recipientInfo, index) => {
              if (recipientInfo.id === payload.recipientInfoId) {
                item.recipientInfo.splice(index, 1)
              }
            })
          }
        }
      })
      state.recipientsSuggestionOptions.forEach((option) => {
        if (option.id === payload.recipientInfoId) {
          option.selected = false
        }
      })
    },
    SET_SHOW_REPLY_BOX(state, payload) {
      state.showReplyBox = payload
    },
    SET_ACTIVE_COMPOSE_MESSAGE(state, payload) {
      state.composeArray.forEach((item) => {
        if (payload === item.id) {
          item.active = true
        } else {
          item.active = false
        }
      })
    },
    DELETE_A_SPECIFIC_COMPOSE_MESSAGE(state, payload) {
      if (state.composeArray.length > 0) {
        state.composeArray.forEach((item, index) => {
          if (item.id === payload) {
            state.composeArray.splice(index, 1)
          }
        })
        if (state.composeArray.length > 0) {
          state.composeArray[state.composeArray.length - 1].active = true
        }
      }
      if (state.composeArray.length === 0) {
        this.commit('emails/RESET_SHOW_COMPOSE_SECTION')
      }
    },
    SET_CONFIDENTIAL_MODE_MODAL(state, payload) {
      state.showConfidentialModeModal = payload
    },
    SET_DRIVE_INSERT_FILE_MODAL(state, payload) {
      state.showDriveInsertFileModal = payload
    },
    SET_PHOTO_INSERT_FILE_MODAL(state, payload) {
      state.showInsertPhotoFileModal = payload
    },
    SET_SHOW_ADD_RECIPIENTS(state, payload) {
      state.showAddRecipients = payload
    },
    SET_SELECTED_COLOR(state, payload) {
      state.selectedColor = payload
    },
    SET_FILE_CHANGED(state, payload) {
      state.fileChanged = payload.fileChanged
      state.fileUrl = payload.fileUrl
    },
    SET_FILE_FROM_PHOTO_INSERT(state, payload) {
      if (state.composeArray && state.composeArray.length > 0) {
        state.composeArray.forEach((item) => {
          if (item.active) {
            item.insertPhotoFile = { ...payload }
          }
        })
      }
    },
    RESET_FILE_FROM_PHOTO_INSERT(state, payload) {
      if (state.composeArray && state.composeArray.length > 0) {
        state.composeArray.forEach((item) => {
          if (item.active) {
            item.insertPhotoFile = { ...payload }
          }
        })
      }
    },
    SET_ATTACHMENT_FROM_PHOTO(state, payload) {
      console.log(
        state.composeArray,
        'state.composeArray',
        // payload.uploadProgress,
      )
      setTimeout(() => {
        if (state.composeArray && state.composeArray.length > 0) {
          state.composeArray.forEach((item) => {
            if (item.active) {
              if (
                item.attachmentFromPhoto &&
                item.attachmentFromPhoto.attachedFiles &&
                item.attachmentFromPhoto.attachedFiles.length > 0
              ) {
                console.log(item.attachmentFromPhoto)
                item.attachmentFromPhoto.attachedFiles.push(
                  ...payload.attachedFiles,
                )
                item.attachmentFromPhoto.fileUrls.push(...payload.fileUrls)
                // console.log(Array.isArray(payload.uploadProgress), 'payload.uploadProgress')
                // item.attachmentFromDrive.uploadProgress.push(
                //   ...payload.uploadProgress,
                // )
                // console.log(
                //   item.attachmentFromDrive.uploadProgress,
                //   'item.attachmentFromDrive.uploadProgress',
                // )
              } else {
                console.log(payload.attachedFiles, 'attachedFiles')
                item.attachmentFromPhoto = payload
              }
            }
          })
        }
      }, 100)
    },
    REMOVE_ATTACHMENT_FROM_PHOTO(state, payload) {
      setTimeout(() => {
        if (state.composeArray && state.composeArray.length > 0) {
          state.composeArray.forEach((item) => {
            if (item.active) {
              if (
                item.attachmentFromPhoto.attachedFiles &&
                item.attachmentFromPhoto.attachedFiles.length > 0
              ) {
                console.log(item.attachmentFromPhoto, payload, 'attachedFiles')
                item.attachmentFromPhoto.attachedFiles.splice(payload, 1)
                item.attachmentFromPhoto.fileUrls.splice(payload, 1)
                // item.attachmentFromDrive.uploadProgress.splice(payload, 1)
              }
            }
          })
        }
      }, 100)
    },
    SET_UPLOAD_FILE_FROM_DRIVE(state, payload) {
      state.uploadedFiles.push({
        id: getNextId(state.uploadedFiles),
        file: payload,
      })
      state.composeArray.forEach((item) => {
        if (item.active) {
          if (!item.uploadedFiles) {
            item.uploadedFiles = []
            item.uploadedFiles.push({
              id: getNextId(item.uploadedFiles),
              file: payload,
            })
          } else {
            item.uploadedFiles.push({
              id: getNextId(item.uploadedFiles),
              file: payload,
            })
          }
        }
      })
    },
    REMOVE_UPLOAD_FILE_FROM_DRIVE(state, payload) {
      state.uploadedFiles.forEach((file, index) => {
        if (file.id === payload) {
          state.uploadedFiles.splice(index, 1)
        }
      })
      state.composeArray.forEach((item) => {
        if (item.active) {
          item.uploadedFiles.forEach((file, index) => {
            if (file.id === payload) {
              item.uploadedFiles.splice(index, 1)
            }
          })
        }
      })
    },
    RESET_UPLOAD_FILE_FROM_DRIVE(state, payload) {
      state.uploadedFiles = []
    },
    SET_CLOSE_ALL_MENUS(state, payload) {
      state.closeAllMenus = payload
    },
    SHOW_DYNAMIC_EMAILS_MODAL(state, payload) {
      state.dynamicEmailModal = payload
    },
    UPDATE_FILTER(state, payload) {
      const index = state.filters.findIndex(
        (filter) => filter.id === payload.id,
      )
      state.filters[index] = {
        ...payload,
      }
    },
    SET_GMAIL_SEARCH_INPUT(state, payload) {
      state.gmailSearchInput = payload
    },
    SET_SELECTED_FILTER(state, payload) {
      state.selectedFilter = payload
    },
    SET_TEMP_FILTERS(state, payload) {
      state.tempFilters = payload
    },
    SET_NEW_FILTER(state, payload) {
      state.filters.push(payload)
    },
    SET_IMAGE_ALT_TEXT_MODAL(state, payload) {
      if (typeof payload === 'boolean') {
        state.showImageAltTextModal = payload
      } else {
        state.showImageAltTextModal = payload.show
        state.imageAltTextModalData = {
          currentAlt: payload.currentAlt || '',
          node: payload.node || null,
          getPos: payload.getPos || 0,
        }
      }
    },
    DELETE_FILTER(state) {
      if (state.deleteFilterModal.type === 'single') {
        state.filters = state.filters.filter(
          (filter) => filter.id !== state.deleteFilterModal?.id,
        )
      } else {
        state.filters = state.filters.filter((filter) => !filter.isChecked)
      }
    },
    SET_CREATE_NEW_FILTER_MODAL(state, payload) {
      state.createNewFilterModal = payload
    },
    SET_DELETE_FILTER_MODAL(state, payload) {
      state.deleteFilterModal = {
        ...payload,
      }
    },
    SET_BLOCKED_ADDRESSES(state, payload) {
      state.blockedAddresses = payload
    },
    SET_UNBLOCK_ADDRESS_MODAL(state, payload) {
      state.unblockAddressModal = {
        ...payload,
      }
    },
    DELETE_BLOCKED_ADDRESS(state) {
      if (state.unblockAddressModal.type === 'single') {
        state.blockedAddresses = state.blockedAddresses.filter(
          (address) => address.id !== state.unblockAddressModal?.id,
        )
      } else {
        state.blockedAddresses = state.blockedAddresses.filter(
          (address) => !address.isChecked,
        )
      }
    },
    UPDATE_FORWARDING_AND_POP_IMAP(state, payload) {
      const { imapAccess, pop, forwarding } = payload
      if (forwarding) {
        state.forwardingAndPopImap.forwarding = {
          ...state.forwardingAndPopImap.forwarding,
          ...forwarding,
        }
      }
      if (imapAccess) {
        state.forwardingAndPopImap.imapAccess = {
          ...state.forwardingAndPopImap.imapAccess,
          ...imapAccess,
        }
      }
      if (pop) {
        state.forwardingAndPopImap.pop = {
          ...state.forwardingAndPopImap.pop,
          ...pop,
        }
      }
    },
    SET_FORWARDING_ADDRESSES(state, payload) {
      state.forwardingAddresses = payload
    },
    SET_FORWARDING_ADDRESS_MODAL(state, payload) {
      state.forwardingAddressModal = payload
    },
    SET_REMOVE_EMAIL_MODAL(state, payload) {
      state.removeEmailModal = {
        ...state.removeEmailModal,
        ...payload,
      }
    },
    SET_UNVERIFIED_EMAIL(state, payload) {
      state.unverifiedEmails.push(payload)
    },
    SET_VERIFIED_EMAIL(state, payload) {
      state.unverifiedEmails = state.unverifiedEmails.filter(
        (email) => email.id !== payload.id,
      )
      state.forwardingEmails.push(payload)
    },
    REMOVE_EMAIL(state) {
      state.forwardingEmails = state.forwardingEmails.filter(
        (email) => email.id !== state.removeEmailModal.email.id,
      )
      if (
        state.removeEmailModal.email?.id ===
        state.forwardingAndPopImap.forwarding.forwardingEmail?.id
      ) {
        state.forwardingAndPopImap.forwarding.status = 'disabled'
        state.forwardingAndPopImap.forwarding.forwardingEmail = null
      }
    },
    REMOVE_UNVERIFIED_EMAIL(state) {
      state.unverifiedEmails = state.unverifiedEmails.filter(
        (email) => email.id !== state.removeEmailModal.email.id,
      )
    },
    UPDATE_JUNK_MAIL(state, payload) {
      state.junkMail = {
        ...state.junkMail,
        ...payload,
      }
    },
    UPDATE_SENDER_LIST(state, payload) {
      const { senderList, selectedSender } = payload
      state.senderList = {
        ...state.senderList,
        ...senderList,
      }
    },
    UPDATE_MESSAGE_HANDLING(state, payload) {
      state.messageHandling = {
        ...state.messageHandling,
        ...payload,
      }
    },
  },

  actions: {},
}

export default emailsStore
