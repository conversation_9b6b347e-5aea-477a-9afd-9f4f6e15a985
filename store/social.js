const socialStore = {
  namespaced: true,
  state() {
    return {
      showManagerOnboarding: false,
      accountItems: [
        {
          id: 1,
          type: 'socials',
          profilePic: `/social/profile-picture.png`,
          provider: 'Facebook',
          name: '<PERSON>archi<PERSON>',
          username: '@official.sharparchive',
          count: 5,
          toggleSelect: true,
          select: false,
        },
        {
          id: 2,
          type: 'socials',
          profilePic: `/social/profile-picture.png`,
          provider: 'Instagram',
          name: '<PERSON>archi<PERSON>',
          username: '@sharparchive',
          count: 3,
          toggleSelect: true,
          select: false,
        },
        {
          id: 3,
          type: 'socials',
          profilePic: `/social/profile-picture.png`,
          provider: 'Twitter',
          name: '<PERSON>archi<PERSON>',
          username: '@sharp_archive',
          count: 4,
          toggleSelect: true,
          select: false,
        },
        {
          id: 4,
          type: 'socials',
          profilePic: `/social/profile-picture.png`,
          provider: 'LinkedIn',
          name: '<PERSON>archi<PERSON>',
          username: '@sharparchive1',
          count: 1,
          toggleSelect: true,
          select: false,
        },
        {
          id: 5,
          type: 'text',
          profilePic: `/social/chad-profile.png`,
          provider: 'iMessage',
          name: '<PERSON>',
          username: '@sharparchive2',
          number: '+1 (555) 444-3333',
          count: 2,
          toggleSelect: true,
          select: false,
        },
        {
          id: 6,
          type: 'text',
          profilePic: `/social/chad-profile.png`,
          provider: 'WhatsApp',
          name: 'Sharparchive (CE0)',
          username: '@sharparchive3',
          number: '+****************',
          count: 1,
          toggleSelect: true,
          select: false,
        },
        {
          id: 7,
          type: 'text',
          profilePic: `/social/chad-profile.png`,
          provider: 'Telegram',
          name: 'Sharparchive (CE0)',
          username: '@sharparchive4',
          number: '+****************',
          count: 0,
          toggleSelect: true,
          select: false,
        },
        {
          id: 8,
          type: 'emails',
          profilePic: `/social/chad-profile.png`,
          provider: 'Google',
          name: 'Larry Johnson',
          username: '<EMAIL>',
          count: 0,
          toggleSelect: true,
          select: false,
        },
        {
          id: 9,
          type: 'emails',
          profilePic: `/social/chad-profile.png`,
          provider: 'Microsoft',
          name: 'Scarlet Jane',
          username: '<EMAIL>',
          count: 0,
          toggleSelect: true,
          select: false,
        },
      ],
      sideBarAccountItems: [],
      finalSideBarAccountItems: [],
      accountItem: null,
      notificatioItems: {
        id: 1,
        name: 'Sharparchive',
        provider: 'Facebook',
        items: [
          {
            id: 7,
            chatId: 83,
            messageId: 235,
            provider: 'Facebook',
            type: 'messages',
            mentionText: `<p class="text-[#525252] line-clamp-2">
          Brandon Vargas
          <span class="text-[#333333] font-semibold">Mentioned</span> you in a
          message
        </p>`,
            img: `/social/default-profile.png`,
            name: 'Brandon Vargas',
            createdAt: 'Jun 20, 2022',
            date: 'now',
            messageText: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
            readed: false,
          },
          {
            id: 1,
            chatId: 83,
            messageId: 244,
            provider: 'Facebook',
            type: 'messages',
            mentionText: `<p class="text-[#525252] line-clamp-2">
          Brandon Vargas
          <span class="text-[#333333] font-semibold">Mentioned</span> you in a
          message
        </p>`,
            img: `/social/default-profile.png`,
            name: 'Brandon Vargas',
            createdAt: 'Mar 18, 2024',
            date: 'Just now',
            messageText: 'How do you approach marketing strategy?',
            readed: false,
          },
          {
            id: 2,
            postId: 10,
            commentId: 122,
            provider: 'Facebook',
            type: 'comments',
            mentionText: `<p class="text-[#525252]">
          Brandon Vargas
          <span class="text-[#333333] font-semibold">Commented</span> on your
          post
          </p>`,
            img: `/social/profile-picture.png`,
            name: 'Sharparchive',
            createdAt: 'Mar 22, 2024',
            description: `🚀 Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries`,
            date: '1h',
            commentText: 'How do you approach marketing strategy?',
            commeterImage: `/social/default-profile.png`,
            readed: false,
          },
          {
            id: 3,
            provider: 'Facebook',
            type: 'mentions',
            mentionText: `<p class="text-[#525252]">
          Brandon Vargas
          <span class="text-[#333333] font-semibold">Mentioned</span> you in a
          post
          </p>`,
            img: `/social/default-profile.png`,
            name: 'Brandon Vargas',
            createdAt: 'Mar 18, 2024',
            description: `🚀 Elevate your business with <span class="font-semibold">Sharp Archive</span>. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries`,
            date: '2h',
            readed: false,
          },
          {
            id: 4,
            commentId: 123,
            provider: 'Facebook',
            type: 'mentions',
            mentionText: `<p class="text-[#525252]">
          Brandon Vargas
          <span class="text-[#333333] font-semibold">Mentioned</span> you in a
          comment
          </p>`,
            img: `/social/greenstar.png`,
            name: 'Greenstar Advisors',
            createdAt: 'Mar 12, 2024',
            description: `Your Path to Freedom. At GreenStar, our goal is to help you reach your goals. We do this by transforming your goals into an actionable strategy that is simple, proactive, Your Path to Freedom. At GreenStar, our goal is to help you reach your goals. We do this by transforming your goals into an actionable strategy that is simple, proactive,... more, Your Path to Freedom. At GreenStar, our goal is to help you reach your goals. We do this by transforming your goals into an actionable strategy that is simple, proactive, Your Path to Freedom. At GreenStar, our goal is to help you reach your goals. We do this by transforming your goals into an actionable strategy that is simple, proactive, Your Path to Freedom. At GreenStar, our goal is to help you reach your goals. We do this by transforming your goals into an actionable strategy that is simple, proactive.`,
            date: '3h',
            commentText: `<span class="text-[#333333] font-semibold"> Sharparchive</span> might be
        helpful for you`,
            commeterImage: `/social/default-profile.png`,
            readed: false,
          },
          {
            id: 5,
            provider: 'Facebook',
            type: 'tagged',
            mentionText: `<p class="text-[#525252]">
          Brandon Vargas
          <span class="text-[#333333] font-semibold">Tagged</span> you in a post
          </p>`,
            img: `/social/default-profile.png`,
            name: 'Brandon Vargas',
            createdAt: 'Mar 21, 2024',
            description: `📧 Emails, social media, websites, oh my! We've got your archiving needs covered. Stay compliant, stay secure. 💻🔒 #DigitalArchiving #BusinessSolutions Check out https://sharparchive.com. 📧 Emails, social media, websites, oh my! We've got your archiving needs covered. Stay compliant, stay secure. 💻🔒 #DigitalArchiving #BusinessSolutions Check out https://sharparchive.com. 📧 Emails, social media, websites, oh my! We've got your archiving needs covered. Stay compliant, stay secure. 💻🔒 #DigitalArchiving #BusinessSolutions Check out https://sharparchive.com.`,
            date: '3h',
            taggedPeople: 5,
            readed: true,
          },
          {
            id: 6,
            chatId: 7,
            messageId: 21,
            provider: 'Facebook',
            type: 'messages',
            mentionText: `<p class="text-[#525252] line-clamp-2">
          Brandon Vargas
          <span class="text-[#333333] font-semibold">Mentioned</span> you in a
          message
        </p>`,
            img: `/social/greenstar.png`,
            name: 'Alvaro Alvin',
            createdAt: 'Mar 18, 2024',
            date: '1m',
            messageText: 'How are you?',
            readed: false,
          },
        ],
      },
      selectedNotificationItems: null,
      showAllMessages: false,
      personLists: {
        id: 1,
        provider: 'Facebook',
        type: 'Page',
        name: 'Sharparchive',
        username: '@sharparchive',
        profilePic: '/social/profile-picture.png',
        items: [
          {
            id: 83,
            socialId: 't_338357465296852',
            pageId: '103338412240799',
            updatedAt: '2025-02-05T08:16:24Z',
            participants: [
              {
                name: 'Brandon Vargas',
                email: '<EMAIL>',
                uid: '6179962028721208',
                profileImageUrl: '/social/default-profile.png',
              },
              {
                name: 'Sharparchive',
                email: '<EMAIL>',
                uid: '103338412240799',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/103338412240799.jpg?provider=Facebook',
              },
            ],
            formerParticipants: [],
            canReply: true,
            isSubscribed: true,
            messageCount: 16,
            unreadCount: 0,
            snippet: 'How do you approach marketing strategy?',
            facebookUrl:
              '/103338412240799/inbox/317830360791602/?section=messages',
            selected: true,
          },
          {
            id: 7,
            socialId: 't_3283786781844066',
            pageId: '103338412240799',
            updatedAt: '2023-09-17T08:43:37Z',
            participants: [
              {
                name: 'Green Star',
                email: '<EMAIL>',
                uid: '7986053301470122',
                profileImageUrl: '/social/greenstar.png',
              },
              {
                name: 'Sharparchive',
                email: '<EMAIL>',
                uid: '103338412240799',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/103338412240799.jpg?provider=Facebook',
              },
            ],
            formerParticipants: [],
            canReply: true,
            isSubscribed: true,
            messageCount: 3,
            unreadCount: 1,
            snippet: 'How are you?',
            facebookUrl:
              '/103338412240799/inbox/175450988362874/?section=messages',
            selected: false,
          },
          {
            id: 6,
            socialId: 't_322593583412657',
            pageId: '103338412240799',
            updatedAt: '2023-09-17T08:39:23Z',
            participants: [
              {
                name: 'بشير أحمد',
                email: '<EMAIL>',
                uid: '7491185484286443',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/7491185484286443.jpg?provider=Facebook',
              },
              {
                name: 'Sharparchive',
                email: '<EMAIL>',
                uid: '103338412240799',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/103338412240799.jpg?provider=Facebook',
              },
            ],
            formerParticipants: [],
            canReply: true,
            isSubscribed: true,
            messageCount: 32,
            unreadCount: 0,
            snippet: 'Testing Messages',
            facebookUrl:
              '/103338412240799/inbox/155290790378894/?section=messages',
            selected: false,
          },
          {
            id: 8,
            socialId: 't_147567564486945',
            pageId: '103338412240799',
            updatedAt: '2022-06-20T13:38:46Z',
            participants: [
              {
                name: 'John J Dami',
                email: '<EMAIL>',
                uid: '5401705403195076',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/5401705403195076.jpg?provider=Facebook',
              },
              {
                name: 'Sharparchive',
                email: '<EMAIL>',
                uid: '103338412240799',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/103338412240799.jpg?provider=Facebook',
              },
            ],
            formerParticipants: [],
            canReply: true,
            isSubscribed: true,
            messageCount: 119,
            unreadCount: 0,
            snippet: 'devxhub',
            facebookUrl:
              '/103338412240799/inbox/149235314317775/?section=messages',
            selected: false,
          },
          {
            id: 9,
            socialId: 't_276562077937803',
            pageId: '103338412240799',
            updatedAt: '2022-06-20T11:30:02Z',
            participants: [
              {
                name: 'Edmond Dantes',
                email: '<EMAIL>',
                uid: '7368864306516953',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/7368864306516953.jpg?provider=Facebook',
              },
              {
                name: 'Sharparchive',
                email: '<EMAIL>',
                uid: '103338412240799',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/103338412240799.jpg?provider=Facebook',
              },
            ],
            formerParticipants: [],
            canReply: true,
            isSubscribed: true,
            messageCount: 39,
            unreadCount: 0,
            snippet: 'You sent a sticker.',
            facebookUrl:
              '/103338412240799/inbox/123861440188496/?section=messages',
            selected: false,
          },
          {
            id: 10,
            socialId: 't_2323008907837029',
            pageId: '103338412240799',
            updatedAt: '2022-06-19T10:27:27Z',
            participants: [
              {
                name: 'Bashir Ahmed Srabon',
                email: '<EMAIL>',
                uid: '5359552637429967',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/5359552637429967.jpg?provider=Facebook',
              },
              {
                name: 'Sharparchive',
                email: '<EMAIL>',
                uid: '103338412240799',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/103338412240799.jpg?provider=Facebook',
              },
            ],
            formerParticipants: [],
            canReply: true,
            isSubscribed: true,
            messageCount: 99,
            unreadCount: 0,
            snippet: 'You sent some attachments.',
            facebookUrl:
              '/103338412240799/inbox/152485303992776/?section=messages',
            selected: false,
          },
          {
            id: 11,
            socialId: 't_150269864241300',
            pageId: '103338412240799',
            updatedAt: '2022-06-16T09:46:26Z',
            participants: [
              {
                name: 'Rudolph Smith',
                email: '<EMAIL>',
                uid: '7491106807629709',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/7491106807629709.jpg?provider=Facebook',
              },
              {
                name: 'Sharparchive',
                email: '<EMAIL>',
                uid: '103338412240799',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/103338412240799.jpg?provider=Facebook',
              },
            ],
            formerParticipants: [],
            canReply: true,
            isSubscribed: true,
            messageCount: 35,
            unreadCount: 0,
            snippet: 'Rudolph sent a photo.',
            facebookUrl:
              '/103338412240799/inbox/154103023831004/?section=messages',
            selected: false,
          },
          {
            id: 12,
            socialId: 't_152566390679132',
            pageId: '103338412240799',
            updatedAt: '2022-06-14T11:30:36Z',
            participants: [
              {
                name: 'Larry E Crisp',
                email: '<EMAIL>',
                uid: '4824531734318133',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/4824531734318133.jpg?provider=Facebook',
              },
              {
                name: 'Sharparchive',
                email: '<EMAIL>',
                uid: '103338412240799',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/103338412240799.jpg?provider=Facebook',
              },
            ],
            formerParticipants: [],
            canReply: true,
            isSubscribed: true,
            messageCount: 1,
            unreadCount: 0,
            snippet: 'Larry sent a GIF.',
            facebookUrl:
              '/103338412240799/inbox/153847777189862/?section=messages',
            selected: false,
          },
          {
            id: 13,
            socialId: 't_102316839136889',
            pageId: '103338412240799',
            updatedAt: '2022-06-08T18:02:55Z',
            participants: [
              {
                name: 'Saila Bomjan Gaphadi',
                email: '<EMAIL>',
                uid: '5395032097207826',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/5395032097207826.jpg?provider=Facebook',
              },
              {
                name: 'Sharparchive',
                email: '<EMAIL>',
                uid: '103338412240799',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/103338412240799.jpg?provider=Facebook',
              },
            ],
            formerParticipants: [],
            canReply: true,
            isSubscribed: true,
            messageCount: 7,
            unreadCount: 0,
            snippet: 'Working Fine',
            facebookUrl:
              '/103338412240799/inbox/141008721807101/?section=messages',
            selected: false,
          },
          {
            id: 14,
            socialId: 't_307450241498001',
            pageId: '103338412240799',
            updatedAt: '2022-05-29T06:38:03Z',
            participants: [
              {
                name: 'MD Siyam Talukder',
                email: '<EMAIL>',
                uid: '4966781153390813',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/4966781153390813.jpg?provider=Facebook',
              },
              {
                name: 'Sharparchive',
                email: '<EMAIL>',
                uid: '103338412240799',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/103338412240799.jpg?provider=Facebook',
              },
            ],
            formerParticipants: [],
            canReply: true,
            isSubscribed: true,
            messageCount: 5,
            unreadCount: 0,
            snippet: 'You sent 4 photos.',
            facebookUrl:
              '/103338412240799/inbox/136983578876282/?section=messages',
            selected: false,
          },
          {
            id: 15,
            socialId: 't_105838308776755',
            pageId: '103338412240799',
            updatedAt: '2022-04-21T09:17:40Z',
            participants: [
              {
                name: 'Piviline Koivogui',
                email: '<EMAIL>',
                uid: '7880658691952194',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/7880658691952194.jpg?provider=Facebook',
              },
              {
                name: 'Sharparchive',
                email: '<EMAIL>',
                uid: '103338412240799',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/103338412240799.jpg?provider=Facebook',
              },
            ],
            formerParticipants: [],
            canReply: true,
            isSubscribed: true,
            messageCount: 1,
            unreadCount: 0,
            snippet: 'Votre couple me plait beaucoup',
            facebookUrl:
              '/103338412240799/inbox/140712238503416/?section=messages',
            selected: false,
          },
          {
            id: 16,
            socialId: 't_135709172360650',
            pageId: '103338412240799',
            updatedAt: '2022-04-15T05:11:34Z',
            participants: [
              {
                name: 'Facebook user',
                email: '<EMAIL>',
                uid: '7200886663265793',
                profileImageUrl: '',
              },
              {
                name: 'Sharparchive',
                email: '<EMAIL>',
                uid: '103338412240799',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/103338412240799.jpg?provider=Facebook',
              },
            ],
            formerParticipants: [],
            canReply: false,
            isSubscribed: true,
            messageCount: 4,
            unreadCount: 0,
            snippet: 'Message unavailable',
            facebookUrl:
              '/103338412240799/inbox/138720838702556/?section=messages',
            selected: false,
          },
          {
            id: 17,
            socialId: 't_2727660197538519',
            pageId: '103338412240799',
            updatedAt: '2022-01-06T19:10:47Z',
            participants: [
              {
                name: 'Tanvir Hossain',
                email: '<EMAIL>',
                uid: '4482915371835857',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/4482915371835857.jpg?provider=Facebook',
              },
              {
                name: 'Sharparchive',
                email: '<EMAIL>',
                uid: '103338412240799',
                profileImageUrl:
                  'https://dev-api.sharparchive.com/api/social/media/103338412240799.jpg?provider=Facebook',
              },
            ],
            formerParticipants: [],
            canReply: true,
            isSubscribed: true,
            messageCount: 8,
            unreadCount: 0,
            snippet:
              'This message is only visible to admins of this page. You confirmed that Tanvir placed an order.',
            facebookUrl:
              '/103338412240799/inbox/103349408906366/?section=messages',
            selected: false,
          },
        ],
      },
      allMessages: {
        id: 83,
        socialId: 't_147567564486945',
        pageId: '103338412240799',
        updatedAt: '2022-06-20T13:38:46Z',
        participants: [
          {
            name: 'Brandon Vargas',
            email: '<EMAIL>',
            uid: '5401705403195076',
            profileImageUrl: '/social/default-profile.png',
          },
          {
            name: 'Sharparchive',
            email: '<EMAIL>',
            uid: '103338412240799',
            profileImageUrl: '/social/profile-picture.png',
          },
        ],
        formerParticipants: [],
        canReply: true,
        isSubscribed: true,
        messageCount: 119,
        unreadCount: 0,
        snippet: 'How do you approach marketing strategy?',
        facebookUrl: '/103338412240799/inbox/149235314317775/?section=messages',
        messages: [
          {
            id: 244,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_IyJO16KQjIOSCCnws2INsDrzf3NteVI5Ksuetb__WMhyK6zFX0y-NTxVTuOiTaAfG6mbr9aKS_nYzc0JfG-d9Q',
            threadId: 't_147567564486945',
            createdAt: '2022-06-20T13:38:46Z',
            fromUid: '5401705403195076',
            toUids: ['103338412240799'],
            text: 'How do you approach marketing strategy?',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: '',
            archivedAttachments: '',
          },
          {
            id: 243,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_ZzydEY5Q3Ka5YzGuwIDHZTrzf3NteVI5Ksuetb__WMiFyoDI_nCOwppfYjHAediMuFGjThUy9lCA8xk0sX3JLg',
            threadId: 't_147567564486945',
            createdAt: '2022-06-20T13:37:08Z',
            fromUid: '5401705403195076',
            toUids: ['103338412240799'],
            text: 'hello',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: '',
            archivedAttachments: '',
          },
          {
            id: 242,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_0xCrFqBjTbgahIvwgD8qRDrzf3NteVI5Ksuetb__WMhOLe1bOIc_9tv6g8TQuvhxxXeuTD7kVcR74eiMvn4sbA',
            threadId: 't_147567564486945',
            createdAt: '2022-06-20T13:37:05Z',
            fromUid: '5401705403195076',
            toUids: ['103338412240799'],
            text: 'fagun',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: '',
            archivedAttachments: '',
          },
          {
            id: 241,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_KfWpudPA-hR0CCI9vGXEQjrzf3NteVI5Ksuetb__WMhyXBaYez9qGr5TSYsBCxBqXakvT2Y6TWbwc51cVsWY4g',
            threadId: 't_147567564486945',
            createdAt: '2022-06-20T13:36:26Z',
            fromUid: '5401705403195076',
            toUids: ['103338412240799'],
            text: 'testing',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: '',
            archivedAttachments: '',
          },
          {
            id: 240,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_bRJFGsHX7vBd2Rb8PEGL5Drzf3NteVI5Ksuetb__WMj-ld-JEaFpcFbbFkhxLocpx3YDh7JPu5f9eHpAm_lyzQ',
            threadId: 't_147567564486945',
            createdAt: '2022-06-20T13:35:27Z',
            fromUid: '5401705403195076',
            toUids: ['103338412240799'],
            text: 'hello devxhub',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: '',
            archivedAttachments: '',
          },
          {
            id: 239,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_ookFbU9wWUAjoK-B9-9igzrzf3NteVI5Ksuetb__WMilokBN_edm_AGBFi3MqpMmcPkZz4oPbmaqcq7v0LeIzA',
            threadId: 't_147567564486945',
            createdAt: '2022-06-20T08:47:19Z',
            fromUid: '5401705403195076',
            toUids: ['103338412240799'],
            text: 'Fagun',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: '',
            archivedAttachments: '',
          },
          {
            id: 238,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_78FXlVhBwTExGp9J9MyeTTrzf3NteVI5Ksuetb__WMhYwd2WRjFrRfRCLS0XtD5AabkBDZsDRFVeVmEV-Accug',
            threadId: 't_147567564486945',
            createdAt: '2022-06-20T08:46:43Z',
            fromUid: '5401705403195076',
            toUids: ['103338412240799'],
            text: 'https://www.facebook.com/',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: '',
            archivedAttachments: '',
          },
          {
            id: 237,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_2905j61GRfQeU-t8ze_1GDrzf3NteVI5Ksuetb__WMiZBVbteeewwckd48iC7KsI82jG1SJukULrFy9m7ZXWwg',
            threadId: 't_147567564486945',
            createdAt: '2022-06-20T08:46:31Z',
            fromUid: '5401705403195076',
            toUids: ['103338412240799'],
            text: '🥹',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: '',
            archivedAttachments: '',
          },
          {
            id: 236,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_zxKlxc3e4UwrJbboFaf-ITrzf3NteVI5Ksuetb__WMjFQXjuO5SpNL2-tgpflo8CGtoasbgy1JKtaeuZ9RDmDA',
            threadId: 't_147567564486945',
            createdAt: '2022-06-20T08:46:15Z',
            fromUid: '5401705403195076',
            toUids: ['103338412240799'],
            text: 'Hello DevXhub',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: '',
            archivedAttachments: '',
          },
          {
            id: 235,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_hgToAx65NOGIfTVFbywWbjrzf3NteVI5Ksuetb__WMh0KG5hrofaHNL3e4xpTWzc7OtcAIO-hMo4C3bTtwf7Bw',
            threadId: 't_147567564486945',
            createdAt: '2022-06-20T08:46:07Z',
            fromUid: '5401705403195076',
            toUids: ['103338412240799'],
            text: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: '',
            archivedAttachments: '',
          },
          {
            id: 234,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_CLizRXmV_G9I5bBdeNjj0Trzf3NteVI5Ksuetb__WMh6cW1-n5Nr5WFZwBDQi3lLWxWTrCPC4M8ErSDiLmAV9A',
            threadId: 't_147567564486945',
            createdAt: '2022-06-20T08:45:09Z',
            fromUid: '103338412240799',
            toUids: ['5401705403195076'],
            text: 'snippet',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'sent',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: '',
            archivedAttachments: '',
          },
          {
            id: 233,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_ccbpP3zSjIjPcHsL7NwX3zrzf3NteVI5Ksuetb__WMgS0BULGRiyvDyf-BwJxCtSAr2_vRNYcLgiqPaGr5bWXA',
            threadId: 't_147567564486945',
            createdAt: '2022-06-20T08:44:14Z',
            fromUid: '103338412240799',
            toUids: ['5401705403195076'],
            text: 'hi',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'sent',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: '',
            archivedAttachments: '',
          },
          {
            id: 232,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_F7VSrtNa8Efz-lQNaf-cZDrzf3NteVI5Ksuetb__WMhMR41sGFUoJmxPHzemeiTtxKfdoghhNFti7YDoEm3w7Q',
            threadId: 't_147567564486945',
            createdAt: '2022-06-20T08:42:38Z',
            fromUid: '103338412240799',
            toUids: ['5401705403195076'],
            text: 'hello',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'sent',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: '',
            archivedAttachments: '',
          },
          {
            id: 231,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_rzYtiTAeIFE20byaBRg3ODrzf3NteVI5Ksuetb__WMiAiM2BgDhkpEavoSbgCHws5uJW2ay7FbXnkngrA8EyIQ',
            threadId: 't_147567564486945',
            createdAt: '2022-06-20T08:15:42Z',
            fromUid: '103338412240799',
            toUids: ['5401705403195076'],
            text: 'hi',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'sent',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: '',
            archivedAttachments: '',
          },
          {
            id: 230,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_9mw0EcDu8QNadWxy7mR42Trzf3NteVI5Ksuetb__WMidtPkZFE_of3Hu1hAylXdwNnt2HruC73kd2msprD-ZJw',
            threadId: 't_147567564486945',
            createdAt: '2022-06-19T08:26:29Z',
            fromUid: '103338412240799',
            toUids: ['5401705403195076'],
            text: 'video format webm',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'sent',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: '',
            archivedAttachments: '',
          },
          {
            id: 229,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_dIPCrcqcmYxHYSx5YaTy6jrzf3NteVI5Ksuetb__WMibJCw_A9EZEIfdWtDHoiGYTwQu5d67NjfRbhSi7qRe6w',
            threadId: 't_147567564486945',
            createdAt: '2022-06-19T08:25:54Z',
            fromUid: '103338412240799',
            toUids: ['5401705403195076'],
            text: '',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'sent',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: [
              {
                mime_type: 'video/mp4',
                name: 'video-**********.mp4',
                size: 1788532,
                url: 'https://video-iad3-2.xx.fbcdn.net/o1/v/t2/f2/m272/AQP5xzMSC-KEaeAyIfN2WgKprk5phxjX0mozNwE8gh3G7kYAdy7br4Bsu2N5WkF4Zdny0LmT22ybFYCfkuNwJToIuc7GvAaX0B-S9K2pSF_OajQatbV97UgBh8LwRA.mp4?_nc_ht=video-iad3-2.xx.fbcdn.net&_nc_cat=109&_nc_eui2=AeFK_JFOrRV-UeNYVRjCAqee7bZKYFclqUvttkpgVyWpS0eWLfFAnS4OdkSWhivjh2Y7p_-Q2nxtVjn2qxZaBMJZ&_nc_oc=AdgbODUxY8hvjqduY9OSwngb2iTQfD3e9PfraBsQbw3RIIWqyvcBKSPxZJtWEpmGa1c&strext=1&ccb=9-4&oh=00_AYCIxKsvOsJAaYzeJPjy7hfiUeqG8aEvhjizTs0XFoeUCw&oe=67A3F6DA&_nc_sid=060d78&dl=1',
                preview_url:
                  'https://scontent-iad3-2.xx.fbcdn.net/v/t15.3394-10/99440668_2772753369518791_1059490844269463196_n.jpg?_nc_cat=109&ccb=1-7&_nc_sid=f10757&_nc_eui2=AeGJa8mcywLbdMmUT_xXNRKasFMeUda0SbWwUx5R1rRJtShmidq_hlmOPmlLV6sWVuL391zq8C_kdQqKO_q5uUTY&_nc_ohc=uKbav_oGOxcQ7kNvgH0gCuA&_nc_oc=AdjvqVF1eSmwbXvtHG9D_QQNT22mGHQ6EOLUxPeCHZjh2oN2aG6PbX4Us_8dxMW4nts&_nc_zt=23&_nc_ht=scontent-iad3-2.xx&edm=AB0BvfgEAAAA&_nc_gid=AsnkDQ5sYPtKVCkYg8yfCjo&oh=03_Q7cD1gF5ByEEGeIH1lAPBGNE760X1C3LoQqCdm9Jpr8f6lFbaw&oe=67A81108',
              },
            ],
            archivedAttachments: [
              {
                mime_type: 'video/mp4',
                name: 'video-**********.mp4',
                size: 1788532,
                url: 'https://dev-api.sharparchive.com/api/social/file/m_dIPCrcqcmYxHYSx5YaTy6jrzf3NteVI5Ksuetb__WMibJCw_A9EZEIfdWtDHoiGYTwQu5d67NjfRbhSi7qRe6w/video-**********.mp4?provider=Facebook&u=3&a=61&c=message_attachments',
                preview_url:
                  'https://dev-api.sharparchive.com/api/social/file/m_dIPCrcqcmYxHYSx5YaTy6jrzf3NteVI5Ksuetb__WMibJCw_A9EZEIfdWtDHoiGYTwQu5d67NjfRbhSi7qRe6w/99440668_2772753369518791_1059490844269463196_n.jpg?provider=Facebook&u=3&a=61&c=message_attachments',
              },
            ],
          },
          {
            id: 228,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_uo5koLIma7lfLxFSslTZzjrzf3NteVI5Ksuetb__WMi7-o1xjF3Wof7DXWBzZxfLCmmv3uAJQpmP2aih3NzxRA',
            threadId: 't_147567564486945',
            createdAt: '2022-06-19T08:23:59Z',
            fromUid: '103338412240799',
            toUids: ['5401705403195076'],
            text: '',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'sent',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: [
              {
                mime_type: 'audio/ogg',
                name: 'test_video_6.ogg',
                size: 574790,
                url: 'https://cdn.fbsbx.com/v/t59.3654-21/288597797_385759963619601_635520328879361647_n.ogg/test_video_6.ogg?_nc_cat=109&ccb=1-7&_nc_sid=d61c36&_nc_eui2=AeGE4qdir6rZ7WXk2lxu81Vn-my38mhTGwv6bLfyaFMbC5M9LMgv-KNIkQZPEpGJodxcfw7JSJkqycq4rLC8I6gh&_nc_ohc=gR9mOKH9jCAQ7kNvgFGUNJg&_nc_oc=AdhnFmP0KsmiVLqqf2sgS50CnmLTrNFC8pwhVcMh15pNg0jPIIbGhkIbVq54y4GcI8Q&_nc_zt=7&_nc_ht=cdn.fbsbx.com&edm=AB0BvfgEAAAA&_nc_gid=AsnkDQ5sYPtKVCkYg8yfCjo&oh=03_Q7cD1gE4W-UKql__FtVhMuydNpzp_nyIlM9WfOfGLdGg5dhcWw&oe=67A40131&dl=1',
              },
            ],
            archivedAttachments: [
              {
                mime_type: 'audio/ogg',
                name: 'test_video_6.ogg',
                size: 574790,
                url: 'https://dev-api.sharparchive.com/api/social/file/m_uo5koLIma7lfLxFSslTZzjrzf3NteVI5Ksuetb__WMi7-o1xjF3Wof7DXWBzZxfLCmmv3uAJQpmP2aih3NzxRA/test_video_6.ogg?provider=Facebook&u=3&a=61&c=message_attachments',
              },
            ],
          },
          {
            id: 227,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_Ujwliv7NH_CEd4VlP0SMgzrzf3NteVI5Ksuetb__WMgMlOJXBA_LdEtzblbWOC-6hwkXN5KA7IzSmtx4wnGMKw',
            threadId: 't_147567564486945',
            createdAt: '2022-06-19T08:22:38Z',
            fromUid: '103338412240799',
            toUids: ['5401705403195076'],
            text: 'video format avi',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'sent',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: '',
            archivedAttachments: '',
          },
          {
            id: 226,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_pz9ZeIFiR0LkQ71bL2zHTjrzf3NteVI5Ksuetb__WMjLp3B3jEZkcRXZhezwtcmaPDFEfG5UH8hXs0S5ee79Sw',
            threadId: 't_147567564486945',
            createdAt: '2022-06-19T08:22:33Z',
            fromUid: '103338412240799',
            toUids: ['5401705403195076'],
            text: '',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'sent',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: [
              {
                mime_type: 'video/mp4',
                name: 'video-**********.mp4',
                size: 1788532,
                url: 'https://video-iad3-2.xx.fbcdn.net/o1/v/t2/f2/m272/AQP5xzMSC-KEaeAyIfN2WgKprk5phxjX0mozNwE8gh3G7kYAdy7br4Bsu2N5WkF4Zdny0LmT22ybFYCfkuNwJToIuc7GvAaX0B-S9K2pSF_OajQatbV97UgBh8LwRA.mp4?_nc_ht=video-iad3-2.xx.fbcdn.net&_nc_cat=109&_nc_eui2=AeFK_JFOrRV-UeNYVRjCAqee7bZKYFclqUvttkpgVyWpS0eWLfFAnS4OdkSWhivjh2Y7p_-Q2nxtVjn2qxZaBMJZ&_nc_oc=AdgbODUxY8hvjqduY9OSwngb2iTQfD3e9PfraBsQbw3RIIWqyvcBKSPxZJtWEpmGa1c&strext=1&ccb=9-4&oh=00_AYCIxKsvOsJAaYzeJPjy7hfiUeqG8aEvhjizTs0XFoeUCw&oe=67A3F6DA&_nc_sid=060d78&dl=1',
                preview_url:
                  'https://scontent-iad3-2.xx.fbcdn.net/v/t15.3394-10/99440668_2772753369518791_1059490844269463196_n.jpg?_nc_cat=109&ccb=1-7&_nc_sid=f10757&_nc_eui2=AeGJa8mcywLbdMmUT_xXNRKasFMeUda0SbWwUx5R1rRJtShmidq_hlmOPmlLV6sWVuL391zq8C_kdQqKO_q5uUTY&_nc_ohc=uKbav_oGOxcQ7kNvgH0gCuA&_nc_oc=AdjvqVF1eSmwbXvtHG9D_QQNT22mGHQ6EOLUxPeCHZjh2oN2aG6PbX4Us_8dxMW4nts&_nc_zt=23&_nc_ht=scontent-iad3-2.xx&edm=AB0BvfgEAAAA&_nc_gid=AsnkDQ5sYPtKVCkYg8yfCjo&oh=03_Q7cD1gF5ByEEGeIH1lAPBGNE760X1C3LoQqCdm9Jpr8f6lFbaw&oe=67A81108',
              },
            ],
            archivedAttachments: [
              {
                mime_type: 'video/mp4',
                name: 'video-**********.mp4',
                size: 1788532,
                url: 'https://dev-api.sharparchive.com/api/social/file/m_pz9ZeIFiR0LkQ71bL2zHTjrzf3NteVI5Ksuetb__WMjLp3B3jEZkcRXZhezwtcmaPDFEfG5UH8hXs0S5ee79Sw/video-**********.mp4?provider=Facebook&u=3&a=61&c=message_attachments',
                preview_url:
                  'https://dev-api.sharparchive.com/api/social/file/m_pz9ZeIFiR0LkQ71bL2zHTjrzf3NteVI5Ksuetb__WMjLp3B3jEZkcRXZhezwtcmaPDFEfG5UH8hXs0S5ee79Sw/99440668_2772753369518791_1059490844269463196_n.jpg?provider=Facebook&u=3&a=61&c=message_attachments',
              },
            ],
          },
          {
            id: 225,
            chatId: 83,
            provider: 'Facebook',
            socialId:
              'm_eVzWcgccWeHS-Ck0iD09Xzrzf3NteVI5Ksuetb__WMgpFM3cgfKA1w_ZCSCHu36vF1fDGEqGj1Y9WEx0DzVu9g',
            threadId: 't_147567564486945',
            createdAt: '2022-06-19T08:19:54Z',
            fromUid: '103338412240799',
            toUids: ['5401705403195076'],
            text: 'video format mp4',
            tags: {
              data: [
                {
                  name: 'inbox',
                },
                {
                  name: 'read',
                },
                {
                  name: 'sent',
                },
                {
                  name: 'source:web',
                },
              ],
            },
            sticker: '',
            archivedSticker: '',
            attachments: '',
            archivedAttachments: '',
          },
        ],
      },
      tempAllMessages: null,
      singlePost: {
        id: 10,
        profilename: 'Sharparchive',
        provider: 'Facebook',
        socialId: '103338412240799_612690651356536',
        socialUid: '103338412240799',
        name: 'Learning',
        profileImageUrl: `/social/profile-picture.png`,
        createdAt: '2025-01-30T06:38:04Z',
        updatedAt: '2025-01-30T06:38:04Z',
        parentId: '',
        type: '',
        statusType: 'added_photos',
        placeName: '',
        placeCity: '',
        placeCountry: '',
        text: `🚀 Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries Elevate your business with Sharp Archive. Archiving solutions tailored for success in regulated industries`,
        link: '',
        linkName: '',
        caption: '',
        description: '',
        privacy: 'Public',
        sharesCount: 0,
        reactionsCount: 0,
        commentsCount: 3,
        isPublished: true,
        isExpired: false,
        isHidden: false,
        event: '',
        fullPicture:
          'https://scontent-iad3-1.xx.fbcdn.net/v/t39.30808-6/474760329_612690628023205_5311268127220583742_n.jpg?stp=dst-jpg_s720x720_tt6&_nc_cat=101&ccb=1-7&_nc_sid=127cfc&_nc_eui2=AeHLXjwLvBY3TUdTQSJBCMo9THHDGv2HfNNMccMa_Yd8091eKNCK-9nubIBuYJZHCGmhTE_ADFaxx_Afex0cnqzJ&_nc_ohc=Dn5x5eho-EYQ7kNvgHMkiLX&_nc_oc=AdgZzFI4IVRz3RIoZe2Uy9RDNWzBEF0sD7WImkCL2N1lzf3r_YWyfksVxAtnjBJ-zVc&_nc_zt=23&_nc_ht=scontent-iad3-1.xx&edm=AKIiGfEEAAAA&_nc_gid=ALElcB_KIUC4Fmthx3S8FLK&oh=00_AYDMvz15Sl3AbrW247v2qqCeV4DaSTjIWETbVS2u0NUYEg&oe=67A8C0EF',
        archivedFullPicture:
          'https://dev-api.sharparchive.com/api/social/media/103338412240799_612690651356536_1738219084.jpg?provider=Facebook&u=3&a=61&c=full_picture',
        sourceUrl: '',
        archivedSourceUrl: '',
        attachmentUrl:
          'https://www.facebook.com/photo.php?fbid=612690621356539&set=a.147570507868555&type=3',
        attachmentImages: [
          'https://scontent-iad3-1.xx.fbcdn.net/v/t39.30808-6/474760329_612690628023205_5311268127220583742_n.jpg?stp=dst-jpg_s720x720_tt6&_nc_cat=101&ccb=1-7&_nc_sid=127cfc&_nc_eui2=AeHLXjwLvBY3TUdTQSJBCMo9THHDGv2HfNNMccMa_Yd8091eKNCK-9nubIBuYJZHCGmhTE_ADFaxx_Afex0cnqzJ&_nc_ohc=Dn5x5eho-EYQ7kNvgHMkiLX&_nc_oc=AdgZzFI4IVRz3RIoZe2Uy9RDNWzBEF0sD7WImkCL2N1lzf3r_YWyfksVxAtnjBJ-zVc&_nc_zt=23&_nc_ht=scontent-iad3-1.xx&edm=AKIiGfEEAAAA&_nc_gid=ALElcB_KIUC4Fmthx3S8FLK&oh=00_AYDMvz15Sl3AbrW247v2qqCeV4DaSTjIWETbVS2u0NUYEg&oe=67A8C0EF',
        ],
        archivedAttachmentImages: [
          'https://dev-api.sharparchive.com/api/social/media/103338412240799_612690651356536_1738219084_0.jpg?provider=Facebook&u=3&a=61&c=attachment_images',
        ],
        facebookUrl:
          'https://www.facebook.com/***************/posts/612690651356536',
      },
      selectedPostComments: [
        {
          id: 123,
          provider: 'Facebook',
          socialId: '194878093137796_760135251823803',
          socialUid: '103338412240799',
          name: 'Brandon Vargas',
          adminCreator: '',
          profileImageUrl: '/social/default-profile.png',
          createdAt: '2023-02-14T08:34:44Z',
          parentId: '103338412240799_194878093137796',
          text: `<span class="text-[#333333] font-semibold"> Sharparchive</span> might be
        helpful for you`,
          messageTags: '',
          type: '',
          title: '',
          reactionsCount: 0,
          likesCount: 0,
          commentsCount: 1,
          canLike: true,
          canComment: false,
          canHide: false,
          canRemove: true,
          isHidden: false,
          isPrivate: false,
          userLikes: false,
          sourceUrl: '',
          archivedSourceUrl: '',
          attachmentUrl: '',
          facebookUrl:
            'https://www.facebook.com/***************/videos/****************?comment_id=760135251823803',
          comments: [
            {
              id: 124,
              provider: 'Facebook',
              socialId: '194878093137796_864783738148563',
              socialUid: '103338412240799',
              name: 'Sharparchive',
              adminCreator: '',
              profileImageUrl: '/social/profile-picture.png',
              createdAt: '2023-02-14T08:35:47Z',
              parentId: '194878093137796_760135251823803',
              text: 'Brandon Vargas Reply comments',
              messageTags: '',
              type: '',
              title: '',
              reactionsCount: 0,
              likesCount: 0,
              commentsCount: 0,
              canLike: true,
              canComment: false,
              canHide: false,
              canRemove: true,
              isHidden: false,
              isPrivate: false,
              userLikes: false,
              sourceUrl: '',
              archivedSourceUrl: '',
              attachmentUrl: '',
              facebookUrl:
                'https://www.facebook.com/***************/videos/****************?comment_id=760135251823803&reply_comment_id=864783738148563',
              showAlert: true,
            },
          ],
          selected: false,
        },
        {
          id: 122,
          provider: 'Facebook',
          socialId: '194878093137796_760135251823803',
          socialUid: '103338412240799',
          name: 'Brandon Vargas',
          adminCreator: '',
          profileImageUrl: '/social/default-profile.png',
          createdAt: '2023-02-14T08:34:44Z',
          parentId: '103338412240799_194878093137796',
          text: 'How do you approach marketing strategy?',
          messageTags: '',
          type: '',
          title: '',
          reactionsCount: 0,
          likesCount: 0,
          commentsCount: 1,
          canLike: true,
          canComment: false,
          canHide: false,
          canRemove: true,
          isHidden: false,
          isPrivate: false,
          userLikes: false,
          sourceUrl: '',
          archivedSourceUrl: '',
          attachmentUrl: '',
          facebookUrl:
            'https://www.facebook.com/***************/videos/****************?comment_id=760135251823803',
          comments: [
            {
              id: 123,
              provider: 'Facebook',
              socialId: '194878093137796_864783738148563',
              socialUid: '103338412240799',
              name: 'Sharparchive',
              adminCreator: '',
              profileImageUrl: '/social/profile-picture.png',
              createdAt: '2023-02-14T08:35:47Z',
              parentId: '194878093137796_760135251823803',
              text: 'Brandon Vargas Reply comments',
              messageTags: '',
              type: '',
              title: '',
              reactionsCount: 0,
              likesCount: 0,
              commentsCount: 0,
              canLike: true,
              canComment: false,
              canHide: false,
              canRemove: true,
              isHidden: false,
              isPrivate: false,
              userLikes: false,
              sourceUrl: '',
              archivedSourceUrl: '',
              attachmentUrl: '',
              facebookUrl:
                'https://www.facebook.com/***************/videos/****************?comment_id=760135251823803&reply_comment_id=864783738148563',
            },
          ],
          selected: false,
        },
        {
          id: 121,
          provider: 'Facebook',
          socialId: '194878093137796_***************',
          socialUid: '6179962028721208',
          name: 'Abdur Rahim',
          adminCreator: '',
          profileImageUrl:
            'https://dev-api.sharparchive.com/api/social/media/6179962028721208.jpg?provider=Facebook',
          createdAt: '2023-03-20T06:47:19Z',
          parentId: '103338412240799_194878093137796',
          text: "That's awesome 👏",
          messageTags: '',
          type: '',
          title: '',
          reactionsCount: 0,
          likesCount: 0,
          commentsCount: 0,
          canLike: true,
          canComment: false,
          canHide: true,
          canRemove: true,
          isHidden: false,
          isPrivate: false,
          userLikes: false,
          sourceUrl: '',
          archivedSourceUrl: '',
          attachmentUrl: '',
          facebookUrl:
            'https://www.facebook.com/***************/videos/****************?comment_id=***************',
          selected: false,
        },
      ],
      conversationOwner: null,
      conversationUser: null,
      specificId: null,
      showSinglePost: false,
      accountType: 'socials',
      emailMessages: [
        {
          id: 1,
          subject: 'Game On Pro',
          snippet: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">"Level Up: Must-Play Games of the Month"</span> - Dear Larry, explore these exciting games to take your gaming experience to</p>`,
          description: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">"Level Up: Must-Play Games of the Month"</span> - Dear Larry, explore these exciting games to take your gaming experience to</p>`,
          createdAt: '10:45PM',
          read: false,
          checked: false,
        },
        {
          id: 2,
          subject: 'Travel Visionary',
          snippet: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Plan Your Dream Getaway ✈️</span> - Here’s how to turn your travel dreams into reality.</p>`,
          description: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Plan Your Dream Getaway ✈️</span> - Here’s how to turn your travel dreams into reality.</p>`,
          createdAt: '8:15PM',
          read: false,
          checked: false,
        },
        {
          id: 3,
          subject: 'Foodie Adventures',
          snippet: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Delicious Recipes to Try Tonight 🍴</span> - Treat yourself to these mouth-watering dishes!</p>`,
          description: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Delicious Recipes to Try Tonight 🍴</span> - Treat yourself to these mouth-watering dishes!</p>`,
          createdAt: '7:30PM',
          read: true,
          checked: false,
        },
        {
          id: 4,
          subject: 'Book HavenX',
          snippet: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">5 Books That Will Change Your Perspective</span> - These books will inspire you and open your mind to new ideas.</p>`,
          description: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">5 Books That Will Change Your Perspective</span> - These books will inspire you and open your mind to new ideas.</p>`,
          createdAt: '6:30PM',
          read: true,
          checked: false,
        },
        {
          id: 5,
          subject: 'Photo Enthusiast',
          snippet: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Capture the Moment: Photography Tips</span> - Enhance your skills and take stunning shots with these techniques.</p>`,
          description: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Capture the Moment: Photography Tips</span> - Enhance your skills and take stunning shots with these techniques.</p>`,
          createdAt: '5:15PM',
          read: true,
          checked: false,
        },
        {
          id: 6,
          subject: 'Success Navigator',
          snippet: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Navigate Your Way to Success</span> - Map out a plan that ensures you achieve your goals.</p>`,
          description: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Navigate Your Way to Success</span> - Map out a plan that ensures you achieve your goals.</p>`,
          createdAt: '3:30PM',
          read: true,
          checked: false,
        },
        {
          id: 7,
          subject: 'InspiredJourneyHQ',
          snippet: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Inspiration Awaits: Take the First Step</span> - Hello Larry, everything great starts with one small step—make yours today.</p>`,
          description: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Inspiration Awaits: Take the First Step</span> - Hello Larry, everything great starts with one small step—make yours today.</p>`,
          createdAt: '1:00PM',
          read: true,
          checked: false,
        },
        {
          id: 8,
          subject: 'Global Forum',
          snippet: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">2024 Global Forum: Registration Open Now</span> - Don’t miss out on a chance to engage with world leaders and innovators.</p>`,
          description: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">2024 Global Forum: Registration Open Now</span> - Don’t miss out on a chance to engage with world leaders and innovators.</p>`,
          createdAt: '11:45AM',
          read: true,
          checked: false,
        },
        {
          id: 9,
          subject: 'Workshop Wizard',
          snippet: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Hands-On Workshop: Learn from the Best</span> - Gain practical insights and skills at our interactive session.</p>`,
          description: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Hands-On Workshop: Learn from the Best</span> - Gain practical insights and skills at our interactive session.</p>`,
          createdAt: '10:21AM',
          read: true,
          checked: false,
        },
        {
          id: 10,
          subject: 'Launch Party Pro',
          snippet: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">You're Invited to Our Product Launch 🎉</span> - Hello Larry, its a good news for you. be among the first to witness our newest</p>`,
          description: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">You're Invited to Our Product Launch 🎉</span> - Hello Larry, its a good news for you. be among the first to witness our newest</p>`,
          createdAt: '8:25AM',
          read: true,
          checked: false,
        },
        {
          id: 11,
          subject: 'Tech Summit',
          snippet: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Tech Innovations Conference 2024: Register Now</span> - Join industry leaders for groundbreaking discussions and networking.</p>`,
          description: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Tech Innovations Conference 2024: Register Now</span> - Join industry leaders for groundbreaking discussions and networking.</p>`,
          createdAt: '3:14AM',
          read: true,
          checked: false,
        },
        {
          id: 12,
          subject: 'Shop Smart Now',
          snippet: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Shop Smarter, Save More 2024: Register Now</span> - Here’s how you can get the most value out of your purchases.</p>`,
          description: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Shop Smarter, Save More 2024: Register Now</span> - Here’s how you can get the most value out of your purchases.</p>`,
          createdAt: 'Nov 22',
          read: true,
          checked: false,
        },
        {
          id: 13,
          subject: 'Exclusive Deals',
          snippet: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">An Exclusive Offer for Our Valued Partners</span> - As a token of appreciation, we’re offering you first access to this deal.</p>`,
          description: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">An Exclusive Offer for Our Valued Partners</span> - As a token of appreciation, we’re offering you first access to this deal.</p>`,
          createdAt: 'Nov 22',
          read: true,
          checked: false,
        },
        {
          id: 14,
          subject: 'Meeting Master',
          snippet: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Meeting Follow-Up: Key Takeaways</span> - Thank you for your time earlier—here are the highlights of our discussion.</p>`,
          description: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Meeting Follow-Up: Key Takeaways</span> - Thank you for your time earlier—here are the highlights of our discussion.</p>`,
          createdAt: 'Nov 22',
          read: true,
          checked: false,
        },
        {
          id: 15,
          subject: 'Time Optimizer',
          snippet: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Maximize Your Productivity Today</span> - Here are a few simple yet effective strategies to optimize your workflow.</p>`,
          description: `<p class="text-[#707070]"><span class="text-[#525252] font-bold">Maximize Your Productivity Today</span> - Here are a few simple yet effective strategies to optimize your workflow.</p>`,
          createdAt: 'Nov 21',
          read: true,
          checked: false,
        },
      ],
      selectedEmailMessage: null,
    }
  },

  getters: {
    conversations(state) {
      const userId = state.conversationUser && state.conversationUser.uid
      if (state.tempAllMessages) {
        return state.tempAllMessages.messages.reduce(
          (p, c) => {
            if (typeof p.fromUid === 'undefined' || p.fromUid !== c.fromUid) {
              p.fromUid = c.fromUid
              // p.groups.push([])
              p.groups.push({
                user: c.fromUid === userId,
                date: c.createdAt,
                messages: [],
              })
            }
            // p.groups[p.groups.length - 1].push(c);
            p.groups[p.groups.length - 1].messages.push(c)
            return p
          },
          { groups: [] },
        ).groups
      } else {
        return []
      }
    },
  },

  mutations: {
    SET_MANAGER_ONBOARDING(state, payload) {
      state.showManagerOnboarding = payload
    },
    SET_PROFILE_CONTENT(state, payload) {
      state.accountItem = payload
      console.log(state.accountItem, 'SET_PROFILE_CONTENT')
    },
    SET_ACCOUNT_ITEMS_TOGGLE_VALUE(state, payload) {
      state.accountItems.forEach((accountItem) => {
        if (accountItem.id === payload) {
          accountItem.toggleSelect = !accountItem.toggleSelect
        }
      })
    },
    CHECK_ACCOUNT_TYPE_BEFORE_LUNCH(state, payload) {
      const hasSocialsSelected = state.accountItems.some(
        (item) => item.type === 'socials' && item.toggleSelect,
      )

      if (!hasSocialsSelected) {
        const firstSelectedItem = state.accountItems.find(
          (item) => item.toggleSelect,
        )
        if (firstSelectedItem) {
          this.commit('social/SET_ONLY_ACCOUNT_TYPE', firstSelectedItem.type)
        }
      }
      const hasTextSelected = state.accountItems.some(
        (item) => item.type === 'text' && item.toggleSelect,
      )
      if (!hasTextSelected) {
        const firstSelectedItem = state.accountItems.find(
          (item) => item.toggleSelect,
        )
        if (firstSelectedItem) {
          this.commit('social/SET_ONLY_ACCOUNT_TYPE', firstSelectedItem.type)
        }
      }
      const hasEmailsSelected = state.accountItems.some(
        (item) => item.type === 'text' && item.toggleSelect,
      )
      if (!hasEmailsSelected) {
        const firstSelectedItem = state.accountItems.find(
          (item) => item.toggleSelect,
        )
        if (firstSelectedItem) {
          this.commit('social/SET_ONLY_ACCOUNT_TYPE', firstSelectedItem.type)
        }
      }
    },
    SET_ONLY_ACCOUNT_TYPE(state, paylaod) {
      state.accountType = paylaod
    },
    SET_ACCOUNT_TYPE(state, paylaod) {
      state.accountType = paylaod
      this.commit('social/SET_SIDEBAR_ACCOUNT_ITEMS_AT_LAUNCH')
    },
    SET_SIDEBAR_ACCOUNT_ITEMS_AT_LAUNCH(state, payload) {
      state.sideBarAccountItems = []
      state.accountItems.forEach((accountItem) => {
        if (accountItem.type === state.accountType) {
          if (accountItem.toggleSelect) {
            accountItem.select = false
            state.sideBarAccountItems.push(accountItem)
          }
        }
      })
      if (state.sideBarAccountItems.length > 0) {
        state.sideBarAccountItems[0].select = true

        this.commit('social/SET_PROFILE_CONTENT', state.sideBarAccountItems[0])
      }
      console.log(
        state.sideBarAccountItems,
        'SET_SIDEBAR_ACCOUNT_ITEMS_AT_LAUNCH',
      )
    },
    UPADTE_SIDEBAR_SELECT_VALUE(state, payload) {
      state.sideBarAccountItems = payload
    },
    SET_SIDEBAR_ACCOUNT_ITEMS(state, payload) {
      state.finalSideBarAccountItems = payload
    },
    SET_SOCIAL_ACCOUNT_ITEMS(state, payload) {
      state.accountItems = payload
      state.accountItems.forEach((item) => {
        item.count = 0
        item.toggleSelect = true
        item.select = false
        if (item.provider === 'Microsoft' || item.provider === 'Google') {
          item.type = 'emails'
        } else if (item.provider === 'RingCentral') {
          item.type = 'text'
        } else {
          item.type = 'socials'
        }
      })
    },
    SET_NEW_ACCOUNT_ITEM(state, payload) {
      state.accountItems.push(payload)
    },
    SET_MESSAGES_NOTIFICATION_ITEMS(state, payload) {
      if (payload === 'messages') {
        state.showAllMessages = false
      }
      const selectedItems = state.notificatioItems.items.filter(
        (item) => item.type === payload,
      )
      state.selectedNotificationItems = JSON.parse(
        JSON.stringify(state.notificatioItems),
      )
      state.selectedNotificationItems.items = selectedItems
    },
    SET_SHOW_ALL_MESSAGES(state, payload) {
      state.showAllMessages = payload
    },
    CHANGE_PERSON_LIST_SELECT_VALUE(state, payload) {
      state.personLists.items.forEach((item) => {
        if (item.id === payload) {
          item.selected = true
        } else {
          item.selected = false
        }
      })
    },
    SET_SINGLE_CONVERTIONS(state, payload) {
      state.tempAllMessages = JSON.parse(JSON.stringify(state.allMessages))
      // state.allMessages = JSON.parse(JSON.stringify(state.tempAllMessages))
      if (state.tempAllMessages) {
        state.tempAllMessages.messages =
          state.tempAllMessages.messages.reverse()
        state.conversationOwner = state.allMessages.participants[1]
        state.conversationUser = state.allMessages.participants[0]
      }
    },
    SET_NEW_SINGLE_CONVERTIONS(state, payload) {
      const tempMessage = JSON.parse(
        JSON.stringify(state.allMessages.messages[0]),
      )
      tempMessage.id = state.allMessages.messages[0].id + 1
      tempMessage.fromUid = '103338412240799'
      tempMessage.toUids = '5401705403195076'
      tempMessage.text = payload.plainText
      tempMessage.attachments = []
      tempMessage.archivedAttachments = []
      if (
        payload.multipleAvatarPreview &&
        payload.multipleAvatarPreview.length > 0
      ) {
        payload.multipleAvatarPreview.forEach((item, index) => {
          tempMessage.attachments.push({
            mime_type: 'image/png',
            name: `fileName_${index}`,
            size: 12345,
            url: item,
            preview_url: item,
          })
        })
        tempMessage.archivedAttachments = tempMessage.attachments
      }
      state.allMessages.messages.unshift(tempMessage)
      state.tempAllMessages = JSON.parse(JSON.stringify(state.allMessages))
      // state.allMessages = JSON.parse(JSON.stringify(state.tempAllMessages))
      if (state.tempAllMessages) {
        state.tempAllMessages.messages =
          state.tempAllMessages.messages.reverse()
        state.conversationOwner = state.allMessages.participants[1]
        state.conversationUser = state.allMessages.participants[0]
      }
    },
    SET_SPECIFIC_ID(state, payload) {
      state.specificId = payload
    },
    RESET_STATES(state, payload) {
      state.conversationOwner = null
      state.conversationUser = null
      state.specificId = null
      state.showSinglePost = false
    },
    SET_SHOW_SINGLE_POST(state, payload) {
      state.showSinglePost = payload
    },
    SET_SINGLE_POST(state, payload) {
      state.singlePost.mentionText = payload.mentionText
      state.singlePost.profileImageUrl = payload.profileImageUrl
      state.singlePost.profilename = payload.profilename
      state.singlePost.text = payload.text
    },
    SET_SELECTED_COMMENT_FOR_REPLY(state, payload) {
      state.selectedPostComments.forEach((comment) => {
        if (comment.id === payload) {
          comment.selected = true
        } else {
          comment.selected = false
        }
      })
    },
    SET_SELECTED_COMMENT_FOR_REPLY_FALSE(state, payload) {
      state.selectedPostComments.forEach((comment) => {
        comment.selected = false
      })
    },
  },

  actions: {},
}

export default socialStore
