const createPostStore = {
  namespaced: true,
  state() {
    return {
      currentTab: 'Text',
      socialPreviewComp: '',
      isOpenPublishPostModal: false,
      runningPostSettings: null,
      singlePostSettings: null,
      showSavePostSettingsModal: false,
      savedPostSettings: [
        {
          id: 1,
          name: 'Sharparchive Promo',
          postType: 'Text',
          autoImage: false,
          autoHashtags: false,
          autoUsernames: false,
          autoShortenLinks: false,
          autoGenerateContent: true,
          wordCountRange: '100 - 250',
          contentStyle: 'Formal',
          autoContent: 'Trending',
          prompt: 'This is Prompt 1',
          postToAccounts: [
            {
              id: 1,
              profilePic: `/social/profile-picture.png`,
              provider: 'Facebook',
              name: '<PERSON>archi<PERSON>',
              username: '@official.sharparchive',
            },
            {
              id: 2,
              profilePic: `/social/profile-picture.png`,
              provider: 'Instagram',
              name: '<PERSON><PERSON><PERSON><PERSON>',
              username: '@sharparchive',
            },
            {
              id: 3,
              profilePic: `/social/profile-picture.png`,
              provider: 'Twitter',
              name: '<PERSON>archi<PERSON>',
              username: '@sharp_archive',
            },
          ],
        },
        {
          id: 2,
          name: `<PERSON>'s Socials`,
          postType: 'Text',
          autoImage: true,
          autoHashtags: false,
          autoUsernames: true,
          autoShortenLinks: true,
          autoGenerateContent: true,
          wordCountRange: '250 - 350',
          contentStyle: 'Formal',
          autoContent: 'Trending',
          prompt: 'This is Prompt 2',
          postToAccounts: [
            {
              id: 3,
              profilePic: `/social/profile-picture.png`,
              provider: 'Twitter',
              name: 'Sharparchive',
              username: '@sharp_archive',
            },
          ],
        },
        {
          id: 3,
          name: 'Only to Facebook',
          postType: 'Text',
          autoImage: true,
          autoHashtags: true,
          autoUsernames: true,
          autoShortenLinks: true,
          autoGenerateContent: false,
          wordCountRange: '350 - 500',
          contentStyle: 'Formal',
          autoContent: 'Trending',
          prompt: 'This is Prompt 3',
          postToAccounts: [
            {
              id: 4,
              profilePic: `/social/profile-picture.png`,
              provider: 'Facebook',
              name: 'Sharparchive',
              username: '@sharparchive1',
            },
          ],
        },
        {
          id: 4,
          name: `Bob's Accounts`,
          postType: 'Text',
          autoImage: true,
          autoHashtags: true,
          autoUsernames: true,
          autoShortenLinks: true,
          autoGenerateContent: true,
          wordCountRange: '500 - 1000',
          contentStyle: 'Formal',
          autoContent: 'Trending',
          prompt: 'This is Prompt 4',
          postToAccounts: [
            {
              id: 5,
              profilePic: `/social/chad-profile.png`,
              provider: 'Facebook',
              name: 'Chad Gordon',
              username: '@sharparchive2',
            },
          ],
        },
        {
          id: 5,
          name: 'Account 5',
          postType: 'Text',
          autoImage: true,
          autoHashtags: true,
          autoUsernames: true,
          autoShortenLinks: true,
          autoGenerateContent: true,
          wordCountRange: '100 - 250',
          contentStyle: 'Formal',
          autoContent: 'Trending',
          prompt: 'This is Prompt 5',
          postToAccounts: [],
        },
      ],
      generatedContent: null,
      previewContent: null,
      capturedVideo: null,
      isCaptureVideoComp: true,
      teleprompter: {
        isOpen: false,
        text: '',
      },
      selectedImages: [],
      editorSavedImages: [],
      objectUrls: [],
      imageEditor: {
        isOpen: false,
        image: null,
      },
    }
  },

  getters: {},

  mutations: {
    SET_CURRENT_TAB(state, payload) {
      state.currentTab = payload
    },
    SET_SOCIAL_PREVIEW_COMP(state, payload) {
      state.socialPreviewComp = payload
    },
    SET_IS_OPEN_PUBLISH_POST_MODAL(state, payload) {
      state.isOpenPublishPostModal = payload
    },
    SET_SHOW_SAVE_POST_SETTINGS_MODAL(state, payload) {
      state.showSavePostSettingsModal = payload
    },
    SET_SINGLE_POST_SETTINGS(state, payload) {
      state.singlePostSettings = payload
    },
    SET_RUNNING_POST_SETTINGS(state, payload) {
      state.runningPostSettings = payload
    },
    UPDATE_RUNNING_POST_SETTINGS(state, payload) {
      if (!state.runningPostSettings) {
        state.runningPostSettings = payload
      } else {
        Object.keys(payload).forEach((key) => {
          state.runningPostSettings[key] = payload[key]
        })
      }
    },
    ADD_NEW_POST_SETTINGS(state, payload) {
      state.savedPostSettings.push(payload)
    },
    SET_SAVED_POST_SETTINGS(state, payload) {
      state.savedPostSettings = payload
    },
    SET_GENERATED_CONTENT(state, payload) {
      state.generatedContent = payload
    },
    UPDATE_GENERATED_CONTENT(state, payload) {
      if (!state.generatedContent) {
        state.generatedContent = payload
      } else {
        Object.keys(payload).forEach((key) => {
          state.generatedContent[key] = payload[key]
        })
      }
    },
    SET_PREVIEW_CONTENT(state, payload) {
      state.previewContent = payload
    },
    UPDATE_PREVIEW_CONTENT(state, payload) {
      if (!state.previewContent) {
        state.previewContent = payload
      } else {
        Object.keys(payload).forEach((key) => {
          state.previewContent[key] = payload[key]
        })
      }
    },
    SET_CAPTURED_VIDEO(state, payload) {
      state.capturedVideo = payload
    },
    SET_IS_CAPTURE_VIDEO_COMP(state, payload) {
      state.isCaptureVideoComp = payload
    },
    SET_TELEPROMPTER_IS_OPEN(state, payload) {
      state.teleprompter.isOpen = payload
    },
    SET_TELEPROMPTER_TEXT(state, payload) {
      state.teleprompter.text = payload
    },
    SET_OBJECT_URLS(state, payload) {
      if (Array.isArray(payload)) {
        state.objectUrls.push(...payload)
      } else {
        state.objectUrls.push(payload)
      }
    },
    REVOKE_OBJECT_URLS(state) {
      state.objectUrls.forEach((url) => URL.revokeObjectURL(url))
      state.objectUrls = []
    },
    SET_IMAGE_EDITOR(state, payload) {
      Object.keys(payload).forEach((key) => {
        state.imageEditor[key] = payload[key]
      })
    },
    SET_SELECTED_IMAGES(state, payload) {
      state.selectedImages = payload
    },
    ADD_OR_UPDATE_SELECTED_IMAGE(state, payload) {
      const index = state.selectedImages.findIndex(
        (img) => img.id === payload.id,
      )
      if (index !== -1) {
        state.selectedImages[index] = payload
      } else {
        state.selectedImages.push(payload)
      }
    },
    REMOVE_SELECTED_IMAGE(state, id) {
      const index = state.selectedImages.findIndex((img) => img.id === id)
      if (index !== -1) {
        state.selectedImages.splice(index, 1)
      }
    },
    DELETE_EDITOR_IMAGE(state, id) {
      const index = state.selectedImages.findIndex((img) => img.id === id)
      if (index !== -1) {
        state.selectedImages.splice(index, 1)
      }

      if (state.selectedImages.length > 0) {
        state.imageEditor = {
          isOpen: true,
          index: 0,
          image: state.selectedImages[0],
        }
      } else {
        state.imageEditor = {
          isOpen: false,
          index: null,
          image: null,
        }
      }
    },
    ADD_EDITOR_SAVED_IMAGE(state, payload) {
      state.editorSavedImages.push(payload)
    },
    ADD_OR_UPDATE_EDITOR_SAVED_IMAGE(state, payload) {
      const index = state.editorSavedImages.findIndex(
        (img) => img.id === payload.id,
      )
      if (index !== -1) {
        state.editorSavedImages[index] = payload
      } else {
        state.editorSavedImages.push(payload)
      }
    },
  },

  actions: {
    getSinglePostSetting({ commit, state }, id) {
      const post =
        state.savedPostSettings.find((post) => post.id === id) || null
      commit('SET_SINGLE_POST_SETTINGS', post)
    },
    addNewPostSettings({ commit, state }, settings) {
      const newId = state.savedPostSettings.length
        ? Math.max(...state.savedPostSettings.map((post) => post.id)) + 101
        : 1
      commit('ADD_NEW_POST_SETTINGS', { ...settings, id: newId })
    },
    updatePostSettings({ commit, state }, settings) {
      const updatedSettings = [...state.savedPostSettings]
      const settingIndex = updatedSettings.findIndex(
        (post) => post.id === settings.id,
      )
      if (settingIndex !== -1) {
        updatedSettings[settingIndex] = {
          ...updatedSettings[settingIndex],
          ...settings,
        }
        commit('SET_SAVED_POST_SETTINGS', updatedSettings)
        commit('SET_SINGLE_POST_SETTINGS', settings)
      }
    },
    deletePostSettings({ commit, state }, id) {
      const updatedSettings = state.savedPostSettings.filter(
        (setting) => setting.id !== id,
      )
      commit('SET_SAVED_POST_SETTINGS', updatedSettings)
    },
    getGeneratedContent({ commit, state }, settings) {
      // Base content that would be generated
      const baseContent = {
        text: 'Bananas are among the most important food crops on the planet. They come from a family of plants called Musa that are native to Southeast Asia and grown in many of the warmer areas of the world. Bananas are a healthy source of fiber, potassium, vitamin B6, vitamin C, and various antioxidants and phytonutrients.',
        images: [
          {
            id: 1,
            url: 'https://images.unsplash.com/photo-1589468619550-3c6350b50ce0?w=500',
          },
          {
            id: 2,
            url: 'https://images.unsplash.com/photo-1529737902881-35c2748ca59a?w=500',
          },
          {
            id: 3,
            url: 'https://images.unsplash.com/photo-**********-de5eba77d644?w=500',
          },
          {
            id: 4,
            url: 'https://images.unsplash.com/photo-1632507126394-dacbcad49054?w=500',
          },
        ],
        hashtags: [
          {
            id: 1,
            name: '#banana',
          },
          {
            id: 2,
            name: '#bananatree',
          },
          {
            id: 3,
            name: '#bananapictures',
          },
          {
            id: 4,
            name: '#ripebanana',
          },
        ],
        tags: [
          {
            id: 1,
            username: '@Lauren',
            name: 'Lauren',
          },
          {
            id: 2,
            username: '@mavik.lee',
            name: 'Mavik Lee',
          },
          {
            id: 3,
            username: '@john.carter',
            name: 'John Carter',
          },
          {
            id: 4,
            username: '@piter.rabit',
            name: 'Piter Rabit',
          },
        ],
      }
      // Create a filtered content object based on settings
      const generatedContent = {
        text: baseContent.text,
        images: settings.autoImage ? baseContent.images : [],
        hashtags: settings.autoHashtags ? baseContent.hashtags : [],
        tags: settings.autoUsernames ? baseContent.tags : [],
      }
      // Commit the generated content to the state
      commit('SET_GENERATED_CONTENT', generatedContent)
      commit('UPDATE_PREVIEW_CONTENT', {
        text: generatedContent.text,
        // images: [],
        hashtags: [],
        tags: [],
      })
    },
    seeMoreImage({ commit, state }) {
      if (!state.generatedContent || !state.generatedContent.images) return

      const images = [...state.generatedContent.images]
      const additionalImages = [...images.slice(0, 4), ...images.slice(0, 4)]
      const newImages = [...images, ...additionalImages]

      commit('UPDATE_GENERATED_CONTENT', { images: newImages })
    },
  },
}

export default createPostStore
