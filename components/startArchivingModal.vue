<template>
  <div
    class="close_account top-15 fixed inset-0 h-screen flex items-center justify-center transition-all duration-500 delay-500"
    :class="showHomeContent ? 'opacity-0' : ''"
  >
    <div
      v-if="!showPasswordConfirmModal"
      class="w-100-minus z-9999 md:h-48 h-32 bg-dark-100 shadow-md rounded-2xl flex flex-col items-center justify-center"
    >
      <p class="md:text-xl text-lg text-white">Add feeds to start archiving</p>
      <div
        class="w-full md:mt-8 mt-3 flex flex-row items-center md:justify-center justify-between md:space-x-5 space-x-3 px-2"
      >
        <!-- <a :href="closeAccountInfo" download> -->
        <button
          :disabled="downloadProcess"
          type="submit"
          class="md:w-44 w-1/2 py-2 text-yellow-primary rounded-full border border-yellow-primary outline-none font-bold md:text-base text-sm"
          :style="{
            color: globalColorPanel.backgroundColor,
            borderColor: globalColorPanel.backgroundColor,
          }"
          @click="closeAccount"
        >
          Close Account
        </button>
        <!-- </a> -->
        <button
          type="submit"
          class="md:w-44 w-1/2 p-2 text-white bg-yellow-primary rounded-full border border-yellow-primary outline-none font-bold md:text-base text-sm"
          :style="{
            backgroundColor: globalColorPanel.backgroundColor,
            borderColor: globalColorPanel.backgroundColor,
          }"
          @click="addFeed()"
        >
          Add Feeds
        </button>
      </div>
    </div>
    <alert-confirm-modal
      title="Are you sure you want to close your account?"
      message="This action cannot be undone, you will be unable to recover your account, and your archive will be deleted from our system."
      confirm-btn-text="Close Account"
      cancel-btn-text="Exit"
      top-exit-btn-color="text-yellow-midlight"
      :processing="downloadProcess"
      :show="deleteAlert"
      @cancel="deleteCancel"
      @delete="deleteConfirm"
    ></alert-confirm-modal>

    <password-confirmation-modal
      v-if="showPasswordConfirmModal"
      @isValidPassword="isValidPassword"
      @close="closePasswordConfirmModal"
    />
  </div>
</template>


<script setup lang="ts">
import AlertConfirmModal from './AlertConfirmModal.vue'
import { USER_PROFILE } from '~/constants/urls'
import PasswordConfirmationModal from '~/components/PasswordConfirmationModal.vue'
import { useFetched } from '~/composables/useFetched'
import { useAuth } from '~/composables/useAuth'
import { useStore } from 'vuex'
import { useNuxtApp } from '#app'

const store = useStore()

const nuxtApp = useNuxtApp()
const router = useRouter()
const { fetch } = useFetched()
const { logout, resetAllValue } = useAuth()

const emit = defineEmits<{
  (event: 'addFeed', value: boolean): void
}>()

// reactive state
const closeAccountInfo = ref<string>('')
const downloadProcess = ref<boolean>(false)
const deleteAlert = ref<boolean>(false)
const showPasswordConfirmModal = ref<boolean>(false)
const showHomeContent = computed(
  () => store.state.loginAnimation.showHomeContent,
)
const globalColorPanel = computed(() => store.state.globalColorPanel)
const user = computed(() => store.state.auth.user)

const deleteCancel = () => {
  deleteAlert.value = false
  addFeed()
}
const deleteConfirm = async () => {
  downloadProcess.value = true
  try {
    const response = await fetch(USER_PROFILE, {
      method: 'DELETE',
    })
    nuxtApp.$toast('success', {
      message: 'Your account closed successfully!',
      className: 'toasted-bg-archive',
    })
    deleteAlert.value = false
    // console.log(response, 'response')
    // if (response.refund > 0) {
    //   this.$store.commit('socialFeed/SET_REFUND_AMOUNT', response)
    //   this.$store.commit('socialFeed/SHOW_REFUND_MODAL', true)
    // }
    // if (response.refund === 0 || this.user.isOwner === false) {
    profileLogout()
    // }
    downloadProcess.value = false
  } catch (err) {
    if (err.response.status === 411) {
      nuxtApp.$toast('success', {
        message: 'Your account closed successfully!',
        className: 'toasted-bg-archive',
      })
      deleteAlert.value = false
      store.commit('socialFeed/SET_REFUND_AMOUNT', err.response.refund)
      store.commit('socialFeed/SHOW_REFUND_MODAL', true)
    }
    downloadProcess.value = false
  }
}
const profileLogout = () => {
  nuxtApp.$toast('clear')
  store.commit('SET_LANDING_LOADER', false)
  router.push('/home')
  setTimeout(() => {
    show_home_content(true)
    resetAllValue()
    router.push('/')
    // this.logout().then((res) => {
    //   if (res) {

    store.commit('socialFeed/SET_REFUND_AMOUNT', {})
    store.commit('socialFeed/SHOW_REFUND_MODAL', false)
    store.commit('SET_LOCK_SCREEN', false)
    store.commit('header/BLUR_ACTIVE_FEED_MODAL', false)
    store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false)
    landing_content(true)
    expand_starter_modal(false)
    collapse_starter_modal(false)
    maximize_starter_modal(false)
    starter_account_maximized(false)
    updateSetupContent('SetupStarterButton')
    setTimeout(() => {
      home_wrapper(true)
    }, 300)

    setTimeout(() => {
      show_home(false)
      show_logo_text(true)
      home_menu_text(true)
      setTimeout(() => {
        all_side_menu(false)
        setTimeout(() => {
          home_side_menu(false)
          home_menu_text(false)
          setTimeout(() => {
            show_logo(false)
            setTimeout(() => {
              home_circle(true)
              sidebar_menu(false)
              sidebar_circle(false)
              width_decrese(true)
              setTimeout(() => {
                home_modal(false)
                slide_left(false)
                slide_right(true)
                login_circle(true)
                setTimeout(() => {
                  home_header(false)
                  slide_full_right(true)
                  home_circle(false)
                  home_sidebar(false)
                  setTimeout(() => {
                    after_logout(true)
                    setTimeout(() => {
                      text_loading(true)
                      setTimeout(() => {
                        successfully(false)
                        notsuccessfully(true)
                        setTimeout(() => {
                          setTimeout(() => {
                            login_button_transition(false)
                            submit_button_transition(false)
                            login_form_transition(false)
                            setTimeout(() => {
                              after_logout(false)
                              text_loading(false)
                              after_loading(false)
                              width_increase(false)
                              full_width(false)
                              header_text(false)
                              loading_text(false)
                              width_decrese(false)
                              slide_right(false)
                              slide_full_right(false)
                            }, 1000)
                          }, 300)
                        }, 500)
                      }, 1800)
                    }, 800)
                  }, 800)
                }, 800)
              }, 600)
            }, 500)
          }, 0)
        }, 0)
      }, 800)
    }, 200)
    // }
    // })
  }, 600)

  set_header_width(false)
  setIsSticky(false)
}
const closeAccount = () => {
  showPasswordConfirmModal.value = true
  store.commit('confirm/SET_FROM_CLOSE_ACCOUNT', true)
}
const addFeed = () => {
  emit('addFeed')
}
const closePasswordConfirmModal = () => {
  showPasswordConfirmModal.value = false
}
const isValidPassword = () => {
  closePasswordConfirmModal()
  deleteAlert.value = true
}

const setIsSticky = (value:boolean) => store.dispatch('set_sticky', value)
const set_header_width = (value:boolean) => store.dispatch('set_header_width', value)
const expand_starter_modal = (value:boolean) =>
  store.dispatch('expand_starter_modal', value)
const collapse_starter_modal = (value:boolean) =>
  store.dispatch('collapse_starter_modal', value)
const show_sign_up = (value:boolean) => store.dispatch('show_sign_up', value)
const maximize_starter_modal = (value:boolean) =>
  store.dispatch('maximize_starter_modal', value)
const starter_account_maximized = (value:boolean) =>
  store.dispatch('starter_account_maximized', value)
const updateSetupContent = (value:string) =>
  store.dispatch('updateSetupContent', value)

const login_button_transition = (value:boolean) =>
  store.dispatch('loginAnimation/login_button_transition', value)
const submit_button_transition = (value:boolean) =>
  store.dispatch('loginAnimation/submit_button_transition', value)
const login_form_transition = (value:boolean) =>
  store.dispatch('loginAnimation/login_form_transition', value)
const successfully = (value:boolean) =>
  store.dispatch('loginAnimation/successfully', value)
const notsuccessfully = (value:boolean) =>
  store.dispatch('loginAnimation/notsuccessfully', value)
const after_loading = (value:boolean) =>
  store.dispatch('loginAnimation/after_loading', value)
const home_modal = (value:boolean) => store.dispatch('loginAnimation/home', value)
const sidebar_menu = (value:boolean) =>
  store.dispatch('loginAnimation/sidebar_menu', value)
const sidebar_circle = (value:boolean) => store.dispatch('loginAnimation/circle', value)
const home_sidebar = (value:boolean) => store.dispatch('loginAnimation/sidebar', value)
const home_circle = (value:boolean) =>
  store.dispatch('loginAnimation/home_circle', value)
const login_circle = (value:boolean) =>
  store.dispatch('loginAnimation/login_circle', value)
const slide_left = (value:boolean) => store.dispatch('loginAnimation/slide_left', value)
const show_logo = (value:boolean) => store.dispatch('loginAnimation/show_logo', value)
const home_header = (value:boolean) => store.dispatch('loginAnimation/header', value)
const width_increase = (value:boolean) =>
  store.dispatch('loginAnimation/width_increase', value)
const full_width = (value:boolean) => store.dispatch('loginAnimation/full_width', value)
const home_side_menu = (value:boolean) =>
  store.dispatch('loginAnimation/home_side_menu', value)
const all_side_menu = (value:boolean) =>
  store.dispatch('loginAnimation/all_side_menu', value)
const show_home = (value:boolean) => store.dispatch('loginAnimation/show_home', value)
const home_menu_text = (value:boolean) =>
  store.dispatch('loginAnimation/home_menu_text', value)
const show_home_content = (value:boolean) =>
  store.dispatch('loginAnimation/show_home_content', value)
const home_wrapper = (value:boolean) =>
  store.dispatch('loginAnimation/home_wrapper', value)
const show_logo_text = (value:boolean) =>
  store.dispatch('loginAnimation/show_logo_text', value)
const header_text = (value:boolean) =>
  store.dispatch('loginAnimation/header_text', value)
const loading_text = (value:boolean) =>
  store.dispatch('loginAnimation/loading_text', value)

const width_decrese = (value:boolean) =>
  store.dispatch('loginAnimation/width_decrese', value)
const slide_right = (value:boolean) =>
  store.dispatch('loginAnimation/slide_right', value)
const slide_full_right = (value:boolean) =>
  store.dispatch('loginAnimation/slide_full_right', value)
const show_login = (value:boolean) => store.dispatch('loginAnimation/show_login', value)
const landing_content = (value:boolean) =>
  store.dispatch('loginAnimation/landing_content', value)
const after_logout = (value:boolean) =>
  store.dispatch('loginAnimation/after_logout', value)
const text_loading = (value:boolean) =>
  store.dispatch('loginAnimation/text_loading', value)
</script>

<style lang="scss" scoped>
.close_account {
  z-index: 102;
}

.w-100-minus {
  @apply w-101;
}

@media (max-width: 767px) {
  .w-100-minus {
    width: calc(100% - 35px);
  }
}

.btn-max-w {
  max-width: 250px;
}
</style>
