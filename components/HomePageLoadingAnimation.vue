<template>
  <div
    class="main-wrapper right-0"
    :class="[afterLoading ? 'main_div' : '', afterLogout ? 'main_div1' : '']"
  >
    <div
      class="inner_login right-0"
      :class="[
        header ? 'z-10' : '',
        widthIncrease ? 'afterloading_innerhome2' : '',
        fullWidth ? 'afterloading_innerhome3' : '',
        widthDecrese ? 'afterlogout_innerhome3' : '',
        slideFullRight ? 'afterlogout_innerhome3' : '',
      ]"
    >
      <LazyHomeLatestArchiveHeader
        v-if="loginFormTransition"
        id="HomeLatestArchiveHeader"
        :header-text="headerText"
        @showProfile="showProfile = true"
      />
    </div>
    <div
      class="login right-0"
      :class="[afterLoading ? 'main_div' : '', afterLogout ? 'main_div1' : '']"
    >
      <TheSideBarAnimate />
      <ClientOnly>
        <DemoHome v-if="loginFormTransition" class="home" />
      </ClientOnly>
      <form
        id="login-form"
        aria-label="login-form"
        novalidate
        @submit.prevent=""
      >
        <transition name="login_form">
          <div class="login_form" v-if="!loginFormTransition">
            <div class="flex flex-row justify-between items-center pt-4">
              <div>
                <h3 class="text-orange-dark text-2xl md:hidden">Log In</h3>
              </div>
              <div
                v-if="route.fullPath !== '/auth/login'"
                class="cursor-pointer"
                @click="hideForm()"
              >
                <ClientOnly>
                  <fa
                    class="text-orange-dark text-2xl"
                    :icon="['fas', 'times']"
                  />
                </ClientOnly>
              </div>
            </div>
            <div class="md:block hidden pt-6">
              <h3 class="text-orange-dark text-2xl">Log In</h3>
            </div>
            <transition name="field-fadeIn" mode="out-in">
              <div
                v-if="hideInputField"
                key="input-field"
                class="mt-5.8 w-full input-field"
              >
                <div
                  class="w-full flex flex-row items-center rounded-full pl-5 bg-white"
                >
                  <ClientOnly>
                    <fa class="text-orange-dark mx-2" :icon="['fas', 'user']" />
                  </ClientOnly>
                  <input
                    v-model.trim="user.email"
                    class="outline-none rounded-r-full bg-white w-full h-10 placeholder-gray-1200 placeholder-opacity-50 text-gray-1200 pl-1 pr-5"
                    type="email"
                    placeholder="Email"
                    @blur="v$.email.$touch()"
                    @input="user.email = sanitizedOutput($event.target.value)"
                  />
                </div>
                <template v-if="v$.email.$error">
                  <p
                    v-if="v$.email.required.$invalid"
                    class="text-red-500 text-xs email-require"
                  >
                    Email is Required
                  </p>
                  <p
                    v-else-if="v$.email.email.$invalid"
                    class="text-red-500 text-xs email-invalid"
                  >
                    Email is Invalid
                  </p>
                </template>
                <p v-if="!errorEmail" class="text-red-500 text-xs wrong-email">
                  Email is Invalid
                </p>
                <div
                  class="w-full mt-5 flex flex-row items-center rounded-full px-5 bg-white"
                >
                  <ClientOnly>
                    <fa class="text-orange-dark mx-2" :icon="['fas', 'lock']" />
                  </ClientOnly>
                  <input
                    v-model.trim="user.password"
                    class="outline-none bg-white w-full h-10 placeholder-gray-1200 placeholder-opacity-50 text-gray-1200 pl-1"
                    :type="togglePassword ? 'password' : 'text'"
                    placeholder="Password"
                    autocomplete
                    @blur="v$.password.$touch()"
                    @input="
                      user.password = sanitizedOutput($event.target.value)
                    "
                  />
                  <ClientOnly>
                    <fa
                      class="text-orange-dark mx-2"
                      :icon="['fas', togglePassword ? 'eye-slash' : 'eye']"
                      @click="togglePassword = !togglePassword"
                    />
                  </ClientOnly>
                </div>
                <template v-if="v$.password.$error">
                  <p
                    v-if="v$.password.required.$invalid"
                    class="text-red-500 text-xs password-require"
                  >
                    Password is Required
                  </p>
                  <p
                    v-else-if="v$.password.minLength.$invalid"
                    class="text-red-500 text-xs password-invalid"
                  >
                    Password is Invalid
                  </p>
                </template>
                <p
                  v-if="!errorPassword"
                  class="text-red-500 text-xs wrong-pass"
                >
                  Password is Invalid
                </p>
                <div
                  class="mt-2.5 relative md:block flex flex-row justify-between items-center md:px-0 px-2"
                >
                  <label class="inline-flex items-center">
                    <input
                      v-model="rememberMe"
                      type="checkbox"
                      class="form-checkbox md:w-5 w-4 md:h-5 h-4 bg-offwhite-300 border-offwhite-300 text-orange-midlight"
                    />
                    <span class="ml-2 text-offwhite-300 md:text-lg text-md"
                      >Remember me</span
                    >
                  </label>
                  <p
                    class="md:hidden text-orange-dark md:text-lg text-md cursor-pointer mobile-forget-button"
                    @click="showForgetPasswordModal()"
                  >
                    Forgot Password
                  </p>
                </div>
                <p
                  class="md:block hidden mb-6 text-orange-dark text-lg cursor-pointer desktop-forget-button"
                  @click="showForgetPasswordModal()"
                >
                  Forgot Password
                </p>
              </div>
              <div
                v-else-if="!hideInputField"
                key="otp-field"
                class="my-0 mt-5.8 text-center email-verify"
              >
                <legend
                  v-if="codeType === 'email'"
                  class="text-white md:text-lg text-md"
                >
                  Please enter the code you were emailed.
                </legend>
                <legend
                  v-else-if="codeType === 'sms'"
                  class="text-white md:text-lg text-md"
                >
                  Please enter the code sent to *** *** **{{ phoneNumber }}.
                </legend>
                <number-verification-panel
                  :otp-code="otpCode"
                  :message="message"
                  class="mt-6"
                  input-border-style="border-b-2 border-yellow-primary border_color"
                  input-text-style="text-3xl text-yellow-primary text-center text_color"
                  @loginVerificationCode="getVerifyCode"
                  @submit="verification"
                ></number-verification-panel>
                <p class="pt-6 resend-code cursor-pointer">Resend Code</p>
                <div class="w-full flex space-x-4 justify-around mt-6">
                  <button
                    class="send-email w-44 py-1.5 rounded-full border-2 border-orange-dark text-orange-dark font-medium"
                    :disabled="sendCodeProcess"
                    @click.stop="resendCode()"
                  >
                    Send Email
                  </button>
                  <button
                    class="send-text w-44 py-1.5 rounded-full border-2 border-orange-dark text-orange-dark font-medium"
                    :disabled="reSendProcess"
                    @click.stop="resendMobileCode()"
                  >
                    Send Text
                  </button>
                </div>
              </div>
            </transition>
          </div>
        </transition>
        <div
          id="loginButton"
          ref="login"
          class="text-center absolute login_button_div"
          :class="[
            successfull ? 'loading_div' : '',
            notsuccessfull ? 'remove_loading_div' : '',
            loginCircle ? '' : 'hidden',
          ]"
        >
          <button
            class="login-button w-44 h-10 text-white bg-orange-dark rounded-full border-none outline-none font-medium"
            type="submit"
            :disabled="process"
            :class="successfull === null ? '' : successfull ? 'loading' : 'removeLoading'"
            @click.stop="
              !loginButtonTransition
                ? [logIn(), setcookie()]
                : [verification(otpCode), setcookie()]
            "
          >
            <transition name="login_button" mode="out-in">
              <p
                v-if="!loginButtonTransition && !submitButtonTransition"
                key="login"
              >
                Login
              </p>
              <p
                v-else-if="loginButtonTransition && !submitButtonTransition"
                key="submit"
              >
                Submit
              </p>
            </transition>
          </button>
          <p
            class="opacity-0 text-center text-gray-900"
            :class="[
              loadingText ? 'loading_text' : '',
              textLoading ? 'text_loading' : '',
            ]"
          >
            Loading...
          </p>
        </div>
      </form>
    </div>
  </div>
</template>
<script setup>
import { defineAsyncComponent } from 'vue'
import { useStore } from 'vuex'
import { USER_LOGIN, SECURITY_OTP } from '~/constants/urls'
import { useVuelidate } from '@vuelidate/core'
import { required, email, minLength } from '@vuelidate/validators'
import { useAuth } from '~/composables/useAuth'
import { useFetched } from '~/composables/useFetched'
import { emailVerify, phoneVerify } from '~/composables/useOtp.js'
import { useInputValidations } from '~/composables/useValidations'
import NumberVerificationPanel from '~/components/inputs/NumberVerificationPanel'
import { useNuxtApp } from '#app'
// import { useRoute } from 'vue-router'

const DemoHome = defineAsyncComponent(() => import('~/pages/home.vue'))

const { fetchUser, setAuthCookies, loggedIn } = useAuth()
const { resendEmailVerifyCode } = emailVerify()
const { resendPhoneVerifyCode, phoneNumber } = phoneVerify()
// const nuxtApp = useNuxtApp()
const { $toast } = useNuxtApp()
const store = useStore()
const route = useRoute()
const router = useRouter()
const saveEmailPassDuration = ref(60 * 60 * 24 * 6)
const setEmailCookies = useCookie('myEmailSharpArchive', {
  path: '/',
  maxAge: saveEmailPassDuration.value,
})
const setPasswordCookies = useCookie('myPasswordSharpArchive', {
  path: '/',
  maxAge: saveEmailPassDuration.value,
})
const rememberMe = ref(false)
const user = ref({
  email: '',
  password: '',
})
const setcookie = () => {
  if (rememberMe.value) {
    setEmailCookies.value = user.value.email
    setPasswordCookies.value = user.value.password
  }
}
const getcookiedate = () => {
  user.value.email = setEmailCookies.value ? setEmailCookies.value : ''
  user.value.password = setPasswordCookies.value ? setPasswordCookies.value : ''
}
const rules = {
  email: {
    required,
    email,
  },
  password: {
    required,
    minLength: minLength(8),
  },
}
const v$ = useVuelidate(rules, user)
const { fetch } = useFetched()
const codeType = ref('email')
const { sanitizedOutput } = useInputValidations()
const errorEmail = ref(true)
const errorPassword = ref(true)
const togglePassword = ref(true)
const hideInputField = ref(true)
const otpCode = ref('')
const process = ref(false)
const message = ref('')
// const DemoHome = defineAsyncComponent(() => import('~/pages/home.vue'))
// Map state properties manually
const loginButtonTransition = computed(
  () => store.state.loginAnimation.loginButtonTransition,
)
const submitButtonTransition = computed(
  () => store.state.loginAnimation.submitButtonTransition,
)
const loginFormTransition = computed(
  () => store.state.loginAnimation.loginFormTransition,
)
const successfull = computed(() => store.state.loginAnimation.successfull)
const notsuccessfull = computed(() => store.state.loginAnimation.notsuccessfull)
const afterLoading = computed(() => store.state.loginAnimation.afterLoading)
const home = computed(() => store.state.loginAnimation.home)
const circle = computed(() => store.state.loginAnimation.circle)
const sidebar = computed(() => store.state.loginAnimation.sidebar)
const loginCircle = computed(() => store.state.loginAnimation.loginCircle)
const header = computed(() => store.state.loginAnimation.header)
const widthIncrease = computed(() => store.state.loginAnimation.widthIncrease)
const fullWidth = computed(() => store.state.loginAnimation.fullWidth)
const headerText = computed(() => store.state.loginAnimation.headerText)
const loadingText = computed(() => store.state.loginAnimation.loadingText)
const widthDecrese = computed(() => store.state.loginAnimation.widthDecrese)
const slideFullRight = computed(() => store.state.loginAnimation.slideFullRight)
const afterLogout = computed(() => store.state.loginAnimation.afterLogout)
const textLoading = computed(() => store.state.loginAnimation.textLoading)
// Define computed properties to access state data
const emailErrorReset = computed(() => user.value.email)
const passwordErrorReset = computed(() => user.value.password)
const userLoggedIn = computed(() => store.state.auth.loggedIn)
const userInfo = computed(() => store.state.auth.user)
// Watchers to react to changes in emailErrorReset and passwordErrorReset
watch(emailErrorReset, () => {
  errorEmail.value = true
})
watch(passwordErrorReset, () => {
  errorPassword.value = true
})
// Define methods that dispatch Vuex actions
const login_button_transition = (value) =>
  store.dispatch('loginAnimation/login_button_transition', value)
const submit_button_transition = (value) =>
  store.dispatch('loginAnimation/submit_button_transition', value)
const login_form_transition = (value) =>
  store.dispatch('loginAnimation/login_form_transition', value)
const successfully = (value) =>
  store.dispatch('loginAnimation/successfully', value)
const notsuccessfully = (value) =>
  store.dispatch('loginAnimation/notsuccessfully', value)
const after_loading = (value) =>
  store.dispatch('loginAnimation/after_loading', value)
const home_modal = (value) => store.dispatch('loginAnimation/home', value)
const sidebar_menu = (value) =>
  store.dispatch('loginAnimation/sidebar_menu', value)
const sidebar_circle = (value) => store.dispatch('loginAnimation/circle', value)
const home_sidebar = (value) => store.dispatch('loginAnimation/sidebar', value)
const home_circle = (value) =>
  store.dispatch('loginAnimation/home_circle', value)
const login_circle = (value) =>
  store.dispatch('loginAnimation/login_circle', value)
const slide_left = (value) => store.dispatch('loginAnimation/slide_left', value)
const show_logo = (value) => store.dispatch('loginAnimation/show_logo', value)
const home_header = (value) => store.dispatch('loginAnimation/header', value)
const width_increase = (value) =>
  store.dispatch('loginAnimation/width_increase', value)
const full_width = (value) => store.dispatch('loginAnimation/full_width', value)
const home_side_menu = (value) =>
  store.dispatch('loginAnimation/home_side_menu', value)
const all_side_menu = (value) =>
  store.dispatch('loginAnimation/all_side_menu', value)
const show_home = (value) => store.dispatch('loginAnimation/show_home', value)
const home_menu_text = (value) =>
  store.dispatch('loginAnimation/home_menu_text', value)
const show_home_content = (value) =>
  store.dispatch('loginAnimation/show_home_content', value)
const home_wrapper = (value) =>
  store.dispatch('loginAnimation/home_wrapper', value)
const show_logo_text = (value) =>
  store.dispatch('loginAnimation/show_logo_text', value)
const header_text = (value) =>
  store.dispatch('loginAnimation/header_text', value)
const loading_text = (value) =>
  store.dispatch('loginAnimation/loading_text', value)
const show_login = (value) => store.dispatch('loginAnimation/show_login', value)
const landing_content = (value) =>
  store.dispatch('loginAnimation/landing_content', value)
const showForgetPassword = (value) =>
  store.dispatch('showForgetPassword', value)

// Mounted logic
onMounted(() => {
  generateURL()
  getcookiedate()
  if (route.query.showLogin === 'true' && !userLoggedIn.value) {
    show_login(true)
  } else if (
    window.location.host === 'demo.sharparchive.com' &&
    !userLoggedIn.value
  ) {
    show_login(true)
  }
})

// Generate URL and check conditions
const generateURL = () => {
  const fullUrl = window.location.origin + route.path
  if (
    fullUrl === 'https://sharparchive.com/' ||
    fullUrl === 'http://sharparchive.com/'
  ) {
    user.value.email = ''
    user.value.password = ''
  } else if (
    fullUrl === 'https://localhost:3000/' ||
    fullUrl === 'http://localhost:3000/'
  ) {
    user.value.email = ''
    user.value.password = ''
  }
}
// Define the emit function
const emit = defineEmits(['hide', 'login-start', 'login-stop'])
// Hide form and reset states
const hideForm = () => {
  emit('hide')
  errorEmail.value = true
  errorPassword.value = true
  user.value.email = ''
  user.value.password = ''
  otpCode.value = ''
  hideInputField.value = true
  rememberMe.value = false
  v$.value.$reset()
  // Trigger actions and reset validations
  show_login(false)
  submit_button_transition(false)
  login_button_transition(false)
  // Delay for landing content
  setTimeout(() => {
    landing_content(false)
  }, 500)
}
const sendCodeProcess = ref(false)
// Method to resend code for email verification
const resendCode = async () => {
  // $toast('clear')
  sendCodeProcess.value = true
  otpCode.value = ''
  await resendEmailVerifyCode({ email: user.value.email })
  codeType.value = 'email'
  sendCodeProcess.value = false
}
const reSendProcess = ref(false)
// Method to resend code for mobile verification
const resendMobileCode = async () => {
  otpCode.value = ''
  reSendProcess.value = true
  await resendPhoneVerifyCode({
    email: user.value.email,
    password: user.value.password,
  })
  codeType.value = 'sms'
  reSendProcess.value = false
}
const logIn = () => {
  v$.value.$touch()
  if (!v$.value.$invalid) {
    emit('login-start', true)
    login_button_transition(true)
    submit_button_transition(true)
    login_form_transition(true)
    notsuccessfully(false)
    setTimeout(() => {
      successfully(true)
      // startAnimation()
      setTimeout(() => {
        startAnimation()
        loading_text(true)
      }, 600)
    }, 800)
  }
}
const getVerifyCode = (code) => {
  otpCode.value = code
}
const verification = (code) => {
  otpCode.value = code
  $toast('clear')
  if (code) {
    if (code.length === 6) {
      process.value = true
      emit('login-start', true)
      submit_button_transition(true)
      login_form_transition(true)
      notsuccessfully(false)
      setTimeout(() => {
        successfully(true)
        setTimeout(() => {
          animation(otpCode.value)
          loading_text(true)
        }, 600)
      }, 800)
    }
  } else if (!code) {
    process.value = true
  } else {
    $toast('error', {
      message: 'Please enter the six digit code',
      className: 'toasted-bg-alert',
    })
  }
}
const startAnimation = async () => {
  process.value = true
  try {
    const data = await $fetch(USER_LOGIN, {
      method: 'POST',
      body: {
        username: user.value.email,
        password: user.value.password,
      },
    })
    if (data.success && data.message) {
      loading_text(false)
      emit('login-stop', false)
      successfully(false)
      notsuccessfully(true)

      setTimeout(() => {
        setTimeout(() => {
          loading_text(false)
          login_button_transition(true)
          login_form_transition(false)
          hideInputField.value = false
          setTimeout(() => {
            submit_button_transition(false)
            $toast('success', {
              message: data.message,
              className: 'toasted-bg-archive',
            })
          })
        }, 300)
      }, 500)
      process.value = false
    } else if (data.success && data.access) {
      setAuthCookies(data.access, data.refresh, true)
      store.commit('SET_LOCK_SCREEN', false)
      errorEmail.value = true
      errorPassword.value = true
      loading_text(false)
      after_loading(true)
      setTimeout(() => {
        home_header(true)
      }, 2800)
      setTimeout(() => {
        home_modal(true)
        width_increase(true)
        header_text(true)
      }, 1100)
      setTimeout(() => {
        home_sidebar(true)
        home_circle(true)
      }, 1850)
      setTimeout(() => {
        login_circle(false)
        slide_left(true)
        full_width(true)
        emit('login-stop', false)
      }, 1900)
      setTimeout(() => {
        sidebar_menu(true)
        sidebar_circle(true)
      }, 2850)
      setTimeout(() => {
        home_circle(false)
        show_logo(true)
        home_side_menu(true)
        home_menu_text(true)
        all_side_menu(true)
      }, 3500)
      setTimeout(() => {
        show_home(true)
        show_logo_text(false)
        home_menu_text(false)
      }, 4100)
      setTimeout(() => {
        if (
          userInfo.value.paymentStatus === '' &&
          userInfo.value.isOwner === true &&
          userInfo.value.userPermission !== 'Partner'
        ) {
          router.replace('/payment')
        } else if (route.query.provider && route.query.id) {
          router.push({
            path: 'settings',
            query: {
              provider: route.query.provider,
              id: route.query.id,
            },
          })
        } else if (route.query.addMoreFeed === 'true') {
          router.replace('/home?addMoreFeed=true')
        } else if (userInfo.value.userPermission !== 'Partner') {
          router.replace('/home')
        } else {
          // store.dispatch('profile/partnerProfileLogout')
          loggedIn.value = null
          store.commit('auth/SET_LOGGEDIN')
          window.location.href =
            'https://partner.sharparchive.com/partners-portal'
        }
      }, 5100)
    } else {
      $toast('error', {
        message: data.message,
        className: 'toasted-bg-alert',
      })
      errorEmail.value = false
      errorPassword.value = false
      loading_text(false)
      emit('login-stop', false)
      successfully(false)
      notsuccessfully(true)
      setTimeout(() => {
        setTimeout(() => {
          loading_text(false)
          login_button_transition(false)
          submit_button_transition(false)
          login_form_transition(false)
        }, 300)
      }, 500)
      process.value = false
    }
  } catch (e) {
    errorEmail.value = false
    errorPassword.value = false
    loading_text(false)
    emit('login-stop', false)
    successfully(false)
    notsuccessfully(true)
    setTimeout(() => {
      loading_text(false)
      setTimeout(() => {
        login_button_transition(false)
        submit_button_transition(false)
        login_form_transition(false)
      }, 300)
    }, 500)
  } finally {
    process.value = false
  }
}
const animation = async (code) => {
  message.value = true
  try {
    const data = await $fetch(SECURITY_OTP, {
      method: 'POST',
      body: {
        username: user.value.email,
        password: user.value.password,
        delivery: codeType.value,
        otp: code,
      },
    })
    if (data.success) {
      message.value = data.success
      setAuthCookies(data.access, data.refresh, true)
      errorEmail.value = true
      errorPassword.value = true
      loading_text(false)
      after_loading(true)
      setTimeout(() => {
        home_header(true)
      }, 2800)
      setTimeout(() => {
        home_modal(true)
        width_increase(true)
        header_text(true)
      }, 1100)
      setTimeout(() => {
        home_sidebar(true)
        home_circle(true)
      }, 1850)
      setTimeout(() => {
        login_circle(false)
        slide_left(true)
        full_width(true)
        emit('login-stop', false)
      }, 1900)
      setTimeout(() => {
        sidebar_menu(true)
        sidebar_circle(true)
      }, 2850)
      setTimeout(() => {
        home_circle(false)
        show_logo(true)
        home_side_menu(true)
        home_menu_text(true)
        all_side_menu(true)
      }, 3500)
      setTimeout(() => {
        show_home(true)
        show_logo_text(false)
        home_menu_text(false)
      }, 4100)
      setTimeout(() => {
        if (
          userInfo.value.paymentStatus === '' &&
          userInfo.value.isOwner === true &&
          userInfo.value.userPermission !== 'Partner'
        ) {
          router.replace('/payment')
        } else if (route.query.provider && route.query.id) {
          router.push({
            path: 'settings',
            query: {
              provider: route.query.provider,
              id: route.query.id,
            },
          })
        } else if (route.query.addMoreFeed === 'true') {
          router.replace('/home?addMoreFeed=true')
        } else if (userInfo.value.userPermission !== 'Partner') {
          router.replace('/home')
        } else {
          // store.dispatch('profile/partnerProfileLogout')
          loggedIn.value = null
          store.commit('auth/SET_LOGGEDIN')
          window.location.href =
            'https://partner.sharparchive.com/partners-portal'
        }
      }, 5100)
    } else {
      consoe.log('console. else')
      $toast('error', {
        message: data.message,
        className: 'toasted-bg-alert',
      })
      loading_text(false)
      emit('login-stop', false)
      successfully(false)
      notsuccessfully(true)
      setTimeout(() => {
        setTimeout(() => {
          loading_text(false)
          login_button_transition(true)
          submit_button_transition(false)
          login_form_transition(false)
          setTimeout(() => {
            message.value = data.success
          }, 500)
        }, 300)
      }, 500)
      process.value = false
    }
  } catch (error) {
    message.value = false
    loading_text(false)
    emit('login-stop', false)
    successfully(false)
    notsuccessfully(true)
    setTimeout(() => {
      loading_text(false)
      setTimeout(() => {
        login_button_transition(true)
        submit_button_transition(false)
        login_form_transition(false)
      }, 300)
    }, 500)
    console.error(error)
  } finally {
    process.value = false
    message.value = ''
  }
}
const showForgetPasswordModal = () => {
  showForgetPassword(true)
  show_login(false)
}
</script>
<style lang="scss" scoped>
.main-wrapper {
  @apply top-0 h-screen login md:z-31 transition-all duration-800 ease-in-out overflow-hidden;
  background-color: transparent !important;
  position: fixed !important;
}
.inner_login {
  @apply h-screen top-0 fixed  overflow-hidden;
  width: 0%;
  background-color: #393e46;
  transition:
    width 1.5s ease-in-out,
    width 1.5s ease-in-out;
}
.afterloading_innerhome2 {
  width: calc(225px + 0%);
  background-color: #393e46;
}
.afterloading_innerhome3 {
  width: 70%;
}
// .afterlogout_innerhome2 {
//   width: calc(225px + 0%);
//   background-color: #393e46;
// }
.afterlogout_innerhome3 {
  width: 0%;
}
.login {
  @apply h-screen absolute top-0 md:px-21 px-4 transition-all duration-800 ease-in-out overflow-hidden md:shadow-none drop-shadow-2xl md:z-31;
  width: 450px;
  background-color: #171d26;
}
.main_div {
  width: 100%;
  transition: width 1s linear;
  transition-delay: 0.4s;
}
.main_div1 {
  width: 450px;
  transition: width 1s linear;
}
.login_button_div {
  width: 390px;
  height: 40px;
  top: 360px;
  right: 30px;
}
.loading_div {
  animation: top 0.6s linear;
  animation-fill-mode: forwards;
}
@keyframes top {
  0% {
    top: 360px;
  }
  25% {
    top: 312.5px;
  }
  50% {
    top: 265px;
  }
  75% {
    top: 217.5px;
  }
  100% {
    top: 170px;
  }
}
.remove_loading_div {
  animation: top12 0.6s linear;
  animation-fill-mode: forwards;
}
@keyframes top12 {
  0% {
    top: 170px;
  }
  25% {
    top: 217.5px;
  }
  50% {
    top: 265px;
  }
  75% {
    top: 312.5px;
  }
  100% {
    top: 360px;
  }
}
.loading_text {
  animation: opacity 5s linear;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
}
@keyframes opacity {
  0% {
    opacity: 1;
  }
  25% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  75% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
  }
}
.text_loading {
  animation: textopacity 2s linear;
  animation-fill-mode: forwards;
}
@keyframes textopacity {
  0% {
    opacity: 1;
  }
  25% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  75% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
  }
}

.loading {
  animation: width 0.6s linear;
  animation-fill-mode: forwards;
}
@keyframes width {
  0% {
    width: 176px;
    top: 360px;
  }
  25% {
    width: 142px;
    top: 312.5px;
  }
  50% {
    width: 108px;
    top: 265px;
  }
  75% {
    width: 74px;
    top: 217.5px;
  }
  100% {
    width: 40px;
    border-radius: 50%;
    top: 170px;
  }
}
.removeLoading {
  animation: width12 0.6s linear;
  animation-fill-mode: forwards;
}
@keyframes width12 {
  0% {
    width: 40px;
    border-radius: 50%;
    top: 170px;
  }
  25% {
    width: 74px;
    top: 217.5px;
  }
  50% {
    width: 108px;
    top: 265px;
  }
  75% {
    width: 142px;
    top: 312.5px;
  }
  100% {
    width: 176px;
    top: 360px;
  }
}

.login_button-enter-active,
.login_button-leave-active {
  transition: opacity 0.5s;
}
.login_button-enter-from,
.login_button-leave-to {
  opacity: 0;
}
.login_button-enter-active {
  transition-delay: 0.5s;
}
.login_form-leave-active,
.login_form-enter-active {
  transition: opacity 1s;
}
.login_form-leave-to,
.login_form-enter-from {
  opacity: 0;
}
.login_form-leave-from,
.login_form-enter-to {
  opacity: 1;
}
.resend-code {
  @apply text-white outline-none border-none;
}
.field-fadeIn-enter-active,
.field-fadeIn-leave-active {
  transition: opacity 0.5s;
}
.field-fadeIn-enter,
.field-fadeIn-leave-to {
  opacity: 0;
}
.field-fadeIn-enter-active {
  transition-delay: 0.6s;
}
@media (max-width: 767px) {
  .login {
    height: 420px;
    width: 100%;
    border-radius: 0 0 1.563rem 1.563rem;
    z-index: 40;
  }
  .home {
    display: none;
  }
  .afterloading_innerhome2 {
    width: 100%;
  }
  .login_button_div {
    width: 100%;
    right: 0px;
  }
  .inner_login {
    z-index: 41;
  }
}
@media (max-width: 826px) {
  .login {
    z-index: 31;
  }
}
</style>
