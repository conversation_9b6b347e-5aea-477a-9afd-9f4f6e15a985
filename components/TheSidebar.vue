<template>
  <div>
    <div
      class="absolute left-0 top-0 h-full transition-all duration-500 ease-in-out rounded-br-3xl sidebar hidden md:block neuton-font"
      :class="collapse ? 'w-26' : 'width-200 delay-500'"
      @mouseover.stop="(emit('squeeze'), squeeze())"
      @mouseleave.stop="(emit('notsqueeze'), notSqueeze())"
      @click="closeSidebar"
    >
      <div class="sidebar__header">
        <a href="/" target="_self" rel="noopener noreferrer" class="h-full">
          <sharp-archive-logo></sharp-archive-logo>
        </a>
      </div>
      <div ref="sidemenu" class="sidemenu flex flex-col">
        <!-- <div
          class="h-5 w-full bg-offgray spacer"
          data-index="0"
        ></div>-->

        <!-- Spill -->
        <div
          class="background"
          :class="backgroundColor"
          :style="{ 'margin-top': `${offsetTopPx}px` }"
        >
          <span
            class="background__circle"
            :class="circleBackgroundColor"
          ></span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="corner-top hidden fill-current"
            :class="textBackgroundColor"
            width="20"
            height="20"
            viewBox="0 0 30 30.004"
          >
            <path
              id="Subtraction_11"
              data-name="Subtraction 11"
              d="M60.008,60.008h-30a30.037,30.037,0,0,0,30-30v30Z"
              transform="translate(-30.009 -30.004)"
            />
          </svg>

          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="corner-bottom hidden fill-current"
            :class="textBackgroundColor"
            width="20"
            height="20"
            viewBox="0 0 30 30.004"
          >
            <path
              id="Subtraction_11"
              data-name="Subtraction 11"
              d="M60.008,60.008h-30a30.037,30.037,0,0,0,30-30v30Z"
              transform="translate(-30.009 -30.004)"
            />
          </svg>
        </div>

        <div class="h-5 w-full bg-offgray spacer" data-index="0"></div>

        <!-- Home -->
        <NuxtLink
          to="/home"
          class="sidemenu__link mb-2.5"
          :class="route.name === 'home' ? 'activeRoute' : ''"
          data-theme-bg="bg-gray-1000"
          data-theme-text="text-orange-dark"
          data-theme-circle="bg-orange-dark"
          data-index="1"
          @click.native="closeSiglePost()"
        >
          <div class="link__title">
            <svg
              id="Capa_1"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              x="0px"
              y="0px"
              width="20"
              height="20"
              viewBox="0 0 460.298 460.297"
              class="fill-current text-orange-dark ml-0.5"
              style="enable-background: new 0 0 460.298 460.297"
              xml:space="preserve"
            >
              <g>
                <g>
                  <path
                    d="M230.149,120.939L65.986,256.274c0,0.191-0.048,0.472-0.144,0.855c-0.094,0.38-0.144,0.656-0.144,0.852v137.041
			c0,4.948,1.809,9.236,5.426,12.847c3.616,3.613,7.898,5.431,12.847,5.431h109.63V303.664h73.097v109.64h109.629
			c4.948,0,9.236-1.814,12.847-5.435c3.617-3.607,5.432-7.898,5.432-12.847V257.981c0-0.76-0.104-1.334-0.288-1.707L230.149,120.939
			z"
                  />
                  <path
                    d="M457.122,225.438L394.6,173.476V56.989c0-2.663-0.856-4.853-2.574-6.567c-1.704-1.712-3.894-2.568-6.563-2.568h-54.816
			c-2.666,0-4.855,0.856-6.57,2.568c-1.711,1.714-2.566,3.905-2.566,6.567v55.673l-69.662-58.245
			c-6.084-4.949-13.318-7.423-21.694-7.423c-8.375,0-15.608,2.474-21.698,7.423L3.172,225.438c-1.903,1.52-2.946,3.566-3.14,6.136
			c-0.193,2.568,0.472,4.811,1.997,6.713l17.701,21.128c1.525,1.712,3.521,2.759,5.996,3.142c2.285,0.192,4.57-0.476,6.855-1.998
			L230.149,95.817l197.57,164.741c1.526,1.328,3.521,1.991,5.996,1.991h0.858c2.471-0.376,4.463-1.43,5.996-3.138l17.703-21.125
			c1.522-1.906,2.189-4.145,1.991-6.716C460.068,229.007,459.021,226.961,457.122,225.438z"
                  />
                </g>
              </g>
              <g />
              <g />
              <g />
              <g />
              <g />
              <g />
              <g />
              <g />
              <g />
              <g />
              <g />
              <g />
              <g />
              <g />
              <g />
            </svg>

            <p
              class="sidemenu__label home transition-all duration-500 ease-in-out"
              :class="collapse ? 'opacity-0' : 'opacity-100 delay-500'"
            >
              Home
            </p>
          </div>
        </NuxtLink>

        <!-- Archive -->
        <NuxtLink
          to="/archive"
          class="sidemenu__link mb-2.5"
          :class="route.name === 'archive' ? 'activeRoute' : ''"
          data-theme-bg="bg-gray-1000"
          data-theme-text="text-green-1100"
          data-theme-circle="bg-green-1100"
          data-index="2"
          @click.native="closeSiglePost()"
        >
          <div class="link__title">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 47.24 37.79"
              class="fill-current text-green-1100 ml-0.5"
              width="20"
              height="20"
            >
              <g id="Layer_2" data-name="Layer 2">
                <g id="Poster_1" data-name="Poster 1">
                  <path
                    id="ic_storage_24px"
                    data-name="ic storage 24px"
                    d="M0,37.79H47.24V28.34H0ZM4.72,30.7H9.45v4.73H4.72ZM0,0V9.45H47.24V0ZM9.45,7.09H4.72V2.36H9.45ZM0,23.62H47.24V14.17H0Zm4.72-7.09H9.45v4.73H4.72Z"
                  />
                </g>
              </g>
            </svg>

            <p
              class="sidemenu__label archivet ransition-all duration-500 ease-in-out"
              :class="collapse ? 'opacity-0' : 'opacity-100 delay-500'"
            >
              Archive
            </p>
          </div>
        </NuxtLink>

        <!-- Search -->
        <NuxtLink
          to="/search"
          class="sidemenu__link mb-2.5"
          :class="route.name === 'search' ? 'activeRoute' : ''"
          data-theme-bg="bg-gray-1000"
          data-theme-text="text-purple-midlight"
          data-theme-circle="bg-purple-midlight"
          data-index="3"
          @click.native="closeSiglePost()"
        >
          <div class="link__title">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 47.27 47.27"
              class="fill-current text-purple-midlight ml-0.5"
              width="20"
              height="20"
            >
              <g id="Layer_2" data-name="Layer 2">
                <g id="Poster_1" data-name="Poster 1">
                  <path
                    id="ic_search_24px"
                    data-name="ic search 24px"
                    d="M33.8,29.75H31.67L30.91,29A17.57,17.57,0,1,0,29,30.91l.73.76V33.8l13.5,13.47,4-4Zm-16.2,0A12.15,12.15,0,1,1,29.75,17.6,12.15,12.15,0,0,1,17.6,29.75Z"
                  />
                </g>
              </g>
            </svg>

            <p
              class="sidemenu__label search transition-all duration-500 ease-in-out"
              :class="collapse ? 'opacity-0' : 'opacity-100 delay-500'"
            >
              Search
            </p>
          </div>
        </NuxtLink>
        <!-- alert -->
        <NuxtLink
          to="/alert"
          class="sidemenu__link mb-2.5"
          :class="route.name === 'alert' ? 'activeRoute' : ''"
          data-theme-bg="bg-gray-1000"
          data-theme-text="text-red-deep"
          data-theme-circle="bg-red-deep"
          data-index="4"
          @click.native="closeSiglePost()"
        >
          <div class="link__title">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="fill-current text-red-deep ml-0.5"
              width="20"
              height="20"
              viewBox="0 0 24 24"
            >
              <path
                id="icon-alert"
                d="M76.868,57.231a3.132,3.132,0,0,0-3.19-2.663q-6.2-.005-12.4,0A3.138,3.138,0,0,0,58.058,57.8q0,4.63,0,9.259a3.332,3.332,0,0,0,.076.755,3.126,3.126,0,0,0,3.151,2.471c2.25,0,4.805,0,7.056,0a.552.552,0,0,1,.417.156c1.227,1.181,2.23,2.168,3.461,3.378a.869.869,0,0,0,.988.213.845.845,0,0,0,.443-.892c-.009-.884,0-1.77-.006-2.651,0-.156.04-.2.2-.214a3.274,3.274,0,0,0,2.971-2.38c.023-.091.01-.2.094-.267V57.311A.375.375,0,0,1,76.868,57.231Zm-9.305,10.8A1.527,1.527,0,1,1,69,66.433a1.475,1.475,0,0,1-1.437,1.6Zm1.446-5.941a1.525,1.525,0,1,1-3.05,0V58.328a1.525,1.525,0,1,1,3.05,0Zm-2.27,10.025a.8.8,0,0,1,.8.8v.176a2.585,2.585,0,0,1-.061.586A2.426,2.426,0,0,1,65.032,75.6c-1.745,0-3.728,0-5.473,0a.427.427,0,0,0-.324.122c-.952.916-1.731,1.683-2.684,2.625a.674.674,0,0,1-.767.165.655.655,0,0,1-.343-.692c.007-.685,0-1.371,0-2.057,0-.122-.031-.159-.157-.165a2.541,2.541,0,0,1-2.3-1.845c-.018-.07-.007-.153-.073-.207V65.535a.271.271,0,0,0,.032-.065A2.428,2.428,0,0,1,55.417,63.4h.008a.8.8,0,0,1,.8.8v2.851a4.968,4.968,0,0,0,5.05,5.059C62.786,72.117,65.423,72.118,66.738,72.118Z"
                transform="translate(-52.91 -54.565)"
                :fill="route.name === 'alert' ? '#fff' : '#D63C3C'"
              />
            </svg>
            <!-- <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 29.99 24.5"
              class="fill-current text-red-deep ml-0.5"
              width="20"
              height="20"
            >
              <g id="Layer_2" data-name="Layer 2">
                <g id="Layer_1-2" data-name="Layer 1">
                  <path
                    id="Path_2755"
                    data-name="Path 2755"
                    class="cls-200"
                    d="M26.92,20.39a1.62,1.62,0,0,1,.45-2.1A8.69,8.69,0,0,0,27.51,6l-.07-.08a12.63,12.63,0,0,0-15.75-1.5,9.1,9.1,0,0,0-4.32,7.1,8.78,8.78,0,0,0,3.84,8,12.29,12.29,0,0,0,10.73,2c.34-.08.65-.2,1,.15a10.58,10.58,0,0,0,4.86,2.77c.39.12.77.19,1-.22s0-.69-.22-1A9.65,9.65,0,0,1,26.92,20.39ZM18.73,4.91c.85,0,1.24.36,1.29,1.29,0,.43,0,.87,0,1.22a44.74,44.74,0,0,1-.6,5.65c-.09.44-.25.73-.75.77s-.68-.28-.81-.68a7.19,7.19,0,0,1-.28-1.73c-.1-1.48-.21-3-.31-4.46,0-.22,0-.45,0-.68a1.38,1.38,0,0,1,1.38-1.38Zm-.05,13a1.38,1.38,0,0,1-1.44-1.29,1.42,1.42,0,0,1,2.84,0,1.38,1.38,0,0,1-1.4,1.28Z"
                  />
                  <path
                    id="Path_2756"
                    data-name="Path 2756"
                    class="cls-200"
                    d="M5.15,15.91a4.48,4.48,0,0,0,1.1-.83.64.64,0,0,1,.88-.15l.12.12c.32.34.24.65-.09,1A6.24,6.24,0,0,1,3.3,17.87a.67.67,0,0,1-.61-1.16l1.1-1.22c.26-.29.28-.46-.11-.72A7.77,7.77,0,0,1,0,8.67a7.37,7.37,0,0,1,2.48-6,10.59,10.59,0,0,1,11-1.93c.46.19.64.57.46.94s-.59.43-1,.28A9,9,0,0,0,4.17,3.06a6.23,6.23,0,0,0,.27,10.63l.14.09C5.8,14.55,5.8,14.55,5.15,15.91Z"
                  />
                </g>
              </g>
            </svg> -->

            <p
              class="sidemenu__label alert transition-all duration-500 ease-in-out"
              :class="collapse ? 'opacity-0' : 'opacity-100 delay-500'"
            >
              Alert
            </p>
          </div>
        </NuxtLink>
        <!-- Pricing -->
        <NuxtLink
          v-if="hideText($config.public.workflow)"
          to="/pricing-calculate"
          class="sidemenu__link mb-2.5"
          :class="route.name === 'pricing-calculate' ? 'activeRoute' : ''"
          data-theme-bg="bg-gray-1000"
          data-theme-text="text-red-deep"
          data-theme-circle="bg-red-deep"
          data-index="5"
          @click.native="closeSiglePost()"
        >
          <div class="link__title">
            <svg
              id="Layer_2"
              data-name="Layer 2"
              class="fill-current text-red-1300 price-icon ml-0.5"
              width="20"
              height="20"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 30 30"
            >
              <g id="Layer_2-2" data-name="Layer 2">
                <g>
                  <rect
                    class="pricingCls"
                    x="3.52"
                    y="5.19"
                    width="17.64"
                    height="1.99"
                    rx=".93"
                    ry=".93"
                  />
                  <rect
                    class="pricingCls"
                    x="3.52"
                    y="9.37"
                    width="17.64"
                    height="1.99"
                    rx=".93"
                    ry=".93"
                  />
                  <rect
                    class="pricingCls"
                    x="3.53"
                    y="13.4"
                    width="10.68"
                    height="1.99"
                    rx=".93"
                    ry=".93"
                  />
                  <rect
                    class="pricingCls"
                    x="3.53"
                    y="17.43"
                    width="8.81"
                    height="1.99"
                    rx=".93"
                    ry=".93"
                  />
                  <rect
                    class="pricingCls"
                    x="3.53"
                    y="21.47"
                    width="8.81"
                    height="1.99"
                    rx=".93"
                    ry=".93"
                  />
                  <path
                    class="pricingCls"
                    d="m21.64,30c-4.61,0-8.36-3.57-8.36-7.95s3.75-7.95,8.36-7.95,8.36,3.57,8.36,7.95-3.75,7.95-8.36,7.95Zm0-14.14c-3.58,0-6.5,2.78-6.5,6.19s2.92,6.19,6.5,6.19,6.5-2.78,6.5-6.19-2.92-6.19-6.5-6.19Z"
                  />
                  <path
                    class="pricingCls"
                    d="m2.92,27.35c-.58,0-1.06-.45-1.06-1.01V2.78c0-.56.48-1.01,1.06-1.01h19.6c.58,0,1.06.45,1.06,1.01v10.28l1.86.77V2.78c0-1.53-1.31-2.78-2.92-2.78H2.92C1.31,0,0,1.25,0,2.78v23.57c0,1.53,1.31,2.78,2.92,2.78h12.77l-1.68-1.77H2.92Z"
                  />
                  <path
                    class="pricingCls"
                    d="m23.73,23.32c0,.97-.51,1.58-1.52,1.72v.86h-.81v-.86c-.64-.09-1.22-.44-1.57-.94l.61-.57c.39.46.85.68,1.36.68.77,0,1.11-.28,1.11-.8,0-.46-.3-.67-1.2-.96-1.14-.37-1.68-.68-1.68-1.7,0-.88.59-1.43,1.37-1.57v-.87h.81v.87c.54.09.97.36,1.35.78l-.56.62c-.38-.36-.71-.58-1.26-.58-.62,0-.89.28-.89.67s.21.59,1.16.9c1.08.36,1.72.67,1.72,1.75Z"
                  />
                </g>
              </g>
            </svg>
            <p
              class="sidemenu__label pricing transition-all duration-500 ease-in-out"
              :class="collapse ? 'opacity-0' : 'opacity-100 delay-500'"
            >
              Calculator
            </p>
          </div>
        </NuxtLink>
        <!-- Agent Book -->
        <NuxtLink
          v-if="hideText($config.public.workflow)"
          to="/agent-book"
          class="sidemenu__link mb-2.5"
          :class="route.name === 'agent-book' ? 'activeRoute' : ''"
          data-theme-bg="bg-gray-1000"
          data-theme-text="text-blue-agentBookLight"
          data-theme-circle="bg-blue-agentBookLight"
          data-index="6"
          @click.native="closeSiglePost()"
        >
          <div class="link__title">
            <svg
              id="Layer_2"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 63.77 55.78"
              class="fill-current w-5 h-5 text-blue-agentBookLight ml-0.5"
            >
              <defs>
                <!-- <style>
                  .cls-1 {
                    fill: #fff;
                    stroke-width: 0px;
                  }
                </style> -->
              </defs>
              <g id="Layer_2-2">
                <path
                  class="cls-1"
                  d="m25.38,26.14c-6.73,2.03-9.97,7.32-10.66,14.13-.26,2.62-.18,5.29-.11,7.94.16,5.48,1.67,7.13,7.05,7.47,2.97.19,5.96.03,8.94.03h.21c4.62-.32,9.07-.05,13.66-.63,1.4-.18,3.21-1.25,3.87-2.44,2.39-4.27.06-18.48-3.3-22.18-5.53-6.09-12.5-6.47-19.66-4.31Z"
                />
                <path
                  class="cls-1"
                  d="m31.91,22.45c6.51,0,11.17-4.72,11.09-11.24C42.93,4.97,37.7-.17,31.64,0c-5.75.17-11.04,5.51-11.1,11.22-.07,6.08,5.14,11.21,11.38,11.22Z"
                />
                <path
                  class="cls-1"
                  d="m17.95,28.11c-7-5.02-15.99-3.03-17.22,3.66-.94,5.12-.89,10.57-.36,15.79.52,5.11,5.08,4.16,8.65,4.29,4.04.14,3.86-2.68,3.84-5.43-.03-6.46-.53-13.04,5.09-18.31Z"
                />
                <path
                  class="cls-1"
                  d="m63.77,35.71c.12-4.16-1.19-7.65-5.1-9.41-4.11-1.85-8.29-1.85-12.71,1.52,4.99,5.57,5.64,11.91,5.3,18.46-.17,3.32.04,5.79,4.5,5.68,5.59-.14,7.74-1.37,7.95-5.82.16-3.47-.04-6.95.06-10.42Z"
                />
                <path
                  class="cls-1"
                  d="m53.19,22.43c3.95.03,7.42-3.21,7.51-7.01.09-3.75-3.3-7.34-7.11-7.52-4.17-.2-7.63,3.14-7.64,7.37,0,4.04,3.12,7.14,7.24,7.17Z"
                />
                <path
                  class="cls-1"
                  d="m10.6,22.43c4.06-.02,7.28-3.12,7.34-7.08.06-3.86-3.22-7.31-7.09-7.45-4.15-.15-7.52,3.21-7.47,7.46.04,4.21,2.99,7.09,7.22,7.08Z"
                />
              </g>
            </svg>
            <p
              class="sidemenu__label agent-book transition-all duration-500 ease-in-out"
              :class="collapse ? 'opacity-0' : 'opacity-100 delay-500'"
            >
              Agent Book
            </p>
          </div>
        </NuxtLink>
        <NuxtLink
          v-if="hideText($config.public.workflow)"
          :to="hubRoute"
          class="sidemenu__link mb-2.5"
          :class="route.matched[0].name === 'source' ? 'activeRoute' : ''"
          data-theme-bg="bg-gray-1000"
          data-theme-text="text-blue-200"
          data-theme-circle="bg-blue-200"
          data-index="7"
          @click.native="closeSiglePost()"
        >
          <div class="link__title">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="25"
              height="25"
              class="mb-0.5 -ml-px transform -translate-y-px"
              viewBox="0 0 30 27"
            >
              <g id="social-tool" transform="translate(-42.001 -406)">
                <path
                  id="social-media-posting-tool-icon"
                  d="M229.15,244.656A4.089,4.089,0,0,0,224.908,241H203.413a4.145,4.145,0,0,0-2.977,1.167,4.062,4.062,0,0,0-1.236,2.925q0,6.767,0,13.534a4.064,4.064,0,0,0,4.093,3.963h2.352c.31,0,.37.109.37.386v3.567a1.806,1.806,0,0,0,.06.466,1.4,1.4,0,0,0,1.012.951,1.41,1.41,0,0,0,1.34-.386c1.881-1.6,3.753-3.2,5.634-4.785a.826.826,0,0,1,.49-.178h9.466a10.29,10.29,0,0,0,1.661,0,4.045,4.045,0,0,0,3.492-4.062q0-5.707,0-11.394A20.067,20.067,0,0,0,229.15,244.656Zm-13.439,11.889a1.294,1.294,0,0,1-1.3,1.288H205a1.294,1.294,0,0,1-1.291-1.288V246.251A1.294,1.294,0,0,1,205,244.963h9.416a1.294,1.294,0,0,1,1.3,1.288Zm9.006.9a.388.388,0,0,1-.39.386h-6.234a.382.382,0,0,1-.272-.114.375.375,0,0,1-.108-.273v-2.2a.374.374,0,0,1,.108-.273.382.382,0,0,1,.272-.114h6.234a.388.388,0,0,1,.39.386Zm0-4.954a.388.388,0,0,1-.39.386h-6.234a.382.382,0,0,1-.272-.114.374.374,0,0,1-.108-.273v-2.2a.375.375,0,0,1,.108-.273.382.382,0,0,1,.272-.114h6.234a.388.388,0,0,1,.39.386Zm0-4.954a.388.388,0,0,1-.39.386h-6.234a.382.382,0,0,1-.272-.114.375.375,0,0,1-.108-.273v-2.2a.374.374,0,0,1,.108-.273.382.382,0,0,1,.272-.114h6.234a.388.388,0,0,1,.39.386Z"
                  transform="translate(-157.199 165.003)"
                  :fill="
                    route.matched[0].name === 'source' ? '#fff' : '#4A71D4'
                  "
                />
              </g>
            </svg>
            <p
              class="sidemenu__label source pb-2 transition-all duration-500 ease-in-out"
              :class="collapse ? 'opacity-0' : 'opacity-100 delay-500'"
            >
              Source
            </p>
          </div>
        </NuxtLink>

        <div class="flex-grow w-full bg-offgray spacer" data-index="14"></div>
        <!-- <div class="flex-grow w-full bg-offgray spacer" data-index="9"></div> -->

        <div
          class="flex flex-col justify-between w-full bg-offgray spacer"
          data-index="14"
        >
          <!-- Settings -->
          <NuxtLink
            to="/setting/alert"
            class="sidemenu__link mb-2.5"
            :class="route.matched[0].name === 'setting' ? 'activeRoute' : ''"
            data-theme-bg="bg-gray-1000"
            data-theme-text="text-[#FFFFFF]"
            data-theme-circle="bg-white"
            data-index="4"
            @click.native="closeSiglePost()"
          >
            <div class="link__title link__title_setting">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                width="25"
                height="25"
                class="mb-0.5 -ml-px transform -translate-y-px"
                viewBox="0 0 38.998 40.096"
              >
                <defs>
                  <filter
                    id="a"
                    x="0"
                    y="0"
                    width="38.998"
                    height="40.096"
                    filterUnits="userSpaceOnUse"
                  >
                    <feOffset dy="3" input="SourceAlpha" />
                    <feGaussianBlur stdDeviation="1.5" result="b" />
                    <feFlood flood-opacity="0.302" />
                    <feComposite operator="in" in2="b" />
                    <feComposite in="SourceGraphic" />
                  </filter>
                </defs>
                <g class="b" transform="matrix(1, 0, 0, 1, 0, 0)">
                  <path
                    class="alert_setting fill-[#C2C2C2]"
                    d="M29.244,18.51a5.952,5.952,0,0,1,2.184-8.081L28.366,5.135a5.949,5.949,0,0,1-2.977.777A5.9,5.9,0,0,1,21.2,4.19,5.9,5.9,0,0,1,19.462,0H13.376A6,6,0,0,1,12.6,2.981,5.934,5.934,0,0,1,4.5,5.135L1.43,10.429a5.9,5.9,0,0,1,2.184,2.157A5.921,5.921,0,0,1,1.43,20.667l3.043,5.294a5.747,5.747,0,0,1,2.946-.777A5.892,5.892,0,0,1,13.31,31.1h6.121a5.777,5.777,0,0,1,.777-2.946,5.922,5.922,0,0,1,8.08-2.157L31.331,20.7a6.328,6.328,0,0,1-2.087-2.188Zm-12.825,3.3a6.254,6.254,0,1,1,4.439-1.826,6.277,6.277,0,0,1-4.439,1.826Z"
                    transform="translate(3.07 1.5)"
                  />
                </g>
              </svg>

              <p
                class="sidemenu__label setting pb-2 transition-all duration-500 ease-in-out"
                :class="collapse ? 'opacity-0' : 'opacity-100 delay-500'"
              >
                Setting
              </p>
            </div>
          </NuxtLink>
          <!-- Settings -->
          <NuxtLink
            to="/settings/services"
            class="sidemenu__link mb-2.5"
            :class="route.matched[0].name === 'settings' ? 'activeRoute' : ''"
            data-theme-bg="bg-gray-1000"
            data-theme-text="text-yellow-midlight"
            data-theme-circle="bg-yellow-midlight"
            data-index="4"
            @click.native="closeSiglePost()"
          >
            <div class="link__title">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                width="25"
                height="25"
                class="mb-0.5 -ml-px transform -translate-y-px"
                viewBox="0 0 38.998 40.096"
              >
                <defs>
                  <filter
                    id="a"
                    x="0"
                    y="0"
                    width="38.998"
                    height="40.096"
                    filterUnits="userSpaceOnUse"
                  >
                    <feOffset dy="3" input="SourceAlpha" />
                    <feGaussianBlur stdDeviation="1.5" result="b" />
                    <feFlood flood-opacity="0.302" />
                    <feComposite operator="in" in2="b" />
                    <feComposite in="SourceGraphic" />
                  </filter>
                </defs>
                <g class="b" transform="matrix(1, 0, 0, 1, 0, 0)">
                  <path
                    class="a"
                    d="M29.244,18.51a5.952,5.952,0,0,1,2.184-8.081L28.366,5.135a5.949,5.949,0,0,1-2.977.777A5.9,5.9,0,0,1,21.2,4.19,5.9,5.9,0,0,1,19.462,0H13.376A6,6,0,0,1,12.6,2.981,5.934,5.934,0,0,1,4.5,5.135L1.43,10.429a5.9,5.9,0,0,1,2.184,2.157A5.921,5.921,0,0,1,1.43,20.667l3.043,5.294a5.747,5.747,0,0,1,2.946-.777A5.892,5.892,0,0,1,13.31,31.1h6.121a5.777,5.777,0,0,1,.777-2.946,5.922,5.922,0,0,1,8.08-2.157L31.331,20.7a6.328,6.328,0,0,1-2.087-2.188Zm-12.825,3.3a6.254,6.254,0,1,1,4.439-1.826,6.277,6.277,0,0,1-4.439,1.826Z"
                    transform="translate(3.07 1.5)"
                  />
                </g>
              </svg>

              <p
                class="sidemenu__label settings pb-2 transition-all duration-500 ease-in-out"
                :class="collapse ? 'opacity-0' : 'opacity-100 delay-500'"
              >
                Settings
              </p>
            </div>
          </NuxtLink>

          <!-- Help -->
          <NuxtLink
            to="/help"
            class="sidemenu__link"
            :class="route.name === 'help' ? 'activeRoute' : ''"
            data-theme-bg="bg-gray-1000"
            data-theme-text="text-yellow-midlight"
            data-theme-circle="bg-yellow-midlight"
            data-index="4"
            @click.native="closeSiglePost()"
          >
            <div class="link__title">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                width="25"
                height="25"
                viewBox="0 0 39 40"
                class="mb-0.5 -ml-px transform -translate-y-px"
              >
                <defs>
                  <filter
                    id="a"
                    x="0"
                    y="0"
                    width="39"
                    height="40"
                    filterUnits="userSpaceOnUse"
                  >
                    <feOffset dy="3" input="SourceAlpha" />
                    <feGaussianBlur stdDeviation="1.5" result="b" />
                    <feFlood flood-opacity="0.302" />
                    <feComposite operator="in" in2="b" />
                    <feComposite in="SourceGraphic" />
                  </filter>
                </defs>
                <g class="b" transform="matrix(1, 0, 0, 1, 0, 0)">
                  <path
                    class="a"
                    d="M15,0A15.256,15.256,0,0,0,0,15.5,15.256,15.256,0,0,0,15,31,15.256,15.256,0,0,0,30,15.5,15.256,15.256,0,0,0,15,0Zm.851,22.862a1.171,1.171,0,0,1-1.515.153A1.263,1.263,0,0,1,13.9,21.51a1.192,1.192,0,0,1,1.342-.742,1.23,1.23,0,0,1,.967,1.215,1.261,1.261,0,0,1-.352.88Zm.461-5.7h0s-.086.066-.086.139v.973a1.251,1.251,0,0,1-2.5,0V17.3a2.808,2.808,0,0,1,1.781-2.461A1.766,1.766,0,0,0,14.97,11.4a1.685,1.685,0,0,0-1.5,1,1.2,1.2,0,0,1-.7.608,1.158,1.158,0,0,1-.91-.081,1.279,1.279,0,0,1-.544-1.624,4.085,4.085,0,0,1,3.664-2.418H15a4.171,4.171,0,0,1,3.979,3.572A4.281,4.281,0,0,1,16.312,17.159Z"
                    transform="translate(4.5 1.5)"
                  />
                </g>
              </svg>

              <p
                class="sidemenu__label help pb-2 transition-all duration-500 ease-in-out"
                :class="collapse ? 'opacity-0' : 'opacity-100 delay-500'"
              >
                Help
              </p>
            </div>
          </NuxtLink>
          <div class="h-10 w-full bg-offgray spacer" data-index="0"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
import { useHideText } from '~/composables/feeds/useHideText'
import SharpArchiveLogo from './SharpArchiveLogo.vue'

const emit = defineEmits<{
  (event: 'squeeze', value: boolean): void
  (event: 'notsqueeze', value: boolean): void
}>()
const nuxtApp = useNuxtApp()
const store = useStore()
const route = useRoute()
const { hideText } = useHideText()

const offsetTopPx = ref<number | string>(20)
const activeLinkIndex = ref<number | string>(-1)
const circleBackgroundColor = ref<number | string>('bg-orange-dark')
const backgroundColor = ref<number | string>('bg-ash-default')
const textBackgroundColor = ref<number | string>('text-ash-default')
const collapse = ref<number | boolean>(true)

const windowHeight = ref<number>(window.innerHeight)

const user = computed(() => store.state.auth.user)

watch(windowHeight, (newHeight, oldHeight) => {
  if (
    newHeight > oldHeight ||
    newHeight < oldHeight ||
    oldHeight == newHeight
  ) {
    setTimeout(async () => {
      animate()
    }, 1000)
  }
})

watch(
  () => route.name,
  (to, from) => {
    setTimeout(() => {
      nextTick(() => {
        animate()
      })
    })
    if (to === 'home') {
      nuxtApp.$bus.$emit('clearDatePicker')
    }
  },
)
const sideBarAccountItems = computed(
  () => store.state.social.sideBarAccountItems,
)
const hubRoute = computed(() => {
  if (sideBarAccountItems.value && sideBarAccountItems.value.length > 0) {
    return `/source/hub/socials/${sideBarAccountItems.value[0].username}/all`
  } else {
    return `/source/hub`
  }
})
onMounted(() => {
  window.addEventListener('resize', handleResize)
  animate()
})
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

const handleResize = () => {
  windowHeight.value = window.innerHeight
}
const closeSiglePost = () => {
  store.dispatch('socialFeed/singleImagePostClose', false)
  store.dispatch('socialFeed/singlePostClose', false)
  store.commit('home/EXPAND_FULL_IMAGE', false)
  store.commit('socialFeed/SET_SHOW_TWITTER', true)
  store.commit('home/RESET_TWITTER_COMMENTS')
}

const closeSidebar = () => {
  store.commit('agentBook/SET_SHOW_USER_INFO', false)
  store.commit('agentBook/SET_AGENT_SHOW_HIDE', false)
  store.commit('home/SET_SELECT_ACCESS_TYPE', 'SelectedAccessType')
  store.commit('archive/SET_BULK_DOWNLOAD_SIDEBAR', false)
  store.commit('profile/SET_PROFILE_MODAL', false)
  store.commit('search/SET_SAVE_SAERCH_MODAL', false)

  if (user.value.paymentStatus !== 'pending') {
    store.commit('account/SET_ACH_BANK_VERIFICATION', false)
  }

  store.commit('socialFeed/SHOW_SOCIAL_EDIT_FEED_MODAL', {
    open: false,
    data: {},
  })
  store.commit('setting/SET_SHOW_CREATE_GROUP', false)
  store.commit('setting/SET_SHOW_EDIT_GROUP', false)
  store.dispatch('showArchiveSettings', false)
  store.commit('setting/SET_SHOW_PROFILE', false)
  store.dispatch('header/removePayment')
  store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false)
  store.commit('notifications/SHOW_NOTIFICATION_MODAL', false)
  store.commit('setting/SET_CHANGE_BILLING_INFO', false)
  store.commit('setting/SET_SHOW_INVOICE', false)
  store.commit('SET_SHOW_SIDE_BAR', false)
}

const animate = () => {
  const activeLink = document.querySelector(
    '.activeRoute',
  ) as HTMLElement | null
  const oldPreviousLink = document.querySelector(
    '.rounded-br-2xl',
  ) as HTMLElement | null
  const oldNextLink = document.querySelector(
    '.rounded-tr-2xl',
  ) as HTMLElement | null

  oldPreviousLink?.classList.remove('rounded-br-2xl')
  oldNextLink?.classList.remove('rounded-tr-2xl')

  if (activeLink) {
    const backgroundColorData = activeLink.dataset.themeBg
    const circleBackgroundColorData = activeLink.dataset.themeCircle
    const activeLinkIndexData = activeLink.dataset.index

    if (
      backgroundColorData &&
      circleBackgroundColorData &&
      activeLinkIndexData
    ) {
      backgroundColor.value = backgroundColorData
      textBackgroundColor.value = 'text-gray-1000'
      circleBackgroundColor.value = circleBackgroundColorData
      activeLinkIndex.value = parseInt(activeLinkIndexData)
      offsetTopPx.value = activeLink.offsetTop
    }
  }
}

const squeeze = () => {
  collapse.value = false
  setTimeout(() => {
    store.commit('SET_IS_SHOW_SIDE_BAR', false)
  }, 500)
}

const notSqueeze = () => {
  collapse.value = true
  setTimeout(() => {
    store.commit('SET_IS_SHOW_SIDE_BAR', true)
  }, 300)
}
</script>

<style lang="scss" scoped>
@import url('https://fonts.googleapis.com/css2?family=Neuton');
.a {
  fill: #cecece;
}
.b {
  filter: url(#a);
}
.neuton-font {
  font-family: 'Neuton', serif;
}
.sidemenu__label {
  color: #8e8e8e;
}
.sidebar {
  width: 100px;
  min-width: 100px;
  overflow: hidden;
  z-index: 300;
  background-color: #171d26;
  // filter: blur(10px);
  // -webkit-filter: blur(10px);

  &__header {
    height: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &:hover {
    // width: 200px;
    // min-width: 200px;

    // .sidemenu__label {
    //   opacity: 1;
    // }

    .sidemenu__link {
      &.bg-gradient {
        background: linear-gradient(
          to right,
          rgba(33, 33, 33, 1) 20%,
          rgba(255, 255, 255, 0)
        );
      }
    }
  }
}

.sidemenu {
  height: calc(100% - 150px);
  overflow-y: hidden;
  overflow-x: hidden;
  position: relative;
  /* this will hide the scrollbar in mozilla based browsers */
  overflow: -moz-scrollbars-none;
  /* this will hide the scrollbar in internet explorers */
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: none; /* Firefox 64 */

  &::-webkit-scrollbar {
    width: 0;
    display: none;
  }
  .background {
    opacity: 1;
    // backdrop-filter: blur(6px);
    /* Add the blur effect */
    // filter: blur(1px);
    // -webkit-filter: blur(1px);
    background-color: #393e46; /* Fallback color */
    background-color: #393e46; /* Black w/opacity/see-through */
    position: absolute;
    left: 27px;
    z-index: 10;
    width: calc(100% - 27px);
    transition: margin-top 0.5s ease-in-out;
    height: 40px;
    @apply flex rounded-l-full;

    .background__circle {
      opacity: 1;
      width: 40px;
      height: 40px;
      // left: 27px;
      position: relative;
      z-index: 50;
      transition: all 0.5s ease-in-out;
      @apply rounded-full inline-block;
    }

    .corner-top {
      opacity: 1 !important;
      position: absolute;
      top: -20px;
      right: 0;
      display: inline-block;
      color: #393e46;
    }

    .corner-bottom {
      opacity: 1 !important;
      position: absolute;
      bottom: -20px;
      right: 0;
      transform: rotate(270deg);
      display: inline-block;
      color: #393e46;
    }
  }

  .activeRoute {
    position: relative;
    .link__title {
      > svg {
        @apply text-white;
      }
    }
  }

  .router-link-exact-active .home {
    color: #e4801d;
  }

  .router-link-exact-active .archive {
    color: #8db230;
  }

  .router-link-exact-active .search {
    color: #7d80bd;
  }

  .router-link-exact-active .pricing {
    color: #a22a2a;
  }
  .router-link-exact-active .agent-book {
    color: #5f9fc7;
  }
  .router-link-exact-active .alert {
    color: #ff5959;
  }

  .activeRoute .settings,
  .router-link-exact-active .help {
    color: #e0ad1f;
  }

  .activeRoute .setting {
    color: #fff;
  }

  .activeRoute {
    position: relative;
    .link__title_setting {
      > svg .alert_setting {
        @apply fill-[#525252];
      }
    }
  }

  .activeRoute .source {
    color: #4a71d4;
  }

  &__link {
    outline: none;
    // transition: background 0.2s ease-in;
    // transition: border-top-right-radius .5s ease-in, border-bottom-right-radius .5s ease-in;
    //@apply flex bg-offgray;

    .link__title {
      margin-left: 25px;
      z-index: 100;
      @apply p-2.5 flex w-full items-center relative;

      > svg {
        transition: all 0.5s ease-in-out;
      }
    }
    // &.bg-gradient {
    //   // background: transparent;
    //   background: linear-gradient(
    //     to right,
    //     rgba(33, 33, 33, 1) 50%,
    //     rgba(255, 255, 255, 0) 70%,
    //     transparent
    //   );
    // }
    // &::before {
    //   content: '';
    //   opacity: 1;
    //   width: 45px;
    //   height: 100%;
    //   // left: -27px;
    //   position: absolute;
    //   // z-index: 10;
    //   //@apply bg-offgray;
    // }
  }

  &__label {
    font-size: 20px;
    position: absolute;
    transform: translate(45px, -1px);
    // opacity: 0;
    white-space: nowrap;
    // transition: all 0.5s ease-in;
    // transition-delay: 0.5s;
  }
}
.width-200 {
  width: 200px;
  min-width: 200px;
}
.price-icon {
  color: #ffd6d6;
}
</style>
