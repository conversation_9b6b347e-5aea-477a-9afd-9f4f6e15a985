<script setup lang="ts">
interface DropsDownProps {
  placeholder?: string
  options: Record<string, any>[]
  labelKey?: string
  subTextKey?: string
  mailLabelKey?: string
  mailLabelColor?: string
  idKey?: string
  menuWidth?: number | '100%'
  menuHeight?: number
  menuBgColor?: string
  menuTextColor?: string
  dropsdownTextColor?: string
  arrowColor?: string
  dropdownWidth?: number | '100%'
  dropdownBgColor?: string
  dropdownMaxHeight?: number
  scrollbarTrackColor?: string
  scrollbarThumbColor?: string
  dorpdownPlaceholder?: boolean
  hoverColor?: string
  hoverTextColor?: string
  showEventSlot?: boolean
  depth?: number
  disabled?: boolean
  selectedOptionClass?: string
  triggerBtnClass?: string
  showSelectedIcon?: boolean
  showArrow?: boolean
  dropdownPosition?: string
  hideArrow?: boolean
  zIndex?: number
  paddingRight?: string
  isOpenMenu?: boolean
}

type OptionType = Record<string, any> | null

const props = withDefaults(defineProps<DropsDownProps>(), {
  placeholder: 'Select an option',
  options: () => [],
  labelKey: 'label',
  subTextKey: 'subtext',
  mailLabelKey: 'headerLabel',
  mailLabelColor: '#4A71D4',
  idKey: 'id',
  menuWidth: 200,
  menuHeight: 40,
  menuBgColor: '#fff',
  menuTextColor: '#525252',
  dropsdownTextColor: '#525252',
  arrowColor: '#4A71D4',
  dropdownWidth: 250,
  dropdownBgColor: '#fff',
  dropdownMaxHeight: 200,
  scrollbarTrackColor: '#a1cdff50',
  scrollbarThumbColor: '#a1cdff',
  dorpdownPlaceholder: false,
  hoverColor: '#F1F2F6',
  hoverTextColor: '#333333',
  showEventSlot: false,
  depth: 0,
  disabled: false,
  selectedOptionClass: '',
  triggerBtnClass: '',
  showSelectedIcon: false,
  showArrow: false,
  dropdownPosition: 'absolute',
  hideArrow: false,
  zIndex: 10,
  paddingRight: '14',
  isOpenMenu: undefined,
})

const isOpen = ref(false)
const model = defineModel<OptionType | string | null>({ default: null })
const dropdownRef = ref<HTMLDivElement | null>(null)
const emit = defineEmits(['change', 'open', 'activate'])

const selectedOption = computed(() => model.value)

const toggleDropdown = () => {
  if (!props.disabled) {
    isOpen.value = !isOpen.value
    emit('open', isOpen.value)
    emit('activate')
  }
}

const selectOption = (option: OptionType) => {
  if (!props.disabled) {
    model.value = option
    isOpen.value = false
    emit('change', option)
    emit('open', isOpen.value)
  }
}

const handleClickOutside = (event: Event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false
    emit('open', isOpen.value)
  }
}

watch(
  () => props.isOpenMenu,
  (newVal) => {
    if (newVal !== undefined) {
      isOpen.value = newVal
    }
  },
  { immediate: true },
)

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div ref="dropdownRef" class="relative">
    <!-- Dropdown button -->
    <div
      tabindex="0"
      class="dropdown_button flex items-center justify-between px-4 py-2 rounded-full transition-all font-semibold"
      :class="[triggerBtnClass, disabled ? 'opacity-50' : 'cursor-pointer']"
      :style="{
        color: menuTextColor,
        width: `${menuWidth === '100%' ? '100%' : `${menuWidth}px`}`,
        height: `${menuHeight}px`,
        backgroundColor: menuBgColor,
      }"
      @click="toggleDropdown"
    >
      <span
        class="line-clamp-1 mr-1"
        :class="[selectedOption ? selectedOptionClass : 'placeholder']"
        >{{ selectedOption ? selectedOption[labelKey] : placeholder }}</span
      >
      <SharedIconHubMicrosoftArrowIcon
        v-if="showArrow && !hideArrow"
        class="w-2 h-1"
      />
      <ClientOnly v-else-if="!showArrow && !hideArrow">
        <fa
          class="text-xl transition-all duration-300 ease-in-out"
          :class="[isOpen ? 'rotate-180' : '']"
          :style="{ color: disabled ? '#9ca3af' : arrowColor }"
          :icon="['fas', 'caret-down']"
        />
      </ClientOnly>
    </div>

    <!-- Dropdown menu -->
    <div
      v-if="isOpen && !disabled && (options.length > 0 || showEventSlot)"
      class="mt-1 rounded-lg z-10 h-auto pb-3 dropdown_menu_container"
      :style="{ zIndex: zIndex }"
      :class="[dropdownPosition]"
    >
      <div
        class="py-2 rounded-lg shadow-xl overflow-y-auto dropdown_menu dropdown-scroll h-full"
        :style="{
          color: dropsdownTextColor,
          width: `${dropdownWidth === '100%' ? '100%' : `${dropdownWidth}px`}`,
          backgroundColor: dropdownBgColor,
          maxHeight: `${dropdownMaxHeight}px`,
          '--scrollbar-track-color': props.scrollbarTrackColor,
          '--scrollbar-thumb-color': props.scrollbarThumbColor,
          '--hovercolor': hoverColor,
          '--hovertextcolor': hoverTextColor,
        }"
      >
        <div
          v-if="dorpdownPlaceholder"
          class="px-4 py-2 cursor-pointer transition-all duration-200 opacity-80 dropdown-palaceholder"
          @click="selectOption(null)"
        >
          {{ placeholder }}
        </div>
        <slot v-if="showEventSlot" name="event" />
        <BaseDropdownOption
          v-for="option in options"
          :key="option[idKey] || JSON.stringify(option)"
          :option="option"
          :labelKey="labelKey"
          :subTextKey="subTextKey"
          :mailLabelKey="mailLabelKey"
          :mailLabelColor="mailLabelColor"
          :idKey="idKey"
          :hoverColor="hoverColor"
          :hoverTextColor="hoverTextColor"
          :padding-right="paddingRight"
          @select="selectOption"
        >
          <template #beforeTick>
            <template v-if="showSelectedIcon">
              <fa
                v-if="selectedOption && selectedOption[idKey] === option[idKey]"
                :icon="['fas', 'check']"
                class="text-base mr-0"
              />
              <div v-else class="w-[14px] inline-block" />
            </template>
          </template>
        </BaseDropdownOption>
        <!-- <div
          v-for="option in options"
          :key="option[props.idKey] || JSON.stringify(option)"
          class="px-4 py-2 cursor-pointer option transition-all duration-200"
          :style="{
            '--hovercolor': hoverColor,
            '--hovertextcolor': hoverTextColor,
          }"
          @click="selectOption(option)"
        >
          {{ option[props.labelKey] || '[Missing Label]' }}
        </div> -->
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.gray-placeholder .placeholder {
  color: #707070;
}
.dropdown-palaceholder:hover {
  background-color: var(--hovercolor);
  color: var(--hovertextcolor);
}
.dropdown-scroll {
  overflow-y: auto;
  overflow-x: auto;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-color) var(--scrollbar-track-color);

  &::-webkit-scrollbar {
    width: 4px !important;
    height: 4px !important;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background: var(--scrollbar-track-color);
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: var(--scrollbar-thumb-color);
  }
}
@media (max-width: 767px) {
  .dropdown-scroll {
    &::-webkit-scrollbar {
      width: 3px;
      height: 3px;
    }
  }
}
.language-dropdown > div {
  font-weight: 400;
}
.option:hover {
  background-color: var(--hovercolor);
  color: var(--hovertextcolor);
}
.dropdown > .dropdown_button {
  width: 100% !important;
}
.dropdown > .dropdown_menu_container {
  width: 100% !important;
}
.dropdown > .dropdown_menu_container > .dropdown_menu {
  width: 100% !important;
}
.select-dropdown > .dropdown_button {
  font-weight: 400 !important;
}
.select-dropdown > .dropdown_button:focus {
  background-color: #e3efff !important;
  color: #707070 !important;
  font-weight: 400 !important;
}
</style>
