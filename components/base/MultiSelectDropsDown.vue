<template>
  <div ref="dropdownRef" class="relative w-min">
    <button
      @click="toggleDropdown"
      class="flex items-center justify-between font-semibold px-4 py-1"
      :class="buttonClass"
    >
      <p class="truncate text-left pr-2">
        <span v-if="displayText && showDisplayText">{{ displayText }}</span>
        <span v-else>{{ placeholder }}</span>
      </p>
      <ClientOnly>
        <fa
          class="text-xl"
          :style="{ color: arrowColor }"
          :icon="['fas', isOpen ? 'caret-up' : 'caret-down']"
      /></ClientOnly>
    </button>

    <!-- Dropdown menu -->
    <div
      v-if="isOpen"
      class="absolute z-10 mt-1 rounded-lg h-auto pb-3"
      :class="dropdownMainClass"
    >
      <div
        class="py-2 rounded-lg shadow-xl overflow-y-auto dropdown-scroll"
        :class="dropdownClass"
      >
        <div v-if="dorpdownPlaceholder" class="px-4 py-2 opacity-60">
          {{ placeholder }}
        </div>
        <div
          v-for="option in preparedOptions"
          :key="getOptionId(option)"
          class="flex items-center px-4 py-1.5 hover:bg-[#F1F2F6] cursor-pointer space-x-2"
          :class="[
            option.disabled ? 'cursor-not-allowed opacity-50' : optionClass,
          ]"
          @click="!option.disabled && toggleOption(option)"
        >
          <InputsCheckBoxInput
            :id="getOptionId(option)"
            v-model="option.checked"
            :checkColor="option.disabled ? '#333333' : checkColor"
            :disabled="option.disabled"
            @update:modelValue="() => toggleOption(option)"
          />

          <slot name="option" :option="option">
            <span class="whitespace-nowrap">{{ getOptionLabel(option) }}</span>
          </slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface OptionType {
  [key: string]: any
}
const emit = defineEmits<{
  (e: 'change', option: OptionType): void
  (e: 'toggle', isOpen: boolean): void
}>()
const props = defineProps({
  options: {
    type: Array as () => OptionType[],
    required: true,
    default: () => [],
  },
  idKey: {
    type: String,
    default: 'id',
  },
  labelKey: {
    type: String,
    default: 'label',
  },
  arrowColor: {
    type: String,
    default: 'white',
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  dorpdownPlaceholder: {
    type: Boolean,
    default: true,
  },
  placeholder: {
    type: String,
    default: 'Select an option',
  },
  checkColor: {
    type: String,
    default: '#4A71D4',
  },
  buttonClass: {
    type: String,
    default: 'w-[240px] h-[35px] text-white bg-[#4A71D4] rounded-full',
  },
  dropdownMainClass: {
    type: String,
    default: '',
  },
  dropdownClass: {
    type: String,
    default:
      'w-[240px] max-h-[260px] bg-white rounded-md shadow-lg text-[#525252]',
  },
  optionClass: {
    type: String,
    default: '',
  },
  scrollbarThumbColor: {
    type: String,
    default: '#a1cdff',
  },
  scrollbarTrackColor: {
    type: String,
    default: '#a1cdff50',
  },
  showDisplayText: {
    type: Boolean,
    default: true,
  },
})

const model = defineModel<OptionType[]>({ default: () => [] })

const isOpen = ref(false)
const dropdownRef = ref<HTMLDivElement | null>(null)
const selectedOptions = ref<OptionType[]>([])

const getOptionId = (option: OptionType): string => {
  return String(option[props.idKey] || '')
}

const getOptionLabel = (option: OptionType): string => {
  return String(option[props.labelKey] || 'helloi')
}

const preparedOptions = computed(() => {
  return props.options.map((option) => {
    return {
      ...option,
      checked:
        option.checked === true || option.checked === false
          ? option.checked
          : isSelected(option),
    }
  })
})

watch(
  () => model.value,
  (newVal) => {
    selectedOptions.value = [...newVal]
  },
  { immediate: true },
)

const displayText = computed(() => {
  if (selectedOptions.value.length === 0) return ''

  if (selectedOptions.value.length === 1) {
    return getOptionLabel(selectedOptions.value[0])
  }
  return selectedOptions.value
    .map((option) => getOptionLabel(option))
    .join(', ')
})

const toggleDropdown = () => {
  isOpen.value = !isOpen.value
  emit('toggle', isOpen.value)
}

const isSelected = (option: OptionType): boolean => {
  return selectedOptions.value.some(
    (item) => getOptionId(item) === getOptionId(option),
  )
}

const toggleOption = (option: OptionType): void => {
  const index = selectedOptions.value.findIndex(
    (item) => getOptionId(item) === getOptionId(option),
  )

  if (index === -1) {
    if (!props.multiple) {
      selectedOptions.value = []
    }
    selectedOptions.value.push({ ...option, checked: true })
  } else {
    selectedOptions.value.splice(index, 1)
  }

  model.value = selectedOptions.value
  emit('change', option)
}

const handleClickOutside = (event: MouseEvent): void => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped lang="scss">
.dropdown-scroll {
  overflow-y: auto;
  overflow-x: auto;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-color: v-bind(scrollbarThumbColor) v-bind(scrollbarTrackColor);

  &::-webkit-scrollbar {
    width: 4px !important;
    height: 4px !important;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background: v-bind(scrollbarTrackColor);
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: v-bind(scrollbarThumbColor);
  }
}
@media (max-width: 767px) {
  .dropdown-scroll {
    &::-webkit-scrollbar {
      width: 3px;
      height: 3px;
    }
  }
}
</style>
