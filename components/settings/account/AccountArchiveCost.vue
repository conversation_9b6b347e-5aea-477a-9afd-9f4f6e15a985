<template>
  <div
    id="billingInfoLarge"
    class="card transition-all duration-500 ease-in-out"
    :class="[
      !expandCollapseFunctionality
        ? 'h-full'
        : archiveCostExpanded
          ? user && user.userPermission !== 'Administrator'
            ? 'h-full'
            : 'expnad-height'
          : 'collapse-height',
      !expandCollapseFunctionality
        ? user && user.userPermission === 'Administrator'
          ? 'w-full'
          : 'w-full'
        : 'w-full',
    ]"
  >
    <div
      class="card-header flex justify-center items-center px-4"
      :class="expandCollapseFunctionality ? 'cursor-pointer' : ''"
      @click="expandCollapseFunctionality ? toggleExpand('ArchiveCost') : ''"
    >
      <h3 class="card-title flex-grow pl-5">Archiving Costs</h3>
      <span
        v-if="expandCollapseFunctionality"
        class="toggle-icon transition-all duration-500 transform"
        :class="archiveCostExpanded ? 'rotate-180' : 'rotate-0'"
      >
        <fa :icon="['fas', 'caret-down']" />
      </span>
    </div>
    <div class="web-billing_height transition-all duration-300 ease-in-out">
      <div class="card-body web-billing_info scroll">
        <table class="table-wrapper min-w-full">
          <!--start => table header section-->
          <thead>
            <tr class="bg-yellow-moreLight sticky top-0 z-1 border-b">
              <th class="table-th">
                <strong>Item</strong>
              </th>
              <th class="table-th">
                <strong>Total Number</strong>
              </th>
              <th class="table-th">
                <strong>Included With Plan</strong>
              </th>
              <th class="table-th">
                <strong>Price Per Item</strong>
              </th>
              <th class="table-th">
                <strong>Amount</strong>
              </th>
            </tr>
          </thead>
          <!--end => table header section-->

          <!--start => table body section :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-default'"-->
          <tbody v-if="billingInfoList" class="body">
            <tr
              v-for="(archiveCost, index) in archiveCostlists"
              :key="index + 'billing-info'"
              class="cursor-pointer tr-border"
            >
              <td class="table-tr">
                <span
                  :class="
                    archiveCost.itemName === 'Monthly Platform Cost'
                      ? 'font-bold'
                      : ''
                  "
                  >{{ archiveCost.itemName }}</span
                >
              </td>
              <td class="table-tr text-right">
                <span>{{ archiveCost.currentNumber }}</span>
              </td>
              <td class="table-tr text-right">
                <span>{{ archiveCost.includedWithPlan }} </span>
              </td>
              <td class="table-tr text-right">
                <span v-if="archiveCost.pricePerItem">
                  ${{ archiveCost.pricePerItem }}
                </span>
              </td>
              <td class="table-tr text-right">
                <span>${{ archiveCost.total }}</span>
              </td>
            </tr>
          </tbody>
          <tbody v-if="billingInfoList" class="body">
            <tr class="cursor-pointer">
              <td class="table-tr font-bold">
                <span>Subtotal</span>
              </td>
              <td class="table-tr text-right"></td>
              <td class="table-tr text-right"></td>
              <td class="table-tr text-right"></td>
              <td class="table-tr text-right font-bold">
                <span
                  >${{
                    monthlyTotal.toLocaleString('en-US', {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })
                  }}</span
                >
              </td>
            </tr>
          </tbody>
          <div class="mt-6"></div>
          <tbody v-if="billingInfoList" class="body">
            <tr class="cursor-pointer">
              <td class="table-tr">
                <span>Storage Usage</span>
              </td>
              <td class="table-tr text-right"></td>
              <td class="table-tr text-right"></td>
              <td class="table-tr text-right"></td>
              <td class="table-tr text-right">
                <span>{{ billingInfoList.storageUsage }} GB</span>
              </td>
            </tr>
          </tbody>
          <tbody v-if="billingInfoList" class="body">
            <tr class="cursor-pointer">
              <td class="table-tr">
                <span
                  >Data Included (20 GB x
                  {{ billingInfoList.storageFeeds }})</span
                >
              </td>
              <td class="table-tr text-right"></td>
              <td class="table-tr text-right"></td>
              <td class="table-tr text-right"></td>
              <td class="table-tr text-right">
                <span>{{ dataIncluded }} GB</span>
              </td>
            </tr>
          </tbody>
          <tbody v-if="billingInfoList" class="body">
            <tr class="cursor-pointer">
              <td class="table-tr font-bold">
                <span>Additional Data Cost</span
                ><i class="block font-normal text-base"
                  >(${{ billingInfoList.storageCost }} Per GB Over Included)</i
                >
              </td>
              <td class="table-tr text-right"></td>
              <td class="table-tr text-right"></td>
              <td class="table-tr text-right"></td>
              <td class="table-tr text-right font-bold">
                <span>${{ billingInfoList.totalStorageCost }}</span>
              </td>
            </tr>
          </tbody>
          <!-- <tbody v-if="billingInfoList" class="body">
            <tr class="cursor-pointer sticky z-1 bg-white top-0 left-0">
              <td class="table-tr font-bold">
                <span>Total</span>
              </td>
              <td class="table-tr text-right"></td>
              <td class="table-tr text-right"></td>
              <td class="table-tr text-right"></td>
              <td class="table-tr text-right font-bold">
                <span>${{ billingInfoList.total }}</span>
              </td>
            </tr>
          </tbody> -->
          <!--end => table body section-->
        </table>
      </div>
      <!-- cart footer-->
      <div
        class="flex items-center justify-end p-4 md:pr-5 py-0 my-4 mt-0 border-t border-solid border-yellow-midlight"
      >
        <div class="text-right w-full md:pt-2 pt-1.5">
          <div v-if="billingInfoList" class="flex justify-between">
            <div
              class="md:pr-4 pr-4 md:pb-2 pb-1.5 whitespace-nowrap text-gray-1200 md:text-lg font-bold"
            >
              <span>Total</span>
            </div>
            <div
              class="md:pl-4 pl-4 md:pb-2 pb-1.5 whitespace-nowrap text-gray-1200 md:text-lg font-bold text-right"
            >
              <span>${{ billingInfoList.total }}</span>
            </div>
          </div>
          <!-- <marquee>
                <p
                  class="
                    text-gray-1200
                    opacity-50
                    lg:mb-3
                    mb-1
                    lg:text-md
                    text-xs
                  "
                >
                  On March 1, 2021 you were billed $44.75 in advance, including
                  today, we have 67% left in the month. If you cancel, we will
                  credit you $29.98.
                </p>
              </marquee>-->
          <!-- <a :href="closeAccountInfo" download> -->
          <!-- <button
            :disabled="downloadProcess"
            class="footer-btn"
            @click="closeAccount()"
          >
            Close Account
          </button> -->
          <!-- </a> -->
          <!-- <button
            v-if="user.userPermission !== 'User'"
            class="footer-btn mx-4"
            @click.stop="changeBilling($event)"
          >
            Change Billing
          </button> -->
          <button class="footer-btn" @click.stop="showInvoiceModal($event)">
            View Invoices
          </button>
        </div>
      </div>
      <!-- cart footer-->
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { BILLING_INFO } from '~/constants/urls'
import { useFetched } from '../../../composables/useFetched'

export default {
  props: {
    expandCollapseFunctionality: {
      type: Boolean,
      default: false,
    },
    userInfoIsExpanded: {
      type: Boolean,
      default: false,
    },
    archiveCostExpanded: {
      type: Boolean,
      default: false,
    },
    downloadProcess: {
      type: Boolean,
      default: false,
    },
    updateBillingInfo: {
      type: Object,
      default: () => {},
    },
    // user: {
    //   type: Object,
    //   default: () => {},
    // },
  },
  setup(props) {
    const { fetch } = useFetched()
    return {
      fetch,
    }
  },
  data() {
    return {
      archiveCostlist: [
        {
          itemName: 'Monthly Platform Cost',
          total: '440.00',
        },
        {
          itemName: 'Social Media',
          currentNumber: 15,
          includedWithPlan: 12,
          pricePerItem: '18.00',
          total: '54.00',
        },
        {
          itemName: 'Email',
          currentNumber: 0,
          includedWithPlan: 0,
          pricePerItem: '10.00',
          total: '0.00',
        },
        {
          itemName: 'Website',
          currentNumber: 1,
          includedWithPlan: 1,
          pricePerItem: '150.00',
          total: '0.00',
        },
      ],
      monthlyTotal: 0,
      storage: '1,099',
      additionalDataCost: '79.90',
      // nextInvoiceAmount: '573.90',
      socialFeedsList: [],
      emailFeedsList: [],
      webFeedsList: [],
      billingInfoList: null,
    }
  },
  computed: {
    user() {
      return this.$store.state.auth.user
    },
    ...mapState('socialFeed', ['socialFeeds']),
    archiveCostlists() {
      this.groupFeedsType()
      this.archiveCostlist.forEach((item) => {
        if (item.itemName === 'Monthly Platform Cost') {
          // item.total = Number(
          //   this.billingInfoList.baseCost +
          //     this.billingInfoList.includedSocials *
          //       this.billingInfoList.socialCost +
          //     this.billingInfoList.includedEmails *
          //       this.billingInfoList.emailCost +
          //     this.billingInfoList.includedEmails * this.billingInfoList.webCost
          // ).toLocaleString('en-US', {
          //   minimumFractionDigits: 2,
          //   maximumFractionDigits: 2,
          // })
          item.total = this.billingInfoList.baseCost
        } else if (item.itemName === 'Social Media') {
          item.currentNumber = this.billingInfoList.socialNumber
          item.includedWithPlan = this.billingInfoList.includedSocials
          item.pricePerItem = this.billingInfoList.socialCost
          item.total = (
            (Number(item.currentNumber) - Number(item.includedWithPlan) > 0
              ? Number(item.currentNumber) - Number(item.includedWithPlan)
              : 0) * Number(item.pricePerItem)
          ).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })
        } else if (item.itemName === 'Email') {
          item.currentNumber = this.billingInfoList.emailNumber
          item.includedWithPlan = this.billingInfoList.includedEmails
          item.pricePerItem = this.billingInfoList.emailCost
          item.total = (
            (Number(item.currentNumber) - Number(item.includedWithPlan) > 0
              ? Number(item.currentNumber) - Number(item.includedWithPlan)
              : 0) * Number(item.pricePerItem)
          ).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })
        } else if (item.itemName === 'Website') {
          item.currentNumber = this.billingInfoList.webNumber
          item.includedWithPlan = this.billingInfoList.includedWebs
          item.pricePerItem = this.billingInfoList.webCost
          item.total = (
            (Number(item.currentNumber) - Number(item.includedWithPlan) > 0
              ? Number(item.currentNumber) - Number(item.includedWithPlan)
              : 0) * Number(item.pricePerItem)
          ).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })
        }
      })
      this.calculateTotal()
      return this.archiveCostlist
    },
    dataIncluded() {
      return 20 * this.billingInfoList.storageFeeds
    },
    nextInvoiceAmount() {
      return this.monthlyTotal + Number(this.additionalDataCost)
    },
  },
  mounted() {
    this.getUserBillingInfo()
    console.log('billing info')
  },
  methods: {
    calculateTotal() {
      // this.monthlyTotal = 0
      // this.archiveCostlist.forEach((item) => {
      //   this.monthlyTotal += Number(item.total)
      // })
      this.monthlyTotal = this.billingInfoList.subtotal
    },
    groupFeedsType() {
      this.socialFeedsList = []
      this.emailFeedsList = []
      this.webFeedsList = []
      for (const item of this.socialFeeds) {
        if (item.provider === 'Microsoft' || item.provider === 'Google') {
          this.emailFeedsList.push(item)
        } else if (item.provider === 'Web') {
          this.webFeedsList.push(item)
        } else {
          this.socialFeedsList.push(item)
        }
      }
    },
    toggleExpand(value) {
      this.$emit('toggle-expand', value)
    },
    closeAccount() {
      this.$emit('close-account')
    },
    changeBilling($event) {
      this.$emit('change-billing', $event)
    },
    showInvoiceModal($event) {
      this.$emit('show-invoice-modal', $event)
    },
    showChangeBillingInfo(billingInfo) {
      this.$emit('show-change-billing-info', billingInfo)
    },
    deleteCard(billingInfoId) {
      this.$emit('delete-card', billingInfoId)
    },
    async getUserBillingInfo() {
      try {
        const response = await this.fetch(BILLING_INFO)
        if (response.success) {
          this.billingInfoList = response.data
          this.$emit('next-billing-date', this.billingInfoList.billingDate)
        }
      } catch (err) {
        console.log(err)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.toggle-icon {
  color: #ffffff;
  svg {
    @apply md:text-3xl text-2xl;
  }
}
#resizeIcon {
  cursor: row-resize;
}
.card-wrapper {
  @apply overflow-y-auto scroll md:pt-4 relative md:space-y-5 space-y-2 h-full;
  // min-height: 900px;
}
.mobile-card-wrapper {
  @apply overflow-y-auto scroll md:pt-4 relative md:space-y-8 space-y-2 h-full;
  // min-height: 700px;
}
.card {
  @apply bg-white rounded-3xl overflow-hidden flex flex-col;
}
.card-header {
  @apply bg-yellow-midlight text-center h-11 py-1.5;
  min-height: 44px;
}
.card-title {
  @apply md:text-white text-offwhite-200 font-bold md:text-xl text-md;
}
.inner-body-height {
  height: calc(100% - 44px);
  @apply flex flex-col;
}
.card-body {
  @apply overflow-auto scroll flex-grow;
}
.userinfo {
  height: calc(100% - 52px);
}
.web-user_info {
  height: calc(100% - 72px);
}
.web-billing_height {
  height: calc(100% - 42px);
}
.web-billing_info {
  height: calc(100% - 102px);
}
.billing_info_height {
  height: calc(100% - 44px);
}
.billing_info {
  height: calc(100% - 84px);
}
.expnad-height {
  height: calc(100% - 44px) !important;
}
.collapse-height {
  height: 44px;
}
.table-th {
  @apply md:px-4 px-4 md:py-2 py-2 md:font-bold md:text-gray-1200 text-yellow-midlight text-right md:text-lg text-md whitespace-nowrap;
}
.table-th:first-child {
  @apply text-left;
}
.table-body {
  @apply flex items-center justify-start md:space-x-5 w-full md:py-1 py-0.5;
}
.table-tr {
  @apply md:px-4 px-4 md:py-2 py-1.5 whitespace-nowrap text-gray-1200 md:text-lg;
}
.tr-border:last-child {
  border-bottom: 1px solid #e0ad1f !important;
}
.action-icon {
  @apply pl-5;
}
.table-tr span {
  @apply text-gray-light xl:text-xl md:text-lg text-md;
}
.card-footer {
  @apply text-right lg:pb-6 pb-2 md:mr-2;
}
.footer-btn {
  @apply focus:outline-none rounded-full
  border-2 border-yellow-midlight py-1 lg:px-5 md:px-2 px-2 text-center text-white
  bg-yellow-midlight font-bold md:text-lg
  text-sm;
}
//Small Device
.mobile-card-body {
  @apply overflow-auto scroll bg-white rounded-2xl py-2 px-2 h-full;
}
.mobile-table-th {
  @apply px-3 py-1 text-left text-yellow-midlight tracking-wider whitespace-nowrap text-xs;
}
.mobile-table-body {
  @apply w-full py-0.5;
}
.mobile-table-td {
  @apply px-3 pl-0 text-gray-900 whitespace-nowrap text-sm;
}
.mb-bill-table-th {
  @apply font-bold text-yellow-midlight text-left w-3/12 break-all text-md;
}
.mb-bill-table-tr {
  @apply text-gray-600 w-3/12 break-all text-xs;
}
.mobile-text-size {
  font-size: 8px;
  text-align: center;
}
.min-width-6 {
  min-width: 6rem;
}
.min-width-8 {
  min-width: 9rem;
}
.min-width-10 {
  min-width: 10rem;
}
.min-width-12 {
  min-width: 12rem;
}
.min-width-16 {
  min-width: 16rem;
}
.min-width-20 {
  min-width: 20rem;
}
.min-width-41 {
  min-width: 55rem;
}
.min-width-55 {
  min-width: 67rem;
}
.min-width-34 {
  min-width: 34rem;
}
.tooltip {
  @apply invisible 
  break-all
  whitespace-normal 
  absolute 
  -top-5 
  left-4 
  z-50 
  bg-yellow-midlight 
  text-white 
  rounded 
  py-1 px-2 
  shadow-lg md:w-40 w-32;
}

.tooltip1 {
  @apply invisible 
  break-all
  whitespace-normal 
  -top-5 
  left-5 
  z-50 
  absolute 
  bg-yellow-midlight 
  text-white 
  rounded 
  py-1 px-2 
  shadow-lg md:w-44 w-32;
}

.has-tooltip:hover .tooltip {
  @apply visible;
}

.has-tooltip1:hover .tooltip1 {
  @apply visible;
}
.scroll {
  scrollbar-color: #e0ad1f #ececec; /* Firefox 64 */

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: #e0ad1f;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #e0ad1f;
  }
}

[data-title]:after {
  color: #bb8b28;
  left: 100%;
  z-index: 9999999999;
  line-height: 18px;
}
[data-title].delete:after {
  line-height: 18px;
  color: red;
  left: 100%;
  z-index: 9999999999;
}
.marqueeStyle {
  display: inline-block;
  /* Apply animation to this element */
  -webkit-animation: scrolling-left1 20s linear infinite;
  animation: scrolling-left1 20s linear infinite;
}

@keyframes scrolling-left1 {
  0% {
    transform: translateX(100%);
    -webkit-transform: translateX(100%);
  }
  100% {
    transform: translateX(-110%);
    -webkit-transform: translateX(-110%);
  }
}

@-webkit-keyframes scrolling-left1 {
  0% {
    -webkit-transform: translateX(100%);
  }
  100% {
    -webkit-transform: translateX(-110%);
  }
}

@media (min-height: 470px) and (max-height: 750px) {
  .card-wrapper {
    // min-height: 900px;
  }
}
@media (max-height: 667px) {
  .billing_info {
    height: calc(100% - 88px);
  }
}
@media (max-width: 768px) {
  .expnad-height {
    height: calc(100% - 88px);
  }
  .collapse-height {
    height: 44px;
  }
}
</style>
