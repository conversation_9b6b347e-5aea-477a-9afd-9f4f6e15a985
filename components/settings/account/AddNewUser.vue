<template>
  <section class="h-full">
    <transition name="slideInOut">
      <div
        v-if="showProfile"
        class="top-0 right-0 fixed bg-ash-dark md:p-21 p-4 md:pt-12 profile transition-all duration-800 ease-in-out overflow-y-auto scroll md:rounded-l-2xl"
        :class="showProfile ? 'md:shadow-2xl' : ''"
        :style="{ '--color': `${globalColorPanel.backgroundColor}` }"
      >
        <div class="h-full flex flex-wrap content-between">
          <div class="w-full card">
            <div class="flex flex-row justify-between items-center">
              <h2
                class="text-yellow-midlight xl:text-2xl md:text-xl text-2xl font-normal title"
                :style="{ color: `${globalColorPanel.backgroundColor}` }"
              >
                {{ isEditableUser ? ' Edit User' : ' Add New User' }}
              </h2>
              <div
                class="w-8 h-8 flex items-center justify-end cursor-pointer"
                @click="isEditableUser ? closeEditProfile() : closeProfile()"
              >
                <ClientOnly>
                  <fa
                    class="text-yellow-midlight xl:text-2xl md:text-xl text-2xl md:font-bold font-normal"
                    :icon="['fas', 'times']"
                    :style="{ color: `${globalColorPanel.backgroundColor}` }"
                  />
                </ClientOnly>
              </div>
            </div>
            <div
              class="flex flex-col justify-center items-center md:mt-10 mt-6"
            >
              <div
                class="flex flex-row justify-center items-center md:w-24 w-20 md:h-24 h-20 rounded-full border-4 border-yellow-midlight"
                :style="{ borderColor: `${globalColorPanel.backgroundColor}` }"
              >
                <div
                  class="md:w-20 w-16 md:h-20 h-16 flex flex-row justify-center items-center rounded-full"
                  @click="$refs.fileInput.click()"
                >
                  <img
                    v-if="avatarPreView"
                    class="md:w-20 w-16 md:h-20 h-16 rounded-full"
                    :src="avatarPreView"
                    :alt="`${editableUser.firstName}'s SharpArchive Profile Image`"
                  />
                  <input
                    ref="fileInput"
                    style="display: none"
                    type="file"
                    accept="image/*"
                    @change="onFileSelected"
                  />
                  <ClientOnly>
                    <fa
                      v-if="!avatarPreView"
                      class="text-white text-2xl cursor-pointer"
                      :icon="['fas', 'plus']"
                    />
                  </ClientOnly>
                </div>
              </div>
              <!-- <h2 class="text-gray-1100 text-xl font-bold pt-6">Jhon Smith</h2>
                <p class="text-gray-1100 opacity-50">
                  Archiving Since January, 2021
                </p> -->
            </div>
            <div class="flex flex-col md:mt-10 mt-4">
              <div class="flex flex-row justify-between">
                <div class="first_last-field">
                  <input
                    id="firstName"
                    v-model="editableUser.firstName"
                    class="w-full h-9 text-md 2xl:text-base outline-none px-5 rounded-full placeholder-gray-1200 placeholder-opacity-50 text-gray-1200 bg-white border-white"
                    type="text"
                    name="firstName"
                    placeholder="First Name"
                    @blur="v$.firstName.$touch()"
                  />
                  <template v-if="v$.firstName.$error">
                    <p
                      v-if="v$.firstName.required.$invalid"
                      class="text-red-500 text-sm firstName_required_error"
                    >
                      First Name is Required
                    </p>
                    <p
                      v-else-if="v$.firstName.maxLength.$invalid"
                      class="text-red-500 text-sm firstName_maxchar_error"
                    >
                      Maximum Character is 20
                    </p>
                  </template>
                </div>
                <div class="first_last-field">
                  <input
                    id="lastName"
                    v-model.trim="editableUser.lastName"
                    class="w-full h-9 text-md 2xl:text-base outline-none px-5 rounded-full placeholder-gray-1200 placeholder-opacity-50 text-gray-1200 bg-white border-white"
                    type="text"
                    name="lastName"
                    placeholder="Last Name"
                    @blur="v$.lastName.$touch()"
                  />
                  <template v-if="v$.lastName.$error">
                    <p
                      v-if="v$.lastName.required.$invalid"
                      class="text-red-500 text-sm lastName_required_error"
                    >
                      Last Name is Required
                    </p>
                    <p
                      v-else-if="v$.lastName.maxLength.$invalid"
                      class="text-red-500 text-sm lastName_maxchar_error"
                    >
                      Maximum Character is 20
                    </p>
                  </template>
                </div>
              </div>
              <div v-if="isEditableUser" class="mt-3">
                <input
                  id="email"
                  class="w-full h-9 text-md 2xl:text-base outline-none px-5 rounded-full placeholder-gray-1200 placeholder-opacity-50 text-gray-1200 bg-white border-white"
                  type="email"
                  name="email"
                  placeholder="Email"
                  :value="editableUser.email"
                  disabled
                  readonly
                />
              </div>
              <div v-else class="mt-3">
                <input
                  id="email"
                  v-model.trim="editableUser.email"
                  class="w-full h-9 text-md 2xl:text-base outline-none px-5 rounded-full placeholder-gray-1200 placeholder-opacity-50 text-gray-1200 bg-white border-white"
                  type="email"
                  name="email"
                  placeholder="Email"
                  @blur="v$.email.$touch()"
                />
                <template v-if="v$.email.$error">
                  <p
                    v-if="v$.email.required.$invalid"
                    class="text-red-500 text-sm email_required_error"
                  >
                    Email is Required
                  </p>
                  <p
                    v-else-if="v$.email.email.$invalid"
                    class="text-red-500 text-sm email_invalid_error"
                  >
                    Email is Invalid
                  </p>
                </template>
              </div>

              <div class="mt-3">
                <ClientOnly>
                  <InputsPhoneNumberInput
                    ref="phoneNumber"
                    color="#505050"
                    input-classes="placeholder-gray-1200 placeholder-opacity-50
                      text-gray-1200"
                    @countryCode="countryCodeEvent"
                    @error="getError($event)"
                  ></InputsPhoneNumberInput>
                </ClientOnly>
              </div>
              <div class="mt-3 h-26">
                <textarea
                  id="suggesion"
                  v-model="editableUser.streetAddress"
                  name="suggesion"
                  cols="30"
                  rows="10"
                  class="w-full pt-2 px-5 h-full resize-none rounded-2xl placeholder-gray-1200 placeholder-opacity-50 text-gray-1200 bg-white border-white outline-none border-none"
                  placeholder="Street Address"
                  @blur="v$.streetAddress.$touch()"
                >
                </textarea>
              </div>
              <template v-if="v$.streetAddress.$error">
                <p
                  v-if="v$.streetAddress.required.$invalid"
                  class="text-red-500 text-sm suggesion_required_error"
                >
                  Street address is Required
                </p>
                <p
                  v-else-if="v$.streetAddress.maxLength.$invalid"
                  class="text-red-500 text-sm suggesion_maxchar_error"
                >
                  Maximum Character is 80
                </p>
              </template>
              <div class="mt-3">
                <input
                  id="country"
                  v-model.trim="editableUser.country"
                  class="w-full h-9 text-md 2xl:text-base outline-none px-5 rounded-full placeholder-gray-1200 placeholder-opacity-50 text-gray-1200 bg-white border-white"
                  type="text"
                  name="country"
                  placeholder="Country"
                  @blur="v$.country.$touch()"
                />
                <template v-if="v$.country.$error">
                  <p
                    v-if="v$.country.required.$invalid"
                    class="text-red-500 text-sm country_required_error"
                  >
                    Country is Required
                  </p>
                  <p
                    v-else-if="v$.country.maxLength.$invalid"
                    class="text-red-500 text-sm country_maxchar_error"
                  >
                    Maximum Character is 30
                  </p>
                </template>
              </div>
              <div class="mt-3">
                <input
                  id="city"
                  v-model.trim="editableUser.city"
                  class="w-full h-9 text-md 2xl:text-base outline-none px-5 rounded-full placeholder-gray-1200 placeholder-opacity-50 text-gray-1200 bg-white border-white"
                  type="text"
                  name="city"
                  placeholder="City"
                  @blur="v$.city.$touch()"
                />
                <template v-if="v$.city.$error">
                  <p
                    v-if="v$.city.required.$invalid"
                    class="text-red-500 text-sm city_required_error"
                  >
                    City is Required
                  </p>
                  <p
                    v-else-if="v$.city.maxLength.$invalid"
                    class="text-red-500 text-sm city_maxchar_error"
                  >
                    Maximum Character is 30
                  </p>
                </template>
              </div>
              <div class="mt-3">
                <input
                  id="state"
                  v-model.trim="editableUser.state"
                  class="w-full h-9 text-md 2xl:text-base outline-none px-5 rounded-full placeholder-gray-1200 placeholder-opacity-50 text-gray-1200 bg-white border-white"
                  type="text"
                  name="state"
                  placeholder="State"
                  @blur="v$.state.$touch()"
                />
                <template v-if="v$.state.$error">
                  <p
                    v-if="v$.state.required.$invalid"
                    class="text-red-500 text-sm state_required_error"
                  >
                    State is Required
                  </p>
                  <p
                    v-else-if="v$.state.maxLength.$invalid"
                    class="text-red-500 text-sm state_maxchar_error"
                  >
                    Maximum Character is 30
                  </p>
                </template>
              </div>
              <div class="mt-3">
                <input
                  id="zip_code"
                  v-model.trim="editableUser.zipCode"
                  class="w-full h-9 text-md 2xl:text-base outline-none px-5 rounded-full placeholder-gray-1200 placeholder-opacity-50 text-gray-1200 bg-white border-white"
                  type="text"
                  name="zipcode"
                  placeholder="Zip Code"
                  @blur="v$.zipCode.$touch()"
                />
                <template v-if="v$.zipCode.$error">
                  <p
                    v-if="v$.zipCode.required.$invalid"
                    class="text-red-500 text-sm zipcode_required_error"
                  >
                    Zip code is Required
                  </p>

                  <p
                    v-else-if="
                      !v$.zipCode.maxLength.$invalid ||
                      !v$.zipCode.numeric.$invalid ||
                      !v$.zipCode.minLength.$invalid
                    "
                    class="text-red-500 text-sm zipcode_invalid_error"
                  >
                    Zip code is invalid
                  </p>
                </template>
              </div>
              <div class="mt-3">
                <InputsSelectInput
                  id="userPermission"
                  v-model="editableUser.userPermission"
                  class="w-full"
                  :options="userPermissions"
                  place-holder="Permission"
                  :place-holder-disabled="true"
                  color="white"
                  :background="globalColorPanel.backgroundColor"
                  :caret-bg="globalColorPanel.backgroundColor"
                  :error="v$.userPermission.$error"
                  :error-message="v$.userPermission.$errors"
                >
                </InputsSelectInput>
              </div>
              <div class="mt-3 h-9">
                <div
                  class="flex flex-row items-center justify-between md:space-x-4 space-x-2"
                >
                  <div class="flex items-center space-x-3 mt-1 h-9">
                    <label
                      for="createAlertAccess"
                      class="text-gray-1400 md:text-lg text-base"
                      >Alerts Access</label
                    >
                    <div
                      class="relative inline-block w-9 mr-2 align-middle select-none transition-all duration-800 ease-in-out"
                    >
                      <input
                        id="createAlertAccess"
                        v-model="alertAccess"
                        checked
                        type="checkbox"
                        name="toggle"
                        class="toggle-checkbox absolute block rounded-full bg-white appearance-none cursor-pointer"
                      />
                      <label
                        for="createAlertAccess"
                        class="toggle-label block overflow-hidden h-5 rounded-full transition-all duration-800 ease-in-out bg-white cursor-pointer"
                      ></label>
                    </div>
                  </div>
                  <transition name="fade">
                    <div v-if="alertAccess" class="md:w-52 w-44 h-9">
                      <InputsSelectInput
                        id="alertPermission"
                        v-model="alertPermissionTemp"
                        class="w-full"
                        :options="alertPermissions"
                        place-holder="Permission"
                        :place-holder-disabled="true"
                        color="white"
                        :background="globalColorPanel.backgroundColor"
                        :caret-bg="globalColorPanel.backgroundColor"
                        :error="v2$.alertPermissionTemp.$error"
                        :error-message="v2$.alertPermissionTemp.$errors"
                        @update:modelValue="
                          editableUser.alertPermission = alertPermissionTemp
                        "
                      >
                      </InputsSelectInput>
                    </div>
                  </transition>
                </div>
              </div>
            </div>
          </div>
          <!--start => footer area-->
          <div class="w-full">
            <div
              class="flex flex-col md:my-5 md:pb-2 mb-5 w-full h-46 rounded-xl"
            >
              <div class="flex flex-row justify-center mt-8 space-x-5 md:px-8">
                <button
                  class="clear focus:outline-none w-44 h-10 text-yellow-midlight bg-ash-dark rounded-full border-2 border-yellow-midlight outline-none font-bold setup font-bold text-base"
                  @click="
                    isEditableUser ? clearProfileInfo() : clearInputField()
                  "
                  :style="{
                    borderColor: `${globalColorPanel.backgroundColor}`,
                    color: `${globalColorPanel.backgroundColor}`,
                  }"
                >
                  <span class="delete_clear_button">
                    {{ isEditableUser ? 'Delete User' : 'Clear' }}
                  </span>
                </button>
                <button
                  class="save focus:outline-none w-44 h-10 text-white bg-yellow-midlight rounded-full border-none outline-none setup font-bold text-base"
                  :disabled="addProcess"
                  @click="isEditableUser ? updateAddNewUser() : storeNewUser()"
                  :style="{ background: `${globalColorPanel.backgroundColor}` }"
                >
                  <div
                    class="rounded-full relative flex items-center justify-around"
                  >
                    <span class="add_update_button">{{
                      isEditableUser ? 'Update' : 'Add'
                    }}</span>
                    <ClientOnly>
                      <fa
                        v-if="addProcess"
                        class="absolute right-5 text-white font-bold animate-spin"
                        :icon="['fas', 'spinner']"
                      />
                    </ClientOnly>
                  </div>
                </button>
              </div>
            </div>
          </div>
          <!--end => footer area-->
        </div>
      </div>
    </transition>
    <AlertConfirmModal
      :processing="userDeleteProcess"
      :show="showDeleteAlert"
      @cancel="deleteCancel"
      @delete="deleteConfirm"
    ></AlertConfirmModal>
  </section>
</template>
<script setup lang="ts">
import { useStore } from 'vuex'
import {
  required,
  email,
  maxLength,
  minLength,
  numeric,
  helpers,
} from '@vuelidate/validators'
import { useVuelidate } from '@vuelidate/core'
import { NEW_USER } from '~/constants/urls'
import { useInputValidations } from '~/composables/useValidations'
import { useNuxtApp } from '#app'

const emit = defineEmits<{
  (event: 'clear-new-user-data'): void
  (event: 'closeProfile', value: boolean): void
  (event: 'createdNewUserData', value: any): void
  (event: 'update-new-user', value: any): void // Replace `any` with the specific type for `data` if known
  (event: 'add-new-user-deleted', value: number): void
}>()

const props = defineProps({
  user: {
    type: Object,
    default: null,
  },
  newUserEmail: {
    type: String,
    default: '',
  },
})
interface EditableUser {
  avatar: string
  firstName: string
  lastName: string
  email: string
  phone: string
  streetAddress: string
  country: string
  city: string
  state: string
  userPermission: string
  zipCode: string
  alertPermission: string
}

interface UserInfo {
  id: number
  userPermission: string
  alertPermission: string
  firstName: string
  lastName: string
  email: string
  lastLogin: string
  isExpired: boolean
  phone: string
  streetAddress: string
  city: string
  state: string
  country: string
  zipCode: string
  avatar: string
  device: any
}
const { $toast } = useNuxtApp()
const editableUser = ref<EditableUser>({
  avatar: '',
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  streetAddress: '',
  country: '',
  city: '',
  state: '',
  userPermission: '',
  zipCode: '',
  alertPermission: '',
})
const alertPermissionTemp = ref<string>('Manager')
const alertAccess = ref<boolean>(true)
const { checkPermission } = useInputValidations()
const rules = {
  firstName: {
    required,
    maxLength: maxLength(20),
  },
  lastName: {
    required,
    maxLength: maxLength(20),
  },
  email: {
    required,
    email,
  },
  streetAddress: {
    required,
    maxLength: maxLength(80),
  },
  country: {
    required,
    maxLength: maxLength(30),
  },
  city: {
    required,
    maxLength: maxLength(30),
  },
  state: {
    required,
    maxLength: maxLength(30),
  },
  userPermission: {
    checkPermission: helpers.withMessage(
      'User permission is required',
      checkPermission,
    ),
  },
  zipCode: {
    required,
    minLength: minLength(4),
    maxLength: maxLength(6),
    numeric,
  },
  // alertPermission: {
  //   checkPermission: helpers.withMessage(
  //     'Alert permission required',
  //     checkPermission
  //   ),
  // },
}
const ruleTwo = {
  alertPermissionTemp: {
    checkPermission: helpers.withMessage(
      'Alert permission is required',
      checkPermission,
    ),
  },
}

const v$ = useVuelidate(rules, editableUser)
const v2$ = useVuelidate(ruleTwo, { alertPermissionTemp })
const { fetch } = useFetched()

const { errorMessages } = useInputValidations()

const defaultAddNewUser = Object.freeze({
  avatar: '',
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  country: '',
  city: '',
  state: '',
  streetAddress: '',
  zipCode: '',
  userPermission: 'Permission',
  alertPermission: 'Manager',
})

editableUser.value = Object.assign({}, defaultAddNewUser)
const { GM_load, GM_instance } = useGoogleMaps()

const avatarPreView = ref<string>('')
const fullAddress = ref<string[]>([])
const addProcess = ref<boolean>(false)
const saveProcess = ref<boolean>(false)
const showDeleteAlert = ref<boolean>(false)
const userDeleteProcess = ref<boolean>(false)
const countryCode = ref<string>('+1')
const geocoder = ref<string>('')
const phoneError = ref<boolean>(true)
const phoneNumber = ref<HTMLElement | null>(null)

const store = useStore()

// Map state from 'account' and 'setting' modules
const userPermissions = computed(() => store.state.account.userPermissions)
const alertPermissions = computed(() => store.state.account.alertPermissions)
const showProfile = computed(() => store.state.setting.showProfile)
const globalColorPanel = computed(() => store.state.globalColorPanel)

// Define the `isEditableUser` computed property
const isEditableUser = computed(() => {
  return !!(props.user && props.user.id)
})

watch(
  () => props.user,
  (data: UserInfo) => {
    if (data && data.data !== null) {
      avatarPreView.value = data.avatar
      editableUser.value = Object.assign({}, data)
      if (data.alertPermission !== 'Blocked' && data.alertPermission) {
        alertAccess.value = true
        editableUser.value.alertPermission = alertAccess.value
          ? data.alertPermission
          : 'Permission'
        alertPermissionTemp.value = editableUser.value.alertPermission
      } else {
        alertAccess.value = false
        editableUser.value.alertPermission = 'Manager'
        alertPermissionTemp.value = editableUser.value.alertPermission
      }
      if (data.phone) {
        setTimeout(() => {
          // this.editableUser.phone = data.phone
          phoneNumber.value.phone = data.phone
        }, 1000)
      }
    } else {
      editableUser.value = Object.assign({}, defaultAddNewUser)
      getUserInfo()
    }
  },
)

watch(
  () => showProfile.value,
  (data: boolean) => {
    if (!data) {
      setTimeout(() => {
        clearAllField()
      })
    } else if (data && !isEditableUser.value) {
      console.log('add new user')
      if (props.newUserEmail) {
        editableUser.value.email = props.newUserEmail
        v$.value.email.$touch()
      }
      getUserInfo()
    }
  },
)

onMounted(async () => {
  getUserInfo()
  await store.dispatch('account/fetchUserPermissons')
  await store.dispatch('account/fetchAlertPermissons')
})

interface PhoneError {
  error: boolean
  phone: string
}
const getError = ($event: PhoneError) => {
  if ($event) {
    phoneError.value = $event.error
    editableUser.value.phone = $event.phone
  }
}
const getUserInfo = () => {
  fullAddress.value = store.state.auth?.user?.address
  console.log(fullAddress.value, 'fullAddress')
  GM_load().then(() => {
    geocoder.value = new GM_instance.value.maps.Geocoder()
  })
  if (geocoder.value) {
    codeAddress(geocoder.value)
  }
}
interface AddressComponent {
  long_name: string
  short_name: string
  types: string[]
}

interface Location {
  lat: number
  lng: number
}

interface Viewport {
  south: number
  west: number
  north: number
  east: number
}

interface Geometry {
  location: Location
  location_type: string
  viewport: Viewport
}

interface PlusCode {
  compound_code: string
  global_code: string
}

interface GeocodeResult {
  address_components: AddressComponent[]
  formatted_address: string
  geometry: Geometry
  place_id: string
  plus_code: PlusCode
  types: string[]
}

// The overall response structure
type GeocodeResponse = GeocodeResult[]
const codeAddress = (newGeocode: any) => {
  newGeocode.geocode(
    { address: fullAddress.value },
    (results: GeocodeResponse, status: string) => {
      if (status === 'OK') {
        fullAddress.value = results[0].formatted_address.split(',')
        if (fullAddress.value.length === 3) {
          const cityWithZip = fullAddress.value[1]?.trim().split(' ')
          editableUser.value.streetAddress = fullAddress.value[0]?.trim()
          editableUser.value.country = fullAddress.value[2]?.trim()
          editableUser.value.city = cityWithZip ? cityWithZip[0] : ''
          editableUser.value.state = cityWithZip ? cityWithZip[0] : ''
          editableUser.value.zipCode = cityWithZip ? cityWithZip[1] : ''
        } else {
          const cityWithZip = fullAddress.value[2]?.trim().split(' ')
          editableUser.value.streetAddress = fullAddress.value[0]?.trim()
          editableUser.value.country = fullAddress.value[3]?.trim()
          editableUser.value.city = cityWithZip ? cityWithZip[0] : ''
          editableUser.value.state = fullAddress.value[1]?.trim()
          editableUser.value.zipCode = cityWithZip ? cityWithZip[1] : ''
        }
      }
    },
  )
}
const clearAllField = () => {
  if (phoneNumber.value) {
    phoneNumber.value.error = false
  }
  editableUser.value = Object.assign({}, defaultAddNewUser)
  alertPermissionTemp.value = editableUser.value.alertPermission
  alertAccess.value = true
  avatarPreView.value = ''
  emit('clear-new-user-data')
  v$.value.$reset()
  v2$.value.$reset()
}
const closeProfile = () => {
  emit('closeProfile', false)
  clearAllField()
  v$.value.$reset()
  v2$.value.$reset()
  store.commit('setting/SET_SHOW_PROFILE', false)
}
const closeEditProfile = () => {
  emit('closeProfile', false)
  clearAllField()
  alertAccess.value = true
  avatarPreView.value = ''
  v$.value.$reset()
  v2$.value.$reset()
}
const clearInputField = () => {
  clearAllField()
  v$.value.$reset()
  v2$.value.$reset()
}
const onFileSelected = (event: Event) => {
  const target = event.target as HTMLInputElement
  const selectFile = target.files?.[0]
  let reader
  const files = target.files
  if (!files && files.length === 0) {
    console.log('empty')
  } else {
    reader = new FileReader()
    reader.onload = (event) => {
      editableUser.value.avatar = selectFile
      avatarPreView.value = event.target?.result as string
    }
    if (files) {
      reader.readAsDataURL(files[0])
    }
  }
}
interface UserResponse {
  status: number
  success: boolean
  message: string
  data: UserData
}

interface UserData {
  id: number
  userPermission: string
  alertPermission: string
  firstName: string
  lastName: string
  email: string
  lastLogin: string
  isExpired: boolean
  phone: string
  streetAddress: string
  city: string
  state: string
  country: string
  zipCode: string
  avatar: string
}
const storeNewUser = async () => {
  const formData = checkAddNewUserData()
  if (formData != null) {
    $toast('clear')
    try {
      addProcess.value = true
      const { success, message, data } = (await fetch(NEW_USER, {
        method: 'POST',
        body: formData,
      })) as UserResponse
      if (success) {
        $toast('success', {
          message: message,
          className: 'toasted-bg-archive',
        })
        emit('createdNewUserData', data)
        closeProfile()
      } else {
        $toast('error', {
          message: message,
          className: 'toasted-bg-alert',
        })
      }
    } catch (err) {
      console.error(err)
    } finally {
      addProcess.value = false
    }
  }
}
const countryCodeEvent = (value: string) => {
  countryCode.value = value
}

const checkAddNewUserData = () => {
  phoneNumber.value?.formBlur()
  v$.value.$touch()
  v2$.value.$touch()
  if (
    !v$.value.$invalid &&
    !phoneError.value &&
    (!v2$.value.$invalid || !alertAccess.value)
  ) {
    $toast('clear')
    const formData = new FormData()
    Object.keys(editableUser.value).forEach((key) => {
      formData.append(key, editableUser.value[key])
    })

    formData.set('phone', formData.get('phone'))
    formData.set(
      'alertPermission',
      alertAccess.value ? formData.get('alertPermission') : 'Blocked',
    )
    // if (formData.get('userPermission') === 'Permission') {
    //   this.nuxtApp.$toast('error', {
    //     message: 'Please select user permission',
    //     className: 'toasted-bg-alert',
    //   })
    //   return
    // }

    // if (
    //   this.alertAccess &&
    //   formData.get('alertPermission') === 'Permission'
    // ) {
    //   this.nuxtApp.$toast('error', {
    //     message: 'Please select alert permission',
    //     className: 'toasted-bg-alert',
    //   })
    //   return
    // }
    return formData
  } else {
    return null
  }
}

const updateAddNewUser = async () => {
  const formData = await checkAddNewUserData()
  if (formData != null) {
    try {
      addProcess.value = true
      const { success, message, data } = (await fetch(NEW_USER, {
        method: 'PUT',
        body: formData,
      })) as UserResponse
      if (success) {
        $toast('success', {
          message: message,
          className: 'toasted-bg-archive',
        })
        emit('update-new-user', data)
        closeProfile()
      } else {
        $toast('error', {
          message: message,
          className: 'toasted-bg-alert',
        })
      }
    } catch (err) {
      console.error(err)
    } finally {
      addProcess.value = false
    }
  }
}

const clearProfileInfo = () => {
  showDeleteAlert.value = true
}
const deleteCancel = () => {
  showDeleteAlert.value = false
}
const deleteConfirm = async () => {
  $toast('clear')
  try {
    userDeleteProcess.value = true
    const { success, message } = await fetch(NEW_USER, {
      method: 'DELETE',
      body: {
        id: props.user.id,
      },
    })
    if (success) {
      emit('add-new-user-deleted', Number(props.user.id))
      $toast('success', {
        message: message,
        className: 'toasted-bg-archive',
      })
      closeProfile()
    } else {
      $toast('error', {
        message: message,
        className: 'toasted-bg-alert',
      })
    }
  } catch (error) {
    console.error(error)
  } finally {
    showDeleteAlert.value = false
    saveProcess.value = false
    userDeleteProcess.value = false
  }
}
</script>
<style lang="scss" scoped>
.profile {
  width: 500px;
  height: 100%;
  z-index: 999;
}

/* for slideInOut */
.slideInOut-enter-active,
.slideInOut-leave-active {
  transition: all 0.8s ease-in-out;
}
.slideInOut-enter-from,
.slideInOut-leave-to {
  right: -100%;
}

.show {
  right: 0px;
}

.hide {
  right: -500px;
}

.first_last-field {
  width: 48%;
}

.scroll {
  scrollbar-color: var(--color) #ececec; /* Firefox 64 */
  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: var(--color);
  }
  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: var(--color);
  }
}

@media (max-width: 767px) {
  .profile {
    position: fixed;
    width: 100%;
    height: 100%;
  }
}
select:disabled {
  background: red;
}

.toggle-checkbox {
  width: 16px;
  height: 16px;
  border: 0px;
  top: 2px;
  left: 2px;
  transition: all 0.5s ease-in-out;
  background-color: #393e46;
  &:checked {
    @apply right-0;
    left: 18px;
    transition: all 0.5s ease-in-out;
    background-color: var(--color);
  }
  &:checked + .toggle-label {
    @apply bg-white;
    transition: all 0.5s ease-in-out;
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
