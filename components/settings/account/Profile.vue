<template>
  <div>
    <transition name="right-sidebar-trans">
      <div
        v-show="show && loggedIn && showSideBar"
        :style="{ '--color': globalColorPanel.backgroundColor }"
        class="profile fixed bg-ash-dark right-0 md:top-15 h-full md:px-21 p-4 md:pt-8 flex flex-col content-between scroll md:rounded-l-2xl md:shadow-2xl md:drop-shadow-2xl transition-all duration-500 delay-500"
        :class="
          showPasswordConfirmModal || showForgetPasswordModal
            ? 'opacity-0 md:opacity-100'
            : 'opacity-100'
        "
        @click="editAble ? hideDeviceLogoutBtn() : ''"
      >
        <div class="content-wrapper w-full h-full relative">
          <transition name="page">
            <div v-if="!editAble" class="w-full h-full absolute top-0">
              <div class="h-full flex flex-wrap content-between">
                <div class="w-full">
                  <div class="flex flex-row justify-between md:mt-4">
                    <h2
                      :style="{ color: globalColorPanel.backgroundColor }"
                      class="xl:text-2xl md:text-xl md:font-medium text-2xl"
                    >
                      Profile
                    </h2>
                    <div
                      class="w-8 h-8 flex items-center justify-end cursor-pointer"
                      @click="cancelProfileEdit()"
                    >
                      <ClientOnly>
                        <fa
                          :style="{ color: globalColorPanel.backgroundColor }"
                          class="text-2xl font-semibold"
                          :icon="['fas', 'times']"
                        />
                      </ClientOnly>
                    </div>
                  </div>
                  <div
                    class="flex flex-col items-center mt-10"
                    @click="editProfile()"
                  >
                    <div
                      :style="{ borderColor: globalColorPanel.backgroundColor }"
                      class="w-24 h-24 rounded-full border-4"
                    >
                      <div
                        class="w-20 h-20 rounded-full mx-auto mt-1 cursor-pointer"
                      >
                        <img
                          class="w-20 h-20 rounded-full"
                          :src="avatarPreview"
                          :alt="`${profileUserInfo.businessName}'s SharpArchive Profile Image`"
                        />
                      </div>
                    </div>
                    <h2
                      class="text-gray-1100 xl:text-2xl md:text-xl font-bold text-2xl md:pt-6 pt-3 text-center cursor-pointer break-all"
                    >
                      <span>{{ profileUserInfo.businessName }}</span>
                    </h2>
                    <p
                      class="text-gray-1100 opacity-50 xl:text-xl md:text-lg text-xl cursor-pointer"
                    >
                      <DateTime
                        :datetime="profileUserInfo.dateJoined"
                        format="MMMM, dd yyyy, hh:mm aa"
                        :show-time="false"
                        class="table-span"
                      />
                    </p>
                  </div>
                  <div
                    class="md:flex hidden flex-col mt-6 cursor-pointer"
                    @click="editProfile()"
                  >
                    <div>
                      <h4
                        class="text-gray-1100 xl:text-xl md:text-lg font-bold text-2xl"
                      >
                        User
                      </h4>
                      <p
                        class="text-gray-1100 xl:text-xl md:text-lg text-xl opacity-50"
                      >
                        {{
                          profileUserInfo.firstName +
                          ' ' +
                          profileUserInfo.lastName
                        }}
                      </p>
                    </div>
                    <div class="mt-6">
                      <h4
                        class="text-gray-1100 xl:text-xl md:text-lg font-bold text-2xl"
                      >
                        Email
                      </h4>
                      <p
                        class="text-gray-1100 xl:text-xl md:text-lg text-xl opacity-50"
                      >
                        {{ profileUserInfo.email }}
                      </p>
                    </div>
                    <div v-if="profileUserInfo.address" class="mt-6">
                      <h4
                        class="text-gray-1100 xl:text-xl md:text-lg font-bold text-2xl"
                      >
                        Address
                      </h4>
                      <p
                        class="text-gray-1100 xl:text-xl md:text-lg text-xl opacity-50 break-all"
                      >
                        {{ profileUserInfo.address }}
                      </p>
                    </div>
                    <div class="mt-6">
                      <h4
                        class="text-gray-1100 xl:text-xl md:text-lg font-bold text-2xl"
                      >
                        Phone
                      </h4>
                      <p
                        class="text-gray-1100 xl:text-xl md:text-lg text-xl opacity-50"
                      >
                        {{ profileUserInfo.phone }}
                      </p>
                    </div>
                    <div class="mt-6">
                      <h4
                        class="text-gray-1100 xl:text-xl md:text-lg font-bold text-2xl whitespace-nowrap"
                      >
                        Change your password
                      </h4>
                    </div>
                  </div>
                  <!-- Logged In Devices Info -->
                  <div
                    class="md:flex hidden flex-col cursor-pointer padding-t-50px"
                    @click="editProfile()"
                  >
                    <h2
                      class="xl:text-2xl md:text-xl font-bold padding-b-30px"
                      :style="{
                        color: globalColorPanel.backgroundColor,
                      }"
                    >
                      Where you’re logged in
                    </h2>

                    <div
                      v-for="(item, index) in loadDevices"
                      :key="index"
                      class="padding-b-15px"
                    >
                      <div
                        class="padding-b-15px"
                        :class="
                          index === loadDevices.length - 1
                            ? 'border-0'
                            : 'border-b border-gray-1100 '
                        "
                      >
                        <h3
                          class="text-gray-1100 xl:text-xl md:text-lg font-bold pb-2 leading-6"
                        >
                          {{ item.name }}
                        </h3>
                        <p
                          class="text-gray-1100 xl:text-xl md:text-lg flex items-center space-x-1 leading-6 padding-b-3px"
                        >
                          <img
                            class="h-6 w-6 mb-0.5"
                            :src="
                              item.os.toLowerCase().includes('mac')
                                ? mac
                                : item.os.toLowerCase().includes('windows')
                                  ? windows
                                  : item.os.toLowerCase().includes('android')
                                    ? android
                                    : item.os.toLowerCase().includes('ios')
                                      ? iosPhone
                                      : item.os.toLowerCase().includes('linux')
                                        ? linux
                                        : item.os
                                              .toLowerCase()
                                              .includes('ubuntu')
                                          ? ubuntu
                                          : item.os
                                                .toLowerCase()
                                                .includes('unknown')
                                            ? unknown
                                            : defaultOs
                            "
                            alt
                          />
                          <span>{{ item.os }} · {{ item.location }}</span>
                        </p>
                        <p
                          class="text-gray-1100 xl:text-xl md:text-lg opacity-50 pb-1.5 leading-6"
                        >
                          <span>{{ item.browser }} ·</span>
                          <span
                            v-if="item.isActive"
                            class="active-color opacity-100"
                            >Active now</span
                          >
                          <DateTime
                            v-else
                            :datetime="item.lastLogin"
                            format="MMMM, dd yyyy, hh:mm aa"
                            class="whitespace-nowrap"
                          />
                        </p>
                        <p
                          class="text-gray-1100 xl:text-xl md:text-lg opacity-50 leading-6"
                        >
                          <span>IP {{ item.ip }}</span>
                        </p>
                      </div>
                    </div>
                  </div>

                  <!-- mobile device -->
                  <div
                    class="md:hidden w-full flex flex-col mt-7 px-8"
                    @click="editProfile()"
                  >
                    <div class="grid grid-cols-12 gap-6 w-full">
                      <div class="col-span-4">
                        <label
                          class="text-gray-1100 text-lg font-bold block space-y-3"
                          >User</label
                        >
                      </div>
                      <div class="col-span-8">
                        <p
                          class="text-gray-1100 text-lg h-7 opacity-50 inline-block space-y-3"
                        >
                          {{
                            profileUserInfo.firstName +
                            ' ' +
                            profileUserInfo.lastName
                          }}
                        </p>
                      </div>
                    </div>
                    <div class="grid grid-cols-12 gap-6 w-full mt-2">
                      <div class="col-span-4">
                        <label
                          class="text-gray-1100 text-lg font-bold block space-y-3"
                          >Email</label
                        >
                      </div>
                      <div class="col-span-8">
                        <p
                          class="text-gray-1100 text-lg h-7 opacity-50 inline-block space-y-3"
                        >
                          {{ profileUserInfo.email }}
                        </p>
                      </div>
                    </div>
                    <div
                      v-if="profileUserInfo.address"
                      class="grid grid-cols-12 gap-6 w-full mt-2"
                    >
                      <div class="col-span-4">
                        <label
                          class="text-gray-1100 text-lg font-bold inline-block space-y-3"
                          >Address</label
                        >
                      </div>
                      <div class="col-span-8">
                        <p
                          class="text-gray-1100 text-lg h-7 opacity-50 inline-block break-all space-y-3"
                        >
                          {{ profileUserInfo.address }}
                        </p>
                      </div>
                    </div>
                    <div class="grid grid-cols-12 gap-6 w-full mt-2">
                      <div class="col-span-4">
                        <label
                          class="text-gray-1100 text-lg font-bold inline-block space-y-3"
                          >Phone</label
                        >
                      </div>
                      <div class="col-span-8">
                        <p
                          class="text-gray-1100 text-lg h-7 opacity-50 inline-block space-y-3"
                        >
                          {{ profileUserInfo.phone }}
                        </p>
                      </div>
                    </div>
                    <div class="grid grid-cols-12 gap-6 w-full mt-2">
                      <div class="col-span-4">
                        <label
                          class="text-gray-1100 text-lg font-bold inline-block space-y-3 whitespace-nowrap"
                          >Change your password</label
                        >
                      </div>
                    </div>
                    <!-- Logged In Devices Info -->
                    <div
                      class="md:hidden flex flex-col cursor-pointer padding-t-50px"
                      @click="editProfile()"
                    >
                      <h2
                        class="font-bold text-lg padding-b-30px"
                        :style="{
                          color: globalColorPanel.backgroundColor,
                        }"
                      >
                        Where you’re logged in
                      </h2>

                      <div
                        v-for="(item, index) in loadDevices"
                        :key="index"
                        class="padding-b-15px"
                      >
                        <div
                          class="padding-b-15px"
                          :class="
                            index === loadDevices.length - 1
                              ? 'border-0'
                              : 'border-b border-gray-1100'
                          "
                        >
                          <h3
                            class="text-gray-1100 text-lg font-bold pb-2 leading-6"
                          >
                            {{ item.name }}
                          </h3>
                          <p
                            class="text-gray-1100 text-lg flex items-center space-x-1.5 leading-6 padding-b-3px"
                          >
                            <img
                              class="h-6 w-6 mb-0.5"
                              :src="
                                item.os.toLowerCase().includes('mac')
                                  ? mac
                                  : item.os.toLowerCase().includes('windows')
                                    ? windows
                                    : item.os.toLowerCase().includes('android')
                                      ? android
                                      : item.os.toLowerCase().includes('ios')
                                        ? iosPhone
                                        : item.os
                                              .toLowerCase()
                                              .includes('linux')
                                          ? linux
                                          : item.os
                                                .toLowerCase()
                                                .includes('ubuntu')
                                            ? ubuntu
                                            : item.os
                                                  .toLowerCase()
                                                  .includes('unknown')
                                              ? unknown
                                              : defaultOs
                              "
                              alt
                            />
                            <span>{{ item.os }} · {{ item.location }}</span>
                          </p>
                          <p
                            class="text-gray-1100 text-lg opacity-50 leading-6 pb-1.5"
                          >
                            <span>{{ item.browser }} ·</span>
                            <span
                              v-if="item.isActive"
                              class="active-color opacity-100"
                              >Active now</span
                            >
                            <DateTime
                              v-else
                              :datetime="item.lastLogin"
                              format="MMMM, dd yyyy, hh:mm aa"
                              class="whitespace-nowrap"
                            />
                          </p>
                          <p
                            class="text-gray-1100 text-lg opacity-50 leading-6"
                          >
                            <span>IP {{ item.ip }}</span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- See More Button -->
                <button
                  v-if="loggedInDevices.length > 4"
                  class="xl:text-xl text-lg w-28 flex items-center space-x-2 font-bold mx-8 md:mx-0"
                  :style="{
                    color: globalColorPanel.backgroundColor,
                  }"
                  @click.stop="loadMore()"
                >
                  <span class="whitespace-nowrap">{{
                    isLoadMore ? 'See more' : 'See less'
                  }}</span>
                  <ClientOnly>
                    <fa v-if="isLoadMore" :icon="['fas', 'caret-down']" />
                    <fa v-if="!isLoadMore" :icon="['fas', 'caret-up']" />
                  </ClientOnly>
                </button>

                <!-- logout button -->
                <div class="w-full sticky bottom-[-16px] bg-ash-dark z-1">
                  <div
                    class="flex flex-row items-center justify-center mt-10 btn-wrapper mb-4"
                  >
                    <button
                      :style="{
                        backgroundColor: globalColorPanel.backgroundColor,
                      }"
                      type="submit"
                      class="w-44 py-2 text-white rounded-full border-none outline-none font-bold text-base setup"
                      :disabled="logoutProcess"
                      @click="profileLogout()"
                    >
                      <div
                        class="rounded-full relative flex items-center justify-around"
                      >
                        <span>Log Out</span>
                        <ClientOnly>
                          <fa
                            v-if="logoutProcess"
                            class="absolute right-5 text-white font-bold animate-spin"
                            :icon="['fas', 'spinner']"
                          />
                        </ClientOnly>
                      </div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </transition>

          <transition name="page">
            <div v-show="editAble" class="w-full h-full absolute top-0">
              <div class="h-full flex flex-wrap content-between">
                <div class="w-full">
                  <div class="flex flex-row justify-between md:mt-4">
                    <h2
                      :style="{ color: globalColorPanel.backgroundColor }"
                      class="xl:text-2xl md:text-xl md:font-medium text-2xl"
                    >
                      Edit Profile
                    </h2>
                    <button
                      class="focus:outline-none"
                      @click.stop="cancelProfileEdit()"
                    >
                      <ClientOnly>
                        <fa
                          :style="{ color: globalColorPanel.backgroundColor }"
                          class="text-2xl font-semibold"
                          :icon="['fas', 'times']"
                        />
                      </ClientOnly>
                    </button>
                  </div>
                  <form
                    v-show="editAble"
                    enctype="multipart/form-data"
                    @submit.prevent="updateProfile"
                  >
                    <div class="space-y-4 form-card">
                      <div class="flex flex-col items-center mt-10">
                        <div
                          :style="{
                            borderColor: globalColorPanel.backgroundColor,
                          }"
                          class="w-24 h-24 rounded-full border-4"
                        >
                          <div
                            class="w-20 h-20 rounded-full mx-auto mt-1 cursor-pointer"
                            @click.stop="$refs.fileInput.click()"
                          >
                            <img
                              class="w-20 h-20 rounded-full"
                              :src="avatarPreview"
                              :alt="`${profileUserInfo.firstName}'s SharpArchive Profile Image`"
                            />
                            <input
                              ref="fileInput"
                              style="display: none"
                              type="file"
                              accept="image/*"
                              @change="onFileSelected"
                            />
                          </div>
                        </div>
                      </div>

                      <div>
                        <label
                          class="text-gray-1100 xl:text-xl text-lg font-bold"
                          for="firstName"
                          >First Name</label
                        >
                        <input
                          id="firstName"
                          v-model="profileUserInfo.firstName"
                          type="text"
                          class="text-white w-full p-1 outline-none focus:outline-none bg-ash-dark border-b border-white"
                          @blur="v$.firstName.$touch()"
                        />

                        <template v-if="v$.firstName.$error">
                          <span
                            class="text-red-500 text-xs mb-0"
                            v-for="error in v$.firstName.$errors"
                            :key="error.$uid"
                          >
                            {{
                              errorMessages(error, profileUserInfo.firstName)
                            }}
                          </span>
                        </template>
                      </div>
                      <div>
                        <label
                          class="text-gray-1100 xl:text-xl text-lg font-bold"
                          for="lastName"
                          >Last Name</label
                        >
                        <input
                          id="lastName"
                          v-model="profileUserInfo.lastName"
                          type="text"
                          class="text-white w-full p-1 outline-none focus:outline-none bg-ash-dark border-b border-white"
                          @blur="v$.lastName.$touch()"
                        />
                        <template v-if="v$.lastName.$error">
                          <span
                            class="text-red-500 text-xs mb-0"
                            v-for="error in v$.lastName.$errors"
                            :key="error.$uid"
                          >
                            {{ errorMessages(error, profileUserInfo.lastName) }}
                          </span>
                        </template>
                      </div>

                      <div>
                        <label
                          class="text-gray-1100 xl:text-xl text-lg font-bold"
                          for="businessName"
                          >Business Name</label
                        >
                        <input
                          id="businessName"
                          v-model="profileUserInfo.businessName"
                          type="text"
                          class="text-white w-full p-1 outline-none focus:outline-none bg-ash-dark border-b border-white"
                          @blur="v$.businessName.$touch()"
                        />
                        <template v-if="v$.businessName.$error">
                          <span
                            class="text-red-500 text-xs mb-0"
                            v-for="error in v$.businessName.$errors"
                            :key="error.$uid"
                          >
                            {{
                              errorMessages(error, profileUserInfo.businessName)
                            }}
                          </span>
                        </template>
                      </div>

                      <div>
                        <label
                          class="text-gray-1100 xl:text-xl text-lg font-bold"
                          for="email"
                          >Email</label
                        >
                        <p
                          class="text-white w-full p-1 outline-none focus:outline-none bg-ash-dark border-b border-white"
                        >
                          {{ profileUserInfo.email }}
                        </p>
                        <!-- <input
                          id="email"
                          v-model="profileUserInfo.email"
                          type="text"
                          class="
                            text-white
                            w-full
                            p-1
                            outline-none
                            focus:outline-none
                            bg-ash-dark
                            border-b border-white
                          "
                        />-->
                      </div>

                      <div>
                        <label
                          class="text-gray-1100 xl:text-xl text-lg font-bold"
                          for="address"
                          >Address</label
                        >
                        <input
                          id="address"
                          ref="origin"
                          v-model="profileUserInfo.address"
                          type="text"
                          class="text-white w-full p-1 outline-none focus:outline-none bg-ash-dark border-b border-white"
                          @blur="v$.address.$touch()"
                          @input="(addressError = false), autoSearch()"
                        />
                        <template v-if="v$.address.$error">
                          <span
                            class="text-red-500 text-xs mb-0"
                            v-for="error in v$.address.$errors"
                            :key="error.$uid"
                          >
                            {{ errorMessages(error, profileUserInfo.address) }}
                          </span>
                        </template>
                        <p
                          v-if="addressError && !v$.address.$error"
                          class="text-red-500 text-xs mb-0"
                        >
                          Address is invalid
                        </p>
                      </div>
                      <div>
                        <label
                          class="text-gray-1100 xl:text-xl text-lg font-bold"
                          for="contact"
                          >Phone</label
                        >
                        <div class="flex flex-row border-b border-white group">
                          <p
                            class="text-white w-full p-1 outline-none focus:outline-none bg-ash-dark"
                          >
                            {{ profileUserInfo.phone }}
                          </p>
                          <ClientOnly>
                            <fa
                              class="text-white mx-2 cursor-pointer hidden group-hover:block"
                              :icon="['fas', 'pencil-alt']"
                              @click.stop="editPhoneNumber"
                            />
                          </ClientOnly>
                        </div>
                        <!-- <input
                          id="contact"
                          v-model="profileUserInfo.phone"
                          v-mask="'+# ### ### ####'"
                          class="
                            text-white
                            w-full
                            p-1
                            outline-none
                            focus:outline-none
                            bg-ash-dark
                            border-b border-white
                          "
                          type="tel"
                          max="14"
                          @blur="v$.profileUserInfo.phone.$touch()"
                        />-->
                        <!-- <template v-if="v$.profileUserInfo.phone.$error">
                          <p class="text-red-500 text-xs mb-0 pl-2">
                            {{ validationMsg(v$.profileUserInfo.phone) }}
                          </p>
                        </template>-->
                      </div>
                      <div>
                        <!-- <label
                          class="
                            text-gray-1100
                            xl:text-2xl
                            md:text-xl
                            text-xl
                            font-bold
                          "
                          for="contact"
                          >Phone</label
                        >-->
                        <div class="flex flex-row items-center group">
                          <label
                            class="w-full text-gray-1100 xl:text-xl text-lg font-bold"
                            for="contact"
                            >Change your password</label
                          >
                          <!-- <p
                            class="
                              text-white
                              w-full
                              p-1
                              outline-none
                              focus:outline-none
                              bg-ash-dark
                            "
                          >
                            Change your password
                          </p>-->
                          <ClientOnly>
                            <fa
                              class="text-white mx-2 cursor-pointer hidden group-hover:block"
                              :icon="['fas', 'pencil-alt']"
                              @click.stop="changePassword"
                            />
                          </ClientOnly>
                        </div>
                      </div>
                    </div>
                  </form>
                  <!-- Logged In Devices Info -->
                  <div
                    v-if="user && user.userPermission !== 'User'"
                    class="flex flex-col padding-t-50px"
                  >
                    <div
                      class="flex flex-wrap justify-between items-center padding-b-30px"
                    >
                      <h2
                        class="xl:text-2xl md:text-xl font-bold text-xl whitespace-nowrap padding-b-30px md:pb-0"
                        :style="{
                          color: globalColorPanel.backgroundColor,
                        }"
                      >
                        Where you’re logged in
                      </h2>
                      <button
                        class="px-4 py-1 md:py-2 rounded-full outline-none self-start font-bold text-base text-white bg-yellow-midlight"
                        :style="{
                          backgroundColor: globalColorPanel.backgroundColor,
                        }"
                        @click.stop="logOutAllDevises()"
                      >
                        Log Out Everywhere
                      </button>
                    </div>
                    <div
                      v-for="(item, index) in loadDevices"
                      :key="index"
                      class="padding-b-15px"
                    >
                      <div
                        class="padding-b-15px"
                        :class="
                          index === loadDevices.length - 1
                            ? 'border-0'
                            : 'border-b border-gray-1100 '
                        "
                      >
                        <div
                          class="flex justify-between items-center relative pb-1 leading-6"
                        >
                          <h3
                            class="text-gray-1100 xl:text-xl md:text-lg font-bold text-lg"
                          >
                            {{ item.name }}
                          </h3>
                          <span
                            class="w-6 h-6 p-4 rounded-full flex justify-center items-center icon-hover cursor-pointer"
                            @click.stop="showDeviceLogoutBtn(item.id)"
                          >
                            <ClientOnly>
                              <fa
                                class="text-gray-1100 opacity-50"
                                :icon="['fas', 'ellipsis-v']"
                              />
                            </ClientOnly>
                          </span>
                          <span
                            class="speech-bubble cursor-pointer right-0 md:-right-2 text-lg xl:text-xl"
                            :class="
                              item.show && isDeviceLogoutBtnShow
                                ? 'block'
                                : 'hidden'
                            "
                            :style="{
                              backgroundColor: globalColorPanel.backgroundColor,
                              '--backgroundColor':
                                globalColorPanel.backgroundColor,
                            }"
                            @click.stop="logoutFromDevice(item.id)"
                            >Log Out</span
                          >
                        </div>
                        <p
                          class="text-gray-1100 xl:text-xl md:text-lg text-lg flex items-center space-x-1.5 leading-6 padding-b-3px"
                        >
                          <img
                            class="h-6 w-6 mb-0.5"
                            :src="
                              item.os.toLowerCase().includes('mac')
                                ? mac
                                : item.os.toLowerCase().includes('windows')
                                  ? windows
                                  : item.os.toLowerCase().includes('android')
                                    ? android
                                    : item.os.toLowerCase().includes('ios')
                                      ? iosPhone
                                      : item.os.toLowerCase().includes('linux')
                                        ? linux
                                        : item.os
                                              .toLowerCase()
                                              .includes('ubuntu')
                                          ? ubuntu
                                          : item.os
                                                .toLowerCase()
                                                .includes('unknown')
                                            ? unknown
                                            : defaultOs
                            "
                            alt
                          />
                          <span>{{ item.os }} · {{ item.location }}</span>
                        </p>
                        <p
                          class="text-gray-1100 xl:text-xl md:text-lg text-lg opacity-50 leading-6 pb-1.5"
                        >
                          <span>{{ item.browser }} ·</span>
                          <span
                            v-if="item.isActive"
                            class="active-color opacity-100"
                            >Active now</span
                          >
                          <DateTime
                            v-else
                            :datetime="item.lastLogin"
                            format="MMMM, dd yyyy, hh:mm aa"
                            class="whitespace-nowrap"
                          />
                        </p>
                        <p
                          class="text-gray-1100 xl:text-xl md:text-lg text-lg opacity-50 leading-6"
                        >
                          <span>IP {{ item.ip }}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  v-if="loggedInDevices.length > 4"
                  class="xl:text-xl text-lg w-28 flex items-center space-x-2 font-bold"
                  :style="{
                    color: globalColorPanel.backgroundColor,
                  }"
                  @click.stop="loadMore()"
                >
                  <span class="whitespace-nowrap">{{
                    isLoadMore ? 'See more' : 'See less'
                  }}</span>
                  <ClientOnly>
                    <fa v-if="isLoadMore" :icon="['fas', 'caret-down']" />
                    <fa v-if="!isLoadMore" :icon="['fas', 'caret-up']" />
                  </ClientOnly>
                </button>
                <div class="w-full bg-ash-dark z-1 sticky bottom-[-16px]">
                  <div
                    v-if="editAble"
                    class="flex flex-row items-center justify-center space-x-3 mt-14 btn-wrapper mb-4"
                  >
                    <button
                      :style="{
                        color: globalColorPanel.backgroundColor,
                        borderColor: globalColorPanel.backgroundColor,
                      }"
                      class="w-44 py-2 rounded-full border-2 outline-none font-bold text-base setup"
                      @click.prevent="cancelProfileEdit()"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      :style="{
                        backgroundColor: globalColorPanel.backgroundColor,
                        borderColor: globalColorPanel.backgroundColor,
                      }"
                      class="w-44 py-2 text-white rounded-full border-2 outline-none font-bold text-base setup"
                      :disabled="updateProcess"
                      @click.stop="updateProfile()"
                    >
                      <div
                        class="rounded-full relative flex items-center justify-around"
                      >
                        <span>Save</span>
                        <ClientOnly>
                          <fa
                            v-if="updateProcess"
                            class="absolute right-5 text-white font-bold animate-spin"
                            :icon="['fas', 'spinner']"
                          />
                        </ClientOnly>
                      </div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </transition>
        </div>
      </div>
    </transition>
    <!--   -->
    <transition name="right-sidebar-trans">
      <LazySettingsAccountUpdatePhone
        v-if="updatePhone"
        :background-color="globalColorPanel.backgroundColor"
        @hide="hideUpdatePhone"
        @newPhone="updateNewPhone"
      ></LazySettingsAccountUpdatePhone>
    </transition>
    <LazyPasswordConfirmationModal
      v-if="showPasswordConfirmModal"
      @isValidPassword="passwordIsValid"
      @close="closePasswordConfirmModal"
    />
  </div>
</template>
<script setup lang="ts">
import { useStore } from 'vuex'
import { useVuelidate } from '@vuelidate/core'
import { required, maxLength } from '@vuelidate/validators'
import defaultImage from '../../../assets/img/users/default.jpg'
import { SECURITY_DEVICE } from '~/constants/urls'
import androidIcon from '~/assets/img/svg/login-devices/android.svg'
import iosPhoneIcon from '~/assets/img/png/login-devices/ios-phone.png'
import macIcon from '~/assets/img/svg/login-devices/mac.svg'
import questionIcon from '~/assets/img/png/login-devices/question.png'
import windowsIcon from '~/assets/img/png/login-devices/windows.png'
import linuxIcon from '~/assets/img/svg/login-devices/linux.svg'
import ubuntuIcon from '~/assets/img/png/login-devices/ubuntu.png'
import defaultIcon from '~/assets/img/png/login-devices/default.png'

const props = defineProps({
  globalColorPanel: {
    type: Object,
    default() {
      return { backgroundColor: '' }
    },
  },
})

interface ProfileUserInfo {
  businessName: string
  firstName: string
  lastName: string
  address: string
  phone: string
}
const { $toast } = useNuxtApp()
const store = useStore()
const router = useRouter()
const config = useRuntimeConfig()
const { fetch } = useFetched()
const notification = useNotification()
const profileUserInfo = ref<ProfileUserInfo>({
  businessName: '',
  firstName: '',
  lastName: '',
  address: '',
  phone: '',
})
const rules = {
  businessName: {
    required,
    maxLength: maxLength(80),
  },
  firstName: {
    required,
    maxLength: maxLength(20),
  },
  lastName: {
    required,
    maxLength: maxLength(20),
  },
  address: {
    required,
    maxLength: maxLength(100),
  },
}
const v$ = useVuelidate(rules, profileUserInfo)
// methods
const { errorMessages } = useInputValidations()
const {
  updateUserProfile,
  logout,
  updateProcess,
  addressError,
  showErrorModal,
  notifications,
} = useAuth()
const { GM_load, GM_instance } = useGoogleMaps()

const fadeOutAll = ref<boolean>(false)
const selectFile = ref<string>('')
const image = ref<string>(defaultImage)
const logoutProcess = ref<boolean>(false)
const updatePhone = ref<boolean>(false)
const avatarPreview = ref<string>('')
const showPasswordConfirmModal = ref<boolean>(false)
const editAble = ref<boolean>(false)
const mac = ref<string>(macIcon)
const android = ref<string>(androidIcon)
const iosPhone = ref<string>(iosPhoneIcon)
const unknown = ref<string>(questionIcon)
const windows = ref<string>(windowsIcon)
const linux = ref<string>(linuxIcon)
const ubuntu = ref<string>(ubuntuIcon)
const defaultOs = ref<string>(defaultIcon)
const loggedInDevices = ref<UserDevice[]>([])
const devises = ref<UserDevice[]>([])
const initialItem = ref<number>(0)
const loadCount = ref<number>(4)
const isDeviceLogoutBtnShow = ref<boolean>(false)
const origin = ref<HTMLElement | null>(null)

const isLoadMore = computed(() => {
  return loadDevices.value.length < loggedInDevices.value.length
})

const loadDevices = computed(() => {
  const device = []
  for (const item of loggedInDevices.value.slice(
    initialItem.value,
    loadCount.value,
  )) {
    device.push(item)
  }
  devises.value = device // This mimics the side effect in the original code.
  return devises.value
})

// Vuex state mappings
const show = computed(() => store.state.profile.show)
const isValidPassword = computed(() => store.state.confirm.isValidPassword)
const showSideBar = computed(() => store.state.showSideBar)
const showForgetPasswordModal = computed(
  () => store.state.showForgetPasswordModal,
)
const loggedIn = computed(() => store.state.auth.loggedIn)
const user = computed(() => store.state.auth.user)
const tokenCookie = computed(() => store.state.auth.tokenCookie)

// Watchers
watch(
  () => showSideBar.value,
  (data: boolean) => {
    if (!data) {
      cancelProfileEdit()
    } else if (data) {
      console.log(
        loggedIn.value && user.value?.avatar ? user.value.avatar : image.value,
      )
      avatarPreview.value =
      loggedIn.value && user.value?.avatar ? user.value.avatar : image.value
    }
  },
)
watch(
  () => editAble.value,
  (data: boolean) => {
    if (!data) {
      isDeviceLogoutBtnShow.value = false
    }
  },
)

// Mounted lifecycle replacement
onMounted(() => {
  getUserDevices()
  setTimeout(() => {
    const addressElement = document.getElementById('address')
    if (addressElement) {
      addressElement.setAttribute('autocomplete', 'shipping address')
    }
  }, 1000)

  autoSearch()

  setTimeout(() => {
    console.log(
      loggedIn.value && user.value?.avatar ? user.value.avatar : image.value,
    )
    getUserInfo()
    avatarPreview.value =
      loggedIn.value && user.value?.avatar ? user.value.avatar : image.value
  }, 1000)
})

const autoSearch = () => {
  GM_load().then(() => {
    const originAutocomplete = new GM_instance.value.maps.places.Autocomplete(
      origin.value,
      {
        bounds: new window.google.maps.LatLngBounds(
          new window.google.maps.LatLng(43.0, -75.0),
        ),
      },
    )
    const input = document.getElementById('address') as HTMLElement
    // Listen for focus event on the input
    input.addEventListener('focus', () => {
      let event = new KeyboardEvent('keydown', {
        keyCode: 40, // Down arrow key
        bubbles: true, // Allow the event to bubble up
      })
      input.dispatchEvent(event)
    })

    originAutocomplete.addListener('place_changed', () => {
      profileUserInfo.value.address =
        originAutocomplete.getPlace().formatted_address
    })
  })
}
interface LogOutAllDevises {
  success: boolean
  message: string
}
const logOutAllDevises = async () => {
  try {
    const res = (await fetch(SECURITY_DEVICE, {
      body: {
        id: -1,
      },
      method: 'DELETE',
    })) as LogOutAllDevises
    if (res.success) {
      // this.loggedInDevices = []
      getUserDevices()
      $toast('success', {
        message: res.message,
        className: 'toasted-bg-archive',
      })
    } else {
      $toast('error', {
        message: res.message,
        className: 'toasted-bg-alert',
      })
    }
  } catch (error) {
    console.error(error)
  }
}
const hideDeviceLogoutBtn = () => {
  isDeviceLogoutBtnShow.value = false
  loggedInDevices.value.forEach((element: UserDevice) => {
    if (element.show) {
      element.show = false
    }
  })
}
const loadMore = () => {
  if (isLoadMore.value) {
    loadCount.value = loadCount.value + 10
  } else {
    loadCount.value = 4
  }
}

interface UserDevice {
  id: number
  name: string
  ip: string
  location: string
  os: string
  browser: string
  device: string
  lastLogin: string // ISO 8601 formatted date string
  firstLogin: string // ISO 8601 formatted date string
  isActive: boolean
  show: boolean
}
interface GetUserDevices {
  success: boolean
  data: UserDevice[]
  message: string
}
const getUserDevices = async () => {
  try {
    const res = (await fetch(SECURITY_DEVICE)) as GetUserDevices
    if (res.success) {
      loggedInDevices.value = res.data.sort(function (a, b) {
        return new Date(b.lastLogin).getTime() - new Date(a.lastLogin).getTime()
      })
      loggedInDevices.value.forEach((element: UserDevice) => {
        element.show = false
        if (!element.browser) {
          element.browser = 'Unknown'
        }
        if (!element.device) {
          element.device = 'Unknown'
        }
        if (!element.location) {
          element.location = 'Unknown'
        }
        if (!element.os) {
          element.os = 'Unknown'
        }
      })
    }
  } catch (err) {
    console.error(err)
  }
  // console.log(this.loggedInDevices, 'devices')
}

const showDeviceLogoutBtn = (id: number) => {
  loggedInDevices.value.forEach((element: UserDevice, i: number) => {
    if (element.id === id) {
      element.show = !element.show
      isDeviceLogoutBtnShow.value = true
    } else {
      element.show = false
    }
    loggedInDevices.value[i] = element
    // this.$set(this.loggedInDevices, i, this.loggedInDevices[i])
  })
}
const logoutFromDevice = async (id: number) => {
  isDeviceLogoutBtnShow.value = false
  try {
    const res = (await fetch(SECURITY_DEVICE, {
      body: {
        id: id,
      },
      method: 'DELETE',
    })) as GetUserDevices
    if (res.success) {
      loggedInDevices.value = loggedInDevices.value.filter(
        (item: UserDevice) => item.id !== id,
      )
      $toast('success', {
        message: res.message,
        className: 'toasted-bg-archive',
      })
    } else {
      $toast('error', {
        message: res.message,
        className: 'toasted-bg-alert',
      })
    }
  } catch (error) {
    console.error(error)
  }
}
const closeProfile = () => {
  fadeOutAll.value = true
  setTimeout(() => {
    store.commit('profile/SET_PROFILE_MODAL', false)
    fadeOutAll.value = false
  }, 200)
}
const changePassword = () => {
  showForgetPassword(true)
}
const editPhoneNumber = () => {
  if (!isValidPassword.value) {
    showPasswordConfirmModal.value = true
    store.commit('confirm/SET_FROM_PROFILE', true)
    store.commit('confirm/SET_PASSWORD_CONFIRM_MODAL', true)
  } else {
    updatePhone.value = true
  }
}
const passwordIsValid = () => {
  closePasswordConfirmModal()
  updatePhone.value = true
}
const hideUpdatePhone = () => {
  updatePhone.value = false
}
const updateNewPhone = (phone: string) => {
  profileUserInfo.value.phone = phone
  hideUpdatePhone()
}
const closePasswordConfirmModal = () => {
  showPasswordConfirmModal.value = false
}
const getUserInfo = () => {
  profileUserInfo.value = Object.assign({}, user.value)
}
const editProfile = () => {
  editAble.value = true
}
const onFileSelected = (event: Event) => {
  const target = event.target as HTMLInputElement
  selectFile.value = target.files?.[0]
  let reader
  const files = target.files
  if (files.length > 0) {
    reader = new FileReader()
    reader.onload = (event) => {
      avatarPreview.value = event.target?.result as string
      profileUserInfo.value.avatar = selectFile.value
    }
    reader.readAsDataURL(files[0])
  }
}
const updateProfile = async () => {
  v$.value.$touch()
  if (!v$.value.$invalid) {
    await updateUserProfile(profileUserInfo.value)
    if (!addressError.value) {
      cancelProfileEdit()
    }
  }
}
const cancelProfileEdit = () => {
  addressError.value = false
  editAble.value = false
  if (!updateProcess.value) {
    getUserInfo()
  }
  store.commit('SET_SHOW_SIDE_BAR', false)
  store.commit('confirm/SET_PASSWORD_CONFIRM_MODAL', false)
  closeProfile()
}
const profileLogout = () => {
  show_login(true)
  store.commit('profile/SET_PROFILE_MODAL', false)
  store.commit('SET_LANDING_LOADER', false)
  store.commit('notifications/SET_OFFSET', 0)
  store.commit('notifications/SET_LIMIT', 25)
  store.commit('notifications/SET_NOTIFICATION', [])
  store.commit('notifications/SET_OLD_NOTIFICATION', [])
  logoutProcess.value = true
  router.push('/home')
  console.log(notification, 'notification')
  setTimeout(async () => {
    show_home_content(true)

    logout().then((res) => {
      if (res) {
        if (config.public.workflow === 'live') {
          notification.closeSocket()
        }
        showErrorModal.value = false
        notifications.value = null
        router.push('/')

        logoutProcess.value = false

        store.commit('header/BLUR_ACTIVE_FEED_MODAL', false)
        store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false)

        landing_content(true)
        expand_starter_modal(false)
        collapse_starter_modal(false)
        maximize_starter_modal(false)
        starter_account_maximized(false)
        updateSetupContent('SetupStarterButton')
        setTimeout(() => {
          home_wrapper(true)
        }, 300)

        setTimeout(() => {
          show_home(false)
          show_logo_text(true)
          home_menu_text(true)
          setTimeout(() => {
            all_side_menu(false)
            setTimeout(() => {
              home_side_menu(false)
              home_menu_text(false)
              setTimeout(() => {
                show_logo(false)
                setTimeout(() => {
                  home_circle(true)
                  sidebar_menu(false)
                  sidebar_circle(false)
                  width_decrese(true)
                  setTimeout(() => {
                    home_modal(false)
                    slide_left(false)
                    slide_right(true)
                    login_circle(true)
                    setTimeout(() => {
                      home_header(false)
                      slide_full_right(true)
                      home_circle(false)
                      home_sidebar(false)
                      setTimeout(() => {
                        after_logout(true)
                        setTimeout(() => {
                          text_loading(true)
                          setTimeout(() => {
                            successfully(false)
                            notsuccessfully(true)
                            setTimeout(() => {
                              setTimeout(() => {
                                login_button_transition(false)
                                submit_button_transition(false)
                                login_form_transition(false)
                                setTimeout(() => {
                                  after_logout(false)
                                  text_loading(false)
                                  after_loading(false)
                                  width_increase(false)
                                  full_width(false)
                                  header_text(false)
                                  loading_text(false)
                                  width_decrese(false)
                                  slide_right(false)
                                  slide_full_right(false)
                                  // this.show_login(true)
                                }, 1000)
                              }, 300)
                            }, 500)
                          }, 1800)
                        }, 800)
                      }, 800)
                    }, 800)
                  }, 600)
                }, 500)
              }, 0)
            }, 0)
          }, 800)
        }, 200)
      }
    })
  }, 600)

  set_header_width(false)
  setIsSticky(false)
}
const setIsSticky = (value: any) => {
  store.dispatch('set_sticky', value)
}
const set_header_width = (value: any) => {
  store.dispatch('set_header_width', value)
}
const expand_starter_modal = (value: any) => {
  store.dispatch('expand_starter_modal', value)
}
const collapse_starter_modal = (value: any) => {
  store.dispatch('collapse_starter_modal', value)
}
const maximize_starter_modal = (value: any) => {
  store.dispatch('maximize_starter_modal', value)
}
const starter_account_maximized = (value: any) => {
  store.dispatch('starter_account_maximized', value)
}
const updateSetupContent = (value: any) => {
  store.dispatch('updateSetupContent', value)
}
const showForgetPassword = (value: any) => {
  store.dispatch('showForgetPassword', value)
}
// Login animation actions
const login_button_transition = (value: any) => {
  store.dispatch('loginAnimation/login_button_transition', value)
}
const submit_button_transition = (value: any) => {
  store.dispatch('loginAnimation/submit_button_transition', value)
}
const login_form_transition = (value: any) => {
  store.dispatch('loginAnimation/login_form_transition', value)
}
const successfully = (value: any) => {
  store.dispatch('loginAnimation/successfully', value)
}
const notsuccessfully = (value: any) => {
  store.dispatch('loginAnimation/notsuccessfully', value)
}
const after_loading = (value: any) => {
  store.dispatch('loginAnimation/after_loading', value)
}
const home_modal = (value: any) => {
  store.dispatch('loginAnimation/home', value)
}
const sidebar_menu = (value: any) => {
  store.dispatch('loginAnimation/sidebar_menu', value)
}
const sidebar_circle = (value: any) => {
  store.dispatch('loginAnimation/circle', value)
}
const home_sidebar = (value: any) => {
  store.dispatch('loginAnimation/sidebar', value)
}
const home_circle = (value: any) => {
  store.dispatch('loginAnimation/home_circle', value)
}
const login_circle = (value: any) => {
  store.dispatch('loginAnimation/login_circle', value)
}
const slide_left = (value: any) => {
  store.dispatch('loginAnimation/slide_left', value)
}
const show_logo = (value: any) => {
  store.dispatch('loginAnimation/show_logo', value)
}
const home_header = (value: any) => {
  store.dispatch('loginAnimation/header', value)
}
const width_increase = (value: any) => {
  store.dispatch('loginAnimation/width_increase', value)
}
const full_width = (value: any) => {
  store.dispatch('loginAnimation/full_width', value)
}
const home_side_menu = (value: any) => {
  store.dispatch('loginAnimation/home_side_menu', value)
}
const all_side_menu = (value: any) => {
  store.dispatch('loginAnimation/all_side_menu', value)
}
const show_home = (value: any) => {
  store.dispatch('loginAnimation/show_home', value)
}
const home_menu_text = (value: any) => {
  store.dispatch('loginAnimation/home_menu_text', value)
}
const show_home_content = (value: any) => {
  store.dispatch('loginAnimation/show_home_content', value)
}
const home_wrapper = (value: any) => {
  store.dispatch('loginAnimation/home_wrapper', value)
}
const show_logo_text = (value: any) => {
  store.dispatch('loginAnimation/show_logo_text', value)
}
const header_text = (value: any) => {
  store.dispatch('loginAnimation/header_text', value)
}
const loading_text = (value: any) => {
  store.dispatch('loginAnimation/loading_text', value)
}
const width_decrese = (value: any) => {
  store.dispatch('loginAnimation/width_decrese', value)
}
const slide_right = (value: any) => {
  store.dispatch('loginAnimation/slide_right', value)
}
const slide_full_right = (value: any) => {
  store.dispatch('loginAnimation/slide_full_right', value)
}
const show_login = (value: any) => {
  store.dispatch('loginAnimation/show_login', value)
}
const landing_content = (value: any) => {
  store.dispatch('loginAnimation/landing_content', value)
}
const after_logout = (value: any) => {
  store.dispatch('loginAnimation/after_logout', value)
}
const text_loading = (value: any) => {
  store.dispatch('loginAnimation/text_loading', value)
}

// ...mapActions({
//   setIsSticky: 'set_sticky',
//   set_header_width: 'set_header_width',
//   expand_starter_modal: 'expand_starter_modal',
//   collapse_starter_modal: 'collapse_starter_modal',
//   maximize_starter_modal: 'maximize_starter_modal',
//   starter_account_maximized: 'starter_account_maximized',
//   updateSetupContent: 'updateSetupContent',
//   showForgetPassword: 'showForgetPassword',

//   login_button_transition: 'loginAnimation/login_button_transition',
//   submit_button_transition: 'loginAnimation/submit_button_transition',
//   login_form_transition: 'loginAnimation/login_form_transition',
//   successfully: 'loginAnimation/successfully',
//   notsuccessfully: 'loginAnimation/notsuccessfully',
//   after_loading: 'loginAnimation/after_loading',
//   home_modal: 'loginAnimation/home',
//   sidebar_menu: 'loginAnimation/sidebar_menu',
//   sidebar_circle: 'loginAnimation/circle',
//   home_sidebar: 'loginAnimation/sidebar',
//   home_circle: 'loginAnimation/home_circle',
//   login_circle: 'loginAnimation/login_circle',
//   slide_left: 'loginAnimation/slide_left',
//   show_logo: 'loginAnimation/show_logo',
//   home_header: 'loginAnimation/header',
//   width_increase: 'loginAnimation/width_increase',
//   full_width: 'loginAnimation/full_width',
//   home_side_menu: 'loginAnimation/home_side_menu',
//   all_side_menu: 'loginAnimation/all_side_menu',
//   show_home: 'loginAnimation/show_home',
//   home_menu_text: 'loginAnimation/home_menu_text',
//   show_home_content: 'loginAnimation/show_home_content',
//   home_wrapper: 'loginAnimation/home_wrapper',
//   show_logo_text: 'loginAnimation/show_logo_text',
//   header_text: 'loginAnimation/header_text',
//   loading_text: 'loginAnimation/loading_text',

//   width_decrese: 'loginAnimation/width_decrese',
//   slide_right: 'loginAnimation/slide_right',
//   slide_full_right: 'loginAnimation/slide_full_right',
//   show_login: 'loginAnimation/show_login',
//   landing_content: 'loginAnimation/landing_content',
//   after_logout: 'loginAnimation/after_logout',
//   text_loading: 'loginAnimation/text_loading',
// }),
</script>
<style lang="scss" scoped>
$color: var(--color);

.padding-b-3px {
  padding-bottom: 3px;
}
.padding-t-50px {
  padding-top: 50px;
}
.padding-b-30px {
  padding-bottom: 30px;
}
.padding-b-15px {
  padding-bottom: 15px;
}
.active-color {
  color: #4eff00;
}
.last-border:last-child {
  @apply border-0;
}
.speech-bubble {
  position: absolute;
  background-color: #e0ad1f;
  color: white;
  font-weight: bold;
  border-radius: 6px;
  padding: 6px 18px;
  top: 2.25rem;
}
.speech-bubble:before {
  @apply absolute top-0 w-0 h-0;
  right: 14px;
  content: '';
  border: 9px solid transparent;
  border-bottom-color: var(--backgroundColor);
  border-top: 0;
  margin-left: -10px;
  margin-top: -8px;
}
.profile {
  width: 550px;
  height: 100%;
  top: 0px;
  z-index: 9999999999;
}

.setup {
  bottom: 530px;
}
.scroll {
  scrollbar-color: $color #ececec; /* Firefox 64 */
  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: $color;
  }
  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: $color;
  }
}
.form-card > .error {
  margin-top: 0px;
}

@media (max-width: 767px) {
  .profile {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    z-index: 9999999999;
  }
  .scroll {
    scrollbar-color: $color #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: $color;
    }
    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: $color;
    }
  }
  .speech-bubble {
    padding: 2px 12px;
  }
  .speech-bubble:before {
    right: 6px;
  }
}

@media (max-height: 769px) {
  .btn-wrapper {
    margin-bottom: 16px !important;
  }
}

.right-sidebar-trans-enter-from,
.right-sidebar-trans-leave-to {
  right: -100%;
}

.right-sidebar-trans-enter-to,
.right-sidebar-trans-leave {
  right: 0;
}

.right-sidebar-trans-enter-active {
  transition: all 0.8s ease-in-out;
}
.right-sidebar-trans-leave-active {
  transition: all 0.8s ease-in-out;
}
.content-wrapper {
  opacity: 1;
}

.icon-hover:hover {
  @apply bg-gray-600 text-white;
}

/*end right sidebar section transition*/
</style>
