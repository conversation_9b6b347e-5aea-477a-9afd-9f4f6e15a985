<template>
  <transition name="slideInOut">
    <div
      v-if="showCreateGroup"
      class="top-0 right-0 absolute bg-ash-dark md:p-21 p-4 md:pt-10 profile transition-all duration-800 ease-in-out overflow-y-auto scroll md:rounded-l-2xl md:shadow-2xl"
      :class="[showCreateGroup ? 'md:shadow-2xl' : '']"
      :style="{ '--color': `${color}` }"
    >
      <div class="h-full flex flex-wrap content-between">
        <div class="w-full card">
          <!--start => title area-->
          <div class="flex flex-row justify-between items-center mb-2">
            <h2
              class="xl:text-2xl md:text-xl text-2xl md:font-bold"
              :style="{ color: `${color}` }"
            >
              Create New Group
            </h2>
            <div
              class="w-8 h-8 flex items-center justify-end cursor-pointer"
              @click="resetAll()"
            >
              <ClientOnly>
                <fa
                  class="xl:text-2xl md:text-xl text-2xl md:font-bold font-normal"
                  :icon="['fas', 'times']"
                  :style="{ color: `${color}` }"
                />
              </ClientOnly>
            </div>
          </div>
          <!--end => title area-->
          <!--start => main area-->
          <div class="flex flex-col h-full inner-body">
            <div class="flex flex-col h-1/2 mt-2.5 w-full min-height-250">
              <div class="w-full md:mb-2 mb-1 lg:mb-0">
                <input
                  id="newgroup"
                  v-model="group.groupName"
                  type="text"
                  placeholder="New Group Name"
                  class="w-full rounded-full py-2 px-2.5 outline-none focus:outline-none bg-white border-b border-white md:text-xl text-sm placeholder-te placeholder-opacity-50"
                  :style="{
                    color: `${color}`,
                    '--placeholderColor': `${color}`,
                  }"
                  @blur="v$.groupName.$touch()"
                />
                <template v-if="v$.groupName.$error">
                  <p
                    v-for="error in v$.groupName.$errors"
                    :key="error.$uid"
                    class="text-red-400 text-xs mb-0 pl-2"
                  >
                    {{ errorMessages(error, group.groupName) }}
                  </p>
                </template>
              </div>
              <div
                class="card_table w-full md:mt-5 mt-2 bg-white rounded-2xl flex flex-col flex-grow"
              >
                <div
                  class="table_header w-full bg-yellow-midlight py-1 rounded-t-2xl"
                  :style="{ backgroundColor: `${color}` }"
                >
                  <h2
                    class="text-white text-center font-bold md:text-xl text-lg"
                  >
                    Feeds in Group
                  </h2>
                </div>
                <div
                  class="table_body flex flex-col pb-2.5 overflow-y-auto scroll"
                >
                  <div
                    class="h-full flex flex-col pt-2.5 overflow-y-auto scroll"
                  >
                    <div class="table-th flex flex-row justify-between px-5">
                      <p
                        class="th md:text-gray-1000 text-color-sm font-bold w-1/2 md:text-lg text-md"
                        :style="{ '--color': `${color}` }"
                      >
                        Source
                      </p>
                      <p
                        class="th md:text-gray-1000 text-color-sm font-bold w-1/2 md:pl-14 md:text-lg text-md"
                        :style="{ '--color': `${color}` }"
                      >
                        Name
                      </p>
                    </div>
                    <div class="flex flex-col mt-2">
                      <transition-group name="feeds">
                        <div
                          v-for="feedInGroup in selectedFeeds"
                          :key="feedInGroup.id"
                          class="cursor-pointer mx-2.5 text-gray-1000 hover:text-white table-td"
                          :style="{ '--color': `${color}` }"
                          @click="removeSelectedFeed(feedInGroup)"
                        >
                          <div class="flex flex-row justify-between">
                            <div class="pl-1.5 pr-2.5">
                              <div class="py-0.5">
                                <p
                                  class="td w-1/2 text-md text-ash-primary whitespace-nowrap"
                                >
                                  {{
                                    feedInGroup.provider === 'Twitter'
                                      ? 'X (Twitter)'
                                      : feedInGroup.provider
                                  }}
                                </p>
                              </div>
                            </div>
                            <span
                              class="td w-1/2 md:pl-14 pt-0.5 text-md text-ash-primary relative has-tooltip"
                            >
                              {{ $strLimit(feedInGroup.name, 14) }}
                              <span class="tooltip">{{
                                feedInGroup.name
                              }}</span>
                            </span>
                          </div>
                        </div>
                      </transition-group>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex flex-col h-1/2 md:mt-4 mt-5 w-full min-height-250">
              <div
                class="w-full flex items-center relative md:mb-2 mb-5 lg:mb-0"
              >
                <ClientOnly>
                  <fa
                    class="absolute ml-3 w-5 search-icon"
                    :icon="['fas', 'search']"
                    :style="{ color: `${color}` }"
                  />
                </ClientOnly>
                <input
                  id="maycontain"
                  v-model="mayContain"
                  type="text"
                  placeholder="Search"
                  class="w-full rounded-full py-2 px-2.5 pl-8 outline-none focus:outline-none bg-white border-b border-white md:text-xl text-sm placeholder-opacity-50"
                  :style="{
                    color: `${color}`,
                    '--placeholderColor': `${color}`,
                  }"
                />
              </div>
              <div
                class="card_table w-full md:mt-5 bg-white rounded-2xl flex flex-col flex-grow"
              >
                <div
                  class="table_header w-full py-1 rounded-t-2xl"
                  :style="{ backgroundColor: `${color}` }"
                >
                  <h2
                    class="text-base text-white text-center font-bold md:text-xl text-lg"
                  >
                    Available Feeds
                  </h2>
                </div>
                <div
                  class="table_body flex flex-col pb-2.5 overflow-y-auto scroll"
                >
                  <div
                    class="h-full flex flex-col pt-2.5 overflow-y-auto scroll"
                  >
                    <div class="table-th flex flex-row justify-between px-5">
                      <p
                        class="th md:text-gray-1000 text-color-sm font-bold w-1/2 md:text-lg text-md"
                        :style="{ '--color': `${color}` }"
                      >
                        Source
                      </p>
                      <p
                        class="th md:text-gray-1000 text-color-sm font-bold w-1/2 md:pl-14 md:text-lg text-md"
                        :style="{ '--color': `${color}` }"
                      >
                        Name
                      </p>
                    </div>
                    <div
                      v-if="searchTableItems.length > 0"
                      class="flex flex-col mt-2"
                    >
                      <div
                        v-for="(availableFeed, index) in searchTableItems"
                        :key="index"
                        class="cursor-pointer mx-2.5 text-gray-1000 hover:text-white table-td"
                        :style="{ '--color': `${color}` }"
                        @click="addFeedToGroup(availableFeed)"
                      >
                        <div class="flex flex-row justify-between">
                          <div class="pl-1 pr-2.5">
                            <div class="py-0.5">
                              <p class="td font-w-1/2 text-md text-ash-primary">
                                {{
                                  availableFeed.provider === 'Twitter'
                                    ? 'X (Twitter)'
                                    : availableFeed.provider
                                }}
                              </p>
                            </div>
                          </div>
                          <span
                            class="td w-1/2 md:pl-14 pt-0.5 text-md text-ash-primary relative has-tooltip"
                          >
                            {{ $strLimit(availableFeed.name, 14) }}
                            <span class="tooltip">{{
                              availableFeed.name
                            }}</span>
                          </span>
                        </div>
                      </div>
                    </div>
                    <div
                      v-else
                      class="w-full h-full flex justify-center items-center"
                    >
                      No feeds available to select
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--end => main area-->
        </div>

        <!--start => footer area-->
        <div class="w-full">
          <div class="flex flex-col w-full h-auto rounded-xl">
            <div class="flex flex-row justify-center space-x-5 md:px-8">
              <button
                class="focus:outline-none w-44 h-9 bg-ash-dark rounded-full border-2 outline-none font-bold setup md:text-lg text-xl"
                :style="{ color: `${color}`, borderColor: `${color}` }"
                @click="clearAllField()"
              >
                <span>Clear</span>
              </button>
              <button
                class="focus:outline-none w-44 h-9 text-white rounded-full border-none outline-none font-bold setup border-2 border-white md:text-lg text-xl"
                :style="{ backgroundColor: `${color}` }"
                :disabled="addProcess"
                @click="addNewGroup()"
              >
                <div
                  class="rounded-full relative flex items-center justify-around"
                >
                  <span>Add</span>
                  <ClientOnly>
                    <fa
                      v-if="addProcess"
                      class="absolute right-5 text-white font-bold animate-spin"
                      :icon="['fas', 'spinner']"
                    />
                  </ClientOnly>
                </div>
              </button>
            </div>
          </div>
        </div>
        <!--end => footer area-->
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
import { useVuelidate } from '@vuelidate/core'
import { required, maxLength } from '@vuelidate/validators'
import { AVAILABLE_FEEDS, GROUPS } from '~/constants/urls'

const emit = defineEmits<{
  (event: 'hide-create-group'): void
  (event: 'add-feed', payload: { item1: Feed }): void
  (event: 'clear-field'): void
  (
    event: 'add-group',
    payload: { feedsInGroup: any[]; groupName: string; id: number },
  ): void
}>()

const props = defineProps({
  color: {
    type: String,
    default: '#E0AD1F',
  },
})

const {$toast} = useNuxtApp()
const store = useStore()
interface FeedsInGroup {
  id: number | string
}
interface Feed {
  id: number
  provider: string
  name: string
  username: string
  selected: boolean
}
interface Group {
  groupName: string
  feedsInGroup: FeedsInGroup[]
}
const group = ref<Group>({
  groupName: '',
  feedsInGroup: [],
})
const rules = {
  groupName: {
    required,
    maxLength: maxLength(40),
  },
}
const v$ = useVuelidate(rules, group)
const { fetch } = useFetched()
const { errorMessages } = useInputValidations()
// Define the state variables
const mayContain = ref<string>('')
const availableFeeds = ref<Feed[]>([])
const addProcess = ref<boolean>(false)
const close = ref<boolean>(true)
const addGroup = ref<boolean>(false)

const currentComponent = computed(() => store.state.setting.currentComponent)
const showCreateGroup = computed(() => store.state.setting.showCreateGroup)
const allGroups = computed(() => store.state.socialFeed.allGroups)
// Computed properties
const searchTableItems = computed(() =>
  unselectedFeeds.value.filter(
    (item: Feed) =>
      item.provider.toUpperCase().match(mayContain.value.toUpperCase()) ||
      item.name.toUpperCase().match(mayContain.value.toUpperCase()),
  ),
)
const selectedFeeds = computed(() => {
  // eslint-disable-next-line array-callback-return
  return availableFeeds.value.filter((item: Feed) => {
    if (item.selected) {
      return item
    }
  })
})
const unselectedFeeds = computed(() => {
  // eslint-disable-next-line array-callback-return
  return availableFeeds.value.filter((item: Feed) => {
    if (!item.selected) {
      return item
    }
  })
})
watch(
  () => showCreateGroup.value,
  (data: boolean) => {
    if (data) {
      group.value.feedsInGroup = []
      addGroup.value = false
      close.value = true
      getAvailableFeeds()
    }
    if (!data) {
      clearAllField()
    }
  },
)
onMounted(() => {
  if (currentComponent.value === 'Organize') {
    getAvailableFeeds()
  }
})

// Methods as arrow functions
const resetAll = () => {
  clearAllField()
  emit('hide-create-group')
  store.commit('setting/SET_SHOW_CREATE_GROUP', false)
}
interface AvailableFeeds {
  success: boolean
  data: Feed[]
}
const getAvailableFeeds = async () => {
  try {
    const res = (await fetch(AVAILABLE_FEEDS)) as AvailableFeeds
    if (res.success) {
      res.data.forEach((item: Feed) => {
        item.selected = false
      })
      availableFeeds.value = res.data
    }
  } catch (error) {
    console.error(error)
  }
}

const addFeedToGroup = (item: Feed) => {
  item.selected = true
  emit('add-feed', { item1: item })
}

const removeSelectedFeed = (item: Feed) => {
  item.selected = false
  emit('add-feed', { item1: item })
}

const clearAllField = () => {
  group.value.groupName = ''
  if (!addGroup.value) {
    group.value.feedsInGroup = []
    makeFeedUnSelected()
  }
  mayContain.value = ''
  if (close.value) {
    setTimeout(() => {
      emit('clear-field')
    }, 500)
  }
  v$.value.$reset()
}
interface AddNewGroup{
  data: Group,
  success: boolean,
  message: string
}
const addNewGroup = async () => {
  v$.value.$touch()
  if (!v$.value.$invalid) {
    selectedFeeds.value.forEach((item: Feed) => {
      group.value.feedsInGroup.push(item.id)
    })
    $toast('clear')
    if (group.value.feedsInGroup.length > 0) {
      try {
        addProcess.value = true
        const res = await fetch(GROUPS, {
          method: 'POST',
          body: group.value,
        }) as AddNewGroup
        if (res.success) {
          close.value = false
          addGroup.value = true
          $toast('success', {
            message: res.message,
            className: 'toasted-bg-archive',
          })
          emit('add-group', {
            feedsInGroup: res.data.feedsInGroup,
            groupName: res.data.groupName,
            id: res.data.id,
          })
          const tempAllGroups = JSON.parse(JSON.stringify(allGroups.value))
          tempAllGroups.push({
            id: res.data.id,
            text: res.data.groupName,
            value: res.data.id,
          })
          store.commit('socialFeed/SET_ALL_GROUPS', tempAllGroups)
          store.commit('setting/SET_SHOW_CREATE_GROUP', false)
          store.dispatch('socialFeed/fatchSocialFeeds')
          resetAll()
        } else {
          $toast('error', {
            message: res.message,
            className: 'toasted-bg-alert',
          })
        }
        addProcess.value = false
      } catch (error) {
        addProcess.value = false
        console.error(error)
      }
    } else if (availableFeeds.value.length > 0) {
      $toast('error', {
        message: 'Please select feeds',
        className: 'toasted-bg-alert',
      })
    }
  }
}

const makeFeedUnSelected = () => {
  availableFeeds.value.forEach((item: Feed) => {
    item.selected = false
  })
}
</script>

<style lang="scss" scoped>
.tooltip {
  color: var(--color);
  @apply absolute bg-offwhite-200 
  z-100 
  left-0
  -top-5 
  text-left 
  invisible 
  md:p-1.5 
  p-0.5 
  md:px-4 
  px-2 
  text-sm 
  rounded-xl 
  shadow-lg;
  width: auto;
}

.has-tooltip:hover .tooltip {
  @apply visible;
  transition: all 0.3s linear;
}
.setup {
  bottom: 530px;
}

.profile {
  width: 500px;
  height: 100%;
  z-index: 999999;
}

/* for slideInOut */
.slideInOut-enter-active,
.slideInOut-leave-active {
  transition: all 0.8s ease-in-out;
}
.slideInOut-enter-from,
.slideInOut-leave-to {
  right: -100%;
}
.card {
  height: calc(100% - 88px);
}
.table-td {
  padding: 3px 5px;
}
.table-td:hover {
  background-color: var(--color);
  // padding-top: 2px;
  // padding-bottom: 2px;
  @apply rounded-full;
}
.card_table {
  height: calc(100% - 75px);
}
.table_body {
  height: calc(100% - 32px);
}
.feeds-enter-from {
  opacity: 0;
}
.feeds-enter-active {
  transition: opacity 0.5s ease-in-out;
}
.feeds-enter-to {
  opacity: 1;
}
.scroll {
  scrollbar-color: var(--color) #ececec; /* Firefox 64 */
  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: var(--color);
  }
  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: var(--color);
  }
}
@media (min-height: 576px) {
  .min-height-250 {
    min-height: 193px;
  }
}
@media (max-width: 767px) {
  .profile {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 9999999999;
  }
  .show {
    width: 100%;
    right: 0px;
  }

  .hide {
    width: 100%;
    right: -100%;
  }
  .card {
    height: calc(100% - 115px);
  }
  .text-color-sm {
    color: var(--color);
  }
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: var(--placeholderColor);
}

:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: var(--placeholderColor);
}

::-ms-input-placeholder {
  /* Microsoft Edge */
  color: var(--placeholderColor);
}
</style>
