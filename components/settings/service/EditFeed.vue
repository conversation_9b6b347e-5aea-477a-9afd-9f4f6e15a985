<template>
  <section class="w-full h-full">
    <transition name="right-sidebar-trans">
      <div
        v-if="showSocialEditFeedModal"
        class="profile fixed bg-ash-dark right-0 md:-top-15 md:px-21 md:pt-8 flex flex-col scroll overflow-y-auto overflow-x-hidden md:rounded-l-2xl md:shadow-2xl md:drop-shadow-2xl"
      >
        <transition name="fadeInParent">
          <div
            v-if="addFeedSection"
            class="w-full h-full flex flex-col justify-between space-y-8 md:p-0 p-4 edit-feed-body"
          >
            <div class="w-full main-content">
              <div
                class="flex flex-row items-center justify-between md:h-12 h-8 relative"
              >
                <h2
                  class="title text-yellow-midlight xl:text-2xl md:text-xl md:font-bold text-xl"
                >
                  <transition name="fadeIn">
                    <span v-if="!emailVerified">Edit Feeds</span>
                  </transition>
                </h2>
                <button
                  :class="[emailVerified ? 'animLeft' : 'animRight']"
                  class="absolute focus:outline-none right-0 w-8 h-8 flex items-center"
                  @click="emailBackBtn ? back() : cancel()"
                >
                  <ClientOnly>
                    <fa
                      class="text-yellow-midlight xl:text-2xl md:text-xl md:font-bold text-2xl"
                      :class="emailVerified ? 'left-194' : ''"
                      :icon="['fas', emailBackBtn ? 'chevron-left' : 'times']"
                    />
                  </ClientOnly>
                </button>
              </div>

              <transition name="modalFadeIn">
                <div
                  v-if="hideInstagramModal"
                  class="w-full flex flex-col items-center"
                >
                  <!-- {{ editableFeedData }} -->

                  <div class="w-full justify-self-start my-5 mb-10">
                    <div
                      class="w-full flex flex-col items-center justify-center space-y-8"
                    >
                      <div v-if="editableFeedData.profilePic">
                        <img
                          :src="editableFeedData.profilePic"
                          class="w-15 h-15 rounded-full profilePic"
                          :alt="`${editableFeedData.name} profile picture`"
                        />
                      </div>
                      <div v-else>
                        <div
                          v-if="
                            currectProvider(
                              editableFeedData.provider,
                              'microsoft',
                            )
                          "
                          class="bg-white rounded-full h-15 w-15 p-2.5"
                        >
                          <img
                            :src="microsoftOutlookIcon"
                            alt="microsoftOutlookIcon"
                          />
                        </div>

                        <img
                          v-if="
                            currectProvider(
                              editableFeedData.provider,
                              'google',
                            ) ||
                            currectProvider(
                              editableFeedData.provider,
                              'youtube',
                            )
                          "
                          :src="gmailIcon"
                          class="h-15 w-15"
                          alt="gmailSvgIcon"
                        />
                        <SharedIconFacebookIcon
                          v-if="
                            currectProvider(
                              editableFeedData.provider,
                              'facebook',
                            )
                          "
                          class="h-15 w-15"
                          alt="facebookSvgIcon"
                        />
                        <SharedIconInstagramIcon
                          v-if="
                            currectProvider(
                              editableFeedData.provider,
                              'instagram',
                            )
                          "
                          class="h-15 w-15"
                          alt="instagramSvgIcon"
                        />
                        <SharedIconLinkedinIcon
                          v-if="
                            currectProvider(
                              editableFeedData.provider,
                              'linkedin',
                            )
                          "
                          class="h-15 w-15"
                          alt="linkedinSvgIcon"
                        />
                        <!-- <TwitterIcon
                          v-if="
                            currectProvider(
                              editableFeedData.provider,
                              'twitter'
                            )
                          "
                          class="h-15 w-15"
                          alt="twitterSvgIcon"
                        /> -->
                        <div
                          v-if="
                            currectProvider(
                              editableFeedData.provider,
                              'twitter',
                            )
                          "
                          class="h-15 w-15 twitter-icon"
                        ></div>
                        <SharedIconPinterestIcon
                          v-if="
                            currectProvider(
                              editableFeedData.provider,
                              'pinterest',
                            )
                          "
                          class="h-15 w-15"
                        >
                        </SharedIconPinterestIcon>
                        <SharedIconRedditIcon
                          v-if="
                            currectProvider(editableFeedData.provider, 'reddit')
                          "
                          class="h-15 w-15"
                        >
                        </SharedIconRedditIcon>
                        <SharedIconTiktokIcon
                          v-if="
                            currectProvider(editableFeedData.provider, 'tiktok')
                          "
                          class="h-15 w-15"
                        >
                        </SharedIconTiktokIcon>
                        <img
                          v-if="
                            currectProvider(editableFeedData.provider, 'web')
                          "
                          :src="websiteIcon"
                          class="w-15 h-15"
                          alt="Web Icon"
                        />
                      </div>
                      <div class="text-white opacity-50 text-left">
                        <h3 v-if="editableFeedData.name" class="break-all">
                          <strong>Name:</strong> {{ editableFeedData.name }}
                        </h3>
                        <p v-if="editableFeedData.username" class="break-all">
                          <strong>Username:</strong>
                          {{ editableFeedData.username }}
                        </p>
                        <p v-if="editableFeedData.type">
                          <strong>Type:</strong> {{ editableFeedData.type }}
                        </p>
                        <p v-if="editableFeedData.dateJoined">
                          <strong>Feed Added:</strong>
                          <date-time
                            :datetime="editableFeedData.dateJoined"
                            format="MMMM dd, yyyy"
                            :show-time="false"
                            :friendly="false"
                          ></date-time>
                        </p>
                        <p v-if="editableFeedData.expireTime">
                          <strong>Expire Time:</strong>
                          <date-time
                            :datetime="editableFeedData.expireTime"
                            format="MMMM dd, yyyy"
                            :show-time="false"
                            :friendly="false"
                          ></date-time>
                        </p>
                      </div>
                    </div>
                  </div>

                  <div
                    class="social-icons-wrapper w-full overflow-hidden"
                    :class="emailVerified ? 'demo2' : 'demo1'"
                  >
                    <transition name="fadeIn">
                      <div
                        v-if="!emailVerified"
                        class="w-full flex flex-col items-center h-full"
                      >
                        <h2
                          class="text-white md:text-xl text-md md:px-0 px-8 opacity-50 mb-5"
                        >
                          Change access:
                        </h2>
                        <div
                          class="flex flex-row items-center justify-center w-full md:px-12"
                        >
                          <img
                            v-if="
                              currectProvider(
                                editableFeedData.provider,
                                'google',
                              )
                            "
                            src="~/assets/img/png/btn_google_signin_light_normal_web.png"
                            class="cursor-pointer"
                            alt="gmailSvg Icon"
                            @click.stop="authorize('google')"
                          />
                          <img
                            v-if="
                              currectProvider(
                                editableFeedData.provider,
                                'microsoft',
                              )
                            "
                            :src="microsoftOutlook"
                            class="cursor-pointer microsoft"
                            alt="microsoftOutlook Icon"
                            @click.stop="authorize('microsoft')"
                          />
                          <img
                            v-if="
                              currectProvider(
                                editableFeedData.provider,
                                'youtube',
                              )
                            "
                            :src="youtube"
                            class="cursor-pointer youtube_icon"
                            alt="Youtube Icon"
                            @click.stop="authorize('youtube')"
                          />
                          <div
                            v-if="
                              currectProvider(
                                editableFeedData.provider,
                                'twitter',
                              )
                            "
                            class="w-15 h-15 cursor-pointer"
                            @click.stop="authorize('twitter')"
                          >
                            <!-- <TwitterIcon class="w-15 h-15 cursor-pointer" /> -->
                            <div
                              class="w-15 h-15 cursor-pointer twitter-icon"
                            ></div>
                          </div>
                          <div
                            v-if="
                              currectProvider(
                                editableFeedData.provider,
                                'facebook',
                              )
                            "
                            class="w-15 h-15 cursor-pointer"
                            @click.stop="authorize('facebook')"
                          >
                            <SharedIconFacebookIcon
                              class="w-15 h-15 cursor-pointer"
                            />
                          </div>
                          <div
                            v-if="
                              currectProvider(
                                editableFeedData.provider,
                                'linkedin',
                              )
                            "
                            class="w-15 h-15 cursor-pointer"
                            @click.stop="authorize('linkedin')"
                          >
                            <SharedIconLinkedinIcon
                              class="w-15 h-15 cursor-pointer"
                            />
                          </div>
                          <div
                            v-if="
                              currectProvider(
                                editableFeedData.provider,
                                'instagram',
                              )
                            "
                            class="w-15 h-15 cursor-pointer instagram"
                            @click.stop="instagramModal()"
                          >
                            <SharedIconInstagramIcon
                              class="w-15 h-15 cursor-pointer"
                            />
                          </div>
                          <div
                            v-if="
                              currectProvider(
                                editableFeedData.provider,
                                'pinterest',
                              )
                            "
                            class="w-15 h-15 cursor-pointer"
                            @click.stop="authorize('pinterest')"
                          >
                            <SharedIconPinterestIcon
                              class="w-15 h-15 cursor-pointer"
                            />
                          </div>
                          <div
                            v-if="
                              currectProvider(
                                editableFeedData.provider,
                                'reddit',
                              )
                            "
                            class="w-15 h-15 cursor-pointer"
                            @click.stop="authorize('reddit')"
                          >
                            <SharedIconRedditIcon
                              class="w-15 h-15 cursor-pointer"
                            />
                          </div>
                          <div
                            v-if="
                              currectProvider(
                                editableFeedData.provider,
                                'tiktok',
                              )
                            "
                            class="w-15 h-15 cursor-pointer"
                            @click.stop="authorize('tiktok')"
                          >
                            <SharedIconTiktokIcon
                              class="w-15 h-15 cursor-pointer"
                            />
                          </div>
                          <img
                            v-if="
                              currectProvider(editableFeedData.provider, 'web')
                            "
                            :src="websiteIcon"
                            class="w-15 h-15"
                            alt="Web Icon"
                          />
                        </div>
                        <div
                          class="border-b-2 border-offwhite-500 w-full md:mt-12 mt-8 rounded-sm"
                        ></div>
                      </div>
                    </transition>
                  </div>
                  <!-- <div
                    class="flex flex-row justify-between items-center pt-1 w-full"
                  >
                    <label for="status" class="text-gray-1400 text-lg"
                      >Active</label
                    >
                    <div
                      class="
                        relative
                        inline-block
                        w-9
                        align-middle
                        select-none
                        transition
                        duration-200
                        ease-in
                      "
                    >
                      <input
                        id="status"
                        type="checkbox"
                        name="toggle"
                        class="
                          toggle-checkbox
                          absolute
                          block
                          w-5
                          h-5
                          rounded-full
                          bg-white
                          border-4
                          appearance-none
                          cursor-pointer
                        "
                      />
                      <label
                        for="status"
                        class="
                          toggle-label
                          block
                          overflow-hidden
                          h-5
                          rounded-full
                          transition-all
                          duration-800
                          ease-in-out
                          bg-white
                          cursor-pointer
                        "
                      ></label>
                    </div>
                  </div> -->
                  <!-- <div class="border-b-2 border-offwhite-500 w-full  mb-[30px] rounded-sm"></div> -->
                  <div v-if="config.public.workflow === 'dev'" class="w-full">
                    <div class="flex justify-between items-center">
                      <h3 class="text-white md:text-xl text-md">
                        Exclude Direct Messages
                      </h3>
                      <div>
                        <SettingsServiceToggleIcon
                          @customEvent="setConfirm"
                        ></SettingsServiceToggleIcon>
                      </div>
                    </div>
                    <div v-if="confirmDelete" class="mt-[50px]">
                      <div
                        class="flex flex-col justify-center items-center text-[#C2C2C2]"
                      >
                        <label class="text-xl"
                          >Do you want to delete your archive of direct</label
                        >
                        <label class="text-xl"
                          >messages connected to this account?</label
                        >
                      </div>
                      <div class="flex justify-center pt-4">
                        <button
                          class="px-[52px] py-[9px] text-white rounded-full border-2 border-white"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                  <div
                    v-if="config.public.workflow === 'dev'"
                    class="border-b-2 border-offwhite-500 w-full mt-[30px] rounded-sm"
                  ></div>
                  <div
                    class="send-guest-email-wrapper w-full flex flex-col transition-all h-40 duration-1000 md:mt-6"
                  >
                    <div
                      class="flex flex-col justify-center items-center text-[#C2C2C2]"
                    >
                      <label class="text-xl"
                        >Need to archive someone else's account?</label
                      >
                      <label class="text-xl mb-2"
                        >Send authorization request to them:</label
                      >
                    </div>
                    <input
                      id="email"
                      v-model="guestEmail"
                      type="email"
                      placeholder="Enter Email"
                      class="text-yellow-midlight w-full rounded-full py-2 px-2.5 outline-none focus:outline-none bg-white border-b border-white placeholder-yellow-midlight placeholder-opacity-50 text-center align-start"
                      @keyup="v$.guestEmail.$touch()"
                    />
                    <!-- @change="checkEmailIsVerified" -->
                    <template v-if="v$.guestEmail.$error">
                      <span
                        v-if="v$.guestEmail.email.$invalid"
                        class="text-red-400 text-xs mt-0 pl-2"
                      >
                        The Email is Invalid
                      </span>
                      <span
                        v-if="v$.guestEmail.required.$invalid"
                        class="text-red-400 text-xs mt-0 pl-2"
                      >
                        The field is required
                      </span>
                    </template>
                    <transition name="fadeIn">
                      <div v-if="emailVerified" class="text-center mt-4">
                        <button
                          type="submit"
                          class="w-44 py-1.5 text-white bg-yellow-midlight rounded-full border border-yellow-midlight outline-none font-bold text-base relative"
                          :disabled="emailSendProcess"
                          @click="sendGuestEmail()"
                        >
                          <span>Send</span>
                          <ClientOnly>
                            <fa
                              v-if="emailSendProcess"
                              class="absolute text-white mx-2 right-0 top-2 font-bold animate-spin"
                              :icon="['fas', 'spinner']"
                            />
                          </ClientOnly>
                        </button>
                      </div>
                    </transition>
                  </div>
                </div>
              </transition>

              <transition name="modalFadeIn">
                <div
                  v-if="showInstagramModal"
                  class="w-full instagram_modal main-content flex flex-col items-center justify-center"
                >
                  <div
                    class="w-full flex flex-col justify-center items-center space-y-10"
                  >
                    <SharedIconInstagramIcon class="w-15 h-15 cursor-pointer" />
                    <p class="md:text-xl text-md text-white opacity-50">
                      Which type of account?
                    </p>
                    <div
                      class="w-full flex items-center justify-center space-x-4"
                    >
                      <button
                        class="w-38 py-1.5 text-lg font-bold text-white rounded-full instagram_Personal"
                        :style="{
                          backgroundColor: globalColorPanel.backgroundColor,
                        }"
                        @click.stop="authorize('instagram')"
                      >
                        Personal
                      </button>
                      <button
                        class="w-38 py-1.5 text-lg font-bold text-white rounded-full instagram_Business"
                        :style="{
                          backgroundColor: globalColorPanel.backgroundColor,
                        }"
                        @click.stop="authorize('facebook')"
                      >
                        Business
                      </button>
                    </div>
                    <p class="md:text-xl text-md text-white text-center">
                      Facebook (Meta) owns Instagram. Clicking on “Personal”
                      will route you to Instagram’s authorization. Clicking on
                      “Business” will ask for your authorization through
                      Facebook.
                    </p>
                  </div>
                </div>
              </transition>
            </div>

            <div class="w-full pb-4 row-span-3">
              <!-- <div class="flex flex-col w-full rounded-xl"> -->
              <div class="flex flex-row justify-around">
                <button
                  v-if="!showInstagramModal"
                  class="disconnect focus:outline-none w-44 h-10 text-yellow-midlight bg-ash-dark rounded-full mr-2.5 border-2 border-yellow-midlight outline-none font-bold md:text-lg text-xl"
                  @click="deleteFeed()"
                >
                  <span>Disconnect</span>
                </button>

                <button
                  v-if="!showInstagramModal"
                  class="cancel focus:outline-none w-44 h-10 text-white bg-yellow-midlight rounded-full mr-2.5 border-2 border-yellow-midlight outline-none font-bold md:text-lg text-xl"
                  @click="cancel()"
                >
                  <span>Exit</span>
                </button>

                <button
                  v-if="showInstagramModal"
                  class="focus:outline-none w-44 h-10 text-yellow-midlight bg-ash-dark rounded-full mr-2.5 border-2 border-yellow-midlight outline-none font-bold md:text-lg text-xl back_button"
                  @click="back()"
                >
                  <span>Back</span>
                </button>
              </div>
              <!-- </div> -->
            </div>
          </div>
        </transition>
      </div>
    </transition>
    <AlertConfirmModal
      :processing="deleteProcess"
      :show="deleteAlert"
      message="Even after disconnecting, you can access the data already
                archived. And if you reconnect a disconnected feed, you are
                charged for the time between disconnecting it and reconnecting
                it."
      title="Are you sure to disconnect this?"
      confirm-btn-text="Disconnect"
      @cancel="deleteCancel"
      @delete="deleteConfirm"
    ></AlertConfirmModal>
  </section>
</template>
<script setup lang="ts">
import { useStore } from 'vuex'
import youtubePng from '~/assets/img/png/yt_logo_rgb_dark.png'
import GmailIcon from '~/assets/img/svg/gmail.svg'
import MicrosoftOutlook from '~/assets/img/svg/ms-symbollockup_signin_light.svg'
import MicrosoftOutlookIcon from '~/assets/img/svg/microsoft-outlook.svg'
import { useVuelidate } from '@vuelidate/core'
import { required, email } from '@vuelidate/validators'
import WebsiteIcon from '~/assets/img/svg/website.svg'
import { SEND_ACCESS_REQUEST, SOCIAL_AUTH } from '~/constants/urls'

import { useNuxtApp } from '#app'

const props = defineProps({
  globalColorPanel: {
    type: Object,
    default() {
      return { backgroundColor: '' }
    },
  },
})

const nuxtApp = useNuxtApp()
const { $toast, $social } = useNuxtApp()
const config = useRuntimeConfig()
const store = useStore()
const confirmDelete = ref<boolean>(false)
const setConfirm = ($event: boolean) => {
  confirmDelete.value = $event
}
const guestEmail = ref<string>('')
const rules = {
  guestEmail: {
    required,
    email,
  },
}
const v$ = useVuelidate(rules, { guestEmail })
const { fetch } = useFetched()

const addFeedSection = ref<boolean>(true)
const youtube = ref<string>(youtubePng)
const websiteIcon = ref<string>(WebsiteIcon)
const gmailIcon = ref<string>(GmailIcon)
const microsoftOutlook = ref<string>(MicrosoftOutlook)
const microsoftOutlookIcon = ref<string>(MicrosoftOutlookIcon)
const emailBackBtn = ref<boolean>(false)
const emailVerified = ref<boolean>(false)
// authorizationProviders: [],
const emailSendProcess = ref<boolean>(false)
const deleteAlert = ref<boolean>(false)
const deleteProcess = ref<boolean>(false)
const hideInstagramModal = ref<boolean>(true)
const showInstagramModal = ref<boolean>(false)

// Computed properties
const showAddFeedsComp = computed(() => store.getters['header/getAddFeed'])
const showBlurActiveModal = computed(
  () => store.getters['header/showBlurActiveModal'],
)
const showSocialEditFeedModal = computed(
  () => store.getters['socialFeed/showEditFeedModal'],
)
const editableFeedData = computed(
  () => store.getters['socialFeed/editableFeedData'],
)

watch(
  () => guestEmail.value,
  (newValue: string) => {
    if (newValue) {
      checkEmailIsVerified()
    }
  },
)

// Define the actions
const deleteSocialFeed = (value:number) => store.dispatch('socialFeed/deleteSocialFeed', value)
const show_login = () => store.dispatch('loginAnimation/show_login')
const profileLogout = () => store.dispatch('profile/profileLogout')

const instagramModal = () => {
  hideInstagramModal.value = false
  setTimeout(() => {
    showInstagramModal.value = true
  }, 500)
}
const checkEmailIsVerified = () => {
  if (!v$.value.$invalid) {
    emailVerified.value = true
    emailBackBtn.value = true
  } else {
    emailVerified.value = false
    emailBackBtn.value = false
  }
}
const cancel = () => {
  guestEmail.value = ''
  v$.value.$reset()
  store.commit('socialFeed/SHOW_SOCIAL_EDIT_FEED_MODAL', {
    open: false,
    data: {},
  })
  store.dispatch('header/removeOverlay')
}
const closeAccount = () => {
  logout()
}
const logout = async () => {
  show_login(true)
  await profileLogout()
  store.commit('SET_LOCK_SCREEN', false)
  store.commit('header/BLUR_ACTIVE_FEED_MODAL', false)
  store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false)
}

const finish = () => {
  addFeedSection.value = false
}
const authorize = async (provider: string) => {
  store.commit('socialFeed/SET_ARCHIVE_FEED', false)
  await $social.redirect(provider)
}
const authorizeTwitter = async () => {
  const apiUrl = `${SOCIAL_AUTH}twitter/`
  await $social.redirectOAuth1('twitter', apiUrl)
}
const closeArchiveSystemSetting = () => {
  setTimeout(() => {
    addFeedSection.value = true
  }, 700)
}
const saveAllWork = () => {
  addFeedSection.value = true
  emailBackBtn.value = false
  guestEmail.value = ''
  store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false)
  store.dispatch('header/showBlurAndActive', false)
}
interface SendGuestEmail {
  message: string
  status: boolean
  success: boolean
}
const sendGuestEmail = async () => {
  v$.value.$touch()
  if (!v$.value.$invalid) {
    $toast('clear')
    try {
      emailSendProcess.value = true
      const res = (await fetch(SEND_ACCESS_REQUEST, {
        method: 'POST',
        body: { email: guestEmail.value },
      })) as SendGuestEmail
      if (res.success) {
        $toast('success', {
          message: res.message,
          className: 'toasted-bg-archive',
        })
        guestEmail.value = ''
        emailBackBtn.value = false
        emailVerified.value = false
        v$.value.$reset()
      } else {
        $toast('error', {
          message: res.message,
          className: 'toasted-bg-alert',
        })
      }
      emailSendProcess.value = false
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(error)
    }
  }
}
const back = () => {
  guestEmail.value = ''
  emailBackBtn.value = false
  emailVerified.value = false
  showInstagramModal.value = false
  setTimeout(() => {
    hideInstagramModal.value = true
  }, 500)
  v$.value.$reset()
}
const deleteFeed = () => {
  deleteAlert.value = true
}
const deleteCancel = () => {
  deleteAlert.value = false
}
const deleteConfirm = async () => {
  $toast('clear')
  deleteProcess.value = true
  try {
    deleteProcess.value = true
    const response = await deleteSocialFeed(editableFeedData.value.id)
    if (response.success) {
      $toast('success', {
        message: response.message,
        className: 'toasted-bg-archive',
      })
      store.commit('socialFeed/SHOW_SOCIAL_EDIT_FEED_MODAL', {
        open: false,
        data: {},
      })
      store.commit('header/REMOVE_OVERLAY')
    } else {
      $toast('error', {
        message: response.message,
        className: 'toasted-bg-alert',
      })
    }
  } catch (error) {
    console.error(error)
  } finally {
    deleteAlert.value = false
    deleteProcess.value = false
  }
}
const currectProvider = (editableProvider: string, currentProvider: string) => {
  return editableProvider.toLowerCase() === currentProvider
}
</script>
<style lang="scss" scoped>
.twitter-icon {
  background-size: cover;
  animation: twitter 6s ease-in-out;
  animation-iteration-count: infinite;
}

@keyframes twitter {
  0% {
    background-image: url('~/assets/img/icon/TwitterIcon/twitter.svg');
  }

  50% {
    background-image: url('~/assets/img/icon/TwitterIcon/X_logo_png.png');
  }

  100% {
    background-image: url('~/assets/img/icon/TwitterIcon/twitter.svg');
  }
}

.youtube_icon {
  height: 44px;
}

.modalFadeIn-enter-active,
.modalFadeIn-leave-active {
  transition: opacity 0.5s;
}

.modalFadeIn-enter-from,
.modalFadeIn-leave-to {
  opacity: 0;
}

.toggle-checkbox {
  width: 16px;
  height: 16px;
  border: 0px;
  top: 2px;
  left: 2px;
  transition: all 0.5s ease-in-out;
  background-color: #393e46;

  &:checked {
    @apply right-0;
    left: 18px;
    transition: all 0.5s ease-in-out;
    background-color: #ffffff;
  }

  &:checked + .toggle-label {
    transition: all 0.5s ease-in-out;
    @apply bg-yellow-midlight;
  }
}

.profile {
  width: 500px;
  height: 100%;
  top: 0px;
  z-index: 9999999999;
}

.scroll {
  overflow-x: hidden;
  scrollbar-color: #e0ad1f #ececec;

  /* Firefox 64 */
  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: #e0ad1f;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #e0ad1f;
  }
}

.form-card > .error {
  margin-top: 0px;
}

.right-sidebar-trans-enter-from,
.right-sidebar-trans-leave-to {
  right: -100%;
}

.right-sidebar-trans-enter-to,
.right-sidebar-trans-leave {
  right: 0;
}

.right-sidebar-trans-enter-active {
  transition: all 0.8s ease-in-out;
}

.right-sidebar-trans-leave-active {
  transition: all 0.8s ease-in-out;
}

// .content-wrapper {
//   height: calc(100% - 40px) !important;
// }

.overlay-web {
  position: fixed;
  top: 60px;
  bottom: 0;
  left: 100px;
  right: 0;
  z-index: 101;
  background-color: rgba(255, 255, 255, 0.1);
  opacity: 1;
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  pointer-events: all;
}

.fadeIn-enter-from,
.fadeIn-leave-to {
  opacity: 0;
}

.fadeIn-enter-to,
.fadeIn-leave-from {
  opacity: 1;
}

.fadeIn-enter-active,
.fadeIn-leave-active {
  transition: all 0.5s ease-in-out;
}

.fadeInParent-enter-from,
.fadeInParent-leave-to {
  opacity: 0;
}

.fadeInParent-enter-to,
.fadeInParent-leave-from {
  opacity: 1;
}

.fadeInParent-enter-active,
.fadeInParent-leave-active {
  transition: all 0.5s ease-in-out;
}

.animLeft {
  transform: translateX(0);
  animation-name: changePositionLeft;
  animation-duration: 1s;
  animation-fill-mode: forwards;
}

@keyframes changePositionLeft {
  to {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
    right: 85.5%;
    // left: 31px;
  }
}

.animRight {
  transform: translateX(52%);
  animation-name: changePositionRight;
  animation-duration: 1s;
}

@keyframes changePositionRight {
  from {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
    right: 92%;
  }

  to {
    -webkit-transform: translateX(52%);
    transform: translateX(52%);
    right: 0%;
  }
}

/*end right sidebar section transition*/

.demo1 {
  height: 180px;
  transition: height 1s 0.5s;
}

.demo2 {
  height: 0;
  transition: height 1s 0.5s;
}

// .main-content {
//   height: calc(100% - 40px); // this 42 isjhj bottom button height
// }

.fadeInBtn-enter-active,
.fadeInBtn-leave-active {
  transition: opacity 1s;
}

.fadeInBtn-enter-from,
.fadeInBtn-leave-to {
  opacity: 0;
}

@media (max-width: 767px) {
  .profile {
    width: 100%;
    height: 100%;
    top: 0;
    z-index: 9999999999;
  }

  .overlay-web {
    left: 0 !important;
    height: 100%;
  }

  .animLeft {
    transform: translateX(0);
    animation-name: changePositionLeft;
    animation-duration: 1s;
    animation-fill-mode: forwards;
  }

  @keyframes changePositionLeft {
    to {
      -webkit-transform: translateX(-100%);
      transform: translateX(-100%);
      right: 100%;
    }
  }

  .left-194 {
    margin-left: 194%;
  }

  // .main-content {
  //   height: calc(100% - 40px); // this 42 is bottom button height
  //   min-height: 100vh;
  // }
}

@media (max-height: 680px) {
  .edit-feed-body {
    height: 150%;
  }
}
</style>
