<template>
  <div
    class="overflow-hidden flex flex-col"
    :style="{
      '--color': color,
      '--background': background,
      '--scroll-color': scrollColor,
      '--caretBg': caretBg,
      '--caretColor': caretColor,
    }"
  >
    <label v-if="label" :for="id">{{ label }}</label>
    <div
      class="input overflow-hidden flex items-center w-full rounded-full pr-3 relative"
    >
      <select
        class="rounded-full"
        :class="[
          classStyleName,
          toggleSelect ? 'rounded-full' : ' round-remove',
        ]"
        :style="{ '--color': color }"
        :value="modelValue"
        v-bind="$attrs"
        tabindex="0"
        @input="$emit('update:modelValue', $event.target.value)"
        @click="toggleSelect = !toggleSelect"
        @change="toggleSelect = false"
        @focusin="closeSideBar()"
        @focusout="toggleSelect = true"
      >
        <option
          v-if="placeHolder"
          :disabled="placeHolderDisabled"
          :selected="placeHolder"
        >
          {{ placeHolder }}
        </option>
        <option
          v-for="option in options"
          :key="option.id ? option.id : option.value"
          :value="responseById ? option.id : option.value"
        >
          {{ option.text }}
        </option>
      </select>
      <!-- <span class="select-toggle"> -->
      <ClientOnly>
        <fa
          class="text-2xl"
          :style="{ color: caretColor }"
          :icon="[
            'fas',
            toggleSelect || showVCalender ? 'caret-down' : 'caret-up',
          ]"
        />
      </ClientOnly>
    </div>

    <!-- </span> -->
    <div v-if="error" class="error text-red-500 text-sm whitespace-nowrap">
      <span v-for="error in errorMessage" :key="error.$uid">
        {{ errorMessages(error, modelValue) }}
      </span>
    </div>
  </div>
</template>
<script setup>
import { useInputValidations } from '~/composables/useValidations'
const props = defineProps({
  id: {
    type: String,
    required: false,
    default: null,
  },
  label: {
    type: String,
    required: false,
    default: null,
  },
  options: {
    type: Array,
    required: false,
    default: () => {},
  },
  placeHolder: {
    type: String,
    required: false,
    default: '',
  },
  placeHolderDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
  errorMessage: {
    type: Array,
    required: false,
    default: () => [],
  },
  error: {
    type: Boolean,
    required: false,
    default: false,
  },
  color: {
    type: String,
    required: false,
    default: '#4D4D4D',
  },
  caretColor: {
    type: String,
    required: false,
    default: '#fff',
  },
  caretBg: {
    type: String,
    required: false,
    default: '',
  },
  background: {
    type: String,
    required: false,
    default: '#fff',
  },
  scrollColor: {
    type: String,
    required: false,
    default: '#fff',
  },
  classStyleName: {
    type: String,
    required: false,
    default: '',
  },
  responseById: {
    type: Boolean,
    required: false,
    default: false,
  },
  showVCalender: {
    type: Boolean,
    default: false,
  },
  modelValue: {
    type: null,
    default: '',
  },
})

const toggleSelect = ref(true)
const { errorMessages } = useInputValidations()

watch(
  () => props.modelValue,
  () => {
    toggleSelect.value = true
  },
)

// Methods
const closeSideBar = () => {
  // Uncomment and replace with actual store commits if needed
  // store.commit('agentBook/SET_SHOW_USER_INFO', false);
  // store.commit('agentBook/SET_AGENT_SHOW_HIDE', false);
  // store.commit('profile/SET_PROFILE_MODAL', false);
  // store.commit('notifications/SHOW_NOTIFICATION_MODAL', false);
  // store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false);
  // store.commit('search/SET_SAVE_SEARCH_MODAL', false);
}
</script>

<style lang="scss" scoped>
.input {
  $color: var(--color);
  $background-color: var(--background);
  $caretBg: var(--caretBg);
  $caretColor: var(--caretColor);
  background-color: $background-color;
  @apply relative;
  label {
    @apply text-white block h-10;
  }
  select {
    color: $color;
    background-color: $background-color;
    // box-shadow: 0px 1px 3px rgba(34, 40, 49, 0.7);
    border-radius: 25px;
    @apply w-full py-2 md:px-4 px-3 focus:outline-none font-bold cursor-pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    // background-image: url("data:image/svg+xml;utf8,<svg height='24' fill='currentColor' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
    // background-repeat: no-repeat;
    // background-position-x: 98%;
    // background-position-y: center;
    option {
      @apply font-semibold lg:text-md text-xs;
      &:disabled {
        @apply text-gray-200;
      }
      &:focus,
      &:checked,
      &:hover {
        color: white;
        background-color: $color !important;
      }
    }
  }
  .round-remove {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
  .select-toggle {
    color: #ffffff;
    background: $caretBg;
    @apply relative pointer-events-none;
    padding-left: 3px !important;
  }
  select option:checked {
    background: linear-gradient(#d6d6d6, #d6d6d6);
    background-color: dodgerblue !important; /* for IE */
    color: white !important;
  }
  // select option:disabled {
  //   color: #5B2121;
  //   font-weight: bold;
  // }
}
select.searchPageScrollWidth::-webkit-scrollbar {
  width: 2px;
}
select.searchPageScrollStyle::-webkit-scrollbar-track {
  background-color: var(--scroll-color);
}
select.searchPageScrollStyle::-webkit-scrollbar-thumb {
  background-color: var(--scroll-color);
}
.search-select-input {
  height: 40px;
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.target-select-input-alert {
  font-weight: normal !important;
  padding-top: 0.4rem !important;
}
</style>
