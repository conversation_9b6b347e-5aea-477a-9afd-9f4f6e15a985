<template>
  <div
    ref="content__wrapper"
    class="content__tabs xl:w-[600px] w-[400px] flex flex-nowrap md:bg-white bg-none items-center rounded-lg sm:rounded-full md:h-10 h-10 md:shadow lg:mt-0"
  >
    <div
      class="background__circle bg-blue-200"
      :style="{
        left: `${offsetLeftPx - 4}px`,
        width:
          activeComponent !== 'Insights'
            ? `${backgroundWidth + 4}px`
            : `${backgroundWidth + 8}px`,
      }"
    ></div>
    <NuxtLink
      :to="hubRoute"
      ref="hub"
      :class="activeComponent === 'Hub' ? 'active' : ''"
      class="tab cursor-pointer rounded-full text-center h-auto md:px-4 px-3 py-1 w-1/4"
      data-index="0"
      @click.native="showTabMenu('Hub'), animate()"
    >
      <span class="pointer-events-none md:text-md text-sm font-semibold"
        >Hub</span
      >
    </NuxtLink>
    <NuxtLink
      :to="postRoute"
      ref="post"
      :class="activeComponent === 'Post' ? 'active' : ''"
      class="tab cursor-pointer rounded-full text-center h-auto md:px-4 px-3 py-1 w-1/4"
      data-index="1"
      @click.native="showTabMenu('Post'), animate()"
    >
      <span class="pointer-events-none md:text-md text-sm font-semibold"
        >Post</span
      >
    </NuxtLink>
    <NuxtLink
      to="/source/browse"
      ref="browse"
      :class="activeComponent === 'Browse' ? 'active' : ''"
      class="tab cursor-pointer rounded-full text-center h-auto md:px-4 px-3 py-1 w-1/4"
      data-index="2"
      @click.native="showTabMenu('Browse'), animate()"
    >
      <span class="pointer-events-none md:text-md text-sm font-semibold"
        >Browse</span
      >
    </NuxtLink>
    <NuxtLink
      to="/source/insights"
      ref="insights"
      :class="activeComponent === 'Insights' ? 'active' : ''"
      class="tab cursor-pointer rounded-full text-center h-auto md:px-4 px-3 py-1 w-1/4"
      data-index="2"
      @click.native="showTabMenu('Insights'), animate()"
    >
      <span class="pointer-events-none md:text-md text-sm font-semibold"
        >Insights</span
      >
    </NuxtLink>
  </div>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
const offsetLeftPx = ref<number>(0)
const backgroundWidth = ref<number>(0)
const circleBackgroundColor = ref<string>('bg-gray-900')
const backgroundColor = ref<string>('bg-gray-500')
const textBackgroundColor = ref<string>('text-gray-900')
const activeComponent = ref<string>('Hub')
const activeOldIndex = ref<number>(0)
const activeCurrentIndex = ref<number>(0)
const hub = ref<HTMLElement | null>(null)
const browse = ref<HTMLElement | null>(null)
const content__wrapper = ref<HTMLElement | null>(null)
const postRoute = ref('/source/post/list')
const post = ref<HTMLElement | null>(null)
const insights = ref<HTMLElement | null>(null)

const windowWidth = ref<number>(window.innerWidth)

const emit = defineEmits<{
  (event: 'change', value: string): void
}>()

const router = useRouter()
const route = useRoute()
const store = useStore()
const currentComponent = computed(() => store.state.setting.currentComponent)

watch(windowWidth, (newWidth: number, oldWidth: number) => {
  if (newWidth > oldWidth || newWidth < oldWidth || oldWidth == newWidth) {
    setTimeout(async () => {
      const activeLink =
        content__wrapper.value?.querySelector<HTMLElement>('.tab.active')
      backgroundWidth.value = await activeLink?.scrollWidth
      const backgroundColor1 = activeLink.dataset?.themeBg
      const textBackgroundColor1 = activeLink.dataset?.themeText
      const circleBackgroundColor1 = activeLink.dataset.themeCircle
      circleBackgroundColor.value = circleBackgroundColor1
      backgroundColor.value = backgroundColor1
      textBackgroundColor.value = textBackgroundColor1
      offsetLeftPx.value = activeLink.offsetLeft
    }, 1100)
  }
})

watch(
  () => currentComponent.value,
  (data: string) => {
    if (data === 'Hub') {
      hub.value?.$el?.click()
    }
  },
)

watch(
  () => route,
  (to: { fullPath: string }, from: { fullPath: string }) => {
    if (to.fullPath === '/source/hub') {
      hub.value?.$el?.click()
    }
  },
)
const sideBarAccountItems = computed(
  () => store.state.social.sideBarAccountItems,
)
const hubRoute = computed(() => {
  if (sideBarAccountItems.value && sideBarAccountItems.value.length > 0) {
    return `/source/hub/socials/${sideBarAccountItems.value[0].username}/all`
  } else {
    return `/source/hub`
  }
})
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
onMounted(() => {
  window.addEventListener('resize', handleResize)
  const hash = route.name.split('-')[1]
  const postHash = route.name.split('-')[2]
  if (hash === 'hub') {
    setTimeout(() => {
      // hub.value?.$el?.click()
    }, 1000)
  } else if (
    postHash === 'list' ||
    postHash === 'week' ||
    postHash === 'month'
  ) {
    postRoute.value = route.path
    setTimeout(() => {
      post.value?.$el?.click()
    }, 1000)
  } else if (hash === 'browse') {
    setTimeout(() => {
      browse.value?.$el?.click()
    }, 1000)
  } else if (hash === 'insights') {
    setTimeout(() => {
      insights.value?.$el?.click()
    }, 1000)
  }

  setTimeout(() => {
    const activeLink =
      content__wrapper.value?.querySelector<HTMLElement>('.tab.active')
    backgroundWidth.value = activeLink?.scrollWidth
    const backgroundColor1 = activeLink.dataset.themeBg
    const textBackgroundColor1 = activeLink.dataset.themeText
    const circleBackgroundColor1 = activeLink.dataset.themeCircle
    circleBackgroundColor.value = circleBackgroundColor1
    backgroundColor.value = backgroundColor1
    textBackgroundColor.value = textBackgroundColor1
    offsetLeftPx.value = activeLink.offsetLeft
  }, 580)
})

// methods
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

const animate = async () => {
  const activeLink = event.target.closest('.tab')
  backgroundWidth.value = await activeLink?.scrollWidth

  const backgroundColor1 = activeLink.dataset.themeBg
  const textBackgroundColor1 = activeLink.dataset.themeText
  const circleBackgroundColor1 = activeLink.dataset.themeCircle
  circleBackgroundColor.value = circleBackgroundColor1
  backgroundColor.value = backgroundColor1
  textBackgroundColor.value = textBackgroundColor1
  offsetLeftPx.value = activeLink.offsetLeft
}

const showTabMenu = (componentName: string) => {
  const tabWrapper = event.target.closest('.content__tabs')
  const oldActiveTab = tabWrapper.querySelector('.tab.active')
  if (componentName !== activeComponent.value) {
    activeOldIndex.value = oldActiveTab.dataset.index
    const activeCurrentIndex1 = event.target.dataset.index

    setTimeout(() => {
      activeCurrentIndex.value = activeCurrentIndex1
      activeComponent.value = componentName
      emit('change', activeComponent.value)
    }, 580)
  } else {
    activeOldIndex.value = oldActiveTab.dataset.index

    setTimeout(() => {
      activeCurrentIndex.value = 0
      activeComponent.value = 'Hub'
      emit('change', activeComponent.value)
    }, 500)
  }
  // console.log(route.path)
  // store.commit('setting/SET_CURRENT_COMPONENT', componentName)
  // router.push(`/source/${componentName.toLowerCase()}/socials`)
}
</script>

<style lang="scss" scoped>
.content__tabs {
  position: relative;
  .background__circle {
    top: 0px;
    left: 0px;
    transition:
      width 0.3s ease-in-out 0.2s,
      left 0.5s ease-in-out;
    @apply absolute h-full rounded-full inline-block;
  }
  .tab {
    @apply relative overflow-hidden;
    > span {
      position: relative;
      transition: color 0.2s ease-in-out;
      @apply text-[#525252];
    }
    &.active {
      > span {
        @apply text-white;
      }
    }
  }
}
</style>
