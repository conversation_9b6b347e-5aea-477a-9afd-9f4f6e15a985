<template>
  <div class="w-full">
    <p
      class="text-orange-600 text-center text-xl mb-4"
      :style="{ color: globalColorPanel.backgroundColor }"
    >
      Social Media
    </p>
    <div class="w-full flex flex-col items-center h-full">
      <div
        class="flex flex-row items-center w-full md:px-12"
        :class="activeComponent !== '' ? 'justify-center' : 'justify-between'"
      >
        <!-- TwitterIcon -->
        <div
          v-if="activeComponent === '' || activeComponent === 'Twitter'"
          class="relative"
        >
          <div v-if="twitterProvider.length > 0" class="social-feed-count">
            <div class="pr-0.5 pt-0.5">
              {{ twitterProvider.length }}
            </div>
          </div>
          <div
            class="rounded-full w-15 h-15 cursor-pointer"
            @click.stop="$emit('selectCurrentAccess', 'twitter')"
          >
            <!-- <TwitterIcon class="rounded-full w-15 h-15 cursor-pointer" /> -->
            <div
              class="rounded-full w-15 h-15 cursor-pointer twitter-icon"
            ></div>
          </div>
        </div>
        <!-- Facebook Icon -->
        <div
          v-if="activeComponent === '' || activeComponent === 'Facebook'"
          class="relative"
        >
          <div v-if="facebookProvider.length > 0" class="social-feed-count">
            <div class="pr-0.5 pt-0.5">
              {{ facebookProvider.length }}
            </div>
          </div>
          <div
            class="rounded-full w-15 h-15 cursor-pointer"
            @click.stop="$emit('selectCurrentAccess', 'facebook')"
          >
            <SharedIconFacebookIcon
              class="rounded-full w-15 h-15 cursor-pointer"
            />
          </div>
        </div>

        <!-- LinkedIn Icon -->
        <div
          v-if="activeComponent === '' || activeComponent === 'LinkedIn'"
          class="relative"
        >
          <div v-if="linkedInProvider.length > 0" class="social-feed-count">
            <div class="pr-0.5 pt-0.5">
              {{ linkedInProvider.length }}
            </div>
          </div>
          <div
            class="rounded-full w-15 h-15 cursor-pointer"
            @click.stop="$emit('selectCurrentAccess', 'linkedin')"
          >
            <SharedIconLinkedinIcon
              class="rounded-full w-15 h-15 cursor-pointer"
            />
          </div>
        </div>

        <!-- InstagramIcon -->
        <div
          v-if="activeComponent === '' || activeComponent === 'Instagram'"
          class="relative"
        >
          <div v-if="instagramProvider.length > 0" class="social-feed-count">
            <div class="pr-0.5 pt-0.5">
              {{ instagramProvider.length }}
            </div>
          </div>
          <div
            class="w-15 h-15 cursor-pointer"
            @click.stop="$emit('selectCurrentAccess', 'instagram')"
          >
            <SharedIconInstagramIcon
              class="w-15 h-15 cursor-pointer rounded-full"
            />
          </div>
        </div>

        <!-- PinterestIcon -->
        <div
          v-if="activeComponent === '' || activeComponent === 'Pinterest'"
          class="relative"
        >
          <div v-if="pinterestProvider.length > 0" class="social-feed-count">
            <div class="pr-0.5 pt-0.5">
              {{ pinterestProvider.length }}
            </div>
          </div>
          <div
            class="rounded-full w-15 h-15 cursor-pointer"
            @click.stop="$emit('selectCurrentAccess', 'pinterest')"
          >
            <SharedIconPinterestIcon
              class="rounded-full w-15 h-15 cursor-pointer"
              @click.stop="$emit('selectCurrentAccess', 'pinterest')"
            />
          </div>
        </div>
      </div>
      <div
        class="flex flex-row items-center justify-center space-x-4 w-full md:px-12 mt-5"
      >
        <!-- Reddit -->
        <div
          v-if="activeComponent === '' || activeComponent === 'Reddit'"
          class="relative"
        >
          <div v-if="redditProvider.length > 0" class="social-feed-count">
            <div class="pr-0.5 pt-0.5">
              {{ redditProvider.length }}
            </div>
          </div>
          <div
            class="rounded-full w-15 h-15 cursor-pointer"
            @click.stop="$emit('selectCurrentAccess', 'reddit')"
          >
            <SharedIconRedditIcon
              class="rounded-full w-15 h-15 cursor-pointer"
              @click.stop="$emit('selectCurrentAccess', 'reddit')"
            />
          </div>
        </div>

        <!-- Tiktok -->
        <div
          v-if="activeComponent === '' || activeComponent === 'TikTok'"
          class="relative"
        >
          <div v-if="tiktokProvider.length > 0" class="social-feed-count">
            <div class="pr-0.5 pt-0.5">
              {{ tiktokProvider.length }}
            </div>
          </div>
          <div
            class="rounded-full w-15 h-15 cursor-pointer"
            @click.stop="$emit('selectCurrentAccess', 'tiktok')"
          >
            <SharedIconTiktokIcon
              class="rounded-full w-15 h-15 cursor-pointer"
              @click.stop="$emit('selectCurrentAccess', 'tiktok')"
            />
          </div>
        </div>
      </div>
      <div
        class="flex flex-row items-center justify-center space-x-4 w-full md:px-12 mt-5"
      >
        <div
          v-if="activeComponent === '' || activeComponent === 'Youtube'"
          class="relative"
        >
          <div v-if="youtubeProvider.length > 0" class="social-feed-count">
            <div class="pr-0.5 pt-0.5">
              {{ youtubeProvider.length }}
            </div>
          </div>
          <img
            :src="youtube"
            class="cursor-pointer youtube_icon"
            alt="Youtube Icon"
            @click.stop="$emit('selectCurrentAccess', 'youtube')"
          />
        </div>
        <!-- <div
          v-if="
            (activeComponent === '' || activeComponent === 'RingCentral') &&
            config.public.workflow === 'dev'
          "
          class="relative"
        >
          <div
            class="w-[196px] h-auto cursor-pointer"
            @click.stop="$emit('selectCurrentAccess', 'ringcentral')"
          >
            <SharedIconRingcentralRingCentralLogo />
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
import youtubePng from 'assets/img/png/yt_logo_rgb_dark.png'

defineProps({
  twitterProvider: {
    type: Array,
    default: () => [],
  },
  facebookProvider: {
    type: Array,
    default: () => [],
  },
  instagramProvider: {
    type: Array,
    default: () => [],
  },
  youtubeProvider: {
    type: Array,
    default: () => [],
  },
  linkedInProvider: {
    type: Array,
    default: () => [],
  },
  pinterestProvider: {
    type: Array,
    default: () => [],
  },
  redditProvider: {
    type: Array,
    default: () => [],
  },
  tiktokProvider: {
    type: Array,
    default: () => [],
  },
})

const youtube = ref<string>(youtubePng)
const activeComponent = ref<string>('')
const store = useStore()
const route = useRoute()
const config = useRuntimeConfig()

// Access state and getters
const globalColorPanel = computed(() => store.state.globalColorPanel)

onMounted(() => {
  const fullPath = route.fullPath

  if (
    fullPath === '/home?addMoreFeed=true&provider=Twitter' ||
    fullPath === '/home?provider=Twitter&addMoreFeed=true'
  ) {
    activeComponent.value = 'Twitter'
  } else if (
    fullPath === '/home?addMoreFeed=true&provider=Facebook' ||
    fullPath === '/home?provider=Facebook&addMoreFeed=true'
  ) {
    activeComponent.value = 'Facebook'
  } else if (
    fullPath === '/home?addMoreFeed=true&provider=LinkedIn' ||
    fullPath === '/home?provider=LinkedIn&addMoreFeed=true'
  ) {
    activeComponent.value = 'LinkedIn'
  } else if (
    fullPath === '/home?addMoreFeed=true&provider=Instagram' ||
    fullPath === '/home?provider=Instagram&addMoreFeed=true'
  ) {
    activeComponent.value = 'Instagram'
  } else if (
    fullPath === '/home?addMoreFeed=true&provider=YouTube' ||
    fullPath === '/home?provider=YouTube&addMoreFeed=true'
  ) {
    activeComponent.value = 'Youtube'
  } else if (
    fullPath === '/home?addMoreFeed=true&provider=Pinterest' ||
    fullPath === '/home?provider=Pinterest&addMoreFeed=true'
  ) {
    activeComponent.value = 'Pinterest'
  } else if (
    fullPath === '/home?addMoreFeed=true&provider=Reddit' ||
    fullPath === '/home?provider=Reddit&addMoreFeed=true'
  ) {
    activeComponent.value = 'Reddit'
  } else if (
    fullPath === '/home?addMoreFeed=true&provider=TikTok' ||
    fullPath === '/home?provider=TikTok&addMoreFeed=true'
  ) {
    activeComponent.value = 'TikTok'
  } else {
    activeComponent.value = ''
  }

  console.log('Active Component:', activeComponent.value)
})
</script>

<style lang="scss" scoped>
.youtube_icon {
  height: 44px;
}
.social-feed-count {
  @apply absolute bg-orange-600 rounded-full text-white w-7 h-7;
  top: -6px;
  left: -0.1rem;
}
.twitter-icon {
  background-size: cover;
  animation: twitter 6s ease-in-out;
  animation-iteration-count: infinite;
}
@keyframes twitter {
  0% {
    background-image: url('~/assets/img/icon/TwitterIcon/twitter.svg');
  }
  50% {
    background-image: url('~/assets/img/icon/TwitterIcon/X_logo_png.png');
  }
  100% {
    background-image: url('~/assets/img/icon/TwitterIcon/twitter.svg');
  }
}
</style>
