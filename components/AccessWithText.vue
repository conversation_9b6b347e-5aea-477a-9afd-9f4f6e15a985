<template>
  <div class="w-full">
    <p
      class="text-orange-600 text-center text-xl mb-4"
      :style="{ color: globalColorPanel.backgroundColor }"
    >
      Text
    </p>
    <div class="w-full flex flex-col items-center h-full">
      <div
        class="flex flex-row items-center justify-center space-x-[30px] w-full md:px-12"
      >
        <!-- RingCentralIcon -->
        <div
          v-if="activeComponent === '' || activeComponent === 'RingCentral'"
          class="relative"
        >
          <div v-if="ringcentralProvider.length > 0" class="social-feed-count">
            <div class="pr-0.5 pt-0.5">
              {{ ringcentralProvider.length }}
            </div>
          </div>
          <div
            class="w-[196px] h-auto cursor-pointer"
            @click.stop="$emit('selectCurrentAccess', 'ringcentral')"
          >
            <SharedIconRingcentralRingCentralLogo />
          </div>
        </div>
        <div
          v-if="activeComponent === '' || activeComponent === 'GoHighLevel'"
          class="relative"
        >
          <div v-if="ringcentralProvider.length > 0" class="social-feed-count">
            <div class="pr-0.5 pt-0.5">
              {{ ringcentralProvider.length }}
            </div>
          </div>
          <div
            class="w-15 h-15 cursor-pointer rounded-full"
            @click.stop="$emit('selectCurrentAccess', 'gohighlevel')"
          >
            <img
              src="/social/gohightlevel.jpeg"
              alt="GoHighLevel Icon"
              width="60"
              height="60"
              class="w-15 h-15 rounded-full"
            />
          </div>
        </div>
        <!-- WhatsAppIcon -->
        <div
          v-if="
            (activeComponent === '' || activeComponent === 'WhatsApp') &&
            config.public.workflow === 'dev'
          "
          class="relative"
        >
          <div
            class="w-15 h-15 cursor-pointer"
            @click.stop="
              (store.commit('home/SET_SHOW_WHATSAPP_ONBOARDING', true),
              $emit('close-modal'))
            "
          >
            <SharedIconWhatsAppIcon class="w-15 h-15" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'

defineProps({
  ringcentralProvider: {
    type: Array,
    default: () => [],
  },
})

const activeComponent = ref<string>('')
const store = useStore()
const route = useRoute()
const config = useRuntimeConfig()

// Access state and getters
const globalColorPanel = computed(() => store.state.globalColorPanel)

onMounted(() => {
  const fullPath = route.fullPath

  if (
    fullPath === '/home?addMoreFeed=true&provider=RingCentral' ||
    fullPath === '/home?provider=RingCentral&addMoreFeed=true'
  ) {
    activeComponent.value = 'RingCentral'
  } else if (
    fullPath === '/home?addMoreFeed=true&provider=WhatsApp' ||
    fullPath === '/home?provider=WhatsApp&addMoreFeed=true'
  ) {
    activeComponent.value = 'WhatsApp'
  } else {
    activeComponent.value = ''
  }
})
</script>

<style lang="scss" scoped></style>
