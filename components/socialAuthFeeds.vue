<template>
  <div>
    <transition name="right-sidebar-trans">
      <div
        v-if="openAddFeedModal && user.userPermission !== 'User'"
        :style="{ '--color': globalColorPanel.backgroundColor }"
        class="profile fixed bg-ash-dark right-0 md:top-15 h-full md:px-5 p-4 md:pt-8 flex flex-col overflow-y-auto scroll md:rounded-l-2xl md:shadow-2xl md:drop-shadow-2xl"
      >
        <transition name="fadeInParent">
          <div
            v-if="addFeedSection"
            class="main-wrapper h-full w-full flex flex-col justify-between gap-14"
          >
            <div class="w-full h-full main-content">
              <div
                class="flex flex-row items-center justify-between md:h-12 h-8 relative"
              >
                <h2
                  :style="{ color: globalColorPanel.backgroundColor }"
                  class="xl:text-2xl md:text-xl md:font-bold text-xl"
                >
                  <transition name="fadeIn">
                    <span
                      v-if="!emailBackBtn && !accessSelected && !emailVerified"
                      >Add Feeds</span
                    >
                  </transition>
                </h2>
                <button
                  :class="[
                    emailBackBtn
                      ? 'animLeft justify-start'
                      : 'animRight justify-end',
                  ]"
                  class="absolute focus:outline-none right-0 w-6 md:w-8 h-8 flex items-center"
                  @click="emailBackBtn ? back() : setDefineAction()"
                >
                  <ClientOnly>
                    <fa
                      :style="{ color: globalColorPanel.backgroundColor }"
                      class="xl:text-2xl md:text-xl md:font-bold text-2xl"
                      :icon="['fas', emailBackBtn ? 'chevron-left' : 'times']"
                    />
                  </ClientOnly>
                </button>
              </div>
              <transition name="modalFadeIn">
                <div
                  v-if="hideInstagramModal"
                  class="w-full h-full flex flex-col items-center justify-center md:mt-0 mt-5"
                >
                  <div
                    key="1"
                    class="social-icons-wrapper w-full overflow-hidden"
                    :class="
                      emailVerified
                        ? 'demo2'
                        : getSelectAccessType
                          ? `${getSelectAccessType}`
                          : 'demo1'
                    "
                    :style="{ '--height': `calc(${demoHeight} - 48px)` }"
                  >
                    <transition name="fadeIn">
                      <!-- v-if="!emailVerified" -->
                      <div class="w-full flex flex-col items-center h-full">
                        <h2
                          v-if="getSelectAccessType !== 'SelectedAccessType'"
                          class="text-[#C2C2C2] md:text-xl text-md md:px-0 md:text-left text-center opacity-50 mb-5"
                        >
                          If you have access, select to connect:
                        </h2>
                        <transition name="fadeInBtn" mode="out-in">
                          <keep-alive>
                            <component
                              :is="getSelectAccessType"
                              @showInstagramModal="instagramModal"
                              @selectCurrentAccess="authorize"
                              @close-modal="(back(), cancel())"
                            >
                            </component>
                          </keep-alive>
                        </transition>
                      </div>
                    </transition>
                  </div>
                  <Transition name="fadeInEmailinput">
                    <div v-if="showEmailInput" class="w-full">
                      <div
                        class="border-b-2 border-offwhite-500 w-full md:mt-12 mt-8 rounded-sm"
                      ></div>
                      <div
                        key="2"
                        class="send-guest-email-wrapper w-full flex flex-col transition-all duration-1000 md:mt-6 mt-6"
                      >
                        <div
                          class="flex flex-col justify-center items-center text-[#C2C2C2]"
                        >
                          <label class="text-xl"
                            >Need to archive someone else's account?</label
                          >
                          <label class="text-xl mb-2"
                            >Send authorization request to them:</label
                          >
                        </div>
                        <input
                          v-model="guestEmail"
                          type="text"
                          placeholder="Enter email address"
                          autocomplete="off"
                          class="w-full rounded-full py-2 px-2.5 outline-none focus:outline-none bg-white text-gray-1200 placeholder-gray-1200 placeholder-opacity-50 text-center align-start"
                          @keyup="v$.guestEmail.$touch()"
                        />
                        <template v-if="v$.guestEmail.$error">
                          <!-- <li
                            v-for="err in v$.guestEmail.$errors"
                            :key="err.$uid"
                          > -->
                          <span class="text-red-400 text-xs mt-0 pl-2">
                            {{ v$.guestEmail.$errors[0].$message }}
                            {{
                              v$.guestEmail.duplicateEmailValidator?.$invalid &&
                              v$.guestEmail.$errors[0].$message ===
                                'Duplicate emails found: '
                                ? [...new Set(duplicateEmails)].join(', ')
                                : ''
                            }}
                          </span>
                          <!-- </li> -->
                          <!-- <span
                            v-if="v$.guestEmail.email.$invalid"
                            class="text-red-400 text-xs mt-0 pl-2"
                          >
                            The Email is Invalid
                          </span>
                          <span
                            v-if="v$.guestEmail.required.$invalid"
                            class="text-red-400 text-xs mt-0 pl-2"
                          >
                            The field is required
                          </span> -->
                        </template>
                        <transition name="fadeIn">
                          <div v-if="emailVerified" class="text-center mt-4">
                            <button
                              type="submit"
                              :style="{
                                backgroundColor:
                                  globalColorPanel.backgroundColor,
                                borderColor: globalColorPanel.backgroundColor,
                              }"
                              class="w-44 py-1.5 text-white rounded-full border outline-none font-bold text-base setup relative"
                              :disabled="emailSendProcess"
                              @click="sendGuestEmail()"
                            >
                              <div
                                class="rounded-full relative flex items-center justify-around"
                              >
                                <span>Send</span>
                                <ClientOnly>
                                  <fa
                                    v-if="emailSendProcess"
                                    class="absolute right-5 text-white font-bold animate-spin"
                                    :icon="['fas', 'spinner']"
                                  />
                                </ClientOnly>
                              </div>
                            </button>
                          </div>
                        </transition>
                      </div>
                    </div>
                  </Transition>
                  <Transition name="fadeIn">
                    <div
                      v-if="
                        getSelectAccessType === 'AccessWithEmail' &&
                        config.public.workflow === 'dev'
                      "
                      class="w-full flex flex-col items-center"
                    >
                      <div
                        class="border-t-2 border-offwhite-500 rounded-sm send-guest-email-wrapper w-full flex flex-col transition-all duration-1000 md:mt-7 md:mb-5 mb-4 mt-6"
                      ></div>
                      <button
                        class="border-2 rounded-full py-[9px] px-6 text-white text-center"
                      >
                        Mass Connect Accounts
                      </button>
                    </div>
                  </Transition>
                  <div
                    v-if="authorizationProviders.length > 0"
                    key="3"
                    class="scroll h-34 md:h-auto my-4"
                  >
                    <p
                      v-for="(notice, index) in connectionNotices"
                      :key="index"
                      class="text-white text-justify mb-2 transition-all delay-500 ease-in-out"
                      :class="showNotice ? 'opacity-1' : 'opacity-0'"
                    >
                      <template
                        v-if="
                          authorizationProviders.includes(
                            notice.provider.toLowerCase(),
                          )
                        "
                      >
                        {{ notice.message }}
                      </template>
                    </p>
                  </div>
                </div>
              </transition>
              <transition name="modalFadeIn">
                <div
                  v-if="showInstagramModal"
                  class="w-full main-content flex flex-col items-center justify-center"
                >
                  <div
                    class="w-full flex flex-col justify-center items-center space-y-10"
                  >
                    <SharedIconInstagramPngIcon
                      class="w-15 h-15 cursor-pointer"
                    ></SharedIconInstagramPngIcon>
                    <p class="md:text-xl text-md text-white opacity-50">
                      Which type of account?
                    </p>
                    <div
                      class="w-full flex items-center justify-center space-x-4"
                    >
                      <button
                        class="w-38 py-1.5 text-lg font-bold text-white rounded-full"
                        :style="{
                          backgroundColor: globalColorPanel.backgroundColor,
                        }"
                        @click.stop="authorize('instagram')"
                      >
                        Personal
                      </button>
                      <button
                        class="w-38 py-1.5 text-lg font-bold text-white rounded-full"
                        :style="{
                          backgroundColor: globalColorPanel.backgroundColor,
                        }"
                        @click.stop="authorize('facebook')"
                      >
                        Business
                      </button>
                    </div>
                    <p class="md:text-xl text-md text-white text-center">
                      Facebook (Meta) owns Instagram. Clicking on “Personal”
                      will route you to Instagram’s authorization. Clicking on
                      “Business” will ask for your authorization through
                      Facebook.
                    </p>
                  </div>
                </div>
              </transition>
            </div>

            <div class="flex justify-center relative w-full h-10">
              <transition name="fadeInBtn" mode="out-in">
                <component
                  :is="currentBtnComponent"
                  :background-color="globalColorPanel.backgroundColor"
                  @back="back()"
                  @cancel="cancel()"
                  @finish="
                    socialFeedLength === 1 || showArchiveSystemSettings
                      ? finish()
                      : cancel()
                  "
                >
                </component>
              </transition>
            </div>
          </div>
        </transition>
        <transition name="fadeInParent">
          <LazyArchiveSystemSettings
            v-if="archiveSystemSetting"
            :global-color-panel="globalColorPanel"
            @closeArchiveSystemSetting="closeArchiveSystemSetting"
            @saveAllWork="saveAllWork"
          ></LazyArchiveSystemSettings>
        </transition>
      </div>
    </transition>
    <LazyStartArchivingModal
      v-if="startArchivePopup && showBlurActiveModal"
      @closeAccount="closeAccount"
      @addFeed="addFeed"
    ></LazyStartArchivingModal>
  </div>
</template>
<script setup lang="ts">
import { useStore } from 'vuex'
import { useVuelidate } from '@vuelidate/core'
import { required, helpers } from '@vuelidate/validators'
import { SEND_ACCESS_REQUEST, SOCIAL_AUTH } from '~/constants/urls'
import { useHideText } from '~/composables/feeds/useHideText'
import { useNuxtApp } from '#app'

interface GlobalColorPanel {
  backgroundColor: string
}

const props = withDefaults(
  defineProps<{
    globalColorPanel: GlobalColorPanel
  }>(),
  {
    globalColorPanel: { backgroundColor: '' },
  },
)

const { demoHeight } = useHideText()
const nuxtApp = useNuxtApp()
const { $toast } = useNuxtApp()
const guestEmail = ref<string>('')
// validator: only space separator allowed (no commas)
const spaceSeparatorOnly = helpers.withMessage(
  'Use a space to separate multiple emails (no commas or other separators)',
  (value: string) => {
    if (!value) return true
    return !/,/.test(value) // fail if comma found
  },
)

// validator: all emails must be valid
const multipleEmails = helpers.withMessage(
  'One or more email addresses are invalid',
  (value: string) => {
    if (!value) return true

    // split only by spaces
    const emails = value.split(/\s+/).filter(Boolean)

    // regex from vuelidate's email validator
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

    return emails.every((email) => emailRegex.test(email))
  },
)
const duplicateEmails = ref<string[]>([])
const duplicateEmailValidator = helpers.withMessage(
  `Duplicate emails found: `,
  (value: string) => {
    if (!value) return true
    const emails = value.split(/\s+/).filter(Boolean)
    const seen = new Set<string>()
    duplicateEmails.value = []

    for (const e of emails) {
      if (seen.has(e)) duplicateEmails.value.push(e)
      else seen.add(e)
    }
    return duplicateEmails.value.length === 0
  },
)

const rules = {
  guestEmail: {
    required,
    multipleEmails,
    spaceSeparatorOnly,
    duplicateEmailValidator,
  },
}
const v$ = useVuelidate(rules, { guestEmail })
const { fetch } = useFetched()
const { logout } = useAuth()

const addFeedSection = ref<boolean>(true)
const startArchivePopup = ref<boolean>(false)
const emailBackBtn = ref<boolean>(false)
const archiveSystemSetting = ref<boolean>(false)
const emailVerified = ref<boolean>(false)
const emailSendProcess = ref<boolean>(false)
const showNotice = ref<boolean>(false)
const currentBtnComponent = ref<string>('SocialAuthFeedBtnCancelBtn')

type ConnectionNotice = {
  provider: string
  message: string
}

const connectionNotices = ref<ConnectionNotice[]>([
  {
    provider: 'Facebook',
    message: `You have connected a Facebook account. You should know that
      Facebook will require you to reauthorize your account every 60 days.
      Yes, we too find this annoying. We will try to make this as painless as possible.
      Every 60 days, you’ll get an email from us with a button to renew the connection.`,
  },
  {
    provider: 'LinkedIn',
    message: `You have connected a LinkedIn account. You should know that LinkedIn will require you to reauthorize your account every year.
      Yes, we too find this annoying. We will try to make this as painless as possible. Each year, you’ll get an email from us with a button
      to renew the connection.`,
  },
])

const authorizationProviders = ref<string[]>([])
const hideInstagramModal = ref<boolean>(true)
const showInstagramModal = ref<boolean>(false)
const accessSelected = ref<boolean>(false)
const showEmailInput = ref<boolean>(true)

// Access the store
const store = useStore()
const config = useRuntimeConfig()
// Router
const route = useRoute()
const router = useRouter()

// Computed properties
const showAddFeedsComp = computed(() => store.getters['header/getAddFeed'])
const showBlurActiveModal = computed(
  () => store.getters['header/showBlurActiveModal'],
)
const showFinishBtn = computed(
  () => store.getters['header/getAddFeedFinishBtn'],
)
const socialFeedLength = computed(
  () => store.getters['socialFeed/socialFeedLength'],
)
const openAddFeedModal = computed(
  () => store.getters['socialFeed/openAddFeedModal'],
)
const user = computed(() => store.state.auth.user)

// Use `mapState` and `mapGetters` manually in script setup
const showArchiveSystemSettings = computed(
  () => store.state.socialFeed.showArchiveSystemSettings,
)
const getSelectAccessType = computed(
  () => store.getters['home/getSelectAccessType'],
)
const sideBarAccountItems = computed(() => {
  // if (store.state.social.sideBarAccountItems.length === 0) {
  //   store.commit('social/SET_MANAGER_ONBOARDING', true)
  // }
  return store.state.social.sideBarAccountItems
})
// Watchers
watch(getSelectAccessType, (data: string) => {
  if (data !== 'SelectedAccessType') {
    accessSelected.value = true
    emailBackBtn.value = true
    currentBtnComponent.value = 'SocialAuthFeedBtnBackBtn'
  } else {
    emailVerified.value = false
    emailBackBtn.value = false
    accessSelected.value = false
    currentBtnComponent.value = 'SocialAuthFeedBtnCancelBtn'
  }

  if (data === 'AccessWithWebsite') {
    showEmailInput.value = false
  } else {
    setTimeout(() => {
      showEmailInput.value = true
    }, 500)
  }
})

watch(
  () => guestEmail.value,
  (data: string) => {
    if (data) {
      checkEmailIsVerified()
    }
  },
)

watch(
  () => showAddFeedsComp.value,
  (data: boolean) => {
    if (data) {
      setTimeout(() => {
        showNotice.value = data
      }, 2000)
    } else {
      showNotice.value = data
    }
  },
)

watch(
  () => showFinishBtn.value,
  (data: boolean) => {
    currentBtnComponent.value = data
      ? 'SocialAuthFeedBtnFinishBtn'
      : 'SocialAuthFeedBtnCancelBtn'
  },
)

watch(
  () => socialFeedLength.value,
  (data: number) => {
    if (data === 0) {
      store.commit('socialFeed/SHOW_ADD_FEED_MODAL', true)
      store.commit('header/BLUR_ACTIVE_FEED_MODAL', true)
    }
  },
)

watch(
  () => openAddFeedModal.value,
  (data: boolean) => {
    if (!data) {
      cancel()
    }
  },
)

// Lifecycle hook
onMounted(() => {
  setTimeout(() => {
    currentBtnComponent.value = showFinishBtn.value
      ? 'SocialAuthFeedBtnFinishBtn'
      : 'SocialAuthFeedBtnCancelBtn'
  }, 1000)

  setTimeout(() => {
    showNotice.value = true
  }, 2000)
})

// Methods
const instagramModal = () => {
  hideInstagramModal.value = false
  currentBtnComponent.value = 'SocialAuthFeedBtnBackBtn'
  setTimeout(() => {
    showInstagramModal.value = true
  }, 500)
}

const checkEmailIsVerified = () => {
  if (!v$.value.$invalid) {
    emailVerified.value = true
    if (getSelectAccessType.value === 'SelectedAccessType') {
      accessSelected.value = true
      emailBackBtn.value = true
      currentBtnComponent.value = 'SocialAuthFeedBtnBackBtn'
    }
  } else {
    emailVerified.value = false
    if (getSelectAccessType.value === 'SelectedAccessType') {
      accessSelected.value = false
      emailBackBtn.value = false
      currentBtnComponent.value = 'SocialAuthFeedBtnCancelBtn'
    }
  }
}

const cancel = async () => {
  guestEmail.value = ''
  v$.value.$reset()
  if (
    route.name?.includes('source') &&
    sideBarAccountItems.value &&
    sideBarAccountItems.value.length === 0
  ) {
    store.commit('social/SET_MANAGER_ONBOARDING', true)
  }
  store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false)
  store.commit('header/REMOVE_ADD_FEED_FINISH_BTN')
  showInstagramModal.value = false
  setTimeout(() => {
    hideInstagramModal.value = true
  }, 500)
  currentBtnComponent.value = 'SocialAuthFeedBtnCancelBtn'
  if (socialFeedLength.value) {
    startArchivePopup.value = false
    store.commit('header/BLUR_ACTIVE_FEED_MODAL', false)
  } else {
    const res = await store.dispatch('socialFeed/fatchSocialFeeds')
    if (res.data.length === 0) {
      store.commit('header/BLUR_ACTIVE_FEED_MODAL', true)
      startArchivePopup.value = true
    } else {
      store.commit('header/BLUR_ACTIVE_FEED_MODAL', false)
      startArchivePopup.value = false
    }
  }
}
const closeAccount = () => {
  store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false)
  startArchivePopup.value = false
  profilelogout()
}

const profilelogout = async () => {
  store.dispatch('loginAnimation/show_login', true)
  store.commit('SET_LANDING_LOADER', false)
  router.push('/home')
  logout().then((res) => {
    updateUIOnLogout()
    store.commit('header/BLUR_ACTIVE_FEED_MODAL', false)
    store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false)
  })
}

const updateUIOnLogout = () => {
  store.dispatch('loginAnimation/show_home_content', true)
  store.dispatch('loginAnimation/landing_content', false)
  // store.dispatch('loginAnimation/show_login', false);
  store.dispatch('loginAnimation/home_wrapper', false)
  store.dispatch('loginAnimation/show_home', false)
  store.dispatch('loginAnimation/show_logo_text', true)
  store.dispatch('loginAnimation/home_menu_text', false)
  store.dispatch('loginAnimation/all_side_menu', false)
  store.dispatch('loginAnimation/home_side_menu', false)
  store.dispatch('loginAnimation/show_logo', false)
  store.dispatch('loginAnimation/home_circle', false)
  store.dispatch('loginAnimation/sidebar_menu', false)
  store.dispatch('loginAnimation/circle', false)
  store.dispatch('loginAnimation/home', false)
  store.dispatch('loginAnimation/slide_left', false)
  store.dispatch('loginAnimation/login_circle', true)
  store.dispatch('loginAnimation/header', false)
  store.dispatch('loginAnimation/after_logout', false)
  store.dispatch('loginAnimation/text_loading', false)
  store.dispatch('loginAnimation/successfully', false)
  store.dispatch('loginAnimation/notsuccessfully', true)
  store.dispatch('loginAnimation/login_button_transition', false)
  store.dispatch('loginAnimation/submit_button_transition', false)
  store.dispatch('loginAnimation/login_form_transition', false)
  store.dispatch('loginAnimation/after_loading', false)
  store.dispatch('loginAnimation/width_increase', false)
  store.dispatch('loginAnimation/full_width', false)
  store.dispatch('loginAnimation/header_text', false)
  store.dispatch('loginAnimation/loading_text', false)
  store.dispatch('loginAnimation/width_decrese', false)
  store.dispatch('loginAnimation/slide_right', false)
  store.dispatch('loginAnimation/slide_full_right', false)
  store.dispatch('set_header_width', false)
  store.dispatch('set_sticky', false)
}

const addFeed = () => {
  store.commit('socialFeed/SHOW_ADD_FEED_MODAL', true)
  startArchivePopup.value = false
}

const finish = () => {
  addFeedSection.value = false
  setTimeout(() => {
    archiveSystemSetting.value = true
  }, 700)
}

const authorize = async (provider: string) => {
  store.commit('socialFeed/SET_ARCHIVE_FEED', false)
  // Replace this with your social auth logic
  await nuxtApp.$social.redirect(provider)
}

const authorizeTwitter = async () => {
  const apiUrl = `${SOCIAL_AUTH}/twitter/` // Ensure SOCIAL_AUTH is defined
  await nuxtApp.$social.redirectOAuth1('twitter', apiUrl)
}

const closeArchiveSystemSetting = () => {
  archiveSystemSetting.value = false
  setTimeout(() => {
    addFeedSection.value = true
  }, 700)
}

const saveAllWork = () => {
  currentBtnComponent.value = 'SocialAuthFeedBtnCancelBtn'
  emailBackBtn.value = false
  guestEmail.value = ''
  if (socialFeedLength.value) {
    addFeedSection.value = true
    startArchivePopup.value = false
    archiveSystemSetting.value = false
    store.commit('header/BLUR_ACTIVE_FEED_MODAL', false)
    store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false)
  } else {
    closeArchiveSystemSetting()
  }
}

const sendGuestEmail = async () => {
  v$.value.$touch()
  $toast('clear')
  if (!v$.value.$invalid) {
    try {
      emailSendProcess.value = true
      const res = await fetch(SEND_ACCESS_REQUEST, {
        method: 'POST',
        body: { email: guestEmail.value },
      })

      if (res.success) {
        $toast('success', {
          message: res.message,
          className: 'toasted-bg-archive',
        })
        guestEmail.value = ''
        if (getSelectAccessType.value === 'SelectedAccessType') {
          accessSelected.value = false
          emailBackBtn.value = false
          currentBtnComponent.value = 'SocialAuthFeedBtnCancelBtn'
        } else {
          emailBackBtn.value = true
        }
        emailVerified.value = false
        v$.value.$reset()
      } else {
        $toast('error', {
          message: res.message,
          className: 'toasted-bg-alert',
        })
      }
      emailSendProcess.value = false
    } catch (error) {
      console.error(error)
    }
  }
}

const back = () => {
  if (route.fullPath.includes('?addMoreFeed=true&provider=')) {
    router.replace('/home?addMoreFeed=true')
  }
  guestEmail.value = ''
  showInstagramModal.value = false
  setTimeout(() => {
    if (
      getSelectAccessType.value !== 'SelectedAccessType' &&
      emailVerified.value
    ) {
      store.commit('home/SET_SELECT_ACCESS_TYPE', getSelectAccessType.value)
    } else {
      emailBackBtn.value = false
      store.commit('home/SET_SELECT_ACCESS_TYPE', 'SelectedAccessType')
    }
    hideInstagramModal.value = true
    if (getSelectAccessType.value === 'SelectedAccessType') {
      accessSelected.value = false
    }
    emailVerified.value = false
  }, 500)
  v$.value.$reset()
}

const setDefineAction = () => {
  if (
    currentBtnComponent.value === 'SocialAuthFeedBtnFinishBtn' &&
    (socialFeedLength.value === 1 || showArchiveSystemSettings.value)
  ) {
    cancel()
    saveAllWork()
  } else {
    cancel()
  }
}
</script>
<style lang="scss" scoped>
$color: var(--color);
.profile {
  // width: 500px;
  width: 550px;
  height: 100%;
  top: 0px;
  z-index: 9999999999;
}

.scroll {
  scrollbar-color: $color #ececec; /* Firefox 64 */
  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: $color;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: $color;
  }
}

.form-card > .error {
  margin-top: 0px;
}

@media (max-width: 767px) {
  .profile {
    width: 100%;
    // height: 100%;
    top: 0;
    z-index: 9999999999;
  }
  .scroll {
    scrollbar-color: $color #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: $color;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: $color;
    }
  }
}

.right-sidebar-trans-enter-from,
.right-sidebar-trans-leave-to {
  right: -100%;
}

.right-sidebar-trans-enter-to,
.right-sidebar-trans-leave {
  right: 0;
}

.right-sidebar-trans-enter-active {
  transition: all 0.8s ease-in-out;
}

.right-sidebar-trans-leave-active {
  transition: all 0.8s ease-in-out;
}

// .content-wrapper {
//   height: calc(100% - 40px) !important;
// }

.overlay-web {
  position: fixed;
  top: 60px;
  bottom: 0;
  left: 100px;
  right: 0;
  z-index: 101;
  background-color: rgba(255, 255, 255, 0.1);
  opacity: 1;
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  pointer-events: all;
}

@media (max-width: 767px) {
  .overlay-web {
    left: 0 !important;
    height: 100%;
  }
}

.fadeIn-enter-from,
.fadeIn-leave-to {
  opacity: 0;
}

.fadeIn-enter-to,
.fadeIn-leave-from {
  opacity: 1;
}

.fadeIn-enter-active,
.fadeIn-leave-active {
  transition: all 0.5s ease-in-out;
}

.fadeInParent-enter-from,
.fadeInParent-leave-to {
  opacity: 0;
}

.fadeInParent-enter-to,
.fadeInParent-leave-from {
  opacity: 1;
}

.fadeInParent-enter-active,
.fadeInParent-leave-active {
  transition: all 0.5s ease-in-out;
}

.animLeft {
  transform: translateX(0);
  animation-name: changePositionLeft;
  animation-duration: 1s;
  animation-fill-mode: forwards;
}

@keyframes changePositionLeft {
  to {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
    right: 86%;
  }
}

.animRight {
  transform: translateX(0);
  animation-name: changePositionRight;
  animation-duration: 1s;
}

@keyframes changePositionRight {
  from {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
    right: 92%;
  }
  to {
    -webkit-transform: translateX(0%);
    transform: translateX(0%);
    right: 0%;
  }
}
/*end right sidebar section transition*/

.demo1 {
  // height: 220px;
  height: var(--height);
  // height: 430px;
  transition: height 1s 0.5s;
}
.demo2 {
  height: 0;
  transition: height 1s 0.5s;
}
.SelectedAccessType {
  height: 188px;
  transition: height 1s 0.5s;
}
.AccessWithEmail,
.AccessWithText {
  height: 170px;
  transition: height 1s 0.5s;
}
.AccessWithSocialAcnt {
  height: 300px;
  transition: height 1s 0.5s;
}
.AccessWithWebsite {
  height: 230px;
  transition: height 1s 0.5s;
}

// .main-content {
//   height: calc(100% - 42px); // this 42 is bottom button height
// }

.main-content > span {
  width: 100%;
}

.fadeInBtn-enter-active,
.fadeInBtn-leave-active {
  transition: opacity 0.5s;
}
.fadeInBtn-enter-from,
.fadeInBtn-leave-to {
  opacity: 0;
}

.notice-enter-active,
.notice-leave-active {
  transition: opacity 1s;
}
.notice-enter,
.notice-leave-to {
  opacity: 0;
}
.scroll {
  overflow-y: auto;
  overflow-x: hidden;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-width: thin;
  scrollbar-color: var(--color) #ececec; /* Firefox 64 */
  &::-webkit-scrollbar {
    width: 6px;
    height: 10px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    // box-shadow: inset 0 0 5px #ECECEC;
    border-radius: 3px;
    background: #ececec;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: var(--color);
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: var(--color);
  }
}
@media (max-width: 767px) {
  // .demo1 {
  //   height: 100%;
  // }
  // .social-icons-wrapper {
  //   @apply scroll;
  // }
  .SelectedAccessType {
    height: 162px;
    transition: height 1s 0.5s;
  }
  .AccessWithEmail {
    height: 200px;
    transition: height 1s 0.5s;
  }
  .AccessWithSocialAcnt {
    height: 300px;
    transition: height 1s 0.5s;
  }
  .AccessWithWebsite {
    height: 260px;
    transition: height 1s 0.5s;
  }
}

@media (max-height: 799px) {
  .main-wrapper {
    height: 160%;
  }
}

.modalFadeIn-enter-active,
.modalFadeIn-leave-active {
  transition: opacity 0.5s;
}
.modalFadeIn-enter-from,
.modalFadeIn-leave-to {
  opacity: 0;
}

.fadeInEmailinput-enter-active {
  transition: opacity 0.5s;
}
.fadeInEmailinput-leave-active {
  transition: opacity 0.5s;
}

.fadeInEmailinput-enter-from,
.fadeInEmailinput-leave-to {
  opacity: 0;
}

.outlook-icon {
  position: absolute;
  top: 9px;
  left: 8px;
}

// @media (max-width: 430px) {
//   .email-logo-wrapper {
//     @apply space-x-0 space-y-4 flex-wrap;
//   }
//   .demo1 {
//     // height: 180px;
//     height: 374px;
//     // height: 430px;
//     transition: height 1s 0.5s;
//   }
// }

.youtube_icon {
  height: 44px;
}
</style>
