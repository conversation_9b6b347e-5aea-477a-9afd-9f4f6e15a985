<template>
  <div ref="nested shadow" class="balance__menu relative font-serif">
    <div
      ref="dropdown"
      class="dropdown-btn bg-yellow-primary transition-border-radius duration-500 rounded-t-3xl outline-none"
      :class="[`${source}`, round ? 'rounded-3xl' : '']"
    >
      <button
        class="w-11/12 text-left border-none outline-none"
        @click="enableClick ? expand($event) : ''"
      >
        <span class="text-lg font-bold font-sans">Pre-Set Pricing</span>
      </button>
      <svg
        width="15"
        height="15"
        class="fill-current text-gray-default transition-all duration-300 ease-in-out cursor-pointer transform"
        :class="!menuOpen ? 'rotate-0' : 'rotate-180'"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 292.362 292.362"
        @click="enableClick ? expand($event) : ''"
      >
        <path
          d="M286.935,69.377c-3.614-3.617-7.898-5.424-12.848-5.424H18.274c-4.952,0-9.233,1.807-12.85,5.424
    C1.807,72.998,0,77.279,0,82.228c0,4.948,1.807,9.229,5.424,12.847l127.907,127.907c3.621,3.617,7.902,5.428,12.85,5.428
    s9.233-1.811,12.847-5.428L286.935,95.074c3.613-3.617,5.427-7.898,5.427-12.847C292.362,77.279,290.548,72.998,286.935,69.377z"
        />
      </svg>
    </div>
    <!-- Desktop -->
    <div
      class="hidden md:flex dropdown fixed flex-col rounded-b-3xl bg-yellow-primary feedsShadow"
      :style="{ '--height': getHeight() }"
      :class="[increaseHeight ? 'expand' : '', `${source}`]"
    >
      <div
        class="menu-wrapper flex-grow relative scroll overall_scroll"
        :class="[`scroll__${source}`]"
      >
        <div class="menu-content">
          <div class="menu-item list-wrapper">
            <div>
              <div
                class="list-item cursor-pointer flex border-b border-b-0 group-archive"
                :class="[`group-archive__${source}`]"
              >
                <template v-if="socialTop">
                  <SavedPrice
                    :visible="isSocialVisible"
                    :type="'Total'"
                    :selected-type="socialType"
                    :feeds-list="savedPriceInfoList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandSocial"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  ></SavedPrice>
                  <!-- <feeds-type
                    :visible="isEmailVisible"
                    :type="'Email'"
                    :selected-type="emailType"
                    :feeds-list="emailFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandEmail"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </feeds-type>
                  <feeds-type
                    :visible="isWebVisible"
                    :type="'Website'"
                    :selected-type="webType"
                    :feeds-list="webFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandWeb"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </feeds-type> -->
                </template>
                <!-- <template v-if="emailTop">
                  <feeds-type
                    :visible="isEmailVisible"
                    :type="'Email'"
                    :selected-type="emailType"
                    :feeds-list="emailFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandEmail"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </feeds-type>
                  <feeds-type
                    :visible="isSocialVisible"
                    :type="'Social Media'"
                    :selected-type="socialType"
                    :feeds-list="savedPriceInfoList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandSocial"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  ></feeds-type>
                  <feeds-type
                    :visible="isWebVisible"
                    :type="'Website'"
                    :selected-type="webType"
                    :feeds-list="webFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandWeb"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </feeds-type>
                </template>
                <template v-if="webTop">
                  <feeds-type
                    :visible="isWebVisible"
                    :type="'Website'"
                    :selected-type="webType"
                    :feeds-list="webFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandWeb"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </feeds-type>
                  <feeds-type
                    :visible="isSocialVisible"
                    :type="'Social Media'"
                    :selected-type="socialType"
                    :feeds-list="savedPriceInfoList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandSocial"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  ></feeds-type>
                  <feeds-type
                    :visible="isEmailVisible"
                    :type="'Email'"
                    :selected-type="emailType"
                    :feeds-list="emailFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandEmail"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </feeds-type>
                </template> -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <div
        v-if="
          (menuOpen && !socialType && !emailType) ||
          (socialType && socialTotalPages === 1) ||
          (emailType && socialTotalPages === 1)
        "
        class="equity cursor-pointer sticky bottom-0 left-0"
        :class="[`${source}`]"
        @click="showEditFeed(), hideMobileHaeder()"
      >
        <span class="text-lg text-bold font-sans">Edit Feeds</span>
      </div> -->
      <div
        v-if="
          menuOpen &&
          ((socialType && socialTotalPages > 1) ||
            (emailType && socialTotalPages > 1))
        "
        class="equity sticky bottom-0 left-0 flex items-center justify-between rounded-b-3xl"
        :class="[`${source}`]"
      >
        <div class="flex items-center space-x-5">
          <button
            v-if="socialType || emailType"
            :disabled="isSocialFirstPage"
            class="flex justify-center items-center w-8 h-8 rounded-full"
            :class="
              isSocialFirstPage
                ? 'bg-white-opasity-50 cursor-default'
                : 'bg-white cursor-pointer'
            "
            @click="clickPreviousPage(false)"
          >
            <fa
              :icon="['fas', 'chevron-left']"
              class="feeds-button"
              :class="[`feeds-button__${source}`]"
            />
          </button>
          <span
            v-if="socialType || emailType"
            class="text-lg font-sans text-normal"
            >{{ socialCurrentPage }}/{{ socialTotalPages }}</span
          >
          <button
            v-if="socialType || emailType"
            :disabled="isSocialLastPage"
            class="flex justify-center items-center w-8 h-8 rounded-full"
            :class="
              isSocialLastPage
                ? 'bg-white-opasity-50  cursor-default'
                : 'bg-white cursor-pointer'
            "
            @click="clickNextPage(false)"
          >
            <fa
              :icon="['fas', 'chevron-right']"
              class="feeds-button"
              :class="[`feeds-button__${source}`]"
            />
          </button>
        </div>
        <!-- <span
          class="cursor-pointer"
          @click="showEditFeed(), hideMobileHaeder()"
        >
          <fa :icon="['fas', 'pencil-alt']" />
        </span> -->
      </div>
    </div>
    <!-- Mobile -->
    <div
      class="md:hidden dropdown flex flex-col rounded-b-3xl bg-yellow-primary"
      :style="[
        { '--maxHeight': `${windowHeight - 130}px` },
        { '--height': getHeight() },
      ]"
      :class="[increaseHeight ? 'expand' : '', `${source}`]"
    >
      <div
        class="menu-wrapper flex-grow relative scroll overall_scroll"
        :class="[`scroll__${source}`]"
      >
        <div class="menu-content">
          <div class="menu-item list-wrapper">
            <div>
              <div
                class="list-item cursor-pointer flex border-b border-b-0 group-archive"
                :class="[`group-archive__${source}`]"
              >
                <template v-if="socialTop">
                  <SavedPrice
                    :visible="isSocialVisible"
                    :type="'Total Pre-Set Pricing'"
                    :selected-type="socialType"
                    :feeds-list="savedPriceInfoList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandSocial"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  ></SavedPrice>
                  <!-- <feeds-type
                    :visible="isEmailVisible"
                    :type="'Email'"
                    :selected-type="emailType"
                    :feeds-list="emailFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandEmail"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </feeds-type>
                  <feeds-type
                    :visible="isWebVisible"
                    :type="'Website'"
                    :selected-type="webType"
                    :feeds-list="webFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandWeb"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </feeds-type> -->
                </template>
                <!-- <template v-if="emailTop">
                  <feeds-type
                    :visible="isEmailVisible"
                    :type="'Email'"
                    :selected-type="emailType"
                    :feeds-list="emailFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandEmail"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </feeds-type>
                  <feeds-type
                    :visible="isSocialVisible"
                    :type="'Social Media'"
                    :selected-type="socialType"
                    :feeds-list="savedPriceInfoList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandSocial"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  ></feeds-type>
                  <feeds-type
                    :visible="isWebVisible"
                    :type="'Website'"
                    :selected-type="webType"
                    :feeds-list="webFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandWeb"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </feeds-type>
                </template>
                <template v-if="webTop">
                  <feeds-type
                    :visible="isWebVisible"
                    :type="'Website'"
                    :selected-type="webType"
                    :feeds-list="webFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandWeb"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </feeds-type>
                  <feeds-type
                    :visible="isSocialVisible"
                    :type="'Social Media'"
                    :selected-type="socialType"
                    :feeds-list="savedPriceInfoList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandSocial"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  ></feeds-type>
                  <feeds-type
                    :visible="isEmailVisible"
                    :type="'Email'"
                    :selected-type="emailType"
                    :feeds-list="emailFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandEmail"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </feeds-type>
                </template> -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="
          (menuOpen && !socialType && !emailType) ||
          (socialType && socialTotalPages === 1) ||
          (emailType && socialTotalPages === 1)
        "
        class="equity cursor-pointer sticky bottom-0 left-0"
        :class="[`${source}`]"
        @click="showEditFeed(), hideMobileHaeder()"
      >
        <span class="text-lg text-bold font-sans">Edit Feeds</span>
      </div>
      <div
        v-if="
          menuOpen &&
          ((socialType && socialTotalPages > 1) ||
            (emailType && socialTotalPages > 1))
        "
        class="equity sticky bottom-0 left-0 flex items-center justify-between rounded-b-3xl"
        :class="[`${source}`]"
      >
        <div class="flex items-center space-x-5">
          <button
            v-if="socialType || emailType"
            :disabled="isSocialFirstPage"
            class="flex justify-center items-center w-8 h-8 rounded-full"
            :class="
              isSocialFirstPage
                ? 'bg-white-opasity-50 cursor-default'
                : 'bg-white cursor-pointer'
            "
            @click="clickPreviousPage(false)"
          >
            <fa
              :icon="['fas', 'chevron-left']"
              class="feeds-button"
              :class="[`feeds-button__${source}`]"
            />
          </button>
          <span
            v-if="socialType || emailType"
            class="text-lg font-sans text-normal"
            >{{ socialCurrentPage }}/{{ socialTotalPages }}</span
          >
          <button
            v-if="socialType || emailType"
            :disabled="isSocialLastPage"
            class="flex justify-center items-center w-8 h-8 rounded-full"
            :class="
              isSocialLastPage
                ? 'bg-white-opasity-50  cursor-default'
                : 'bg-white cursor-pointer'
            "
            @click="clickNextPage(false)"
          >
            <fa
              :icon="['fas', 'chevron-right']"
              class="feeds-button"
              :class="[`feeds-button__${source}`]"
            />
          </button>
        </div>
        <!-- <span
          class="cursor-pointer"
          @click="showEditFeed(), hideMobileHaeder()"
        >
          <fa :icon="['fas', 'pencil-alt']" />
        </span> -->
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
// // import { defineComponent, ref } from '@nuxtjs/composition-api'
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core'
import SavedPrice from '~/components/calculation/SavedPrice.vue'

export default defineComponent({
  name: 'ArchiveSourceDropdown',
  filters: {
    currency(value) {
      const formattedValue = new Intl.NumberFormat().format(Math.abs(value))
      return value >= 0 ? `$${formattedValue}` : `$(${formattedValue})`
    },
  },
  components: {
    SavedPrice,
  },
  props: {
    active: {
      type: Boolean,
      required: true,
      default: false,
    },
    height: {
      type: Number,
      required: true,
      default: 0,
    },
    source: {
      type: String,
      required: false,
      default: '',
    },
    windowHeight: {
      type: Number,
      default: 0,
    },
  },
  setup() {
    const feedBodyHeight = ref(0)
    const breakpoints = useBreakpoints(breakpointsTailwind)
    const nuxtApp = useNuxtApp()
    return {
      feedBodyHeight,
      isDesktop: breakpoints.greaterOrEqual('md'),
      nuxtApp,
    }
  },
  data() {
    return {
      collapse: false,
      menuOpen: false,
      increaseHeight: false,
      toggle: false,
      scroll: false,
      round: true,
      progress: false,
      socialType: true,
      emailType: true,
      webType: true,
      savedPriceInfoList: [],
      emailFeedsList: [],
      webFeedsList: [],
      menuHeight: false,
      showFeeds: true,
      social: [],
      email: [],
      socialTop: false,
      emailTop: false,
      webTop: false,
      socialStatus: false,
      emailStatus: false,
      webStatus: false,
      oldScrollY: 0,
      perPage: 0,
      windowWidth: '',
    }
  },
  computed: {
    loggedIn() {
      return this.$auth.loggedIn
    },
    ...mapState('setting', ['savedPriceInfo']),
    ...mapState('socialFeed', [
      'socialFeeds',
      'showSinglePost',
      'showSingleImagePost',
    ]),
    ...mapState('feedsDropdown', [
      'socialInitialItem',
      'emailInitialItem',
      'socialLoadCount',
      'emailLoadCount',
      'socialPerPage',
      'emailPerPage',
      'socialCurrentPage',
      'emailCurrentPage',
      'socialTotalPages',
      'emailTotalPages',
      'socialSelectItemPage',
      'emailSelectItemPage',
      'socialCurrentInitialItem',
      'emailCurrentInitialItem',
      'socialCurrentLoadCount',
      'emailCurrentLoadCount',
      'oldScrollTop',
      'currentOldScrollTop',
      'currentScrollPosition',
    ]),
    socialPage: {
      get() {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.social = []
        for (const item of this.savedPriceInfoList.slice(
          this.socialInitialItem,
          this.socialLoadCount,
        )) {
          // eslint-disable-next-line vue/no-side-effects-in-computed-properties
          this.social.push(item)
        }
        return this.social
      },
      set(newValue) {
        this.social = []
        this.social = newValue
      },
    },
    emailPage: {
      get() {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.email = []
        for (const item of this.emailFeedsList.slice(
          this.socialInitialItem,
          this.socialLoadCount,
        )) {
          // eslint-disable-next-line vue/no-side-effects-in-computed-properties
          this.email.push(item)
        }
        return this.email
      },
      set(newValue) {
        this.email = []
        this.email = newValue
      },
    },
    isSocialFirstPage() {
      return this.socialCurrentPage === 1
    },
    isEmailFirstPage() {
      return this.emailCurrentPage === 1
    },
    isSocialLastPage() {
      return this.socialCurrentPage === this.socialTotalPages
    },
    isEmailLastPage() {
      return this.emailCurrentPage === this.emailTotalPages
    },
    enableClick() {
      return this.savedPriceInfo && this.savedPriceInfo.length > 0
    },
    isSocialVisible() {
      return this.savedPriceInfoList && this.savedPriceInfoList.length > 0
    },
    isEmailVisible() {
      return this.emailFeedsList.length > 0
    },
    isWebVisible() {
      return this.webFeedsList.length > 0
    },
  },
  watch: {
    savedPriceInfo(data) {
      if (data) {
        this.resizeWindow()
        this.setTotalPages()
      }
    },
    windowWidth(data) {
      this.setTotalPages()
    },
  },
  created() {
    this.nuxtApp.$bus.$on('expand', () => {
      this.finalCollapsed()
    })
  },
  mounted() {
    this.windowWidth = window.innerWidth
    window.addEventListener('resize', this.resizeWindow)
    window.addEventListener('resize', this.getWindowWindth)
    if (this.savedPriceInfo && this.savedPriceInfo.length > 0) {
      this.resizeWindow()
    }
  },
  destroyed() {
    setTimeout(() => {
      window.removeEventListener('resize', this.resizeWindow)
      window.removeEventListener('resize', this.getWindowWindth)
    }, 2000)
  },
  methods: {
    getWindowWindth() {
      this.windowWidth = window.innerWidth
    },
    getHeight() {
      const totalType = 1
      // if (
      //   this.savedPriceInfoList.length > 0 &&
      //   this.emailFeedsList.length > 0 &&
      //   this.webFeedsList.length > 0
      // ) {
      //   totalType = 3
      // } else if (
      //   (this.savedPriceInfoList.length > 0 &&
      //     this.emailFeedsList.length > 0) ||
      //   (this.savedPriceInfoList.length > 0 && this.webFeedsList.length > 0) ||
      //   (this.webFeedsList.length > 0 && this.emailFeedsList.length > 0)
      // ) {
      //   totalType = 2
      // } else if (
      //   this.savedPriceInfoList.length > 0 ||
      //   this.emailFeedsList.length > 0 ||
      //   this.webFeedsList.length > 0
      // ) {
      //   totalType = 1
      // } else {
      //   totalType = 0
      // }
      if (this.socialTotalPages === 1) {
        if (
          this.menuOpen &&
          this.socialType &&
          this.emailType &&
          this.webType
        ) {
          return `${
            this.savedPriceInfoList.length * 42 +
            this.emailFeedsList.length * 42 +
            this.webFeedsList.length * 42 +
            totalType * 42 +
            40
          }px`
        } else if (
          this.menuOpen &&
          this.socialType &&
          this.emailType &&
          !this.webType
        ) {
          return `${
            this.savedPriceInfoList.length * 42 +
            this.emailFeedsList.length * 42 +
            totalType * 42 +
            40
          }px`
        } else if (
          this.menuOpen &&
          this.socialType &&
          !this.emailType &&
          this.webType
        ) {
          return `${
            this.savedPriceInfoList.length * 42 +
            this.webFeedsList.length * 42 +
            totalType * 42 +
            40
          }px`
        } else if (
          this.menuOpen &&
          !this.socialType &&
          this.emailType &&
          this.webType
        ) {
          return `${
            this.emailFeedsList.length * 42 +
            this.webFeedsList.length * 42 +
            totalType * 42 +
            40
          }px`
        } else if (
          this.menuOpen &&
          this.socialType &&
          !this.emailType &&
          !this.webType
        ) {
          return `${
            this.savedPriceInfoList.length * 42 + totalType * 42 + 40
          }px`
        } else if (
          this.menuOpen &&
          !this.socialType &&
          this.emailType &&
          !this.webType
        ) {
          return `${this.emailFeedsList.length * 42 + totalType * 42 + 40}px`
        } else if (
          this.menuOpen &&
          !this.socialType &&
          !this.emailType &&
          this.webType
        ) {
          return `${this.webFeedsList.length * 42 + totalType * 42 + 40}px`
        } else if (
          this.menuOpen &&
          !this.socialType &&
          !this.emailType &&
          !this.webType
        ) {
          return `${totalType * 42 + 40}px`
        }
      } else if (this.socialTotalPages > 1) {
        if (
          this.menuOpen &&
          this.socialType &&
          this.emailType &&
          this.webType
        ) {
          return `${this.socialPerPage * 42 + totalType * 42 + 40}px`
        } else if (
          this.menuOpen &&
          this.socialType &&
          this.emailType &&
          !this.webType
        ) {
          return `${this.socialPerPage * 42 + totalType * 42 + 40}px`
        } else if (
          this.menuOpen &&
          this.socialType &&
          !this.emailType &&
          this.webType
        ) {
          return `${this.socialPerPage * 42 + totalType * 42 + 40}px`
        } else if (
          this.menuOpen &&
          !this.socialType &&
          this.emailType &&
          this.webType
        ) {
          return `${this.socialPerPage * 42 + totalType * 42 + 40}px`
        } else if (
          this.menuOpen &&
          this.socialType &&
          !this.emailType &&
          !this.webType
        ) {
          return `${this.socialPerPage * 42 + totalType * 42 + 40}px`
        } else if (
          this.menuOpen &&
          !this.socialType &&
          this.emailType &&
          !this.webType
        ) {
          return `${this.socialPerPage * 42 + totalType * 42 + 40}px`
        } else if (
          this.menuOpen &&
          !this.socialType &&
          !this.emailType &&
          this.webType
        ) {
          return `${this.socialPerPage * 42 + totalType * 42 + 40}px`
        } else if (
          this.menuOpen &&
          !this.socialType &&
          !this.emailType &&
          !this.webType
        ) {
          return `${totalType * 42 + 40}px`
        }
        // if (
        //   this.menuOpen &&
        //   this.savedPriceInfoList.length > 0 &&
        //   this.emailFeedsList.length > 0 &&
        //   this.webFeedsList.length > 0
        // ) {
        //   return `${this.socialPerPage * 42 + 3 * 42 + 40}px`
        // } else if (
        //   this.menuOpen &&
        //   this.savedPriceInfoList.length > 0 &&
        //   this.emailFeedsList.length > 0
        // ) {
        //   return `${this.socialPerPage * 42 + 2 * 42 + 40}px`
        // } else if (
        //   this.menuOpen &&
        //   this.savedPriceInfoList.length > 0 &&
        //   this.webFeedsList.length > 0
        // ) {
        //   return `${this.socialPerPage * 42 + 2 * 42 + 40}px`
        // } else if (
        //   this.menuOpen &&
        //   this.emailFeedsList.length > 0 &&
        //   this.webFeedsList.length > 0
        // ) {
        //   return `${this.socialPerPage * 42 + 2 * 42 + 40}px`
        // } else if (
        //   this.menuOpen &&
        //   (this.savedPriceInfoList.length > 0 ||
        //     this.emailFeedsList.length > 0 ||
        //     this.webFeedsList.length > 0)
        // ) {
        //   return `${this.socialPerPage * 42 + 42 + 40}px`
        // } else {
        //   return ''
        // }
      }
      if (this.menuOpen && this.menuHeight) {
        return `${totalType * 42 + 40}px`
      } else {
        return ''
      }
    },
    handleScroll() {
      const allItem = document.querySelectorAll('.overall_scroll')
      allItem.forEach((item) => {
        if (item.offsetHeight !== 0) {
          if (this.oldScrollY < item.scrollTop) {
            if (
              item.scrollTop >= this.oldScrollTop + this.socialPerPage * 42 &&
              this.socialTotalPages - this.socialCurrentPage !== 1
            ) {
              this.$store.commit(
                'feedsDropdown/SET_OLD_SCROLL_TOP',
                this.oldScrollTop + this.socialPerPage * 42,
              )
              this.clickNextPage(true)
            } else if (
              item.scrollTop + item.clientHeight === item.scrollHeight &&
              this.socialTotalPages !== this.socialCurrentPage
            ) {
              const lastPageTotalItem =
                (this.savedPriceInfoList.length +
                  this.emailFeedsList.length +
                  this.webFeedsList.length +
                  3 -
                  (this.socialTotalPages - 1) * this.socialPerPage) *
                42
              this.$store.commit(
                'feedsDropdown/SET_OLD_SCROLL_TOP',
                this.oldScrollTop + lastPageTotalItem,
              )
              this.clickNextPage(true)
            }
          } else if (!(this.oldScrollY < item.scrollTop)) {
            if (this.socialTotalPages === this.socialCurrentPage) {
              const lastPageTotalItem =
                (this.savedPriceInfoList.length +
                  this.emailFeedsList.length +
                  this.webFeedsList.length +
                  3 -
                  (this.socialTotalPages - 1) * this.socialPerPage) *
                42
              if (item.scrollTop <= this.oldScrollTop - lastPageTotalItem) {
                this.$store.commit(
                  'feedsDropdown/SET_OLD_SCROLL_TOP',
                  this.oldScrollTop - lastPageTotalItem,
                )
                this.clickPreviousPage(true)
              }
            } else if (
              this.socialTotalPages !== this.socialCurrentPage &&
              this.socialCurrentPage !== 1
            ) {
              if (item.scrollTop === 0) {
                this.$store.commit(
                  'feedsDropdown/SET_OLD_SCROLL_TOP',
                  this.oldScrollTop - this.socialPerPage * 42,
                )
                this.clickPreviousPage(true)
              } else if (
                item.scrollTop <=
                this.oldScrollTop - this.socialPerPage * 42
              ) {
                this.$store.commit(
                  'feedsDropdown/SET_OLD_SCROLL_TOP',
                  this.oldScrollTop - this.socialPerPage * 42,
                )
                this.clickPreviousPage(true)
              }
            }
          }
          this.oldScrollY = item.scrollTop <= 0 ? 0 : item.scrollTop
        }
      })
    },
    activeFeed() {
      this.socialTop = false
      this.emailTop = false
      this.webTop = false
      this.socialStatus = false
      this.emailStatus = false
      this.webStatus = false
      if (
        (this.savedPriceInfoList && this.savedPriceInfoList.length > 0) ||
        (this.emailFeedsList && this.emailFeedsList.length > 0) ||
        (this.webFeedsList && this.webFeedsList.length > 0)
      ) {
        if (
          this.savedPriceInfo[0].provider !== 'Microsoft' &&
          this.savedPriceInfo[0].provider !== 'Google' &&
          this.savedPriceInfo[0].provider !== 'Web'
        ) {
          this.socialType = true
          this.emailType = true
          this.webType = true
          this.socialTop = true
        } else if (
          this.savedPriceInfo[0].provider === 'Microsoft' ||
          this.savedPriceInfo[0].provider === 'Google'
        ) {
          this.emailType = true
          this.socialType = true
          this.webType = true
          this.emailTop = true
        } else if (this.savedPriceInfo[0].provider === 'Web') {
          this.emailType = true
          this.socialType = true
          this.webType = true
          this.webTop = true
        }
      }
      // else if (this.savedPriceInfoList && this.savedPriceInfoList.length > 0) {
      //   if (
      //     this.savedPriceInfo[0].provider !== 'Microsoft' &&
      //     this.savedPriceInfo[0].provider !== 'Google'
      //   ) {
      //     this.socialType = true
      //     this.emailType = false
      //     this.socialTop = true
      //   } else if (
      //     this.savedPriceInfo[0].provider === 'Microsoft' ||
      //     this.savedPriceInfo[0].provider === 'Google'
      //   ) {
      //     this.emailType = true
      //     this.socialType = true
      //     this.emailTop = true
      //   }
      // } else if (this.emailFeedsList && this.emailFeedsList.length > 0) {
      //   if (
      //     this.savedPriceInfo[0].provider !== 'Microsoft' &&
      //     this.savedPriceInfo[0].provider !== 'Google'
      //   ) {
      //     this.socialType = false
      //     this.emailType = true
      //     this.socialTop = true
      //   } else if (
      //     this.savedPriceInfo[0].provider === 'Microsoft' ||
      //     this.savedPriceInfo[0].provider === 'Google'
      //   ) {
      //     this.emailType = true
      //     this.socialType = false
      //     this.emailTop = true
      //   }
      // }
    },
    selectedItem(item) {
      this.savedPriceInfoList.forEach((element) => {
        element.backgroundColor = false
      })
      // this.emailFeedsList.forEach((element) => {
      //   element.backgroundColor = false
      // })
      // this.webFeedsList.forEach((element) => {
      //   element.backgroundColor = false
      // })
      if (this.socialType || this.emailType || this.webType) {
        this.savedPriceInfoList.forEach((element, i) => {
          if (element.id === item) {
            element.backgroundColor = true
          } else {
            element.backgroundColor = false
          }
          this.savedPriceInfoList[i] = element
        })
        // this.emailFeedsList.forEach((element, i) => {
        //   if (element.id === item) {
        //     element.backgroundColor = true
        //   } else {
        //     element.backgroundColor = false
        //   }
        //   this.$set(this.emailFeedsList, i, this.emailFeedsList[i])
        // })
        // this.webFeedsList.forEach((element, i) => {
        //   if (element.id === item) {
        //     element.backgroundColor = true
        //   } else {
        //     element.backgroundColor = false
        //   }
        //   this.$set(this.webFeedsList, i, this.webFeedsList[i])
        // })
        this.socialPage = this.savedPriceInfoList
      }
    },
    resizeWindow() {
      if (this.isDesktop) {
        this.feedBodyHeight = window.innerHeight - (60 + 16 + 16)
      } else if (!this.isDesktop) {
        if (this.windowHeight > 0) {
          this.feedBodyHeight = this.windowHeight - 84
          console.log(this.feedBodyHeight, 'feedBodyHeight')
        } else {
          this.feedBodyHeight = window.innerHeight - 149 - 84
          console.log(this.feedBodyHeight, 'feedBodyHeight')
        }
      }
      if (this.feedBodyHeight >= 202) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_PER_PAGE',
          Math.floor((this.feedBodyHeight - 40 * 2) / 42),
        )
      } else {
        this.$store.commit('feedsDropdown/SET_SOCIAL_PER_PAGE', 1)
        this.$store.commit('feedsDropdown/SET_EMAIL_PER_PAGE', 1)
      }
      if (this.savedPriceInfo && this.savedPriceInfo.length > 0) {
        this.groupFeedByType()
      }
    },
    setTotalPages() {
      if (this.increaseHeight) {
        setTimeout(() => {
          this.collapsed()
        }, 100)
      }
      this.$store.commit(
        'feedsDropdown/SET_SOCIAL_LOAD_COUNT',
        this.socialPerPage,
      )
      this.$store.commit('feedsDropdown/SET_SOCIAL_CURRENT_PAGE', 1)
      this.$store.commit(
        'feedsDropdown/SET_SOCIAL_SELECT_ITEM_PAGE',
        this.socialCurrentPage,
      )
      this.$store.commit('feedsDropdown/SET_SOCIAL_INITIAL_ITEM', 0)
      this.$store.commit(
        'feedsDropdown/SET_SOCIAL_CURRENT_INITIAL_ITEM',
        this.socialInitialItem,
      )
      this.$store.commit(
        'feedsDropdown/SET_SOCIAL_CURRENT_LOAD_COUNT',
        this.socialLoadCount,
      )
      this.$store.commit(
        'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
        Math.ceil(
          (this.savedPriceInfoList.length +
            this.emailFeedsList.length +
            this.webFeedsList.length +
            1) /
            this.socialPerPage,
        ),
      )
    },
    setSocialEmailTotalPages() {
      document.querySelectorAll('.overall_scroll').forEach((item) => {
        item.removeEventListener('scroll', this.handleScroll)
        item.scrollTop = 0
      })
      this.$store.commit('feedsDropdown/SET_SOCIAL_CURRENT_PAGE', 1)
      this.$store.commit('feedsDropdown/SET_OLD_SCROLL_TOP', 0)
      console.log(this.socialType, this.emailType, this.webType, 'hello')
      if (!this.emailType && this.socialType && this.webType) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil(
            (this.savedPriceInfoList.length + this.webFeedsList.length + 1) /
              this.socialPerPage,
          ),
        )
      } else if (!this.socialType && this.emailType && this.webType) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil(
            (this.emailFeedsList.length + this.webFeedsList.length + 1) /
              this.socialPerPage,
          ),
        )
      } else if (!this.webType && this.socialType && this.emailType) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil(
            (this.savedPriceInfoList.length + this.emailFeedsList.length + 1) /
              this.socialPerPage,
          ),
        )
      } else if (!this.emailType && !this.socialType && this.webType) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil((this.webFeedsList.length + 1) / this.socialPerPage),
        )
      } else if (!this.webType && !this.socialType && this.emailType) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil((this.emailFeedsList.length + 1) / this.socialPerPage),
        )
      } else if (!this.emailType && !this.webType && this.socialType) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil((this.savedPriceInfoList.length + 1) / this.socialPerPage),
        )
      } else if (!this.socialType && !this.emailType && !this.webType) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil(2 / this.socialPerPage),
        )
      } else if (this.emailType && this.socialType && this.webType) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil(
            (this.savedPriceInfoList.length +
              this.emailFeedsList.length +
              this.webFeedsList.length +
              1) /
              this.socialPerPage,
          ),
        )
      }
      setTimeout(() => {
        document.querySelectorAll('.overall_scroll').forEach((item) => {
          item.addEventListener('scroll', this.handleScroll)
        })
      }, 500)
    },
    clickPreviousPage(value) {
      if (this.socialCurrentPage > 0) {
        if (this.socialType || this.emailType) {
          if (!value) {
            const allItem = document.querySelectorAll('.overall_scroll')
            allItem.forEach((item) => {
              item.removeEventListener('scroll', this.handleScroll)
              if (
                this.socialTotalPages !== this.socialCurrentPage &&
                item.offsetHeight !== 0
              ) {
                item.scrollTop = this.oldScrollTop - this.socialPerPage * 42
                if (item.offsetHeight !== 0) {
                  this.$store.commit(
                    'feedsDropdown/SET_OLD_SCROLL_TOP',
                    this.oldScrollTop - this.socialPerPage * 42,
                  )
                }
              } else if (
                this.socialTotalPages === this.socialCurrentPage &&
                item.offsetHeight !== 0
              ) {
                const lastPageTotalItem =
                  (this.savedPriceInfoList.length +
                    this.emailFeedsList.length +
                    this.webFeedsList.length +
                    3 -
                    (this.socialTotalPages - 1) * this.socialPerPage) *
                  42
                item.scrollTop = this.oldScrollTop - lastPageTotalItem
                if (item.offsetHeight !== 0) {
                  this.$store.commit(
                    'feedsDropdown/SET_OLD_SCROLL_TOP',
                    this.oldScrollTop - lastPageTotalItem,
                  )
                }
              }
            })
          }
          this.$store.commit('feedsDropdown/SET_SOCIAL_PREVIOUS_PAGE', 1)
          this.$store.commit(
            'feedsDropdown/SET_SOCIAL_LOAD_COUNT',
            this.socialInitialItem,
          )
          this.$store.commit(
            'feedsDropdown/SET_SET_SOCIAL_INITIAL_ITEM_PREVIOUS',
            this.socialPerPage,
          )
        }
      }
      setTimeout(() => {
        const allItem = document.querySelectorAll('.overall_scroll')
        allItem.forEach((item) => {
          item.addEventListener('scroll', this.handleScroll)
        })
      }, 500)
    },
    clickNextPage(value) {
      if (this.socialCurrentPage < this.socialTotalPages) {
        if (this.socialType || this.emailType || this.webType) {
          if (!value) {
            const allItem = document.querySelectorAll('.overall_scroll')
            allItem.forEach((item) => {
              item.removeEventListener('scroll', this.handleScroll)
              if (
                this.socialTotalPages - this.socialCurrentPage !== 1 &&
                item.offsetHeight !== 0
              ) {
                item.scrollTop = this.oldScrollTop + this.socialPerPage * 42
                if (item.offsetHeight !== 0) {
                  this.$store.commit(
                    'feedsDropdown/SET_OLD_SCROLL_TOP',
                    this.oldScrollTop + this.socialPerPage * 42,
                  )
                }
              } else if (
                this.socialTotalPages - this.socialCurrentPage === 1 &&
                item.offsetHeight !== 0
              ) {
                const lastPageTotalItem =
                  (this.savedPriceInfoList.length +
                    this.emailFeedsList.length +
                    this.webFeedsList.length +
                    3 -
                    (this.socialTotalPages - 1) * this.socialPerPage) *
                  42
                item.scrollTop = this.oldScrollTop + lastPageTotalItem
                if (item.offsetHeight !== 0) {
                  this.$store.commit(
                    'feedsDropdown/SET_OLD_SCROLL_TOP',
                    this.oldScrollTop + lastPageTotalItem,
                  )
                }
              }
            })
          }
          this.$store.commit('feedsDropdown/SET_SOCIAL_NEXT_PAGE', 1)
          this.$store.commit(
            'feedsDropdown/SET_SOCIAL_INITIAL_ITEM',
            this.socialLoadCount,
          )
          this.$store.commit(
            'feedsDropdown/SET_SOCIAL_LOAD_COUNT_NEXT',
            this.socialPerPage,
          )
        }
      }
      setTimeout(() => {
        const allItem = document.querySelectorAll('.overall_scroll')
        allItem.forEach((item) => {
          item.addEventListener('scroll', this.handleScroll)
        })
      }, 500)
    },
    groupFeedByType() {
      this.savedPriceInfoList = []
      this.emailFeedsList = []
      this.webFeedsList = []
      for (const item of this.savedPriceInfo) {
        if (item.provider === 'Microsoft' || item.provider === 'Google') {
          this.emailFeedsList.push(item)
        } else if (item.provider === 'Web') {
          this.webFeedsList.push(item)
        } else {
          this.savedPriceInfoList.push(item)
        }
      }
      this.activeFeed()
    },
    expandSocial() {
      let totalType = 0
      if (
        this.savedPriceInfoList.length > 0 &&
        this.emailFeedsList.length > 0 &&
        this.webFeedsList.length > 0
      ) {
        totalType = 3
      } else if (
        (this.savedPriceInfoList.length > 0 &&
          this.emailFeedsList.length > 0) ||
        (this.savedPriceInfoList.length > 0 && this.webFeedsList.length > 0) ||
        (this.webFeedsList.length > 0 && this.emailFeedsList.length > 0)
      ) {
        totalType = 2
      } else if (
        this.savedPriceInfoList.length > 0 ||
        this.emailFeedsList.length > 0 ||
        this.webFeedsList.length > 0
      ) {
        totalType = 1
      } else {
        totalType = 0
      }
      if (!this.socialType) {
        if (this.emailType && this.webType) {
          this.$emit(
            'define-height',
            this.savedPriceInfoList.length * 42 +
              this.emailFeedsList.length * 42 +
              this.webFeedsList.length * 42 +
              totalType * 42 +
              40,
          )
        } else if (this.emailType && !this.webType) {
          this.$emit(
            'define-height',
            this.savedPriceInfoList.length * 42 +
              this.emailFeedsList.length * 42 +
              totalType * 42 +
              40,
          )
        } else if (!this.emailType && this.webType) {
          this.$emit(
            'define-height',
            this.savedPriceInfoList.length * 42 +
              this.webFeedsList.length * 42 +
              totalType * 42 +
              40,
          )
        } else {
          this.$emit(
            'define-height',
            this.savedPriceInfoList.length * 42 + totalType * 42 + 40,
          )
        }
        setTimeout(() => {
          this.socialType = true
          this.setSocialEmailTotalPages()
          this.menuHeight = false
        }, 300)
      } else if (this.socialType) {
        this.socialType = false
        this.setSocialEmailTotalPages()
        setTimeout(() => {
          if (this.emailType && this.webType) {
            this.$emit(
              'define-height',
              this.emailFeedsList.length * 42 +
                this.webFeedsList.length * 42 +
                totalType * 42 +
                40,
            )
          } else if (this.emailType && !this.webType) {
            this.$emit(
              'define-height',
              this.emailFeedsList.length * 42 + totalType * 42 + 40,
            )
          } else if (!this.emailType && this.webType) {
            this.$emit(
              'define-height',
              this.webFeedsList.length * 42 + totalType * 42 + 40,
            )
          } else {
            this.$emit('define-height', totalType * 42 + 40)
          }
        }, 300)
        if (!this.socialType && !this.emailType && !this.webType) {
          this.menuHeight = true
        }
      }
    },
    expandEmail() {
      let totalType = 0
      if (
        this.savedPriceInfoList.length > 0 &&
        this.emailFeedsList.length > 0 &&
        this.webFeedsList.length > 0
      ) {
        totalType = 3
      } else if (
        (this.savedPriceInfoList.length > 0 &&
          this.emailFeedsList.length > 0) ||
        (this.savedPriceInfoList.length > 0 && this.webFeedsList.length > 0) ||
        (this.webFeedsList.length > 0 && this.emailFeedsList.length > 0)
      ) {
        totalType = 2
      } else if (
        this.savedPriceInfoList.length > 0 ||
        this.emailFeedsList.length > 0 ||
        this.webFeedsList.length > 0
      ) {
        totalType = 1
      } else {
        totalType = 0
      }
      if (!this.emailType) {
        if (this.socialType && this.webType) {
          this.$emit(
            'define-height',
            this.emailFeedsList.length * 42 +
              this.savedPriceInfoList.length * 42 +
              this.webFeedsList.length * 42 +
              totalType * 42 +
              40,
          )
        } else if (this.socialType && !this.webType) {
          this.$emit(
            'define-height',
            this.savedPriceInfoList.length * 42 +
              this.emailFeedsList.length * 42 +
              totalType * 42 +
              40,
          )
        } else if (!this.socialType && this.webType) {
          this.$emit(
            'define-height',
            this.emailFeedsList.length * 42 +
              this.webFeedsList.length * 42 +
              totalType * 42 +
              40,
          )
        } else {
          this.$emit(
            'define-height',
            this.emailFeedsList.length * 42 + totalType * 42 + 40,
          )
        }
        setTimeout(() => {
          this.emailType = true
          this.setSocialEmailTotalPages()
          this.menuHeight = false
        }, 300)
      } else if (this.emailType) {
        this.emailType = false
        this.setSocialEmailTotalPages()
        setTimeout(() => {
          if (this.socialType && this.webType) {
            this.$emit(
              'define-height',
              this.savedPriceInfoList.length * 42 +
                this.webFeedsList.length * 42 +
                totalType * 42 +
                40,
            )
          } else if (this.socialType && !this.webType) {
            this.$emit(
              'define-height',
              this.savedPriceInfoList.length * 42 + totalType * 42 + 40,
            )
          } else if (!this.socialType && this.webType) {
            this.$emit(
              'define-height',
              this.webFeedsList.length * 42 + totalType * 42 + 40,
            )
          } else {
            this.$emit('define-height', totalType * 42 + 40)
          }
        }, 300)
        if (!this.socialType && !this.emailType && !this.webType) {
          this.menuHeight = true
        }
      }
    },
    expandWeb() {
      let totalType = 0
      if (
        this.savedPriceInfoList.length > 0 &&
        this.emailFeedsList.length > 0 &&
        this.webFeedsList.length > 0
      ) {
        totalType = 3
      } else if (
        (this.savedPriceInfoList.length > 0 &&
          this.emailFeedsList.length > 0) ||
        (this.savedPriceInfoList.length > 0 && this.webFeedsList.length > 0) ||
        (this.webFeedsList.length > 0 && this.emailFeedsList.length > 0)
      ) {
        totalType = 2
      } else if (
        this.savedPriceInfoList.length > 0 ||
        this.emailFeedsList.length > 0 ||
        this.webFeedsList.length > 0
      ) {
        totalType = 1
      } else {
        totalType = 0
      }
      if (!this.webType) {
        if (this.socialType && this.emailType) {
          this.$emit(
            'define-height',
            this.webFeedsList.length * 42 +
              this.savedPriceInfoList.length * 42 +
              this.emailFeedsList.length * 42 +
              totalType * 42 +
              40,
          )
        } else if (this.socialType && !this.emailType) {
          this.$emit(
            'define-height',
            this.webFeedsList.length * 42 +
              this.savedPriceInfoList.length * 42 +
              totalType * 42 +
              40,
          )
        } else if (!this.socialType && this.emailType) {
          this.$emit(
            'define-height',
            this.emailFeedsList.length * 42 +
              this.webFeedsList.length * 42 +
              totalType * 42 +
              40,
          )
        } else {
          this.$emit(
            'define-height',
            this.webFeedsList.length * 42 + totalType * 42 + 40,
          )
        }
        setTimeout(() => {
          this.webType = true
          this.setSocialEmailTotalPages()
          this.menuHeight = false
        }, 300)
      } else if (this.webType) {
        this.webType = false
        this.setSocialEmailTotalPages()
        setTimeout(() => {
          if (this.socialType && this.emailType) {
            this.$emit(
              'define-height',
              this.savedPriceInfoList.length * 42 +
                this.emailFeedsList.length * 42 +
                totalType * 42 +
                40,
            )
          } else if (this.socialType && !this.emailType) {
            this.$emit(
              'define-height',
              this.savedPriceInfoList.length * 42 + totalType * 42 + 40,
            )
          } else if (!this.socialType && this.emailType) {
            this.$emit(
              'define-height',
              this.emailFeedsList.length * 42 + totalType * 42 + 40,
            )
          } else {
            this.$emit('define-height', totalType * 42 + 40)
          }
        }, 300)
        if (!this.socialType && !this.emailType && !this.webType) {
          this.menuHeight = true
        }
      }
    },
    selectedFeed(listItem) {
      let username = ''
      if (listItem.provider === 'Facebook') {
        username = listItem.name ? listItem.name : listItem.username
      } else {
        username = listItem.username ? listItem.username : listItem.name
      }
      this.$router.push('/home')
      this.nuxtApp.$bus.$emit('clear-all-date-range')
      this.$store.commit('home/RESET_START_END_DATE')
      if (this.showSinglePost || this.showSingleImagePost) {
        setTimeout(() => {
          this.$store.commit('home/SET_CURRENT_SOCIAL_COMPONENT', {
            provider: listItem.provider,
            username,
            id: listItem.id,
            selectedFeed: listItem,
          })
          this.$store.commit('home/SET_TEMP_ARRAY', [])
          this.$store.commit('socialFeed/SET_SHOW_TWITTER', true)
        }, 305)
      } else if (
        listItem.provider === 'Google' ||
        listItem.provider === 'Microsoft'
      ) {
        this.$store.commit('home/SET_EMAIL_DYNAMIC_COMP', {
          comp: 'EmailContent',
        })
        this.$store.commit('home/EXPAND_FULL_IMAGE', false)
        this.$store.commit('home/SET_TEMP_ARRAY', [])
        setTimeout(() => {
          this.$store.commit('home/SET_CURRENT_SOCIAL_COMPONENT', {
            provider: listItem.provider,
            username,
            id: listItem.id,
            selectedFeed: listItem,
          })
        }, 500)
      } else {
        this.$store.commit('home/SET_CURRENT_SOCIAL_COMPONENT', {
          provider: listItem.provider,
          username,
          id: listItem.id,
          selectedFeed: listItem,
        })

        this.$store.commit('home/SET_TEMP_ARRAY', [])
      }
      this.$store.commit('home/RESET_WEB_SEARCH')
      this.$store.commit('home/SET_SHOW_COMP', false)
      this.$store.commit('home/SET_CURRENT_TAB', 'All')
      this.$store.commit('home/SET_CURRENT_HEADER', 'RealTimeFeed')
      this.$store.dispatch('socialFeed/singlePostClose', false)
      this.$store.dispatch('socialFeed/singleImagePostClose', false)
      this.$store.commit('home/RESET_TWITTER_COMMENTS')
      this.$store.commit('home/RESET_PREVIOUS_TWITTER_COMMENTS')
      this.$store.commit('socialFeed/RESET_PREVIOUS_SINGLE_POST')
    },
    showEditFeed() {
      this.$router.push('/settings#service')
    },
    showAddFeedsComp() {
      this.$store.commit('socialFeed/SHOW_ADD_FEED_MODAL', true)
    },
    checkSelectedItem() {
      if (this.socialType && this.emailType && this.webType) {
        let tempArray = []
        if (this.socialTop) {
          tempArray = [
            ...this.savedPriceInfoList,
            ...this.emailFeedsList,
            ...this.webFeedsList,
          ]
        } else if (this.emailTop) {
          tempArray = [
            ...this.emailFeedsList,
            ...this.savedPriceInfoList,
            ...this.webFeedsList,
          ]
        } else if (this.webTop) {
          tempArray = [
            ...this.webFeedsList,
            ...this.savedPriceInfoList,
            ...this.emailFeedsList,
          ]
        }

        let enter = true
        if (enter) {
          for (const item of tempArray.slice(
            this.socialInitialItem,
            this.socialLoadCount,
          )) {
            if (item.backgroundColor === true) {
              enter = false
              this.socialStatus = true
              this.emailStatus = true
              this.webStatus = true
            }
          }
        }
        if (
          !this.socialStatus &&
          !this.emailStatus &&
          !this.webStatus &&
          enter
        ) {
          this.clickNextPage(false)
          // this.checkSelectedItem()
        }
      }
    },
    finalCollapsed() {
      if (this.isDesktop) {
        setTimeout(() => {
          // window.dispatchEvent(new Event('resize'))
          // if (
          //   this.socialPage.every((item) => item.backgroundColor === false) &&
          //   this.totalPages !== this.currentPage
          // ) {
          //   this.clickNextPage()
          // } else if (this.currentPage !== 1) {
          //   this.clickPreviousPage()
          // }
        }, 800)
      }
      this.toggle = false
      // if (!this.toggle && this.progress) {
      document.querySelectorAll('.overall_scroll').forEach((item) => {
        item.removeEventListener('scroll', this.handleScroll)
        item.scrollTop = 0
      })
      this.scroll = false
      this.increaseHeight = false
      this.$store.commit('feedsDropdown/SET_SHOW_DROPDOWN', false)
      setTimeout(() => {
        this.$emit('collapse-header', false)
      }, 140)
      setTimeout(() => {
        this.round = true
        this.menuOpen = false
        this.menuHeight = false
        this.$emit('expand', false)
        this.$store.commit('home/SET_FEEDS_DROPDOWN', false)
      }, 400)
      setTimeout(() => {
        this.progress = false
      }, 900)
      // }
    },
    collapsed($event) {
      if (this.progress) {
        if (this.isDesktop) {
          setTimeout(() => {
            // window.dispatchEvent(new Event('resize'))
            // if (
            //   this.socialPage.every((item) => item.backgroundColor === false) &&
            //   this.totalPages !== this.currentPage
            // ) {
            //   this.clickNextPage()
            // } else if (this.currentPage !== 1) {
            //   this.clickPreviousPage()
            // }
          }, 800)
        }
        this.toggle = false
      }
      if (!this.toggle && this.progress) {
        document.querySelectorAll('.overall_scroll').forEach((item) => {
          item.removeEventListener('scroll', this.handleScroll)
          item.scrollTop = 0
        })
        this.scroll = false
        this.increaseHeight = false
        this.$store.commit('feedsDropdown/SET_SHOW_DROPDOWN', false)
        setTimeout(() => {
          this.$emit('collapse-header', false)
        }, 140)
        setTimeout(() => {
          this.round = true
          this.menuOpen = false
          this.menuHeight = false
          this.$emit('expand', false)
          this.$store.commit('home/SET_FEEDS_DROPDOWN', false)
        }, 400)
        setTimeout(() => {
          this.progress = false
        }, 900)
      }
    },
    expand($event) {
      if (!this.progress) {
        this.$store.commit('feedsDropdown/SET_SOCIAL_INITIAL_ITEM', 0)
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_LOAD_COUNT',
          this.socialPerPage,
        )
        this.$store.commit('feedsDropdown/SET_OLD_SCROLL_TOP', 0)
        this.$store.commit('feedsDropdown/SET_CURRENT_SCROLL_POSITION', 0)
        this.$store.commit('feedsDropdown/SET_CURRENT_OLD_SCROLL_TOP', 0)
        this.$store.commit('feedsDropdown/SET_SOCIAL_CURRENT_PAGE', 1)
        this.activeFeed()
        setTimeout(() => {
          // this.checkSelectedItem()
        }, 380)
        if (this.isDesktop && this.showSingleImagePost) {
          setTimeout(() => {
            window.dispatchEvent(new Event('resize'))
            // if (
            //   this.socialPage.every((item) => item.backgroundColor === false) &&
            //   this.totalPages !== this.currentPage
            // ) {
            //   this.clickNextPage()
            // } else if (this.currentPage !== 1) {
            //   this.clickPreviousPage()
            // }
          }, 100)
        }
        this.toggle = true
      } else if (this.progress) {
        if (this.isDesktop && this.showSingleImagePost) {
          setTimeout(() => {
            window.dispatchEvent(new Event('resize'))
            // if (
            //   this.socialPage.every((item) => item.backgroundColor === false) &&
            //   this.totalPages !== this.currentPage
            // ) {
            //   this.clickNextPage()
            // } else if (this.currentPage !== 1) {
            //   this.clickPreviousPage()
            // }
          }, 800)
        }
        this.toggle = false
      }
      if (
        this.toggle &&
        !this.progress &&
        (this.emailType || this.socialType || this.webType) &&
        (this.emailFeedsList.length > 0 ||
          this.savedPriceInfoList.length > 0 ||
          this.webFeedsList.length > 0)
      ) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil(
            (this.savedPriceInfoList.length +
              this.emailFeedsList.length +
              this.webFeedsList.length +
              1) /
              this.socialPerPage,
          ),
        )
        document.querySelectorAll('.overall_scroll').forEach((item) => {
          if (item.offsetHeight === 0) {
            setTimeout(() => {
              item.addEventListener('scroll', this.handleScroll)
            }, 500)
          }
        })
        this.menuOpen = true
        this.menuHeight = false
        this.round = false
        this.$emit('collapse-header', true)
        if (
          this.savedPriceInfoList.length > 0 &&
          this.emailFeedsList.length > 0 &&
          this.webFeedsList.length > 0
        ) {
          if (
            this.savedPriceInfoList.length +
              this.emailFeedsList.length +
              this.webFeedsList.length +
              3 >=
            this.socialPerPage
          ) {
            this.$emit('define-height', this.socialPerPage * 42 + 3 * 42 + 40)
          } else if (
            this.savedPriceInfoList.length +
              this.emailFeedsList.length +
              this.webFeedsList.length +
              3 <
            this.socialPerPage
          ) {
            this.$emit(
              'define-height',
              this.savedPriceInfoList.length * 42 +
                this.emailFeedsList.length * 42 +
                this.webFeedsList.length * 42 +
                3 * 42 +
                40,
            )
          }
        } else if (
          this.savedPriceInfoList.length > 0 &&
          this.emailFeedsList.length > 0
        ) {
          if (
            this.savedPriceInfoList.length + this.emailFeedsList.length + 2 >=
            this.socialPerPage
          ) {
            this.$emit('define-height', this.socialPerPage * 42 + 2 * 42 + 40)
          } else if (
            this.savedPriceInfoList.length + this.emailFeedsList.length + 2 <
            this.socialPerPage
          ) {
            this.$emit(
              'define-height',
              this.savedPriceInfoList.length * 42 +
                this.emailFeedsList.length * 42 +
                2 * 42 +
                40,
            )
          }
        } else if (
          this.savedPriceInfoList.length > 0 &&
          this.webFeedsList.length > 0
        ) {
          if (
            this.savedPriceInfoList.length + this.webFeedsList.length + 2 >=
            this.socialPerPage
          ) {
            this.$emit('define-height', this.socialPerPage * 42 + 2 * 42 + 40)
          } else if (
            this.savedPriceInfoList.length + this.webFeedsList.length + 2 <
            this.socialPerPage
          ) {
            this.$emit(
              'define-height',
              this.savedPriceInfoList.length * 42 +
                this.webFeedsList.length * 42 +
                2 * 42 +
                40,
            )
          }
        } else if (
          this.emailFeedsList.length > 0 &&
          this.webFeedsList.length > 0
        ) {
          if (
            this.emailFeedsList.length + this.webFeedsList.length + 2 >=
            this.socialPerPage
          ) {
            this.$emit('define-height', this.socialPerPage * 42 + 2 * 42 + 40)
          } else if (
            this.emailFeedsList.length + this.webFeedsList.length + 2 <
            this.socialPerPage
          ) {
            this.$emit(
              'define-height',
              this.emailFeedsList.length * 42 +
                this.webFeedsList.length * 42 +
                2 * 42 +
                40,
            )
          }
        } else if (this.savedPriceInfoList.length > 0) {
          if (this.savedPriceInfoList.length + 1 >= this.socialPerPage) {
            this.$emit('define-height', this.socialPerPage * 42 + 42 + 40)
          } else {
            this.$emit(
              'define-height',
              this.savedPriceInfoList.length * 42 + 42 + 40,
            )
          }
        } else if (this.emailFeedsList.length > 0) {
          if (this.emailFeedsList.length + 1 >= this.socialPerPage) {
            this.$emit('define-height', this.socialPerPage * 42 + 42 + 40)
          } else {
            this.$emit(
              'define-height',
              this.emailFeedsList.length * 42 + 42 + 40,
            )
          }
        } else if (this.webFeedsList.length > 0) {
          if (this.webFeedsList.length + 1 >= this.socialPerPage) {
            this.$emit('define-height', this.socialPerPage * 42 + 42 + 40)
          } else {
            this.$emit('define-height', this.webFeedsList.length * 42 + 42 + 40)
          }
        }
        this.$store.commit('home/SET_FEEDS_DROPDOWN', true)
        this.$emit('expand', true)
        setTimeout(() => {
          this.increaseHeight = true
          this.$store.commit('feedsDropdown/SET_SHOW_DROPDOWN', true)
        }, 300)
        setTimeout(() => {
          this.scroll = true
          this.progress = true
        }, 900)
      } else if (!this.toggle && this.progress) {
        document.querySelectorAll('.overall_scroll').forEach((item) => {
          item.removeEventListener('scroll', this.handleScroll)
          item.scrollTop = 0
        })
        this.scroll = false
        this.increaseHeight = false
        this.$store.commit('feedsDropdown/SET_SHOW_DROPDOWN', false)
        setTimeout(() => {
          this.$emit('collapse-header', false)
        }, 140)
        setTimeout(() => {
          this.round = true
          this.menuOpen = false
          this.menuHeight = false
          this.$emit('expand', false)
          this.$store.commit('home/SET_FEEDS_DROPDOWN', false)
        }, 400)
        setTimeout(() => {
          this.progress = false
          this.activeFeed()
        }, 900)
      }
    },
    hideMobileHaeder() {
      this.$store.dispatch('header/closeMobileHeader')
    },
    setNameOrUsername(feed) {
      if (feed.provider === 'Twitter') {
        const addAtSign = !feed.name.length > 0
        const name = feed.name.length > 0 ? feed.name : feed.username
        return addAtSign ? '@' + name : name
      } else {
        return feed.name.length > 0 ? feed.name : feed.username
      }
    },
  },
})
</script>

<style lang="scss" scoped>
@import url('https://fonts.googleapis.com/css2?family=Neuton');

name-font {
  font-family: 'Neuton', serif;
}

.feedsShadow {
  box-shadow: 0 25px 50px 20px rgb(0 0 0 / 25%);
}

.bg-white-opasity-50 {
  background-color: #ffffff80;
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s;
}
.fade-enter,
.fade-leave-to {
  height: 100%;
  opacity: 100;
}
.tooltip {
  @apply invisible;
}
.has-tooltip:hover .tooltip {
  @apply visible;
  left: -30px;
  padding: 3px 10px;
}
.text-xxs {
  font-size: 11px;
  line-height: 16px;
}
.min-w-7-2 {
  min-width: 1.875rem !important;
}
.min-h-7-2 {
  min-height: 1.875rem !important;
}

.menu-wrapper {
  .background {
    opacity: 0;
    position: absolute;
    z-index: 10;
    transition:
      margin-top 0.5s ease-in-out,
      opacity 0.3s ease 0.5s,
      background 0.3s ease 0.5s;
    height: 2.75rem;
    @apply w-full;

    .corner-top {
      position: absolute;
      top: -20px;
      right: 0px;
      display: inline-block;
      transition: color 0.3s ease 0.5s;
    }

    .corner-bottom {
      position: absolute;
      bottom: -20px;
      right: 0px;
      transform: rotate(270deg);
      display: inline-block;
      transition: color 0.3s ease 0.5s;
    }

    &.active {
      opacity: 1;
      .corner-top,
      .corner-bottom {
        // opacity: 1;
      }
    }
  }
}

.balance__menu {
  z-index: 80;
  transition:
    border-radius 0.5s ease-in-out,
    border-top-left-radius 0.5s ease-in-out,
    border-top-right-radius 0.5s ease-in-out;
  max-width: 16rem;
  min-width: 16rem;
  @apply md:w-full w-full;
  .btn-wrapper {
    background: #4c5764;
    @apply w-full px-4 flex justify-between items-center shadow-sm;

    .dropdown-btn {
      direction: ltr;
      line-height: 2.15rem !important;
      @apply text-white w-full h-10 text-lg focus:outline-none;

      * {
        // pointer-events: none;
      }
    }
  }
  .dropdown-btn {
    direction: ltr;
    @apply text-white w-full h-10 px-3.5 text-lg flex justify-between items-center shadow-sm focus:outline-none;

    * {
      // pointer-events: none;
    }
  }
}

/* these classname shuld be provided through the `source` props. */
.archive {
  background: #8db230 !important;
}

.search {
  background: #7d80bd !important;
}

.pricing {
  background: #a22a2a !important;
}

.alert {
  background: #e05252 !important;
}

.settings {
  background: #e0ad1f !important;
}

.help {
  background: #e05252 !important;
}

.feeds-button {
  color: #e4801d;
}
.username__archive,
.feeds-button__archive {
  color: #8db230 !important;
}
.username__search,
.feeds-button__search {
  color: #7d80bd !important;
}
.username__pricing,
.feeds-button__pricing {
  color: #a22a2a !important;
}
.username__alert,
.feeds-button__alert {
  color: #e05252 !important;
}
.username__settings,
.feeds-button__settings {
  color: #e0ad1f !important;
}

.dropdown {
  max-width: 16rem;
  overflow: hidden;
  @apply w-full;
  z-index: 100;
  overflow-x: hidden;
  height: 0;
  max-height: calc(100% - 132px);
  transform-origin: top;
  transition:
    transform 0.3s linear,
    height 0.5s linear;
  .dropdown-btn,
  .menu-title,
  .list-title,
  .equity {
    //direction: ltr;
    // background: #b76d1d;
    background: #e4801d;
    line-height: 2.15rem !important;
    @apply text-white w-full h-10 px-3 text-lg flex justify-between items-center shadow-sm focus:outline-none;

    * {
      // pointer-events: none;
    }

    &__archive {
      background: #5f822d !important;
    }

    &__search {
      background: #5a57a2 !important;
    }

    &__pricing {
      background: #a22a2a !important;
    }

    &__alert {
      background: #9d1616 !important;
    }

    &__settings {
      background: #695316 !important;
    }

    &__help {
      background: #e05252 !important;
    }
  }

  .equity {
    @apply py-2;
    padding-left: 13px;
    padding-right: 13px;
  }

  // .scroll-hidden {
  //   overflow: hidden;
  // }

  .scroll {
    scroll-behavior: smooth;
    overflow-y: auto;
    overflow-x: hidden;
    -ms-overflow-style: none; /* IE 11 */
    scrollbar-width: thin;
    scrollbar-color: #b76d1d #ececec; /* Firefox 64 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    /* Track */
    &::-webkit-scrollbar-track {
      border-radius: 3px;
      background: #ececec;
    }

    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #b76d1d;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #b76d1d;
    }
  }
  .scroll__archive {
    scroll-behavior: smooth;
    scrollbar-color: #5f822d #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #5f822d;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #5f822d;
    }
  }
  .scroll__search {
    scroll-behavior: smooth;
    scrollbar-color: #5a57a2 #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #5a57a2;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #5a57a2;
    }
  }
  .scroll__pricing {
    scroll-behavior: smooth;
    scrollbar-color: #a22a2a #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #a22a2a;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #a22a2a;
    }
  }
  .scroll__alert {
    scroll-behavior: smooth;
    scrollbar-color: #9d1616 #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #9d1616;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #9d1616;
    }
  }
  .scroll__settings {
    scroll-behavior: smooth;
    scrollbar-color: #695316 #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #695316;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #695316;
    }
  }

  .list-wrapper {
    .group-archive {
      background: #b76d1d;
      font-style: normal;
      font-variant: normal;
      font-weight: normal;
      border-bottom: 0px;
      letter-spacing: 0;
      font-size: 1.125rem;
      color: #f2f2f2;

      &__name {
        height: 42px;
      }
      &__archive {
        background: #5f822d !important;
      }
      &__search {
        background: #5a57a2 !important;
      }
      &__pricing {
        background: #a22a2a !important;
      }
      &__alert {
        background: #9d1616 !important;
      }
      &__settings {
        background: #695316 !important;
      }
      &__help {
        background: #e05252 !important;
      }

      .list {
        .list-item {
          height: 42px;
          background: #e4801d;
          border-color: #b76d1d;
        }
        .__archive {
          background: #8db230;
          border-color: #5f822d !important;
        }
        .__search {
          background: #7d80bd;
          border-color: #5a57a2 !important;
        }
        .__pricing {
          background: #d94848;
          border-color: #a22a2a !important;
        }
        .__alert {
          background: #e05252;
          border-color: #9d1616 !important;
        }
        .__settings {
          background: #e0ad1f;
          border-color: #695316 !important;
        }
        .__help {
          background: #8db230;
          border-color: #5f822d !important;
        }

        .__active_home {
          background: #cc6f15;
        }
        .__active_archive {
          background: #7b9a29;
          border-color: #5f822d !important;
        }
        .__active_search {
          background: #696db4;
          border-color: #5a57a2 !important;
        }
        .__active_pricing {
          background: #c44040;
          border-color: #a22a2a !important;
        }
        .__active_alert {
          background: #9d1616;
          border-color: #9d1616 !important;
        }
        .__active_settings {
          background: #b18714;
          border-color: #695316 !important;
        }
      }
    }

    /*.list {
        .list-item {
          direction: ltr;
          height: 45px;
          .background {
            opacity: 0;
            // transition: opacity 1s;
            // outline: none;
            // overflow: hidden;

            transform: translateY(-100%);
            transition: transform 0.5s ease-in-out,
              opacity 0.3s ease-in-out 0.5s;
          }
          &.active {
            // z-index: 1000;
            // @apply relative;

            .background {
              @apply h-full w-full;
              margin-left: -10px;
              position: absolute;
              left: 10px;
              opacity: 1;
              transform: translateY(0);
              z-index: 10;
              width: 100%;
              &__circle {
                @apply h-10 w-10 rounded-full inline-block;
              }

              .corner-top {
                position: absolute;
                top: -19px;
                right: 0px;
                display: inline-block;
              }

              .corner-bottom {
                position: absolute;
                bottom: -19px;
                right: 0px;
                transform: rotate(270deg);
                display: inline-block;
              }
            }
          }
          &-content {
            z-index: 20;
            transition: all 0.5s ease-in-out;
            @apply relative w-full;
          }
        }
      }*/
  }

  .list-item {
    // direction: ltr;
    text-align: left;
  }

  &.expand {
    height: var(--height);
    // height: calc(100vh - 172px);
  }
  &.expand1 {
    height: auto;
  }
}

.dropdown-icon {
  pointer-events: none;

  &.rotate {
    transform: rotate(180deg) !important;
  }
}
.line-height {
  line-height: 16px;
}
.list-inner-width {
  width: calc(100% - 40px);
}
.username-width {
  width: calc(100% - 90px);
}
.username-clamp {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.mail-icon-wrapper {
  @apply flex justify-center items-center absolute w-3.5 h-3.5 rounded-full bg-white;
  bottom: -2px;
  right: -1px;
}
.social-icon {
  @apply absolute w-3.5 h-3.5;
  bottom: -2px;
  right: -1px;
}
.letter-spacing-1px {
  letter-spacing: 1px;
}
@media (max-width: 767px) {
  .dropdown {
    // transition: height 1s linear;
    max-width: 100%;
    max-height: var(--maxHeight);
  }
  .balance__menu {
    max-width: 100% !important;
  }
  .mobile-inner-width {
    width: calc(100% - 30px);
  }
  .mobile-username-width {
    width: calc(100% - 100px);
  }
  .mobile-clamp {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .expand {
    max-height: var(--maxHeight);
  }
}
/* for page */
.fadeIn-enter-active,
.fadeIn-leave-active {
  transition: opacity 0.5s;
}
.fadeIn-enter,
.fadeIn-leave-to {
  opacity: 0;
}
</style>
