<template>
  <div>
    <div
      v-if="visible"
      class="flex flex-row items-center justify-between px-3.5 group-archive__name text-lg font-bold button-font"
     
    >
      <span class="letter-spacing-1px">{{ type }}</span>
      <span v-if="!selectedType">
        <fa class="fa-lg" :icon="['fas', 'caret-down']" />
      </span>
      <span v-else>{{ feedsList.length }}</span>
    </div>
    <ul
      class="list overflow-hidden transition-all duration-500 ease-linear"
      :style="[
        selectedType
          ? {
              height: `${feedsList.length * 42}px`,
              opacity: 1,
              'transition-delay': '0s',
            }
          : {
              height: '0px',
              opacity: 0,
              'transition-delay': '0s',
            },
      ]"
    >
      <transition-group name="fadeIn" mode="out-in">
        <template v-if="showFeeds">
          <li
            v-for="(listItem, index) in feedsList"
            :key="index"
            class="list-item border-b cursor-pointer py-1 px-3 flex flex-row"
            :class="[
              !listItem.backgroundColor ? `__${source}` : `__active_${source}`,
            ]"
            @click="selected(listItem.id)"
          >
            <div class="w-full h-full flex flex-row justify-between items-center">
              <div class="w-full h-full flex flex-row items-center space-x-2">
                <!-- <div
                  class="min-w-7-2 w-7.2 min-h-7-2 h-7.2 rounded-full relative"
                  :style="{
                    background: listItem.logoBackgroundColor,
                  }"
                > -->
                  <!-- <template v-if="!listItem.profilePic">
                    <facebook-icon
                      v-if="listItem.provider === 'Facebook'"
                      class="min-w-7-2 w-7.2 min-h-7-2 h-7.2 rounded-full"
                    ></facebook-icon>
                    <linkedin-icon
                      v-if="listItem.provider === 'LinkedIn'"
                      class="min-w-7-2 w-7.2 min-h-7-2 h-7.2 rounded-full"
                    ></linkedin-icon>
                    <twitter-icon
                      v-if="listItem.provider === 'Twitter'"
                      class="min-w-7-2 w-7.2 min-h-7-2 h-7.2 rounded-full"
                    ></twitter-icon>
                    <img
                      v-if="listItem.provider === 'Instagram'"
                      :src="instagram"
                      class="min-w-7-2 w-7.2 min-h-7-2 h-7.2 rounded-full"
                      alt="Instagram Icon"
                    />
                    <PinterestIcon
                      v-if="listItem.provider === 'Pinterest'"
                      class="min-w-7-2 w-7.2 min-h-7-2 h-7.2 rounded-full"
                    ></PinterestIcon>
                    <RedditIcon
                      v-if="listItem.provider === 'Reddit'"
                      class="min-w-7-2 w-7.2 min-h-7-2 h-7.2 rounded-full"
                    ></RedditIcon>
                    <div
                      v-if="listItem.provider === 'Twitter'"
                      class="min-w-7-2 w-7.2 min-h-7-2 h-7.2 rounded-full twitter-icon"
                    ></div>
                    <microsoft-icon
                      v-if="listItem.provider === 'Microsoft'"
                      class="min-w-7-2 w-7.2 min-h-7-2 h-7.2 rounded-full"
                    ></microsoft-icon>
                    <img
                      v-if="listItem.provider === 'Google'"
                      :src="GoogleIcon"
                      class="min-w-7-2 w-7.2 min-h-7-2 h-7.2 rounded-full"
                      alt="Google Icon"
                    />
                    <img
                      v-if="listItem.provider === 'Web'"
                      :src="WebsiteIcon"
                      class="min-w-7-2 w-7.2 min-h-7-2 h-7.2 rounded-full"
                      alt="Web Icon"
                    />
                  </template>
                  <img
                    v-else
                    class="min-w-7-2 w-7.2 min-h-7-2 h-7.2 rounded-full"
                    :src="listItem.profilePic"
                    :alt="`${setNameOrUsername(listItem)} Profile Picture`"
                  /> -->
                  <!-- <template>
                    <facebook-icon
                      v-if="listItem.provider === 'Facebook'"
                      class="social-icon"
                    ></facebook-icon>
                    <linkedin-icon
                      v-if="listItem.provider === 'LinkedIn'"
                      class="social-icon"
                    ></linkedin-icon>
                    <div
                      v-if="listItem.provider === 'Twitter'"
                      class="social-icon twitter-icon"
                    ></div>
                    <img
                      v-if="listItem.provider === 'Instagram'"
                      :src="instagram"
                      class="social-icon"
                      alt="Instagram Icon"
                    />
                    <PinterestIcon
                      v-if="listItem.provider === 'Pinterest'"
                      class="social-icon"
                    ></PinterestIcon>
                    <RedditIcon
                      v-if="listItem.provider === 'Reddit'"
                      class="social-icon"
                    ></RedditIcon>
                    <TikTokIcon
                      v-if="listItem.provider === 'TikTok'"
                      class="social-icon"
                    ></TikTokIcon>
                    <img
                      v-if="listItem.provider === 'YouTube'"
                      class="social-icon"
                      :src="YoutubeIcon"
                      alt="Youtube Circle Icon"
                    />
                    <microsoft-icon
                      v-if="listItem.provider === 'Microsoft'"
                      class="social-icon"
                    ></microsoft-icon>
                    <img
                      v-if="listItem.provider === 'Google'"
                      :src="GoogleIcon"
                      class="social-icon"
                      alt="Google Icon"
                    />
                    <div
                      v-if="listItem.provider === 'Web'"
                      class="social-icon bg-white flex justify-center items-center"
                    >
                      <img
                        :src="WebsiteIcon"
                        class="w-2.5 h-2.5"
                        alt="Web Icon"
                      />
                    </div>
                  </template> -->
                <!-- </div> -->
                <div class="list-inner-width h-full flex flex-col">
                  <div class="relative h-full has-tooltip">
                    <p
                      class="text-lg h-full flex items-center line-height font-sans line-clamp-1 text-color text-normal"
                    >
                      {{ listItem.name }}
                    </p>
                    <!-- <span
                      v-if="
                        !hideText($config.public.workflow) &&
                        setNameOrUsername(listItem).length > 17
                      "
                      class="break-words absolute bg-white invisible text-left z-100 rounded-2xl tooltip text-md text-orange-dark text-normal"
                      :class="[
                        `username__${source}`,
                        feedsList.length - 1 === index && index !== 0
                          ? '-top-10'
                          : index === 0
                          ? 'top-0'
                          : '-top-5',
                      ]"
                      >{{ setNameOrUsername(listItem) }}</span
                    > -->
                  </div>
                  <!-- <div class="w-full flex flex-row justify-between pr-0.5">
                    <div class="username-width relative has-tooltip">
                      <p
                        class="text-xs font-sans username-clamp text-color text-normal"
                      >
                        {{ listItem.username | atSignUsername }}
                      </p>
                      <span
                        v-if="
                          !hideText($config.public.workflow) &&
                          listItem.username.length > 14
                        "
                        class="absolute bg-white invisible text-left z-100 rounded-2xl tooltip text-md text-orange-dark text-normal username_tooltip"
                        :class="[
                          `username__${source}`,
                          feedsList.length - 1 === index && index !== 0
                            ? '-top-10'
                            : index === 0
                            ? '-top-3'
                            : '-top-5',
                          listItem.username.length > 40
                            ? 'break-all'
                            : 'break-words ',
                        ]"
                      >
                        {{ listItem.username | atSignUsername }}
                      </span>
                    </div>
                    <span class="text-xs font-sans text-normal text-color">
                      {{ listItem.dateJoined | diffForHumans }}
                      ago
                    </span>
                  </div> -->
                </div>
              </div>
            </div>
          </li>
        </template>
      </transition-group>
    </ul>
  </div>
</template>

<script>
// // import { defineComponent } from '@nuxtjs/composition-api'
import instagramPng from 'assets/img/icon/instagramIcon.png'
// import FacebookIcon from '~/components/shared/icon/FacebookIcon.vue'
// import LinkedinIcon from '~/components/shared/icon/LinkedinIcon.vue'
// import TwitterIcon from '~/components/shared/icon/TwitterIcon.vue'
// import InstagramIcon from '~/components/shared/icon/InstagramIcon.vue'
// import MicrosoftIcon from '~/components/shared/icon/MicrosoftIcon.vue'
import YoutubeCircleIcon from '~/assets/img/png/youtube_social_circle_white.png'
import GoogleSvgIcon from '~/assets/img/svg/Google__G__Logo.svg'
import WebsiteIcon from '~/assets/img/svg/website.svg'
// import PinterestIcon from '~/components/shared/icon/PinterestIcon.vue'
// import RedditIcon from '~/components/shared/icon/RedditIcon.vue'
// import TikTokIcon from '~/components/shared/icon/tiktok/TiktokIcon.vue'
import { useHideText } from '~/composables/feeds/useHideText'

export default defineComponent({
  name: 'FeedsType',
  components: {
    // FacebookIcon,
    // LinkedinIcon,
    // // TwitterIcon,
    // MicrosoftIcon,
    // PinterestIcon,
    // RedditIcon,
    // TikTokIcon,
  },
  props: {
    visible: {
      type: Boolean,
      default: true,
    },
    type: {
      type: String,
      default: '',
    },
    selectedType: {
      type: Boolean,
      default: true,
    },
    feedsList: {
      type: Array,
      default: () => [],
    },
    showFeeds: {
      type: Boolean,
      default: true,
    },
    source: {
      type: String,
      default: '',
    },
  },
  setup() {
    const { hideText } = useHideText()
    const nuxtApp = useNuxtApp()
    return {
      hideText,
      nuxtApp
    }
  },
  data() {
    return {
      instagram: instagramPng,
      YoutubeIcon: YoutubeCircleIcon,
      GoogleIcon: GoogleSvgIcon,
      WebsiteIcon,
    }
  },
  methods: {
    setNameOrUsername(feed) {
      if (feed.provider === 'Twitter') {
        const addAtSign = !feed.name.length > 0
        const name = feed.name.length > 0 ? feed.name : feed.username
        return addAtSign ? '@' + name : name
      } else {
        return feed.name.length > 0 ? feed.name : feed.username
      }
    },
    expandType() {
      this.$emit('expand-type')
    },
    selected(id) {
      // this.$emit('selected-feed', id)
      this.$emit('selected-item', id)
      this.$store.dispatch('setting/getSingleSavedPrice', id)
      this.nuxtApp.$bus.$emit('watch-expand-exit', false)
    },
  },
})
</script>

<style lang="scss" scoped>
@import url('https://fonts.googleapis.com/css2?family=Neuton');

name-font {
  font-family: 'Neuton', serif;
}
.bg-white-opasity-50 {
  background-color: #ffffff80;
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s;
}
.fade-enter,
.fade-leave-to {
  height: 100%;
  opacity: 100;
}
.tooltip {
  @apply invisible;
}
.has-tooltip:hover .tooltip {
  @apply visible;
  left: -30px;
  padding: 3px 10px;
}
.text-xxs {
  font-size: 11px;
  line-height: 16px;
}
.min-w-7-2 {
  min-width: 1.875rem !important;
}
.min-h-7-2 {
  min-height: 1.875rem !important;
}

.menu-wrapper {
  .background {
    opacity: 0;
    position: absolute;
    z-index: 10;
    transition: margin-top 0.5s ease-in-out, opacity 0.3s ease 0.5s,
      background 0.3s ease 0.5s;
    height: 2.75rem;
    @apply w-full;

    .corner-top {
      position: absolute;
      top: -20px;
      right: 0px;
      display: inline-block;
      transition: color 0.3s ease 0.5s;
    }

    .corner-bottom {
      position: absolute;
      bottom: -20px;
      right: 0px;
      transform: rotate(270deg);
      display: inline-block;
      transition: color 0.3s ease 0.5s;
    }

    &.active {
      opacity: 1;
      .corner-top,
      .corner-bottom {
        // opacity: 1;
      }
    }
  }
}

.balance__menu {
  z-index: 80;
  transition: border-radius 0.5s ease-in-out,
    border-top-left-radius 0.5s ease-in-out,
    border-top-right-radius 0.5s ease-in-out;
  max-width: 16rem;
  min-width: 16rem;
  @apply md:w-full w-full;
  .btn-wrapper {
    background: #4c5764;
    @apply w-full px-4 flex justify-between items-center shadow-sm;

    .dropdown-btn {
      direction: ltr;
      line-height: 2.15rem !important;
      @apply text-white w-full h-10 text-lg focus:outline-none;

      * {
        // pointer-events: none;
      }
    }
  }
  .dropdown-btn {
    direction: ltr;
    @apply text-white w-full h-10 px-3.5 text-lg flex justify-between items-center shadow-sm focus:outline-none;

    * {
      // pointer-events: none;
    }
  }
}

/* these classname shuld be provided through the `source` props. */
.archive {
  background: #8db230 !important;
}

.search {
  background: #7d80bd !important;
}

.pricing {
  background: #a22a2a !important;
}

.alert {
  background: #e05252 !important;
}

.settings {
  background: #e0ad1f !important;
}

.help {
  background: #e05252 !important;
}

.feeds-button {
  color: #e4801d;
}
.username__archive,
.feeds-button__archive {
  color: #8db230 !important;
}
.username__search,
.feeds-button__search {
  color: #7d80bd !important;
}
.username__pricing,
.feeds-button__pricing {
  color: #a22a2a !important;
}
.username__alert,
.feeds-button__alert {
  color: #e05252 !important;
}
.username__settings,
.feeds-button__settings {
  color: #e0ad1f !important;
}

.dropdown {
  max-width: 16rem;
  overflow: hidden;
  @apply w-full;
  z-index: 100;
  overflow-x: hidden;
  height: 0;
  max-height: calc(100% - 132px);
  transform-origin: top;
  transition: transform 0.3s linear, height 0.5s linear;
  .dropdown-btn,
  .menu-title,
  .list-title,
  .equity {
    //direction: ltr;
    // background: #b76d1d;
    background: #e4801d;
    line-height: 2.15rem !important;
    @apply text-white w-full h-10 px-3 text-lg flex justify-between items-center shadow-sm focus:outline-none;

    * {
      // pointer-events: none;
    }

    &__archive {
      background: #5f822d !important;
    }

    &__search {
      background: #5a57a2 !important;
    }

    &__pricing {
      background: #a22a2a !important;
    }

    &__alert {
      background: #9d1616 !important;
    }

    &__settings {
      background: #695316 !important;
    }

    &__help {
      background: #e05252 !important;
    }
  }

  .equity {
    @apply py-2;
    padding-left: 13px;
    padding-right: 13px;
  }

  // .scroll-hidden {
  //   overflow: hidden;
  // }

  .scroll {
    scroll-behavior: smooth;
    overflow-y: auto;
    overflow-x: hidden;
    -ms-overflow-style: none; /* IE 11 */
    scrollbar-width: thin;
    scrollbar-color: #b76d1d #ececec; /* Firefox 64 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    /* Track */
    &::-webkit-scrollbar-track {
      border-radius: 3px;
      background: #ececec;
    }

    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #b76d1d;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #b76d1d;
    }
  }
  .scroll__archive {
    scroll-behavior: smooth;
    scrollbar-color: #5f822d #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #5f822d;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #5f822d;
    }
  }
  .scroll__search {
    scroll-behavior: smooth;
    scrollbar-color: #5a57a2 #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #5a57a2;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #5a57a2;
    }
  }
  .scroll__pricing {
    scroll-behavior: smooth;
    scrollbar-color: #a22a2a #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #a22a2a;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #a22a2a;
    }
  }
  .scroll__alert {
    scroll-behavior: smooth;
    scrollbar-color: #9d1616 #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #9d1616;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #9d1616;
    }
  }
  .scroll__settings {
    scroll-behavior: smooth;
    scrollbar-color: #695316 #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #695316;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #695316;
    }
  }

  .list-wrapper {
    .group-archive {
      background: #b76d1d;
      font-style: normal;
      font-variant: normal;
      font-weight: normal;
      border-bottom: 0px;
      letter-spacing: 0;
      font-size: 1.125rem;
      color: #f2f2f2;

      &__name {
        height: 42px;
      }
      &__archive {
        background: #5f822d !important;
      }
      &__search {
        background: #5a57a2 !important;
      }
      &__pricing {
        background: #a22a2a !important;
      }
      &__alert {
        background: #9d1616 !important;
      }
      &__settings {
        background: #695316 !important;
      }
      &__help {
        background: #e05252 !important;
      }

      .list {
        .list-item {
          height: 42px;
          background: #e4801d;
          border-color: #b76d1d;
        }
        .__archive {
          background: #8db230;
          border-color: #5f822d !important;
        }
        .__search {
          background: #7d80bd;
          border-color: #5a57a2 !important;
        }
        .__pricing {
          background: #ca4646;
          border-color: #a22a2a !important;
        }
        .__alert {
          background: #e05252;
          border-color: #9d1616 !important;
        }
        .__settings {
          background: #e0ad1f;
          border-color: #695316 !important;
        }
        .__help {
          background: #8db230;
          border-color: #5f822d !important;
        }

        .__active_home {
          background: #cc6f15;
        }
        .__active_archive {
          background: #7b9a29;
          border-color: #5f822d !important;
        }
        .__active_search {
          background: #696db4;
          border-color: #5a57a2 !important;
        }
        .__active_pricing {
          background: #ab2e2e;
          border-color: #a22a2a !important;
        }
        .__active_alert {
          background: #9d1616;
          border-color: #9d1616 !important;
        }
        .__active_settings {
          background: #b18714;
          border-color: #695316 !important;
        }
      }
    }

    /*.list {
        .list-item {
          direction: ltr;
          height: 45px;
          .background {
            opacity: 0;
            // transition: opacity 1s;
            // outline: none;
            // overflow: hidden;

            transform: translateY(-100%);
            transition: transform 0.5s ease-in-out,
              opacity 0.3s ease-in-out 0.5s;
          }
          &.active {
            // z-index: 1000;
            // @apply relative;

            .background {
              @apply h-full w-full;
              margin-left: -10px;
              position: absolute;
              left: 10px;
              opacity: 1;
              transform: translateY(0);
              z-index: 10;
              width: 100%;
              &__circle {
                @apply h-10 w-10 rounded-full inline-block;
              }

              .corner-top {
                position: absolute;
                top: -19px;
                right: 0px;
                display: inline-block;
              }

              .corner-bottom {
                position: absolute;
                bottom: -19px;
                right: 0px;
                transform: rotate(270deg);
                display: inline-block;
              }
            }
          }
          &-content {
            z-index: 20;
            transition: all 0.5s ease-in-out;
            @apply relative w-full;
          }
        }
      }*/
  }

  .list-item {
    // direction: ltr;
    text-align: left;
  }

  &.expand {
    height: var(--height);
    // height: calc(100vh - 172px);
  }
  &.expand1 {
    height: auto;
  }
}

.dropdown-icon {
  pointer-events: none;

  &.rotate {
    transform: rotate(180deg) !important;
  }
}
.line-height {
  line-height: 16px;
  display: flex;
}
.list-inner-width {
  width: calc(100% - 40px);
}
.username-width {
  width: calc(100% - 90px);
}
.username-clamp {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.mail-icon-wrapper {
  @apply flex justify-center items-center absolute w-3.5 h-3.5 rounded-full bg-white;
  bottom: -2px;
  right: -1px;
}
.social-icon {
  @apply absolute w-3.5 h-3.5 rounded-full;
  bottom: -2px;
  right: -1px;
}
.twitter-icon {
  background-size: cover;
  animation: twitter 6s ease-in-out;
  animation-iteration-count: infinite;
}
@keyframes twitter {
  0% {
    background-image: url('~/assets/img/icon/TwitterIcon/twitter.svg');
  }
  50% {
    background-image: url('~/assets/img/icon/TwitterIcon/X_logo.png');
  }
  100% {
    background-image: url('~/assets/img/icon/TwitterIcon/twitter.svg');
  }
}
.letter-spacing-1px {
  letter-spacing: 1px;
}
@media (max-width: 767px) {
  .dropdown {
    // transition: height 1s linear;
    max-width: 100%;
    max-height: var(--maxHeight);
  }
  .balance__menu {
    max-width: 100% !important;
  }
  .mobile-inner-width {
    width: calc(100% - 30px);
  }
  .mobile-username-width {
    width: calc(100% - 100px);
  }
  .mobile-clamp {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .expand {
    max-height: var(--maxHeight);
  }
}
/* for page */
.fadeIn-enter-active,
.fadeIn-leave-active {
  transition: opacity 0.5s;
}
.fadeIn-enter,
.fadeIn-leave-to {
  opacity: 0;
}
</style>
