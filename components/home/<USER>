<template>
  <div
    ref="searchInputRef"
    class="rounded-full lg:w-[35.33%] flex flex-row items-center relative px-2"
    :class="[
      expandCollapse && isCustomSizeTwo ? 'bg-orange-dark' : 'bg-white',
      bgColor,
    ]"
  >
    <div
      class="w-10 min-w-[40px] h-10 flex justify-center items-center search-container"
    >
      <ClientOnly>
        <fa
          class="search-icon"
          :class="[
            expandCollapse && isCustomSizeTwo
              ? 'text-white'
              : 'text-yellow-600',
            textColor,
          ]"
          :icon="['fas', 'search']"
        />
      </ClientOnly>
    </div>
    <input
      v-model="searchText"
      :placeholder="currentComp === 'Web' ? 'Search a URL' : placeholder"
      class="transition-all duration-500 flex-auto text-lg py-1.5 text-orange-dark placeholder-orange-light border-none"
      :class="[
        expandCollapse && isCustomSizeTwo
          ? 'px-0 bg-orange-dark rounded-full'
          : 'pl-2 pr-4 bg-white rounded-r-full',
        bgColor,
        textColor,
      ]"
      @keyup.enter="submit($event)"
      @input="(handleShowSearchSuggestion(), searchOnRealTime())"
    />
    <div
      v-if="showSuggestion && searchText"
      class="size-5 min-w-5 flex justify-center items-center cursor-pointer mr-2"
      @click.stop="searchText = ''"
    >
      <fa :icon="['fas', 'times']" class="text-xl text-[#525252]" />
    </div>
    <div
      v-if="
        showFilter &&
        (!createNewFilterModal.isOpen || createNewFilterModal.for === 'menu')
      "
      class="w-10 min-w-[40px] h-10 flex justify-center items-center cursor-pointer"
      @click.stop="handleShowFilterModal"
    >
      <SharedIconHubEqualizer class="text-[#525252]" />
    </div>
    <div
      v-if="showSuggestion && showSearchSuggestion && searchText"
      class="absolute top-0 w-[606px] h-auto bg-white rounded-[18px] shadow-[0px_0px_6px_#0000000D,0px_2px_18px_#0000001A] z-10 pb-1"
      :class="[isLarge ? 'right-0' : 'left-0']"
    >
      <div class="relative px-6 py-0.5">
        <ClientOnly>
          <fa
            class="absolute left-6 top-1/2 -translate-y-1/2"
            :icon="['fas', 'search']"
          />
        </ClientOnly>
        <input
          ref="searchInputInSuggestionRef"
          type="text"
          v-model="searchText"
          placeholder="Search mail"
          class="outline-none border-none text-base text-[#333333] w-full pl-9"
        />
      </div>
      <ul class="pt-1 text-[#525252]">
        <li
          v-for="suggestion in searchSuggestions"
          :key="suggestion.id"
          class="flex items-center py-[7px] space-x-4 hover:bg-[#F1F2F6] px-6 cursor-pointer"
          @click="handleClickSuggestion(suggestion.text)"
        >
          <SharedIconHubClock class="text-[#525252]" />
          <span class="text-base leading-[21px]">{{ suggestion.text }}</span>
        </li>
      </ul>
    </div>
    <SourceHubEmailsSettingsCreateNewFilterModal
      v-if="createNewFilterModal.isOpen"
      :wrapperClass="`${isLarge ? 'right-0' : 'left-0'} absolute top-full min-w-[668px] shadow-[0px_0px_6px_#2228313D,0px_2px_18px_#2228313D]`"
    />
  </div>
</template>

<script setup lang="ts">
import { useNuxtApp } from '#app'
import { useBreakpoints } from '@vueuse/core'
import { useStore } from 'vuex'

const props = defineProps({
  expandCollapse: {
    type: Boolean,
    default: false,
  },
  isCustomSizeTwo: {
    type: Boolean,
    default: false,
  },
  showSuggestion: {
    type: Boolean,
    default: false,
  },
  showFilter: {
    type: Boolean,
    default: false,
  },
  bgColor: {
    type: String,
    default: '',
  },
  textColor: {
    type: String,
    default: '',
  },
})

const nuxtApp = useNuxtApp()
const store = useStore()

const searchText = ref('')
const placeholder = ref('Search')
const emit = defineEmits<{
  (event: 'clickSearch', value: string): void
  (event: 'hide-menubar', value: boolean): void
  (event: 'real-time-search', value: string): void
}>()
// computed
const currentComp = computed(() => store.getters['home/currentComp'])
const webSearch = computed(() => store.state.home.webSearch)
const webSearching = computed(() => store.state.home.webSearching)
const currentSocialComponent = computed(
  () => store.state.home.currentSocialComponent,
)
const webSearchClear = computed(() => store.state.home.webSearchClear)
const createNewFilterModal = computed(
  () => store.state.emails.createNewFilterModal,
)
const breakpoints = useBreakpoints({
  large: 1536,
})
const isLarge = breakpoints.greaterOrEqual('large')

watch(
  () => webSearchClear.value,
  (data) => {
    if (!data) {
      searchText.value = ''
    }
  },
)

const submit = (event: Event) => {
  event = event || window.event
  const inputValue = event.target as HTMLInputElement
  const charCode = inputValue.value
    .charAt(event.target.selectionStart - 1)
    .charCodeAt()

  if (
    (!event.isTrusted && charCode !== 32) ||
    charCode === 13 ||
    event.keyCode === 13
  ) {
    if (currentComp.value === 'Web' && !webSearching.value) {
      if (!searchText.value) {
        nuxtApp.$toast('clear')
        nuxtApp.$toast('error', {
          message: 'Enter text below',
          className: 'toasted-bg-alert',
        })
      } else {
        store.commit('home/SET_WEB_SEARCH', {
          accountId: currentSocialComponent.value.id,
          search: searchText.value,
        })
        setTimeout(() => {
          if (webSearch.value.archiveDate) {
            emit('hide-menubar')
          }
        })
      }
    } else {
      emit('clickSearch', searchText.value)
    }
  }
}

const searchInputRef = ref<HTMLDivElement | null>(null)
const searchInputInSuggestionRef = ref<HTMLInputElement | null>(null)
const showSearchSuggestion = ref(false)

const handleShowSearchSuggestion = () => {
  if (!props.showSuggestion) return
  showSearchSuggestion.value = true
  closeFilterModal()
  nextTick(() => {
    searchInputInSuggestionRef.value?.focus()
  })
}

const handleClickSuggestion = (text: string) => {
  showSearchSuggestion.value = false
}

const handleClickOutside = (event: Event) => {
  if (
    searchInputRef.value &&
    !searchInputRef.value.contains(event.target as Node)
  ) {
    showSearchSuggestion.value = false
    closeFilterModal()
  }
}

const closeFilterModal = () => {
  store.commit('emails/SET_CREATE_NEW_FILTER_MODAL', {
    isOpen: false,
    type: '',
    for: '',
  })
}

const handleShowFilterModal = () => {
  store.commit('emails/SET_CREATE_NEW_FILTER_MODAL', {
    isOpen: true,
    type: 'create',
    for: 'search',
  })
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
const searchOnRealTime = () => {
  emit('real-time-search', searchText.value)
}
const searchSuggestions = [
  {
    id: '1',
    text: '<EMAIL>',
  },
  {
    id: '2',
    text: '<EMAIL>',
  },
  {
    id: '3',
    text: '<EMAIL>',
  },
  {
    id: '4',
    text: 'Amazon',
  },
  {
    id: '2',
    text: 'shopify',
  },
]
</script>
<style lang="scss" scoped>
input {
  @apply border-none;
  outline: none !important;
  &:focus {
    outline: none !important;
    box-shadow: none !important; // removes default glow
  }
}
input::placeholder {
  opacity: 0.5;
  font-size: 18px;
}
.search-bar {
  > input {
    background: #f1f2f6 !important;
    color: #707070 !important;
    outline: none;
    @apply border-none;
    outline: none !important;
    &:focus {
      outline: none !important;
      box-shadow: none !important; // removes default glow
    }

    &::placeholder {
      color: #707070 !important;
    }
  }
}
.search-bar > input::placeholder {
  color: #707070 !important;
}
.search-bar > .search-container > .search-icon {
  color: #333333 !important;
}
</style>
