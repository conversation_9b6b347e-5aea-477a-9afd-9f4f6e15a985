<template>
  <section v-if="tempSingleMessage.length > 0">
    <transition-group name="message">
      <template v-if="hideShow">
        <div
          v-for="(showEmail, emailIndex) in tempSingleMessage"
          :id="showEmail.id"
          :key="emailIndex + 'ShowEmail'"
          class="email-body"
          :class="
            showEmailContent
              ? 'opacity-100 transition-all duration-500 ease-in-out'
              : 'opacity-0 transition-all duration-500 ease-in-out'
          "
        >
          <!-- <transition-group name="fadeIn" mode="out-in"> -->
          <transition name="fadeIn" mode="out-in">
            <!-- store.commit('home/SHOW_CURRENT_CONTENT', showEmail.id) -->
            <div
              v-if="!showEmail.selected"
              key="email"
              class="flex justify-between 2xl:flex-nowrap 2xl:space-y-0 flex-wrap space-y-1 transition-all duration-300 2xl:space-x-4 text-ash-primary border-b border-yellow-primary cursor-pointer py-1 break-words"
              @click.stop="
                [
                  store.commit('home/SHOW_CURRENT_CONTENT', showEmail.id),
                  !showEmail.body ? getEmailBody(showEmail.id) : '',
                  resetHeight(),
                ]
              "
            >
              <!-- hideSignature(
                  `button_${showEmail.id}`,
                  showEmail.id,
                  showEmail.height
                ) -->
              <div
                class="name-wrapper flex space-x-2 justify-between items-center"
              >
                <div v-if="showEmail.fromEmail" class="mail-name text-lg">
                  {{ showEmail.fromEmail.name }}
                  <span class="text-gray-1700 line-clamp-1"
                    >({{ showEmail.fromEmail.address }})</span
                  >
                </div>
                <div
                  v-if="showEmail.hasAttachments"
                  class="attachment-icon-flag cursor-pointer self-start pt-1"
                >
                  <img
                    class="mx-auto h-5 w-5"
                    src="../../../../assets/img/svg/ic-attachment.svg"
                    alt="ic-attachment"
                  />
                </div>
              </div>
              <div v-if="showEmail.sentAt" class="date">
                <DateTime
                  :datetime="showEmail.sentAt"
                  format="MMMM, dd yyyy"
                  class="whitespace-nowrap"
                />
              </div>
            </div>
            <div
              v-else-if="showEmail.selected"
              :id="`${showEmail.id}_content`"
              key="content"
              class="overflow-hidden flex flex-col space-y-2 transition-all duration-300 text-ash-primary border-b border-yellow-primary pb-3 pt-2 text-lg content"
            >
              <div class="w-full flex flex-col space-y-2">
                <div
                  class="subject-wrapper flex xl:flex-nowrap justify-between flex-wrap cursor-pointer xl:space-x-2 space-x-0 md:items-start items-center"
                  @click.stop="
                    store.commit('home/SHOW_CURRENT_CONTENT', showEmail.id)
                  "
                >
                  <div class="subject">
                    <p v-if="showEmail.subject">
                      <span class="font-bold md:inline block">Subject:</span>
                      <span class="md:inline hidden">
                        {{ showEmail.subject }}
                      </span>
                    </p>
                  </div>
                  <div
                    v-if="showEmail.sentAt"
                    class="time text-base text-gray-1700"
                  >
                    <DateTime
                      :datetime="showEmail.sentAt"
                      format="MMMM, dd yyyy"
                      class="whitespace-nowrap"
                    />
                  </div>
                </div>
                <div class="subject md:hidden">
                  <p v-if="showEmail.subject">
                    <span class="block">{{ showEmail.subject }}</span>
                  </p>
                </div>
                <div class="from">
                  <p>
                    <span class="font-bold">From:</span>
                    {{ showEmail.fromEmail.name }} ({{
                      showEmail.fromEmail.address
                    }})
                  </p>
                </div>
                <div class="flex justify-between">
                  <div class="flex space-x-1">
                    <span class="font-bold">To:</span>
                    <div>
                      <div
                        v-for="(toEmail, toEmailIndex) in showEmail.toEmails"
                        :key="toEmailIndex"
                        class="to"
                      >
                        <span>
                          {{
                            toEmail.emailAddress.address === allEmails.username
                              ? allEmails.name
                              : toEmail.emailAddress.name
                          }}
                          ({{ toEmail.emailAddress.address }})
                        </span>
                        <span
                          v-if="toEmailIndex !== showEmail.toEmails.length - 1"
                          >,</span
                        >
                      </div>
                    </div>
                  </div>
                  <button
                    v-if="!showEmail.ccRecipients"
                    id="download_icon"
                    class="w-5 h-5"
                    :disabled="downloadProgress"
                    @click="
                      downloadPDF(
                        showEmail.subject,
                        showEmail.body,
                        `${showEmail.id}_content`,
                      )
                    "
                  >
                    <ClientOnly>
                      <fa
                        class="mr-0.875 text-green-1100"
                        :icon="['fas', 'download']"
                      />
                    </ClientOnly>
                  </button>
                </div>
                <template v-if="showEmail.ccRecipients">
                  <div class="flex justify-between">
                    <div class="flex space-x-1">
                      <span class="font-bold">Cc:</span>
                      <div>
                        <div
                          v-for="(
                            ccRecipient, toCcIndex
                          ) in showEmail.ccRecipients"
                          :key="toCcIndex"
                          class="cc"
                        >
                          <span>
                            {{
                              ccRecipient.emailAddress.address ===
                              allEmails.username
                                ? allEmails.name
                                : ccRecipient.emailAddress.name
                            }}
                            ({{ ccRecipient.emailAddress.address }})
                          </span>
                          <span
                            v-if="
                              toCcIndex !== showEmail.ccRecipients.length - 1
                            "
                            >,</span
                          >
                        </div>
                      </div>
                    </div>
                    <button
                      v-if="!showEmail.bccRecipients"
                      id="download_icon"
                      class="w-5 h-5"
                      :disabled="downloadProgress"
                      @click="
                        downloadPDF(
                          showEmail.subject,
                          showEmail.body,
                          `${showEmail.id}_content`,
                        )
                      "
                    >
                      <ClientOnly>
                        <fa
                          class="mr-0.875 text-green-1100"
                          :icon="['fas', 'download']"
                        />
                      </ClientOnly>
                    </button>
                  </div>
                </template>
                <template v-if="showEmail.bccRecipients">
                  <div class="flex justify-between">
                    <div class="flex space-x-1">
                      <span class="font-bold">Bcc:</span>
                      <div>
                        <div
                          v-for="(
                            bccRecipient, toBccIndex
                          ) in showEmail.bccRecipients"
                          :key="toBccIndex"
                          class="to"
                        >
                          <span>
                            {{
                              bccRecipient.emailAddress.address ===
                              allEmails.username
                                ? allEmails.name
                                : bccRecipient.emailAddress.name
                            }}
                            ({{ bccRecipient.emailAddress.address }})
                          </span>
                          <span
                            v-if="
                              toBccIndex !== showEmail.bccRecipients.length - 1
                            "
                            >,</span
                          >
                        </div>
                      </div>
                    </div>
                    <button
                      id="download_icon"
                      class="w-5 h-5"
                      :disabled="downloadProgress"
                      @click="
                        downloadPDF(
                          showEmail.subject,
                          showEmail.body,
                          `${showEmail.id}_content`,
                        )
                      "
                    >
                      <ClientOnly>
                        <fa
                          class="mr-0.875 text-green-1100"
                          :icon="['fas', 'download']"
                        />
                      </ClientOnly>
                    </button>
                  </div>
                </template>
                <div class="attachment-signature space-y-1">
                  <template
                    v-if="
                      showEmail.attachments && showEmail.attachments.length > 0
                    "
                  >
                    <div
                      v-for="(
                        attachment, attachmentIndex
                      ) in showEmail.attachments"
                      :key="attachmentIndex"
                      class=""
                      @click.stop="
                        store.commit('home/SET_EMAIL_DYNAMIC_COMP', {
                          comp: 'EmailDocuments',
                          attachments: showEmail.attachments,
                          attachmentIndex,
                          currentIndex: attachmentIndex,
                          messageId: showEmail.id,
                          attachment,
                        })
                      "
                    >
                      <div
                        v-if="!attachment.isInline"
                        class="feedback-attachment cursor-pointer text-yellow-primary items-start text-lg font-bold flex space-x-1"
                      >
                        <div class="attachment cursor-pointer pt-1">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 11"
                            class="mx-auto h-5 w-5"
                          >
                            <g id="Layer_2" data-name="Layer 2">
                              <g id="Layer_1-2" data-name="Layer 1">
                                <path
                                  id="ic_attachment_24px"
                                  data-name="ic attachment 24px"
                                  class="ic_attachment"
                                  d="M0,5.5A5.5,5.5,0,0,1,5.5,0H16a4,4,0,0,1,0,8H7.5a2.5,2.5,0,0,1,0-5H15V5H7.41c-.55,0-.55,1,0,1H16a2,2,0,0,0,0-4H5.5a3.5,3.5,0,0,0,0,7H15v2H5.5A5.5,5.5,0,0,1,0,5.5Z"
                                />
                              </g>
                            </g>
                          </svg>
                        </div>
                        <div class="break-all">{{ attachment.name }}</div>
                        <!-- <a :href="attachment.url" target="_blank" download @click.stop=""> -->
                        <button
                          :disabled="downloadProcessing"
                          @click.stop="
                            download(attachment.url, attachment.name)
                          "
                        >
                          <ClientOnly>
                            <fa
                              class="ml-0.875 cursor-pointer text-green-1100 mt-[6px]"
                              :icon="['fas', 'download']"
                            />
                          </ClientOnly>
                        </button>
                        <!-- <span class="download_tooltip">Download</span> -->
                        <!-- </a> -->
                      </div>
                    </div>
                  </template>
                </div>
              </div>
              {{
                hideSignature(
                  `button_${showEmail.id}`,
                  showEmail.id,
                  showEmail.height,
                  showEmail.attachments,
                )
              }}
              <iframe
                v-if="showEmail.body"
                :id="`iframe_${showEmail.id}`"
                class="text-message break-words overflow-hidden email_body_height transition-all duration-500 ease-in-out"
                :srcdoc="showEmail.body"
                :style="{ '--height': `${showEmail.height}px` }"
              >
              </iframe>
              <fa
                v-if="showEmail.height === 0 && showEmail.body !== ''"
                class="text-orange-dark font-bold animate-spin text-4xl"
                :icon="['fas', 'spinner']"
              />
              <button
                v-if="showEmail.showSignatureButton && showEmail.height !== 0"
                :id="`button_${showEmail.id}`"
                class="w-40 z-10 h-9 rounded-full outline-none font-bold text-sm my-1"
                :class="
                  showEmail.setSignatureColor
                    ? 'bg-yellow-primary text-white'
                    : 'border-yellow-primary text-yellow-primary border-2'
                "
                @click.stop="
                  showSignature(
                    `button_${showEmail.id}`,
                    showEmail.id,
                    showEmail.setSignatureColor,
                    showEmail.signatureHeight,
                  )
                "
              >
                Signature
              </button>
            </div>
          </transition>
        </div>
      </template>
    </transition-group>
  </section>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const { fetch } = useFetched()
const { $toast } = useNuxtApp()

const tempSingleMessage = computed(() => store.state.home.tempSingleMessage)
const emailDynamicComp = computed(() => store.state.home.emailDynamicComp)

const hideShow = ref<boolean>(true)
const tableHeight = ref<number>(0)
const height = ref<string>('0px')
const showEmailContent = ref<boolean>(false)
const downloadProcessing = ref<boolean>(false)
const downloadProgress = ref<boolean>(false)

// Access getters
const allEmails = computed(() => store.getters['home/allEmails'])
const singleMessageShow = computed(
  () => store.getters['home/singleMessageShow'],
)

// Access state
const selectedMessage = computed(() => store.state.home.selectedMessage)
watch(
  () => selectedMessage.value,
  (data: any) => {
    if (data) {
      setSingleMessage()
      hideShow.value = false
      setTimeout(() => {
        hideShow.value = true
      }, 200)
    } else {
      setSingleMessage()
      hideShow.value = false
      setTimeout(() => {
        hideShow.value = true
      }, 200)
    }
  },
)
watch(
  () => emailDynamicComp.value,
  (data: string) => {
    if (data === 'EmailContent') {
      showEmailContent.value = false
      setTimeout(() => {
        showEmailContent.value = true
      }, 350)
    }
  },
)
watch(
  () => tempSingleMessage.value,
  (data: any) => {
    if (data) {
      data.forEach((item: any) => {
        if (item.selected && !item.body) {
          getEmailBody(item.id)
        }
      })
    }
  },
)

onMounted(() => {
  setTimeout(() => {
    setSingleMessage()
  }, 500)
  if (emailDynamicComp.value === 'EmailContent') {
    showEmailContent.value = false
    setTimeout(() => {
      showEmailContent.value = true
    }, 350)
  }
})

const getCurrentSocialArticle = (payload: any) =>
  store.dispatch('home/getCurrentSocialArticle', payload)

const downloadPDF = async (fileName: string, htmlBody: string, id: string) => {
  store.commit('home/CLEAR_SET_TEMP_EMAIL_BODY', '')
  downloadProgress.value = true
  store.commit('archive/SET_DOWNLOAD_LOADER', true)
  const htmlContent = document.getElementById(`${id}`)?.innerHTML || ''
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = htmlContent
  const iframe = tempDiv.querySelector('iframe')

  const downloadIcon = tempDiv.querySelectorAll('button')
  if (iframe) {
    const newElement = document.createElement('div')
    iframe.replaceWith(newElement)
    newElement.innerHTML = htmlBody
  }
  if (downloadIcon && downloadIcon.length > 0) {
    downloadIcon.forEach((item) => {
      if (
        item.id === 'download_icon' ||
        `${item.id}_content` === `button_${id}`
      ) {
        item.replaceWith('')
      }
    })
  }
  const updatedHtmlString = tempDiv.innerHTML
  store.commit('home/SET_TEMP_EMAIL_BODY', {
    body: updatedHtmlString,
    title: fileName,
  })
  downloadProgress.value = false
  store.dispatch('home/startDownloadPDF')
}
const download = async (url: string, name: string) => {
  downloadProcessing.value = true
  $toast('clear')
  $toast('success', {
    message: 'Download is processing',
    className: 'toasted-bg-archive',
  })
  try {
    let fileName = name
    const blobResponse = await fetch(url, {
      responseType: 'blob',
      // onResponse({ request, response, options }) {
      //   console.log(response.headers, 'download')
      //   const contentDisposition = response.headers?.get(
      //     'content-disposition'
      //   )
      //   console.log(contentDisposition, 'download')
      //   if (contentDisposition) {
      //     const fileNameMatch = contentDisposition.match(
      //       /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
      //     )
      //     console.log(fileNameMatch, 'download')
      //     fileName = fileNameMatch[1]
      //   }
      // },
    })
    if (blobResponse) {
      const url = window.URL.createObjectURL(blobResponse)
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      link.remove()
      $toast('clear')
      $toast('success', {
        message: 'Download is Completed',
        className: 'toasted-bg-archive',
      })
      downloadProcessing.value = false
    } else {
      $toast('error', {
        message: 'No data to be downloaded',
        className: 'toasted-bg-alert',
      })
      downloadProcessing.value = false
    }
  } catch (error) {
    $toast('error', {
      message: error,
      className: 'toasted-bg-alert',
    })
    downloadProcessing.value = false
  }
}
const resetHeight = () => {
  tableHeight.value = 0
}
const hideSignature = (
  id: number,
  emailId: number,
  emailHeight: number,
  attachments: any,
) => {
  setTimeout(() => {
    const innerBody = document.getElementById(`iframe_${emailId}`)
    if (
      innerBody &&
      innerBody.contentWindow.document &&
      innerBody.contentWindow.document.body
    ) {
      // setTimeout(() => {
      innerBody.contentWindow.document.body.style.overflowY = 'hidden'
      // Set the CSS properties using the style property
      innerBody.contentWindow.document.body.style.overflowX = 'auto'
      // element.style.scrollBehavior = 'smooth'; // If needed, uncomment this line
      innerBody.contentWindow.document.body.style.msOverflowStyle =
        'none' /* IE 11 */
      innerBody.contentWindow.document.body.style.scrollbarWidth = 'thin'
      innerBody.contentWindow.document.body.style.scrollbarColor =
        '#ff8308 #ececec' /* Firefox 64 */
      // eslint-disable-next-line
      const body = innerBody.contentWindow.document.body
      // Create a nested style for WebKit browsers
      const webkitScrollbar = document.createElement('style')
      webkitScrollbar.innerHTML = `
          body::-webkit-scrollbar {
                width: 6px;
                height: 10px;
              }

              body::-webkit-scrollbar-track {
                border-radius: 3px;
                background: #ececec;
              }

              body::-webkit-scrollbar-thumb {
                background: #ff8308;
                border-radius: 3px;
              }

              body::-webkit-scrollbar-thumb:hover {
                background: #ff8308;
              }
            `
      // Append the WebKit scrollbar style to the document head
      innerBody.contentWindow.document.head.appendChild(webkitScrollbar)
      // }, 1000)
      const table =
        innerBody.contentWindow.document.getElementsByTagName('table')
      if (table && table.length > 0) {
        for (let i = 0; i < table.length; i++) {
          table[i].style.width = '100%'
        }
      }
      const signature =
        innerBody.contentWindow.document.getElementById('Signature')
      // setTimeout(() => {
      if (signature) {
        if (signature.offsetHeight !== 0) {
          store.commit('home/SET_SHOW_SIGNATURE_BUTTON', {
            id: emailId,
            showSignatureButton: true,
            signatureHeight: signature.offsetHeight,
          })
        }
      } else {
        store.commit('home/SET_SHOW_SIGNATURE_BUTTON', {
          id: emailId,
          showSignatureButton: false,
        })
      }
      // }, 1000)
    }
    const img = innerBody
      ? innerBody.contentWindow.document.getElementsByTagName('img')
      : ''
    if (img && img.length > 0) {
      const entries = Object.keys(img)
      entries.forEach((key) => {
        if (attachments && attachments.length > 0) {
          attachments.forEach((attachment: any) => {
            if (
              img[key].src.includes(attachment.contentId) &&
              attachment.contentId
            ) {
              img[key].src = attachment.url
            }
          })
        }
      })
    }
    setTimeout(() => {
      if (innerBody && innerBody.contentWindow && emailHeight === 0) {
        innerBody.contentWindow.document.body.style.overflowY = 'hidden'
        // Set the CSS properties using the style property
        innerBody.contentWindow.document.body.style.overflowX = 'auto'
        const table =
          innerBody.contentWindow.document.getElementsByTagName('table')

        let tableHeight = 0
        if (table && table.length > 0) {
          for (let i = 0; i < table.length; i++) {
            table[i].style.width = '100%'
            tableHeight = table[0].offsetHeight
          }
        }

        if (tableHeight === 0) {
          height.value = `${
            innerBody.contentWindow.document.body.offsetHeight + 16
          }px`
        }
        const signature =
          innerBody.contentWindow.document.getElementById('Signature')

        if (signature) {
          if (signature.offsetHeight !== 0) {
            store.commit('home/SET_CURRENT_CONTENT_HEIGHT', {
              height:
                tableHeight !== 0 &&
                tableHeight >=
                  innerBody.contentWindow.document.body.offsetHeight
                  ? tableHeight
                  : innerBody.contentWindow.document.body.offsetHeight +
                    16 -
                    signature.offsetHeight,
              id: emailId,
            })
          }
          // signature.style.transition = "all 1s ease-in-out";
          signature.style.height = 0
          signature.style.opacity = 0
        } else {
          // this.showSignatureButton = false
          store.commit('home/SET_SHOW_SIGNATURE_BUTTON', {
            id: emailId,
            showSignatureButton: false,
          })
          store.commit('home/SET_CURRENT_CONTENT_HEIGHT', {
            height:
              tableHeight !== 0 &&
              tableHeight >= innerBody.contentWindow.document.body.offsetHeight
                ? tableHeight
                : innerBody.contentWindow.document.body.offsetHeight + 16,
            id: emailId,
          })
        }
      }
    }, 800)
  }, 300)
}
const getEmailBody = (emailId: number) => {
  getCurrentSocialArticle({
    accountId: allEmails.value.id,
    id: emailId,
  })
}
const showSignature = (
  id: number,
  emailId: number,
  setSignatureColor: boolean,
  signatureHeight: number,
) => {
  setTimeout(() => {
    const innerBody = document.getElementById(`iframe_${emailId}`)
    if (
      innerBody &&
      innerBody.contentWindow.document &&
      innerBody.contentWindow.document.body
    ) {
      innerBody.contentWindow.document.body.style.overflowY = 'hidden'
      // Set the CSS properties using the style property
      innerBody.contentWindow.document.body.style.overflowX = 'auto'
      // element.style.scrollBehavior = 'smooth'; // If needed, uncomment this line
      innerBody.contentWindow.document.body.style.msOverflowStyle =
        'none' /* IE 11 */
      innerBody.contentWindow.document.body.style.scrollbarWidth = 'thin'
      innerBody.contentWindow.document.body.style.scrollbarColor =
        '#ff8308 #ececec' /* Firefox 64 */
      // eslint-disable-next-line
      const body = innerBody.contentWindow.document.body
      // Create a nested style for WebKit browsers
      const webkitScrollbar = document.createElement('style')
      webkitScrollbar.innerHTML = `
          body::-webkit-scrollbar {
                width: 6px;
                height: 10px;
              }

              body::-webkit-scrollbar-track {
                border-radius: 3px;
                background: #ececec;
              }

              body::-webkit-scrollbar-thumb {
                background: #ff8308;
                border-radius: 3px;
              }

              body::-webkit-scrollbar-thumb:hover {
                background: #ff8308;
              }
            `
      // Append the WebKit scrollbar style to the document head
      innerBody.contentWindow.document.head.appendChild(webkitScrollbar)
      const table =
        innerBody.contentWindow.document.getElementsByTagName('table')
      let tableHeight = 0
      if (table && table.length > 0) {
        for (let i = 0; i < table.length; i++) {
          table[i].style.width = '100%'
          tableHeight = table[0].offsetHeight
        }
      }
      if (tableHeight === 0) {
        height.value = `${
          innerBody.contentWindow.document.body.offsetHeight + 16
        }px`
      }
      const signature =
        innerBody.contentWindow.document.getElementById('Signature')
      const button = document.getElementById(`${id}`)
      if (signature && button && !setSignatureColor) {
        // signature.style.transition = "all 1s ease-in-out";
        signature.style.height = signatureHeight + 'px'
        signature.style.opacity = 1
        store.commit('home/SET_CURRENT_CONTENT_SIGNATURE', {
          height:
            (tableHeight !== 0 &&
            tableHeight >= innerBody.contentWindow.document.body.offsetHeight
              ? tableHeight
              : innerBody.contentWindow.document.body.offsetHeight) + 16,
          id: emailId,
          setSignatureColor: true,
        })
      } else if (signature && button && setSignatureColor) {
        store.commit('home/SET_CURRENT_CONTENT_SIGNATURE', {
          height:
            (tableHeight !== 0 &&
            tableHeight >= innerBody.contentWindow.document.body.offsetHeight
              ? tableHeight
              : innerBody.contentWindow.document.body.offsetHeight) +
            16 -
            signature.offsetHeight,
          id: emailId,
          setSignatureColor: false,
        })
        // signature.style.transition = "all 1s ease-in-out";
        signature.style.height = 0
        signature.style.opacity = 0
      }
    }
  }, 150)
}
const setSingleMessage = () => {
  store.commit('home/SET_TEMP_ARRAY', singleMessageShow.value)
}
const emailType = (labels: string) => {
  if (labels.includes('INBOX')) {
    return 'Inbox'
  } else if (labels.includes('SENT')) {
    return 'Sent'
  } else if (labels.includes('TRASH')) {
    return 'TRASH'
  } else if (labels.includes('SPAM')) {
    return 'Spam'
  }
}
</script>

<style lang="scss" scoped>
.ic_attachment {
  fill: #e4801d;
}
.fadeIn-enter-active,
.fadeIn-leave-active {
  transition: all 0.4s;
}
.fadeIn-enter-from,
.fadeIn-leave-to {
  opacity: 0;
}
.fadeIn-enter-active {
  transition-delay: 0.2s;
}
.message-enter-active,
.message-leave-active {
  transition: all 0.4s;
}
.message-enter-from,
.message-leave-to {
  opacity: 0;
}
.message-enter-active {
  transition-delay: 0.4s;
}
[data-title]:after {
  color: #e4801d;
  right: 100%;
  z-index: 99999999;
}
.scroll {
  overflow-y: auto;
  overflow-x: hidden;
  // scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-color: #ff8308 #ececec; /* Firefox 64 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background: #ececec;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: #ff8308;
    border-radius: 3px;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #ff8308;
  }
}
.email_body_height {
  height: var(--height);
  transition: height 2s ease-in-out 0s;
}
</style>
