<template>
  <section class="email-feed-wrapper w-full h-full">
    <transition name="media">
      <div
        v-if="allEmails && !loadArticles"
        class="w-full h-full flex md:flex-row flex-col flex-nowrap"
      >
        <!-- Start Email Feed Header -->
        <!-- <template v-if="allEmails.items.length > 0"> -->
        <div
          class="transition-all duration-500 ease-in-out"
          :class="
            expandFullImage
              ? 'w-0 opacity-0 md:h-full h-0'
              : 'md:w-1/3 w-full md:h-full mobile-height opacity-100 flex flex-col'
          "
        >
          <div
            class="bg-orange-200 flex w-full h-9 min-h-9 px-2 space-x-6 text-md font-bold text-ash-primary items-center"
          >
            <div
              class="px-1 h-full flex items-center cursor-pointer"
              :class="
                activeOption === 'Messages'
                  ? 'font-bold border-b-[2px] border-[#434343]'
                  : ''
              "
              @click="handleClick"
            >
              <div class="heading">Messages</div>
            </div>
            <div
              class="px-1 h-full flex items-center cursor-pointer"
              :class="
                activeOption === 'Labels' || activeOption === 'singleLabel'
                  ? 'font-bold border-b-[2px] border-[#434343]'
                  : ''
              "
              @click="activeOption = 'Labels'"
            >
              <div
                class="heading"
                :class="
                  activeOption === 'Labels' || activeOption === 'singleLabel'
                    ? 'font-bold'
                    : ''
                "
              >
                Labels
              </div>
            </div>
          </div>
          <div
            v-if="activeOption === 'Labels'"
            class="flex-grow email-view-wrapper"
          >
            <HomeRealtimeFeedRssEmailSidebar
              class="flex w-full h-full pb-0 scroll"
              @select-single-item="($event) => (activeOption = $event)"
            />
          </div>
          <div class="flex flex-col w-full email-view-wrapper pb-0">
            <div
              v-if="activeOption === 'singleLabel'"
              class="flex justify-start items-center space-x-1 px-4 py-2"
            >
              <div
                class="flex justify-start items-center size-8 cursor-pointer"
                @click="activeOption = 'Labels'"
              >
                <ClientOnly>
                  <fa class="text-[#525252]" :icon="['fa', 'arrow-left']" />
                </ClientOnly>
              </div>
              <div class="flex justify-center items-center !space-x-3">
                <SharedIconHubEmailsSidebarLabelsProduct class="w-4 h-4" />
                <p class="text-[#434343] text-xl line-clamp-1">
                  {{ selectedEmailLabel.modifiedLabels || selectedEmailLabel.name }}
                </p>
              </div>
            </div>
            <!-- Start Email Messages -->
            <div
              v-if="allEmails.items.length > 0 && activeOption !== 'Labels'"
              class="email-messages w-full h-full md:border-r border-yellow-300 scroll bottom-left-radius"
            >
              <div
                v-for="(allEmail, index) in allEmails.items"
                :key="index"
                class="w-full pb-2 pt-1 message-body"
                :class="
                  allEmail.individualSelect
                    ? 'bg-orange-dark text-white'
                    : index % 2 === 0
                      ? 'bg-white'
                      : 'bg-gray-default'
                "
              >
                <div
                  class="singleMessageWrapper px-4 cursor-pointer relative"
                  @click="
                    ;[
                      store.commit('home/SET_SINGLE_MESSAGE_SHOW', {
                        index,
                        id: allEmail.id,
                        singleEmail: allEmail,
                      }),
                      store.commit('home/SET_EMAIL_DYNAMIC_COMP', {
                        comp: 'EmailContent',
                      }),
                    ]
                  "
                >
                  <div
                    class="platform text-lg font-bold flex lg:space-x-0 space-x-2 whitespace-normal break-words line-clamp-1 h-7 justify-between items-center flex-nowrap name"
                    :class="
                      allEmail.individualSelect
                        ? 'text-white'
                        : 'text-ash-primary'
                    "
                  >
                    <span class="line-clamp-1"
                      >{{ allEmail.fromEmail.name }}&nbsp;<span
                        class="font-normal"
                        :class="
                          allEmail.individualSelect
                            ? 'text-offwhite-100'
                            : 'text-gray-light'
                        "
                        >{{
                          allEmail.TotalMessage > 1 ? allEmail.TotalMessage : ''
                        }}</span
                      ></span
                    >
                    <span class="font-normal text-base whitespace-nowrap">{{
                      emailType(allEmail.messageType)
                    }}</span>
                  </div>
                  <div class="flex justify-between lg:space-x-0 space-x-2 h-5">
                    <div class="flex space-x-2">
                      <template
                        v-if="
                          allEmail.labelName && allEmail.labelName.length > 0
                        "
                      >
                        <template
                          v-for="(
                            labelName, labelNameIndex
                          ) in allEmail.labelName"
                        >
                          <div
                            v-if="
                              selectedEmailLabel &&
                              labelName !== selectedEmailLabel.name
                            "
                            :key="labelNameIndex"
                            class="flex justify-center items-center px-[4px] py-[4px] text-sm rounded-md whitespace-nowrap"
                            :class="
                              allEmail.individualSelect
                                ? 'bg-gray-default text-[#656565]'
                                : index % 2 === 0
                                  ? 'bg-gray-default text-[#656565]'
                                  : 'bg-white text-[#656565]'
                            "
                          >
                            <p>
                              {{
                                labelName.charAt(0).toUpperCase() +
                                labelName.slice(1).toLowerCase()
                              }}
                            </p>
                          </div>
                          <div
                            v-if="
                              !selectedEmailLabel &&
                              labelName !== emailType(allEmail.messageType)
                            "
                            :key="`${labelNameIndex}_${labelName}`"
                            class="flex justify-center items-center px-[4px] py-[4px] text-sm rounded-md whitespace-nowrap"
                            :class="
                              allEmail.individualSelect
                                ? 'bg-gray-default text-[#656565]'
                                : index % 2 === 0
                                  ? 'bg-gray-default text-[#656565]'
                                  : 'bg-white text-[#656565]'
                            "
                          >
                            <p>
                              {{
                                labelName.charAt(0).toUpperCase() +
                                labelName.slice(1).toLowerCase()
                              }}
                            </p>
                          </div>
                        </template>
                      </template>
                      <div
                        class="heading text-sm whitespace-normal break-words line-clamp-1"
                        :class="
                          allEmail.individualSelect
                            ? 'text-white'
                            : 'text-ash-primary'
                        "
                      >
                        {{ allEmail.subject }}
                      </div>
                    </div>
                    <div
                      v-if="allEmail.hasAttachments"
                      class="attachment cursor-pointer"
                      :class="
                        allEmail.individualSelect
                          ? 'text-white'
                          : 'text-gray-light'
                      "
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 11"
                        class="mx-auto h-5 w-5"
                      >
                        <g id="Layer_2" data-name="Layer 2">
                          <g id="Layer_1-2" data-name="Layer 1">
                            <path
                              id="ic_attachment_24px"
                              data-name="ic attachment 24px"
                              :class="
                                allEmail.individualSelect
                                  ? 'ic_attachment1'
                                  : 'ic_attachment'
                              "
                              d="M0,5.5A5.5,5.5,0,0,1,5.5,0H16a4,4,0,0,1,0,8H7.5a2.5,2.5,0,0,1,0-5H15V5H7.41c-.55,0-.55,1,0,1H16a2,2,0,0,0,0-4H5.5a3.5,3.5,0,0,0,0,7H15v2H5.5A5.5,5.5,0,0,1,0,5.5Z"
                            />
                          </g>
                        </g>
                      </svg>
                    </div>
                  </div>
                  <div
                    class="flex text-sm justify-between lg:space-x-2 space-x-2"
                  >
                    <div
                      class="short-message italic whitespace-normal break-words line-clamp-1"
                      :class="
                        allEmail.individualSelect
                          ? 'text-white'
                          : 'text-gray-light'
                      "
                    >
                      <span
                        v-if="allEmail.snippet"
                        v-html="allEmail.snippet"
                      ></span>
                    </div>
                    <div class="time text-sm text-gray-light whitespace-nowrap">
                      <date-time
                        class="text-xs"
                        :class="
                          allEmail.individualSelect
                            ? 'text-white'
                            : 'text-gray-light'
                        "
                        :datetime="allEmail.sentAt"
                        format="MM dd"
                        :show-time="false"
                        :friendly="false"
                      ></date-time>
                    </div>
                  </div>
                  <!-- <a class="more_link" :href="`#${allEmail.id}`"></a> -->
                </div>
              </div>
              <div
                v-if="allEmails.totalCount > 20"
                class="w-full flex justify-center py-4"
              >
                <button
                  class="text-lg h-10 w-34 rounded-full relative flex justify-around space-x-1 items-center px-4 font-medium whitespace-nowrap"
                  :class="
                    loadMoreArticles
                      ? 'bg-yellow-primary text-white'
                      : 'bg-yellow-primary text-white'
                  "
                  :disabled="disableLoadMoreProcess"
                  @click="loadMoreArticles ? loadMore() : showEndMessage()"
                >
                  <span>{{ loadMoreArticles ? 'Load More' : 'End' }}</span>
                  <ClientOnly>
                    <fa
                      v-if="disableLoadMoreProcess"
                      class="text-white font-bold animate-spin"
                      :icon="['fas', 'spinner']"
                    />
                  </ClientOnly>
                </button>
              </div>
            </div>
            <!-- End Email Messages -->
          </div>
        </div>

        <!-- End Email Feed Header -->
        <div
          class="md:hidden px-2 w-full h-px bg-orange-dark my-2"
          :class="expandFullImage ? 'hidden' : 'block'"
        ></div>
        <!-- Start Email View -->
        <div
          class="transition-all duration-500 ease-in-out"
          :class="
            expandFullImage
              ? 'w-full h-full'
              : 'md:w-2/3 w-full md:h-full mobile-height'
          "
        >
          <div
            class="bg-orange-200 flex w-full h-9 text-md font-bold text-ash-primary items-center"
          >
            <div class="flex justify-between items-center px-4 w-full">
              <div class="heading">Content</div>
              <div class="extend cursor-pointer flex items-center space-x-6">
                <button
                  id="download_icon"
                  class="w-5 h-5"
                  @click="
                    $store.commit('home/CLEAR_SET_TEMP_EMAIL_BODY', ''),
                      $store.dispatch('home/getAllEmailBody', allEmails.id)
                  "
                >
                  <ClientOnly>
                    <fa
                      class="mr-0.875 text-green-1100"
                      :icon="['fas', 'download']"
                    />
                  </ClientOnly>
                </button>
                <div
                  class="plus_button"
                  :data-title="allExpanded ? 'Collapse All' : 'Extend All'"
                  @click="store.commit('home/EXPAND_All')"
                >
                  <svg
                    v-if="!allExpanded"
                    class="my-2 mx-auto h-5 w-5"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                  >
                    <g id="Layer_2" data-name="Layer 2">
                      <g id="Layer_1-2" data-name="Layer 1">
                        <path
                          id="ic_add_to_photos_24px"
                          data-name="ic add to photos 24px"
                          class="expand-1"
                          d="M2,4H0V18a2,2,0,0,0,2,2H16V18H2ZM18,0H6A2,2,0,0,0,4,2V14a2,2,0,0,0,2,2H18a2,2,0,0,0,2-2V2A2,2,0,0,0,18,0ZM17,9H13v4H11V9H7V7h4V3h2V7h4Z"
                        />
                      </g>
                    </g>
                  </svg>

                  <svg
                    v-else
                    xmlns="http://www.w3.org/2000/svg"
                    class="my-2 mx-auto h-5 w-5"
                    viewBox="0 0 20 20"
                  >
                    <g id="Layer_2" data-name="Layer 2">
                      <g id="Layer_1-2" data-name="Layer 1">
                        <path
                          id="ic_add_to_photos_24px"
                          data-name="ic add to photos 24px"
                          class="collapse-1"
                          d="M2,4H0V18a2,2,0,0,0,2,2H16V18H2ZM18,0H6A2,2,0,0,0,4,2V14a2,2,0,0,0,2,2H18a2,2,0,0,0,2-2V2A2,2,0,0,0,18,0ZM17,9H7V7H17Z"
                        />
                      </g>
                    </g>
                  </svg>
                </div>
              </div>
            </div>
          </div>
          <div
            v-if="allEmails.items.length > 0"
            class="flex w-full email-view-wrapper pb-0"
          >
            <!-- Start Email Content Wrapper-->
            <div
              class="email-content h-full w-full px-4 pt-2 scroll email_content_scroll"
            >
              <!-- Start Email Body -->
              <transition name="media" mode="out-in">
                <component
                  :is="`HomeRealtimeFeedRssMicrosoftMail${emailDynamicComp}`"
                  ref="child"
                >
                </component>
              </transition>
              <!-- End Email Body -->
            </div>
            <!-- End Email Content -->
          </div>
          <!-- End Email View -->
          <template v-else>
            <div
              class="w-full h-full mb-2 text-blue-600 text-xl font-semibold relative flex justify-center items-center"
            >
              <div
                class="text-2xl text-center text-black font-semibold absolute"
              >
                No Data Found
              </div>
            </div>
          </template>
        </div>
        <!-- </template> -->
      </div>
    </transition>
  </section>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
import { useLoadMore } from '~/composables/feeds/useLoadMore'
import EmailContent from '~/components/home/<USER>/microsoft-mail/EmailContent.vue'
import EmailDocuments from '~/components/home/<USER>/microsoft-mail/EmailDocuments.vue'

const { loadMore, showEndMessage, disableLoadMoreProcess } = useLoadMore()
const store = useStore()

const allExpanded = computed(() => store.state.home.allExpanded)
const emailDynamicComp = computed(() => store.state.home.emailDynamicComp)
const expandFullImage = computed(() => store.state.home.expandFullImage)

// Access state
const loadArticles = computed(() => store.state.home.loadArticles)
const loadMoreArticles = computed(() => store.state.home.loadMoreArticles)
const singleMessage = computed(() => store.state.home.loadMoreArticles)
const allEmails = computed(() => store.getters['home/allEmails'])
const selectedEmailLabel = computed(() => store.state.home.selectedEmailLabel)
const currentSocialComponent = computed(
  () => store.state.home.currentSocialComponent,
)

const emailType = (label: number) => {
  if (label === 1) {
    return 'Inbox'
  } else if (label === 2) {
    return 'Junk'
  } else if (label === 3) {
    return 'Sent'
  } else if (label === 4) {
    return 'Deleted'
  } else if (label === 5) {
    return 'Permanently Deleted'
  }
}
const activeOption = ref('Messages')
onMounted(() => {
  if (selectedEmailLabel.value) {
    activeOption.value = 'singleLabel'
  }
})
watch(
  () => selectedEmailLabel.value,
  (value) => {
    if (value) {
      activeOption.value = 'singleLabel'
    }
  },
)
const handleClick = () => {
  activeOption.value = 'Messages'
  if (selectedEmailLabel.value || allEmails.value.items.length === 0) {
    store.commit('home/SET_CURRENT_SOCIAL_COMPONENT', {
      provider: currentSocialComponent.value.provider,
      id: currentSocialComponent.value.id,
    })
    store.commit('home/RESET_SELETED_LABEL_ITEM')
  }
}
</script>

<style lang="scss" scoped>
.collapse-1 {
  fill: #e4801d;
}
.expand-1 {
  fill: #e4801d;
}
.email-view-wrapper {
  height: calc(100% - 36px);
}

.fadeIn-enter,
.fadeIn-leave-to {
  opacity: 0;
}

.fadeIn-enter-to,
.fadeIn-leave-from {
  opacity: 1;
}

.fadeIn-enter-active,
.fadeIn-leave-active {
  transition: opacity 1s;
}
.media-enter-active,
.media-leave-active {
  transition: opacity 0.5s;
}
.media-enter,
.media-leave-to {
  opacity: 0;
}
[data-title]:after {
  color: #e4801d;
  right: 100%;
  z-index: 99999999;
}
.scroll {
  overflow-y: auto;
  overflow-x: hidden;
  // scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-color: #ff8308 #ececec; /* Firefox 64 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background: #ececec;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: #ff8308;
    border-radius: 3px;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #ff8308;
  }
}
.email_content_scroll {
  overflow-x: auto !important;
}

.more_link {
  position: absolute;
  display: block;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-indent: -9999px;
  // z-index: 1000;
}
.bottom-left-radius {
  border-bottom-left-radius: 25px;
}
.message-body {
  height: 82px;
}
@media (max-width: 767px) {
  .mobile-height {
    height: calc(50% - 17px);
  }
  .bottom-left-radius {
    border-bottom-left-radius: 0px;
  }
}

.ic_attachment {
  fill: #656565;
}
.ic_attachment1 {
  fill: #ffffff;
}
.name {
  display: flex;
}
</style>
