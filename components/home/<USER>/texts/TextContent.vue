<template>
  <section>
    <!-- v-for="(showText, textIndex) in singleMessages"
      :id="showText.id"
      :key="textIndex + 'ShowText'" -->
    <div>
      <!-- <transition v-if="showText.individualSelect.value"> -->
      <!-- Start Texts Content Wrapper-->
      <!-- Start Texts Body -->
      <div class="texts-body h-full pt-4">
        <div
          class="flex justify-between lg:flex-nowrap lg:space-y-0 flex-wrap space-y-1 text-ash-primary cursor-pointer"
        >
          <div class="name-wrapper flex space-x-2 justify-between items-center">
            <div class="mail-name text-lg font-bold">
              {{
                selectedPerson.members.length > 2
                  ? 'Group Name'
                  : selectedPerson.members[0].name === chatList.name
                    ? selectedPerson.members[1].name
                    : selectedPerson.members[0].name
              }}
            </div>
          </div>
          <div class="text-gray-1700">
            <div class="attachment cursor-pointer">
              <img
                class="mx-auto h-9 w-9"
                src="../../../../assets/img/svg/download_background.svg"
                alt="SharpArchive download-texts Icon"
              />
            </div>
          </div>
        </div>

        <div class="text-lg text-ash-primary">
          {{
            selectedPerson.members.length > 2
              ? ''
              : selectedPerson.members[0].name === chatList.name
                ? selectedPerson.members[1].number
                : selectedPerson.members[0].number
          }}
        </div>

        <div class="text-lg text-ash-primary pt-0.5">
          <span class="font-bold">Last Message: </span>
          <DateTime
            :datetime="selectedPerson.updatedAt"
            format="MMMM, dd yyyy, hh:mm aa"
            class="whitespace-nowrap"
          />
        </div>

        <div class="text-lg text-ash-primary mt-1.5">
          <span class="font-bold">Explanation: </span> {{ selectedPerson.text }}
        </div>

        <div
          class="flex items-end"
          :class="
            selectedPerson.attachments ? 'justify-between' : 'justify-end'
          "
        >
          <div v-if="selectedPerson.attachments" class="flag-wrapper mt-3">
            <button
              class="w-52 bg-red-moreLightness text-red-deep h-9 rounded-full outline-none font-bold text-sm relative overflow-hidden cursor-pointer"
            >
              <span>Workplace Violence</span>
              <div
                class="flag-button w-9 h-9 rounded-full bg-red-deep shadow-lg absolute left-0 top-0 flex justify-center items-center"
              >
                <img
                  class="h-3.5 w-3.5"
                  src="../../../../assets/img/svg/flag.svg"
                  alt="flag-icon"
                />
              </div>
            </button>

            <button
              class="w-40 bg-red-deep text-offwhite-800 h-9 mt-2 lg:mt-0 rounded-full outline-none font-bold text-sm"
            >
              Open Flag
            </button>
          </div>

          <div class="cursor-pointer flex items-center gap-2 z-20">
            <div class="zoom-icon-container shadow-lg">
              <fa
                class="text-white text-lg hidden md:block"
                :icon="['fas', 'search-plus']"
                @click="zoomIn"
              />
            </div>
            <div class="zoom-icon-container shadow-lg">
              <fa
                class="text-white text-lg hidden md:block"
                :icon="['fas', 'search-minus']"
                @click="zoomOut"
              />
            </div>
          </div>
        </div>

        <!-- Start text message -->
        <div id="zoom" ref="zoom" class="overflow-hidden w-full h-full zoom">
          <div
            id="zoomContainer"
            ref="objRecordList"
            class="text-message-wrapper mt-4 zoomcontainer"
          >
            <div
              id="messageWrapper"
              ref="chatContainer"
              class="messageWrapper p-4 flex flex-col space-y-3 text-xl bg-white mt-1 overflow-scroll scroll"
              @scroll="
                !showMoreMessageSkeleton && !showMessageskeleton
                  ? handleScroll()
                  : ''
              "
            >
              <div
                v-if="showMessageskeleton || showMoreMessageSkeleton"
                class="animate-pulse"
              >
                <div v-for="message in 2" :key="message" class="w-full">
                  <!-- <div class="w-[200px] py-2 text-gray-1700 bg-gray-500 h-6">

                  </div> -->
                  <div class="send-right-wrapper flex flex-row-reverse">
                    <div class="send-right my-2 max-w-75">
                      <!-- <div class="send-right-top">I am going to be free from tomorrow.</div> -->
                      <div class="bg-gray-500 h-10 w-28 rounded-full"></div>
                      <!-- <div class="send-right-bottom">Do you have any plan?</div> -->
                    </div>
                  </div>

                  <div class="receive-left-wrapper">
                    <div class="sender-image m-2 self-end">
                      <div
                        class="w-13-point-5 h-13-point-5 rounded-full object-cover border border-gray-400 bg-gray-500"
                      ></div>
                    </div>
                    <div class="receive-left space-y-1">
                      <div class="bg-gray-500 h-10 w-28 rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-if="!showMessageskeleton"
                class="w-full flex flex-col space-y-2"
              >
                <div
                  v-for="(singleMessage, index) in singleMessages"
                  :key="index"
                >
                  <div
                    class="w-full flex flex-col space-y-3 items-end"
                    v-if="
                      (singleMessage.messageStatus === 'Delivered' ||
                        singleMessage.messageStatus === 'DeliveryFailed') &&
                      singleMessage.messages &&
                      singleMessage.messages.length > 0
                    "
                  >
                    <div w-full>
                      <DateTime
                        :datetime="singleMessage.date"
                        format="MMMM, dd yyyy, hh:mm aa"
                        class="whitespace-nowrap text-[#8e8e8e]"
                      />
                    </div>
                    <div class="flex flex-col space-y-2">
                      <div
                        class="w-full flex flex-col items-end justify-end"
                        :class="
                          message.text.trim() ||
                          (message.attachments &&
                            message.attachments.length > 0)
                            ? ''
                            : 'hidden'
                        "
                        v-for="(
                          message, messageIndex
                        ) in singleMessage.messages"
                        :key="message.id"
                      >
                        <div
                          v-if="message.text.trim()"
                          class="max-w-[340px] bg-[#119cfb] text-white break-words"
                          :class="
                            singleMessage.messages.length - 1 === messageIndex
                              ? 'send-right-top'
                              : 'send-right-middle'
                          "
                        >
                          {{ message.text }}
                        </div>
                        <div
                          v-if="
                            message.attachments &&
                            message.attachments.length > 0
                          "
                          class="grid gap-2"
                          :class="
                            message.attachments.length >= 3
                              ? 'grid-cols-3'
                              : message.attachments.length >= 2
                                ? 'grid-cols-2'
                                : 'grid-cols-1'
                          "
                        >
                          <div
                            v-for="(attachment, index) in message.attachments"
                            :key="index"
                            :class="
                              message.attachments.length >= 2
                                ? 'w-[120px] h-[120px]'
                                : 'w-[160px] h-[160px]'
                            "
                          >
                            <img
                              class="w-full h-full"
                              :src="attachment"
                              :alt="attachment"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="w-full"
                    v-if="
                      singleMessage.messageStatus === 'Received' &&
                      singleMessage.messages &&
                      singleMessage.messages.length > 0
                    "
                  >
                    <div class="flex space-x-3 items-end">
                      <div
                        class="w-[54px] min-w-[54px] h-[54px] min-h-[54px] rounded-full text-white font-bold text-xl bg-[#3C7E44] flex justify-center items-center"
                      >
                        <span>{{ singleMessage.initials }}</span>
                      </div>
                      <div class="w-full flex flex-col space-y-3 items-start">
                        <div w-full>
                          <DateTime
                            :datetime="singleMessage.date"
                            format="MMMM, dd yyyy, hh:mm aa"
                            class="whitespace-nowrap text-[#8e8e8e]"
                          />
                        </div>
                        <div class="flex flex-col space-y-2">
                          <div
                            class="w-full flex justify-start"
                            :class="
                              message.text.trim() ||
                              (message.attachments &&
                                message.attachments.length > 0)
                                ? ''
                                : 'hidden'
                            "
                            v-for="(
                              message, messageIndex
                            ) in singleMessage.messages"
                            :key="message.id"
                          >
                            <div
                              v-if="message.text.trim()"
                              class="max-w-[340px] bg-[#e5e6eb] text-[#505050] break-words"
                              :class="
                                singleMessage.messages.length - 1 ===
                                messageIndex
                                  ? 'receive-left-top'
                                  : 'receive-left-middle'
                              "
                            >
                              {{ message.text }}
                            </div>
                            <div
                              v-if="
                                message.attachments &&
                                message.attachments.length > 0
                              "
                              class="grid gap-2"
                              :class="
                                message.attachments.length > 3
                                  ? 'grid-cols-3'
                                  : message.attachments.length > 2
                                    ? 'grid-cols-2'
                                    : 'grid-cols-1'
                              "
                            >
                              <div
                                v-for="(
                                  attachment, index
                                ) in message.attachments"
                                :key="index"
                                :class="
                                  message.attachments.length >= 2
                                    ? 'w-[120px] h-[120px]'
                                    : 'w-[160px] h-[160px]'
                                "
                              >
                                <img
                                  class="w-full h-full"
                                  :src="attachment"
                                  :alt="attachment"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- <div
              id="messageWrapper"
              class="messageWrapper text-xl bg-white mt-1 overflow-scroll scroll"
            >
              <div class="send-right-wrapper flex flex-row-reverse my-8">
                <div class="send-right text-white space-y-1 w-3/4">
                  <div class="send-right-top">
                    I am going to be free from tomorrow.
                  </div>
                  <div class="send-right-middle">
                    We should decide about the trip
                  </div>
                  <div class="send-right-bottom">Do you have any plan?</div>
                </div>
              </div>

              <div class="receive-left-wrapper">
                <div class="sender-image m-2 self-end">
                  <img
                    width="54"
                    height="54"
                    src="@/assets/img/bp/dp-5.svg"
                    alt=""
                  />
                </div>
                <div class="receive-left text-gray-1200 space-y-1">
                  <div class="receive-left-top">
                    Let's pick a location for the vacation.
                  </div>
                  <div class="receive-left-middle">Let me know what you</div>
                  <div class="receive-left-bottom">
                    Let me know what you are thinking
                  </div>
                </div>
              </div>

              <div class="send-right-wrapper flex flex-row-reverse">
                <div class="send-right text-white space-y-1 w-3/4">
                  <div class="send-right-top">If you ask me....</div>
                  <div class="send-right-middle">
                    I will suggest to go for a fire camp and spend a night in
                    deep forest.
                  </div>
                  <div class="send-right-bottom">What do you think?</div>
                </div>
              </div>

              <div class="receive-left-wrapper">
                <div class="sender-image m-2 self-end">
                  <img
                    width="54"
                    height="54"
                    src="@/assets/img/bp/dp-5.svg"
                    alt=""
                  />
                </div>
                <div class="receive-left text-gray-1200 space-y-1">
                  <div class="receive-left-top">
                    Let's pick a location for the vacation.
                  </div>
                  <div class="receive-left-middle">Let me know what you</div>
                  <div class="receive-left-bottom">
                    Let me know what you are thinking
                  </div>
                </div>
              </div>
            </div> -->
          </div>
        </div>
        <!-- End text message -->
      </div>
      <!-- End Texts Body -->
      <!-- End Texts Content -->
      <!-- </transition> -->
    </div>
  </section>
</template>

<script>
import { useStore } from 'vuex'
export default defineComponent({
  props: {
    selectedPerson: {
      type: Object,
      default: () => {},
    },
  },
  setup() {
    const store = useStore()
    const chatList = computed(() => store.getters['home/getChatList'])
    const singleMessages = computed(
      () => store.getters['home/getSingleMessages'],
    )
    console.log(singleMessages.value, 'singleMessages')
    const orginalMessagesArray = computed(() => store.state.home.singleMessages)

    const chatContainer = ref(null)
    const showMessageskeleton = computed(
      () => store.state.home.showMessageskeleton,
    )
    const showMoreMessageSkeleton = computed(
      () => store.state.home.showMoreMessageSkeleton,
    )
    const hasMore = computed(() => store.state.home.hasMoreToLoad)
    // Scroll handler
    const handleScroll = async () => {
      const el = chatContainer.value
      if (el.scrollTop === 0 && hasMore.value) {
        const prevHeight = el.scrollHeight
        console.log(chatList.value)
        await store.dispatch('home/getMoreRingCentralMessages', {
          provider: chatList.value.provider,
          chatId: orginalMessagesArray.value.messages[0].chatId,
          id: orginalMessagesArray.value.messages[0].id,
          accountId: chatList.value.id
        })
        await nextTick()
        el.scrollTop = el.scrollHeight - prevHeight
      }
    }
    return {
      // emailDynamicComp: computed(() => store.state.home.emailDynamicComp),
      store,
      chatList,
      singleMessages,
      chatContainer,
      showMessageskeleton,
      showMoreMessageSkeleton,
      handleScroll,
    }
  },
  data() {
    return {
      scale: 1,
      pointX: 0,
      pointY: 0,
    }
  },
  async mounted() {
    await nextTick(() => {
      this.store.commit('home/SET_HAS_MORE_TO_LOAD', true)
      this.checkClass = document.getElementById('messageWrapper')
      this.scrollDown()
    })
  },
  watch: {
    async singleMessages(newVal, oldVal) {
      if (newVal) {
        await nextTick(() => {
          this.store.commit('home/SET_HAS_MORE_TO_LOAD', true)
          this.scrollDown()
        })
      }
      // Do something when 'someDataProperty' changes
    },
  },
  methods: {
    scrollDown() {
      if (this.chatContainer) {
        this.chatContainer.scrollTop = this.chatContainer.scrollHeight
      }
    },
    zoomIn(e) {
      if (this.checkClass.classList.contains('text-xs')) {
        this.checkClass.classList.remove('text-xs')
        this.checkClass.classList.add('text-sm')
      } else if (this.checkClass.classList.contains('text-sm')) {
        this.checkClass.classList.remove('text-sm')
        this.checkClass.classList.add('text-base')
      } else if (this.checkClass.classList.contains('text-base')) {
        this.checkClass.classList.remove('text-base')
        this.checkClass.classList.add('text-lg')
      } else if (this.checkClass.classList.contains('text-lg')) {
        this.checkClass.classList.remove('text-lg')
        this.checkClass.classList.add('text-xl')
      }
      /* const display = document.getElementById('zoomContainer')
      const matrix = window.getComputedStyle(display).transform
      const matrixArray = matrix.replace('matrix(', '').split(',')
      let scaleX = parseFloat(matrixArray[0]) // convert from string to number
      scaleX *= 1.2
      if (scaleX < 1.1) {
        this.pointY = this.pointY + 6
        display.style.transform = `translate(${this.pointX}px, ${this.pointY}px) scale( ${scaleX} )`
      } else {
        display.style.transform = `translate(${this.pointX}px, ${this.pointY}px) scale(1)`
      } */
    },
    zoomOut(e) {
      if (this.checkClass.classList.contains('text-xl')) {
        this.checkClass.classList.remove('text-xl')
        this.checkClass.classList.add('text-lg')
      } else if (this.checkClass.classList.contains('text-lg')) {
        this.checkClass.classList.remove('text-lg')
        this.checkClass.classList.add('text-base')
      } else if (this.checkClass.classList.contains('text-base')) {
        this.checkClass.classList.remove('text-base')
        this.checkClass.classList.add('text-sm')
      } else if (this.checkClass.classList.contains('text-sm')) {
        this.checkClass.classList.remove('text-sm')
        this.checkClass.classList.add('text-xs')
      }
      /* const display = document.getElementById('zoomContainer')
      const matrix = window.getComputedStyle(display).transform
      const matrixArray = matrix.replace('matrix(', '').split(',')
      let scaleX = parseFloat(matrixArray[0]) // convert from string to number
      scaleX /= 1.2
      if (scaleX >= 0.482252) {
        this.pointY = this.pointY - 6
        display.style.transform = `translate(${this.pointX}px, ${this.pointY}px) scale( ${scaleX} )`
      } */
    },
  },
})
</script>

<style lang="scss" scoped>
.zoom-icon-container {
  @apply bg-yellow-primary w-9 h-9 flex items-center justify-center rounded-full;
}
.zoomcontainer {
  transform: translate(0px, 0px) scale(1);
}
.media-enter-active,
.media-leave-active {
  transition: opacity 0.5s;
}
.media-enter,
.media-leave-to {
  opacity: 0;
}
.singleMessageWrapper {
  // @apply hover:bg-yellow-primary;
}
.text-xxs {
  font-size: 9px;
}
.small-text-middle {
  vertical-align: middle;
}
.h-5-5 {
  height: 22px;
}
.flag-button-small {
  margin-top: 1px;
  margin-left: 1px;
}
.scroll {
  overflow-y: auto;
  overflow-x: hidden;
  // scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-color: #ff8308 #ececec; /* Firefox 64 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background: #ececec;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: #ff8308;
    border-radius: 3px;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #ff8308;
  }
}
.messageWrapper {
  max-height: 750px;
  box-shadow: 0px 3px 6px #2228314c;
}
.send-right {
  text-align: -webkit-right;
  margin-right: 16px;
}
.send-right-top {
  @apply p-3;
  background: #119cfb;
  border-radius: 16px 16px 0px 16px;
  opacity: 1;
  width: fit-content;
}
.send-right-middle {
  @apply p-3;
  background: #119cfb;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  width: fit-content;
}
.send-right-bottom {
  @apply p-3;
  background: #119cfb;
  border-radius: 35px 5px 35px 35px;
  opacity: 1;
  width: fit-content;
}
.receive-left-wrapper {
  @apply flex my-8 mr-2;
}
.receive-left-top {
  @apply p-3;
  background: #e5e6eb;
  border-radius: 16px 16px 16px 0px;
  opacity: 1;
  width: fit-content;
}
.receive-left-middle {
  @apply p-3;
  background: #e5e6eb;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  width: fit-content;
}
.receive-left-bottom {
  @apply p-3;
  background: #e5e6eb;
  border-radius: 5px 35px 35px 35px;
  opacity: 1;
  width: fit-content;
}
</style>
