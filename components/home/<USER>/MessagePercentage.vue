<script setup lang="ts">
import { useVuelidate } from '@vuelidate/core'
import { required, numeric, maxValue, minValue } from '@vuelidate/validators'
import { useStore } from 'vuex'

const store = useStore()
const { tokenCookie } = useAuth()
const downloadLoader = computed(() => store.state.archive.downloadLoader)
const percentage = ref(1)
const placeholder = '1'
const inputWidth = ref(20) // default width
const mirror = ref<HTMLElement | null>(null)
const rules = {
  percentage: {
    required,
    numeric,
    minValue: minValue(1),
    maxValue: maxValue(100),
  },
}
const v$ = useVuelidate(rules, { percentage })
const isNumber = (event) => {
  event = event || window.event
  const charCode = event.target.value
    .charAt(event.target.selectionStart - 1)
    .charCodeAt()
  if (charCode > 31 && (charCode < 48 || charCode > 57)) {
    event.target.value = event.target.value.replace(/[^0-9]+/g, '')
    event.preventDefault()
  } else {
    return true
  }
}
const isNumberKey = (event) => {
  if (!/\d/.test(event.key)) return event.preventDefault()
}
const updateWidth = async () => {
  await nextTick() // ensure DOM updated
  if (mirror.value) {
    inputWidth.value = mirror.value.offsetWidth
  }
}
onMounted(() => {
  updateWidth()
})
const startDownload = () => {
  v$.value.$touch()
  if (!v$.value.$invalid) {
    store.dispatch('home/startAllEmailPDFDownload', {
      percentage: percentage.value,
      tokenCookie: tokenCookie.value,
    })
    store.commit('home/SET_DOWNLOAD_PERCENTAGE', percentage.value)
    store.commit('home/SET_SHOW_EMAIL_PERCENTAGE_MODAL', false)
  }
}
</script>

<template>
  <div
    class="bg-dark-100 shadow-md z-9999 p-6 rounded-2xl w-1/2 max-w-[500px] absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
  >
    <div class="w-full flex justify-end">
      <ClientOnly>
        <fa
          class="text-2xl cursor-pointer text-[#e4801d]"
          :icon="['fas', 'times']"
          @click="store.commit('home/SET_SHOW_EMAIL_PERCENTAGE_MODAL', false)"
        />
      </ClientOnly>
    </div>
    <div class="w-full mt-6">
      <p class="text-lg text-white">
        Enter the percentage of emails you want to download as PDF
      </p>
      <div
        class="w-full mt-6 rounded-full h-10 bg-white flex space-x-1 items-center px-4 py-1"
      >
        <input
          v-model.number="percentage"
          type="text"
          class="outline-none border-none bg-white min-w-[20px] max-w-full"
          placeholder="1"
          @keyup="isNumber($event)"
          @keypress="isNumberKey($event)"
          @blur="v$.percentage.$touch()"
          @input="updateWidth"
          :style="{ minWidth: inputWidth + 'px', width: inputWidth + 'px' }"
        />
        <p>%</p>
      </div>
      <template v-if="v$.percentage.$error">
        <p
          v-if="v$.percentage.required.$invalid"
          class="text-red-500 px-4 text-xs"
        >
          Field is Required
        </p>
        <p
          v-else-if="v$.percentage.numeric.$invalid"
          class="text-red-500 px-4 text-xs"
        >
          Field is Invalid
        </p>
        <p
          v-else-if="v$.percentage.minValue.$invalid"
          class="text-red-500 px-4 text-xs"
        >
          Minimum value is 1
        </p>
        <p
          v-else-if="v$.percentage.maxValue.$invalid"
          class="text-red-500 px-4 text-xs"
        >
          100% is the maximum percentage
        </p>
      </template>
      <!-- Hidden span for measuring -->
      <span ref="mirror" class="invisible absolute whitespace-pre px-1">{{
        percentage || placeholder
      }}</span>
    </div>
    <div class="flex justify-center w-full mt-6">
      <button
        class="bg-[#E4801D] text-white font-semibold w-[120px] h-10 rounded-full flex justify-center items-center"
        :disabled="downloadLoader"
        @click="startDownload"
      >
        Download
      </button>
    </div>
  </div>
</template>

<style scoped></style>
