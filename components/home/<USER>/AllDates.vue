<template>
  <div
    class="wrapper text-lg font-bold relative group cursor-pointer"
    :style="{ '--width': width }"
    @click="vCalenderShow()"
    @mouseleave.stop="vCalenderHide()"
  >
    <div class="all_dates">
      <select-input
        id="allDates"
        v-model="allDates"
        class="pointer-events-none selectSearch w-full"
        class-style-name="searchPageScrollStyle searchPageScrollWidth search-select-input"
        :place-holder="allDates"
        color="#F8F8F8"
        background="#e4801d"
        caret-bg="#e4801d"
        scroll-color="#5b5fcc"
      >
      </select-input>
    </div>
    <div
      class="md:w-100 w-full shadow-xl md:absolute fixed top-13 right-0 z-999999 block cursor-pointer bg-white rounded-3xl border-top"
      :class="showVCalender ? 'md:h-full h-4/5' : ''"
    >
      <v-calender-with-preset
        :show-v-calender="showVCalender"
        :height="370"
        header-bg-color="bg-orange-dark"
        sidebar-hover-class="hover:bg-orange-dark hover:text-white"
        sidebar-active-color="bg-orange-dark text-white"
        border-color="#E4801D"
        scroll-color="#E4801D"
        date-picker-color="home"
        content-body-color="bg-orange-dark"
        content-body-hover-color="hover:bg-white hover:text-orange-dark"
        content-body-active-color="bg-white text-orange-dark"
        @dateRange="pastMonthDateRageEvent"
        @pastmonth="pastMonthsValue"
        @hide-v-calender="vCalenderHide()"
        @clear-date-range="clearDateRange()"
        @clear-all-date="clearAllDateRange()"
        @clear-all-date-range="clearAllDateRange()"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { format, parseISO } from 'date-fns'
import VCalenderWithPreset from '~/components/VCalenderWithPreset.vue'
import SelectInput from '~/components/inputs/SelectInput.vue'
import { useNuxtApp } from '#app'
import { useStore } from 'vuex'
const pastMonthPlaceholder = (
  start: string,
  end: string,
  dateFormat: string,
) => {
  return (
    format(parseISO(start), dateFormat) +
    ' - ' +
    format(parseISO(end), dateFormat)
  )
}

const props = defineProps({
  width: {
    type: String,
    default: '100%',
  },
})

const showVCalender = ref<boolean>(false)
const allDates = ref<string>('All Dates')
const allDatesValue = ref<string>('')
const startDate = ref<string>('')
const endDate = ref<string>('')

const nuxtApp = useNuxtApp()
const store = useStore()

const globalDateformat = computed(() => store.state.system.formatDate)
const webSearchClear = computed(() => store.state.home.webSearchClear)
const articles = computed(() => store.state.home.articles)
const date = computed(() => store.state.home.date)
const selectedEmailLabel = computed(() => store.state.home.selectedEmailLabel)
const currentSocialComponent = computed(
  () => store.state.home.currentSocialComponent,
)

watch(
  () => webSearchClear.value,
  (data) => {
    if (!data) {
      nuxtApp.$bus.$emit('clearDatePicker')
      allDates.value = 'All Dates'
    }
  },
)
// methods

const getAllEmailArticle = (value: {
  accountId: number
  label: string
  startDate: string
  endDate: string
}) => store.dispatch('home/getAllEmailArticle', value)
const getSelectedAllEmails = (value: {
  accountId: number
  label: string
  startDate: string
  endDate: string
}) => store.dispatch('home/getSelectedAllEmails', value)

const vCalenderShow = () => {
  showVCalender.value = true
}
const vCalenderHide = () => {
  showVCalender.value = false
}
const clearDateRange = () => {
  if (startDate.value !== '' && endDate.value !== '') {
    startDate.value = ''
    endDate.value = ''
    allDates.value = 'All Dates'
  }
  if (
    // (this.startDate !== '' && this.endDate !== '') ||
    date.value.startDate !== '' &&
    date.value.endDate !== '' &&
    articles.value.provider !== 'Web'
  ) {
    startDate.value = ''
    endDate.value = ''
    allDates.value = 'All Dates'
    store.commit('home/RESET_START_END_DATE')

    getAllEmailArticle({
      accountId: articles.value.id,
      label: selectedEmailLabel.value
        ? selectedEmailLabel.value.orginalName ||
          selectedEmailLabel.value.labels ||
          selectedEmailLabel.value.name
        : '',
      startDate: startDate.value,
      endDate: endDate.value,
    })
  }
}
const clearAllDateRange = () => {
  if (startDate.value !== '' && endDate.value !== '') {
    startDate.value = ''
    endDate.value = ''
    allDates.value = 'All Dates'
    store.commit('home/RESET_START_END_DATE')
  }
}
const pastMonthDateRageEvent = (daterange: { start: string; end: string }) => {
  allDates.value = pastMonthPlaceholder(
    daterange.start,
    daterange.end,
    globalDateformat.value,
  )
  startDate.value = daterange.start
  endDate.value = daterange.end
  allDatesValue.value = ''
  // this.$store.commit('home/SET_WEB_SEARCH', {
  //   archiveDate: daterange.start,
  // })
  getAllEmailArticle({
    accountId: articles.value.id,
    label: selectedEmailLabel.value
      ? selectedEmailLabel.value.orginalName ||
        selectedEmailLabel.value.labels ||
        selectedEmailLabel.value.name
      : '',
    startDate: startDate.value,
    endDate: endDate.value,
  })
}
const pastMonthsValue = (pastmonth) => {
  // this.allDates = pastmonth.text
  // this.allDatesValue = pastmonth.value
  // this.startDate = ''
  // this.endDate = ''
}
</script>

<style scoped>
.wrapper {
  width: var(--width);
}
.selectSearch {
  /* max-width: var(--width);*/
  height: 40px;
}
</style>
