<template>
  <!-- min-[1208px]:w-[120px] min-[1178px]:w-[100px] min-[1037px]:w-[60px]  -->
  <div
    ref="content__wrapper"
    class="content__tabs flex flex-wrap items-center rounded-full h-10 md:shadow transition-all duration-500"
    :class="
      !expandCollapse && isCustomSizeTwo
        ? 'w-[40px] overflow-hidden bg-orange-dark'
        : 'md:w-[280px] min-[1338px]:w-[300px] min-[1477px]:w-[342px] min-[1634px]:w-96 w-full bg-white'
    "
  >
    <div
      class="background__circle bg-orange-dark"
      :class="!showHide && isCustomSizeTwo ? '!hidden' : 'block'"
      :style="{
        left: `${offsetLeftPx}px`,
        width: `${backgroundWidth + 1}px`,
      }"
    ></div>
    <div
      :class="[!expandCollapse && isCustomSizeTwo ? 'block' : 'hidden']"
      class="cursor-pointer rounded-full text-center h-10 md:px-2 min-[1477px]:px-4 px-3 py-1 w-full"
      data-index="0"
    >
      <ClientOnly>
        <fa
          class="pointer-events-none text-3xl font-bold text-white transform -rotate-90"
          :icon="['fas', 'caret-down']"
        />
      </ClientOnly>
      <!-- <span
        class="pointer-events-none xl:text-lg md:text-md text-sm font-bold text-white"
        >Menu</span
      > -->
    </div>
    <div
      ref="all"
      :class="[
        currentTab === 'All' ? 'active' : '',
        !showHide && isCustomSizeTwo ? '!hidden' : 'block',
      ]"
      class="tab cursor-pointer rounded-full text-center h-auto md:px-2 min-[1477px]:px-4 px-3 py-1 w-1/3"
      data-index="0"
      @click="showTabMenu('All'), animate()"
    >
      <span class="pointer-events-none xl:text-lg md:text-md text-sm font-bold"
        >All</span
      >
    </div>

    <div
      ref="incoming"
      :class="[
        currentTab === 'Incoming' ? 'active' : '',
        !showHide && isCustomSizeTwo ? '!hidden' : 'block',
      ]"
      class="tab cursor-pointer rounded-full text-center h-auto md:px-2 min-[1477px]:px-4 px-3 py-1 w-1/3"
      data-index="1"
      @click="showTabMenu('Incoming'), animate()"
    >
      <span class="pointer-events-none xl:text-lg md:text-md text-sm font-bold"
        >Incoming</span
      >
    </div>

    <div
      ref="outgoing"
      :class="[
        currentTab === 'Outgoing' ? 'active' : '',
        !showHide && isCustomSizeTwo ? '!hidden' : 'block',
      ]"
      class="tab cursor-pointer rounded-full text-center h-auto md:px-2 min-[1477px]:px-4 px-3 py-1 w-1/3"
      data-index="2"
      @click="showTabMenu('Outgoing'), animate()"
    >
      <span class="pointer-events-none xl:text-lg md:text-md text-sm font-bold"
        >Outgoing</span
      >
    </div>
    <!-- <div
      ref="system"
      :class="activeComponent === 'System' ? 'active' : ''"
      class="
        tab
        cursor-pointer
        rounded-full
        text-center
        h-auto
        md:px-4
        px-3
        py-1
        w-1/4
      "
      data-index="2"
      @click="showTabMenu('System'), animate()"
    >
      <span class="pointer-events-none xl:text-lg md:text-md text-sm font-bold"
        >System</span
      >
    </div>
    <div
      ref="account"
      :class="activeComponent === 'Account' ? 'active' : ''"
      class="
        tab
        cursor-pointer
        rounded-full
        text-center
        h-auto
        md:px-4
        px-3
        py-1
        w-1/4
      "
      data-index="2"
      @click="showTabMenu('Account'), animate()"
    >
      <span class="pointer-events-none xl:text-lg md:text-md text-sm font-bold"
        >Account</span
      >
    </div> -->
  </div>
</template>
<script setup lang="ts">
import { useStore } from 'vuex'
import { useNuxtApp } from '#app'
const props = defineProps({
  expandCollapse: {
    type: Boolean,
    default: false,
  },
  isCustomSizeTwo: {
    type: Boolean,
    default: false,
  },
})

const offsetLeftPx = ref<number>(0)
const offsetTopPx = ref<number>(0)
const backgroundWidth = ref<number>(0)
const circleBackgroundColor = ref<string>('bg-gray-900')
const backgroundColor = ref<string>('bg-gray-500')
const textBackgroundColor = ref<string>('text-gray-900')
const activeComponent = ref<string>('All')
const activeOldIndex = ref<number>(0)
const activeCurrentIndex = ref<number>(0)
const showHide = ref<boolean>(true)
const all = ref<null | HTMLElement>(null)
const content__wrapper = ref<HTMLElement | null>(null)

const store = useStore()
const nuxtApp = useNuxtApp()

const currentTab = computed(() => store.state.home.currentTab)
const currentSocialComponent = computed(
  () => store.state.home.currentSocialComponent,
)

watch(
  () => currentTab.value,
  (data) => {
    if (data === 'All') {
      all.value.click()
    }
  },
)

watch(
  () => props.expandCollapse,
  (data) => {
    if (!data) {
      showHide.value = false
    } else {
      setTimeout(() => {
        showHide.value = true
      }, 80)
    }
  },
)
onMounted(() => {
  const activeLink =
    content__wrapper.value?.querySelector<HTMLElement>('.tab.active')
  setTimeout(() => {
    backgroundWidth.value = activeLink.scrollWidth
    const backgroundColor1 = activeLink.dataset.themeBg
    const textBackgroundColor1 = activeLink.dataset.themeText
    const circleBackgroundColor1 = activeLink.dataset.themeCircle
    circleBackgroundColor.value = circleBackgroundColor1
    backgroundColor.value = backgroundColor1
    textBackgroundColor.value = textBackgroundColor1
    offsetLeftPx.value = activeLink.offsetLeft
  }, 600)
})
// methods
const animate = () => {
  const activeLink = event.target.closest('.tab')
  backgroundWidth.value = activeLink.scrollWidth

  const backgroundColor1 = activeLink.dataset.themeBg
  const textBackgroundColor1 = activeLink.dataset.themeText
  const circleBackgroundColor1 = activeLink.dataset.themeCircle
  circleBackgroundColor.value = circleBackgroundColor1
  backgroundColor.value = backgroundColor1
  textBackgroundColor.value = textBackgroundColor1
  offsetLeftPx.value = activeLink.offsetLeft
}
const getAnimationClass = (index) => {
  let animationClass = ''
  if (activeCurrentIndex.value === index && activeOldIndex.value < index) {
    animationClass = 'slide-left'
  } else if (
    activeCurrentIndex.value === index &&
    activeOldIndex.value > index
  ) {
    animationClass = 'slide-right'
  } else if (activeCurrentIndex.value < index) {
    animationClass = 'slide-left'
  } else if (activeCurrentIndex.value > index) {
    animationClass = 'slide-right'
  }
  return animationClass
}
const showTabMenu = (componentName: string) => {
  store.commit('home/RESET_SELETED_LABEL_ITEM')
  nuxtApp.$bus.$emit('clearDatePicker')
  const tabWrapper = event.target.closest('.content__tabs')
  const oldActiveTab = tabWrapper.querySelector('.tab.active')
  if (componentName !== currentTab.value) {
    activeOldIndex.value = oldActiveTab.dataset.index
    const activeCurrentIndex1 = event.target.dataset.index

    setTimeout(() => {
      activeCurrentIndex.value = activeCurrentIndex1
      store.commit('home/SET_CURRENT_TAB', componentName)
    }, 300)
  } else {
    activeOldIndex.value = oldActiveTab.dataset.index
    setTimeout(() => {
      activeCurrentIndex.value = 0
      store.commit('home/SET_CURRENT_TAB', 'All')
      store.dispatch('home/getAllSocialArticle', {
        id: currentSocialComponent.value.id,
      })
    }, 300)
  }
  //   this.$store.commit('setting/SET_CURRENT_COMPONENT', componentName)
  //   this.$router.push('settings?#' + componentName.toLowerCase())
}
</script>
<style lang="scss" scoped>
.content__tabs {
  position: relative;
  .background__circle {
    top: 0px;
    left: 0px;
    z-index: -1;
    transition:
      width 0.3s ease-in-out 0.2s,
      left 0.5s ease-in-out;
    z-index: 1;
    @apply absolute h-full rounded-full inline-block;
  }
  .tab {
    @apply relative overflow-hidden;
    > span {
      position: relative;
      transition: color 0.2s ease-in-out;
      z-index: 10;
      @apply text-orange-dark;
    }
    &.active {
      > span {
        @apply text-white;
      }
    }
  }
}
</style>
