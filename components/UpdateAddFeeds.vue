<template>
  <section>
    <transition name="right-sidebar-trans">
      <div
        v-if="showUpdateAddFeedsComp"
        class="profile fixed bg-ash-dark right-0 md:top-15 h-full py-4 md:pt-8 overflow-hidden parent-scroll md:rounded-l-2xl md:shadow-2xl md:drop-shadow-2xl font-roboto"
      >
        <transition name="fadeInParent">
          <div class="h-full flex flex-wrap content-between">
            <div class="w-full h-full">
              <div
                class="w-full flex flex-row items-center justify-between md:h-12 h-8 relative md:px-21 px-4"
              >
                <h2
                  class="text-yellow-primary xl:text-2xl md:text-xl md:font-bold text-xl"
                >
                  <span>Add Feeds</span>
                </h2>
                <button
                  class="focus:outline-none xl:h-[24px] md:h-[20px] text-[24px] flex items-center justify-between"
                >
                  <ClientOnly>
                    <fa
                      class="text-yellow-primary xl:text-2xl md:text-xl md:font-bold text-2xl cursor-pointer"
                      :icon="['fas', 'times']"
                      @click="cancel()"
                    />
                  </ClientOnly>
                </button>
              </div>
              <div
                class="h-[calc(100%-60px)] flex flex-wrap content-between md:px-21 px-4 overflow-y-auto scroll parent-scroll"
              >
                <div class="w-full">
                  <div
                    class="w-full flex flex-col addFeedsTable rounded-2xl mt-8 p-4 bg-white"
                  >
                    <div
                      v-if="
                        fbAuthData &&
                        fbAuthData.items &&
                        fbAuthData.items.length > 0
                      "
                      class="w-full flex flex-col h-auto rounded-2xl p-2 bg-white space-y-4 !overflow-y-hidden scroll table-scroll"
                    >
                      <div
                        class="header flex w-full h-10 min-width-27 space-x-2"
                      >
                        <div
                          class="min-width-4 text-yellow-primary md:text-lg text-md md:font-bold break-all self-center"
                        >
                          <span>Add</span>
                        </div>
                        <div
                          class="min-width-17 text-yellow-primary text-2xl md:text-lg text-md md:font-bold break-all self-center"
                        >
                          <span>{{
                            provider === 'ringcentral' ? 'Phone Number' : 'Name'
                          }}</span>
                        </div>
                        <div
                          class="min-width-8 text-yellow-primary md:text-lg text-md md:font-bold break-all self-center"
                        >
                          <span>Type</span>
                        </div>
                      </div>
                      <div
                        class="main-body flex flex-col w-full h-full space-y-4 min-width-27"
                      >
                        <div
                          v-for="(fbData, fbIndex) in fbAuthData.items"
                          :key="fbIndex"
                          class="w-full h-14 flex items-center space-x-2"
                        >
                          <div
                            class="min-width-4"
                            @click="socialStatusChange(fbData.id)"
                          >
                            <div class="toggle-btn-wrapper">
                              <input
                                :id="fbData.id"
                                v-model="fbData.added"
                                type="checkbox"
                                name="toggle"
                                class="toggle-input"
                              />
                              <label
                                :for="fbData.id"
                                class="toggle-input-label toggle-label"
                              ></label>
                            </div>
                          </div>
                          <div class="min-width-17">
                            <div class="flex items-center space-x-2">
                              <div
                                class="min-w-3 min-h-3 profile-image rounded-full bg-red-600"
                              >
                                <template v-if="provider === 'facebook'">
                                  <img
                                    v-if="fbData.picture"
                                    class="min-w-3 min-h-3 profile-image rounded-full"
                                    :src="fbData.picture"
                                    :alt="`${fbData.name} Facebook Profile Picture`"
                                  />
                                  <instagram-icon
                                    v-else-if="fbData.type === 'Instagram'"
                                    class="min-w-3 min-h-3 profile-image rounded-full"
                                  ></instagram-icon>
                                  <facebook-icon
                                    v-else
                                    class="min-w-3 min-h-3 profile-image rounded-full"
                                  ></facebook-icon>
                                </template>
                                <template v-if="provider === 'linkedin'">
                                  <img
                                    v-if="fbData.profile_image_url"
                                    class="min-w-3 min-h-3 profile-image rounded-full"
                                    :src="fbData.profile_image_url"
                                    :alt="`${fbData.name} LinkedIn Profile Picture`"
                                  />
                                  <linkedin-icon
                                    v-else
                                    class="min-w-3 min-h-3 profile-image rounded-full"
                                  ></linkedin-icon>
                                </template>
                                <template v-if="provider === 'microsoft'">
                                  <img
                                    v-if="fbData.profile_image_url"
                                    class="min-w-3 min-h-3 profile-image rounded-full"
                                    :src="fbData.profile_image_url"
                                    :alt="`${fbData.name} Microsoft Profile Picture`"
                                  />
                                  <microsoft-icon
                                    v-else
                                    class="min-w-3 min-h-3 profile-image rounded-full"
                                  ></microsoft-icon>
                                </template>
                                <template v-if="provider === 'ringcentral'">
                                  <img
                                    v-if="fbData.profile_image_url"
                                    class="min-w-3 min-h-3 profile-image rounded-full"
                                    :src="fbData.profile_image_url"
                                    :alt="`${fbData.name} LinkedIn Profile Picture`"
                                  />
                                  <SharedIconRingcentralRingCentralRoundedLogo
                                    v-else
                                    class="min-w-3 min-h-3 profile-image rounded-full"
                                  ></SharedIconRingcentralRingCentralRoundedLogo>
                                </template>
                              </div>
                              <div class="flex flex-col text-width">
                                <div class="has-tooltip relative">
                                  <h2
                                    class="text-xl font-medium text-ash-dark username-clamp"
                                  >
                                    {{ fbData.name }}
                                  </h2>
                                  <span
                                    v-if="
                                      fbData.name && fbData.name.length > 20
                                    "
                                    class="tooltip"
                                    :class="
                                      fbAuthData.items.length - 1 === fbIndex &&
                                      fbAuthData.items.length > 4
                                        ? '-top-14'
                                        : '-top-2'
                                    "
                                  >
                                    {{ fbData.name }}</span
                                  >
                                </div>
                                <div class="has-tooltip relative">
                                  <h2
                                    class="text-sm font-normal text-ash-dark opacity-80 username-clamp"
                                  >
                                    {{ fbData.username }}
                                  </h2>
                                  <span
                                    v-if="
                                      fbData.username &&
                                      fbData.username.length > 24
                                    "
                                    class="text-sm font-normal text-ash-dark tooltip"
                                    :class="
                                      fbAuthData.items.length - 1 === fbIndex &&
                                      fbAuthData.items.length > 4
                                        ? '-top-14'
                                        : '-top-2'
                                    "
                                  >
                                    {{ fbData.username }}</span
                                  >
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="min-width-8 text-ash-dark text-xl font-medium"
                          >
                            <span class="pr-1">{{ fbData.type }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      v-else-if="provider === 'linkedin'"
                      class="w-full h-full flex justify-center items-center"
                    >
                      <p class="text-ash-dark text-lg">
                        There is no organization under this account
                      </p>
                    </div>
                    <div
                      v-else-if="provider === 'facebook'"
                      class="w-full h-full flex justify-center items-center"
                    >
                      <p class="text-ash-dark text-lg">
                        There is no page or group under this account
                      </p>
                    </div>
                    <div
                      v-else-if="provider === 'ringcentral'"
                      class="w-full h-full flex justify-center items-center"
                    >
                      <p class="text-ash-dark text-lg">
                        There is no text under this account
                      </p>
                    </div>
                  </div>
                  <div
                    v-if="$config.public.workflow === 'dev'"
                    class="flex flex-col mt-[30px]"
                  >
                    <div class="flex justify-between items-center">
                      <p class="text-xl font-medium text-white">
                        {{
                          !excludeDms
                            ? 'Exclude Direct Messages'
                            : 'Exclude Direct Messages from Archive'
                        }}
                      </p>
                      <div class="toggle-btn-wrapper">
                        <input
                          id="excludeDms"
                          v-model="excludeDms"
                          type="checkbox"
                          name="toggle"
                          class="exclude_toggle-input"
                        />
                        <label
                          for="excludeDms"
                          class="exclude_toggle-input-label toggle-label"
                        ></label>
                      </div>
                    </div>
                    <Transition name="globalFadeInFadeOut">
                      <div
                        v-if="excludeDms"
                        class="w-full flex flex-col addFeedsTable rounded-2xl mt-5 p-4 bg-white"
                      >
                        <div
                          v-if="
                            directMessages &&
                            directMessages.items &&
                            directMessages.items.length > 0
                          "
                          class="w-full flex flex-col h-auto rounded-2xl p-2 bg-white space-y-4 !overflow-y-hidden scroll table-scroll"
                        >
                          <div
                            class="header flex w-full h-10 justify-between min-width-27 space-x-3"
                          >
                            <div
                              class="min-width-17 max-w-[16rem] text-yellow-primary text-2xl md:text-lg text-md md:font-bold break-all self-center"
                            >
                              <span>Account</span>
                            </div>
                            <div
                              class="min-width-8 text-end text-yellow-primary md:text-lg text-md md:font-bold break-all self-center"
                            >
                              <span>Exclude Messages</span>
                            </div>
                          </div>
                          <div
                            class="main-body flex flex-col w-full h-full space-y-4 min-width-27"
                          >
                            <div
                              v-for="(fbData, fbIndex) in directMessages.items"
                              :key="fbIndex"
                              class="w-full h-14 flex items-center space-x-3 justify-between"
                            >
                              <div class="min-width-17 max-w-[16rem]">
                                <div class="flex items-center space-x-2">
                                  <div
                                    class="min-w-3 min-h-3 profile-image rounded-full"
                                  >
                                    <div v-if="provider === 'facebook'">
                                      <img
                                        v-if="fbData.picture"
                                        class="min-w-3 min-h-3 profile-image rounded-full"
                                        :src="fbData.picture"
                                        :alt="`${fbData.name} Facebook Profile Picture`"
                                      />
                                      <instagram-icon
                                        v-else-if="fbData.type === 'Instagram'"
                                        class="min-w-3 min-h-3 profile-image rounded-full"
                                      ></instagram-icon>
                                      <facebook-icon
                                        v-else
                                        class="min-w-3 min-h-3 profile-image rounded-full"
                                      ></facebook-icon>
                                    </div>
                                    <template v-if="provider === 'linkedin'">
                                      <img
                                        v-if="fbData.profile_image_url"
                                        class="min-w-3 min-h-3 profile-image rounded-full"
                                        :src="fbData.profile_image_url"
                                        :alt="`${fbData.name} LinkedIn Profile Picture`"
                                      />
                                      <linkedin-icon
                                        v-else
                                        class="min-w-3 min-h-3 profile-image rounded-full"
                                      ></linkedin-icon>
                                    </template>
                                    <template v-if="provider === 'microsoft'">
                                      <img
                                        v-if="fbData.profile_image_url"
                                        class="min-w-3 min-h-3 profile-image rounded-full"
                                        :src="fbData.profile_image_url"
                                        :alt="`${fbData.name} Microsoft Profile Picture`"
                                      />
                                      <microsoft-icon
                                        v-else
                                        class="min-w-3 min-h-3 profile-image rounded-full"
                                      ></microsoft-icon>
                                    </template>
                                  </div>
                                  <div class="flex flex-col text-width">
                                    <div class="relative has-tooltip">
                                      <h2
                                        class="text-xl font-medium text-ash-dark username-clamp"
                                      >
                                        {{ fbData.name }}
                                      </h2>
                                      <span
                                        v-if="
                                          fbData.name && fbData.name.length > 20
                                        "
                                        class="tooltip"
                                        :class="
                                          directMessages.items.length - 1 ===
                                            fbIndex &&
                                          directMessages.items.length > 4
                                            ? '-top-14'
                                            : '-top-2'
                                        "
                                      >
                                        {{ fbData.name }}</span
                                      >
                                    </div>
                                    <div class="relative has-tooltip">
                                      <h2
                                        class="text-sm font-normal text-ash-dark opacity-80 username-clamp"
                                      >
                                        {{ fbData.username }}
                                      </h2>
                                      <span
                                        v-if="
                                          fbData.username &&
                                          fbData.username.length > 24
                                        "
                                        class="text-sm font-normal text-ash-dark tooltip"
                                        :class="
                                          directMessages.items.length - 1 ===
                                            fbIndex &&
                                          directMessages.items.length > 4
                                            ? '-top-14'
                                            : '-top-2'
                                        "
                                      >
                                        {{ fbData.username }}</span
                                      >
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div
                                class="min-width-4 text-end"
                                @click="excludeDirectMessageChange(fbData.id)"
                              >
                                <div class="toggle-btn-wrapper !mr-0">
                                  <input
                                    :id="`${fbData.id}_message`"
                                    v-model="fbData.excludeDirectMessage"
                                    type="checkbox"
                                    name="toggle"
                                    class="toggle-input"
                                  />
                                  <label
                                    :for="`${fbData.id}_message`"
                                    class="toggle-input-label toggle-label"
                                  ></label>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          v-else-if="provider === 'linkedin'"
                          class="w-full h-full flex justify-center items-center"
                        >
                          <p class="text-ash-dark text-lg">
                            There is no organization under this account
                          </p>
                        </div>
                        <div
                          v-else-if="provider === 'facebook'"
                          class="w-full h-full flex justify-center items-center"
                        >
                          <p class="text-ash-dark text-lg">
                            There is no page or group under this account
                          </p>
                        </div>
                      </div>
                    </Transition>
                  </div>
                </div>
                <div class="w-full sticky z-1 bottom-0 left-0 bg-ash-dark">
                  <template class="md:px-21 px-4 pt-[30px]" v-if="fbAuthData">
                    <div
                      v-if="provider === 'facebook'"
                      class="w-full text-gray-1100 text-lg font-normal text-justify mt-10"
                    >
                      <p>{{ connectionNoticesFacebook.message }}</p>
                      <p class="mt-4">
                        {{ connectionNoticesFacebook.subMessage }}
                      </p>
                    </div>
                    <div
                      v-else-if="provider === 'linkedin'"
                      class="w-full text-white text-lg font-normal text-justify mt-10"
                    >
                      <p>{{ connectionNoticesLinkedIn.message }}</p>
                      <p class="mt-4">
                        {{ connectionNoticesLinkedIn.subMessage }}
                      </p>
                    </div>
                  </template>
                  <div
                    class="flex w-full justify-center relative mb-4 mt-[30px]"
                  >
                    <button
                      type="submit"
                      class="w-44 py-2 text-white bg-yellow-primary rounded-full border border-yellow-primary outline-none font-bold text-base relative"
                      :disabled="saveProcess"
                      @click="
                        provider === 'ringcentral'
                          ? connectToFb(TEXT_AUTH)
                          : connectToFb(SOCIAL_AUTH)
                      "
                    >
                      <div
                        class="rounded-full relative flex items-center justify-around"
                      >
                        <span>Connect</span>
                        <ClientOnly>
                          <fa
                            v-if="saveProcess"
                            class="absolute right-5 text-white font-bold animate-spin"
                            :icon="['fas', 'spinner']"
                          />
                        </ClientOnly>
                      </div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </transition>
      </div>
    </transition>
    <start-archiving-modal
      v-if="startArchiveModal && showBlurActiveModal"
      @closeAccount="closeAccount"
      @addFeed="addFeed"
    ></start-archiving-modal>
  </section>
</template>

<script setup lang="ts">
import LinkedinIcon from './shared/icon/LinkedinIcon.vue'
import InstagramIcon from './shared/icon/InstagramIcon.vue'
import FacebookIcon from './shared/icon/FacebookIcon.vue'
import MicrosoftIcon from '~/components/shared/icon/MicrosoftIcon.vue'
import { SOCIAL_AUTH, TEXT_AUTH } from '~/constants/urls'
import StartArchivingModal from '~/components/startArchivingModal.vue'
import { useFetched } from '~/composables/useFetched'
import { useStore } from 'vuex'

const route = useRoute()
const store = useStore()
const nuxtApp = useNuxtApp()
const { fetch } = useFetched()

const excludeDms = ref<boolean>(false)
const saveProcess = ref<boolean>(false)
const startArchiveModal = ref<boolean>(false)
const showNotice = ref<boolean>(false)
const connectionNoticesFacebook = {
  message: 'Each of these are considered separate feeds.',
  subMessage: `You have connected a Facebook account. You should know that
           Facebook will require you to reauthorize your account every 60 days.
            Yes, we too find this annoying. We will try to make this as painless as possible.
             Every 60 days, you’ll get an email from us with a button to renew the connection.`,
}
const connectionNoticesLinkedIn = {
  message: 'Each of these are considered separate feeds.',
  subMessage: `You have connected a LinkedIn account. You should know that LinkedIn will require you to reauthorize your account every year.
          Yes, we too find this annoying. We will try to make this as painless as possible. Each year, you’ll get an email from us with a button
          to renew the connection.`,
}

const showUpdateAddFeedsComp = computed(
  () => store.getters['header/getUpdateAddFeed'],
)
const showBlurActiveModal = computed(
  () => store.getters['header/showBlurActiveModal'],
)
const provider = computed(() => store.state.header.providerName)
const socialFeedLength = computed(
  () => store.getters['socialFeed/socialFeedLength'],
)
const directMessages = computed({
  get() {
    return JSON.parse(JSON.stringify(store.state.header.directMessageData))
  },
  set(value) {
    store.commit('header/SET_FB_AUTH_DATA', value)
  },
})
const fbAuthData = computed({
  get() {
    return JSON.parse(JSON.stringify(store.state.header.getFbAuthData))
  },
  set(value) {
    store.commit('header/SET_FB_AUTH_DATA', value)
  },
})

onMounted(() => {
  setTimeout(() => {
    showNotice.value = true
  }, 2000)
})

const socialStatusChange = (id: number) => {
  fbAuthData.value.items.map((item) => {
    if (item.id === id) {
      item.added = !item.added
    }
    return item
  })
}
const excludeDirectMessageChange = (id: number) => {
  directMessages.value?.items.map((item) => {
    if (item.id === id) {
      item.excludeDirectMessage = !item.excludeDirectMessage
    }
    return item
  })
}
const connectToFb = async (type: string) => {
  const fbData = {
    profile_id: '',
    ids: [],
    state: '',
    excludeDirectMessageIds: [],
  }
  fbAuthData.value.items.forEach((item: { id: number }) => {
    if (item.added) {
      fbData.ids.push(item.id)
    }
  })
  directMessages.value.items.forEach((item) => {
    if (item.excludeDirectMessage) {
      fbData.excludeDirectMessageIds.push(item.id)
    }
  })
  fbData.profile_id = fbAuthData.value.profile_id
  fbData.state = route.params.token
  saveProcess.value = true
  nuxtApp.$toast('clear')
  if (fbData.ids && fbData.ids.length > 0) {
    try {
      const res = await fetch(`${type}${provider.value}/`, {
        body: fbData,
        method: 'PUT',
      })
      if (res.success) {
        if (route.name === 'guest-token') {
          store.commit('guest/SET_CHECK_REQUEST', true)
          store.dispatch('header/removeUpdateAddFeed')
          saveProcess.value = false
        }
        // load feed data
        if (socialFeedLength.value === 0) {
          store.commit('socialFeed/SET_SHOW_ARCHIVE_SYSYTEM_SETTINGS', true)
        } else {
          store.commit('socialFeed/SET_SHOW_ARCHIVE_SYSYTEM_SETTINGS', false)
        }
        if (route.name !== 'guest-token') {
          await fatchSocialFeeds()
          await getAllLatestData()
          await getAllSocialArticle()
        }

        store.dispatch('header/setFbAuthData', fbAuthData.value)
        store.dispatch('header/setArchiveFbData', true)
        nuxtApp.$toast('success', {
          message: res.message,
          className: 'toasted-bg-archive',
        })
        store.dispatch('header/removeUpdateAddFeed')
        saveProcess.value = false
        setTimeout(() => {
          store.commit('socialFeed/SHOW_ADD_FEED_MODAL', true)
          store.commit('header/SHOW_ADD_FEED_FINISH_BTN')
        }, 500)
        if (route.name?.includes('source')) {
          store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false)
          store.commit('social/SET_MANAGER_ONBOARDING', true)
        }
      } else {
        saveProcess.value = false
        store.dispatch('header/setArchiveFbData', false)
        nuxtApp.$toast('error', {
          message: res.message,
          className: 'toasted-bg-alert',
        })
      }
    } catch (err) {
      saveProcess.value = false
      store.dispatch('header/setArchiveFbData', false)
      nuxtApp.$toast('error', {
        message: 'Something went wrong, please try again later',
        className: 'toasted-bg-alert',
      })
    }
  } else {
    nuxtApp.$toast('error', {
      message: 'Need to add atleast one feed',
      className: 'toasted-bg-alert',
    })
    saveProcess.value = false
  }
}
const cancel = () => {
  store.dispatch('header/removeUpdateAddFeed')
}
const getAllLatestData = () => store.dispatch('home/getAllLatestData')
const getAllSocialArticle = () => store.dispatch('home/getAllSocialArticle')
const fatchSocialFeeds = () => store.dispatch('socialFeed/fatchSocialFeeds')
</script>

<style lang="scss" scoped>
.addFeedsTable {
  height: auto;
}
.profile {
  width: 550px;
  height: 100%;
  top: 0px;
  z-index: 9999999999;
}

.scroll {
  scrollbar-color: #e4801d #ececec; /* Firefox 64 */
  overflow-x: auto;
  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: #e4801d;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #e4801d;
  }
}

.form-card > .error {
  margin-top: 0px;
}

@media (max-width: 767px) {
  .profile {
    width: 100%;
    height: 100%;
    top: 0;
    z-index: 9999999999;
  }
  .scroll {
    overflow-x: auto;
    scrollbar-color: #e4801d #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #e4801d;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #e4801d;
    }
  }
}

.right-sidebar-trans-enter-from,
.right-sidebar-trans-leave-to {
  right: -100%;
}

.right-sidebar-trans-enter-to,
.right-sidebar-trans-leave-from {
  right: 0;
}

.right-sidebar-trans-enter-active {
  transition: all 0.8s ease-in-out;
}

.right-sidebar-trans-leave-active {
  transition: all 0.8s ease-in-out;
}

.content-wrapper {
  height: calc(100% - 40px) !important;
}

.overlay-web {
  position: fixed;
  top: 60px;
  bottom: 0;
  left: 100px;
  right: 0;
  z-index: 101;
  background-color: rgba(255, 255, 255, 0.1);
  opacity: 1;
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  pointer-events: all;
}

@media (max-width: 767px) {
  .overlay-web {
    left: 0 !important;
    height: 100%;
  }
  .min-width-27 {
    min-width: 27rem;
  }
  .scroll {
    overflow-x: hidden;
  }
  .table-scroll {
    overflow-x: auto;
  }
}

.fadeIn-enter-from,
.fadeIn-leave-to {
  opacity: 0;
}

.fadeIn-enter-to,
.fadeIn-leave-from {
  opacity: 1;
}

.fadeIn-enter-active,
.fadeIn-leave-active {
  transition: all 0.5s ease-in-out;
}

.fadeInParent-enter-from,
.fadeInParent-leave-to {
  opacity: 0;
}

.fadeInParent-enter-to,
.fadeInParent-leave-from {
  opacity: 1;
}

.fadeInParent-enter-active,
.fadeInParent-leave-active {
  transition: all 0.5s ease-in-out;
}

.animLeft {
  transform: translateX(0);
  animation-name: changePositionLeft;
  animation-duration: 1s;
  animation-fill-mode: forwards;
}

@keyframes changePositionLeft {
  to {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
    right: 92%;
  }
}

.animRight {
  transform: translateX(0);
  animation-name: changePositionRight;
  animation-duration: 1s;
}

@keyframes changePositionRight {
  from {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
    right: 92%;
  }
  to {
    -webkit-transform: translateX(0%);
    transform: translateX(0%);
    right: 0%;
  }
}
/*end right sidebar section transition*/

.demo1 {
  height: 180px;
  transition: height 1s 0.5s;
}
.demo2 {
  height: 0;
  transition: height 1s 0.5s;
}

.main-content {
  height: calc(100% - 42px); // this 42 is bottom button height
}

.min-width- {
  &4 {
    min-width: 4rem;
  }
  &8 {
    min-width: 7rem;
  }
  &17 {
    min-width: 17rem;
  }
}

// .min-width-10{

// }

.fadeInBtn-enter-active,
.fadeInBtn-leave-active {
  transition: opacity 1s;
}
.fadeInBtn-enter,
.fadeInBtn-leave-to {
  opacity: 0;
}

.notice-enter-active,
.notice-leave-active {
  transition: opacity 1s;
}
.notice-enter,
.notice-leave-to {
  opacity: 0;
}

.toggle-btn-wrapper {
  @apply relative inline-block w-9 min-w-9  mr-2 align-middle select-none transition duration-200 ease-in;
}

.toggle-input {
  @apply outline-none focus:outline-none toggle-checkbox absolute block rounded-full bg-white appearance-none cursor-pointer;
}

.toggle-input-label {
  @apply block overflow-hidden h-5 rounded-full bg-ash-default transition-all
                    duration-800
                    ease-in-out cursor-pointer;
}

.toggle-checkbox {
  width: 16px;
  height: 16px;
  border: 0px;
  top: 2px;
  left: 2px;
  transition: all 0.5s ease-in-out;
  background-color: #ffffff;
  &:checked {
    @apply right-0;
    left: 18px;
    transition: all 0.5s ease-in-out;
    background-color: #ffffff;
  }
  &:checked + .toggle-label {
    transition: all 0.5s ease-in-out;
    @apply bg-yellow-primary;
  }
}

.exclude_toggle-input {
  @apply outline-none focus:outline-none exclude_toggle-checkbox absolute block rounded-full bg-[#222831] appearance-none cursor-pointer;
}

.exclude_toggle-input-label {
  @apply block overflow-hidden h-5 rounded-full bg-white transition-all
                    duration-800
                    ease-in-out cursor-pointer;
}

.exclude_toggle-checkbox {
  width: 16px;
  height: 16px;
  border: 0px;
  top: 2px;
  left: 2px;
  transition: all 0.5s ease-in-out;
  background-color: #222831;
  &:checked {
    @apply right-0;
    left: 18px;
    transition: all 0.5s ease-in-out;
    background-color: #ffffff;
  }
  &:checked + .toggle-label {
    transition: all 0.5s ease-in-out;
    @apply bg-yellow-primary;
  }
}
.parent-scroll {
  overflow-x: hidden;
}
.profile-image {
  @apply w-13 h-13;
}

.min-w-3 {
  min-width: 3.125rem !important;
}
.min-h-3 {
  min-width: 3.125rem !important;
}
.tooltip {
  @apply absolute
  bg-yellow-primary
  text-white
  z-100
  left-0
  text-left
  invisible
  p-1.5
  px-4
  rounded-xl 
  shadow-lg w-44
  whitespace-normal break-words;
}

.has-tooltip:hover .tooltip {
  @apply visible;
  transition: all 0.3s linear;
}
.text-width {
  max-width: calc(100% - 66px);
}
.username-clamp {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.min-width-17 {
  min-width: 16rem;
}
</style>