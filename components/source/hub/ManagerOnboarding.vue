<script setup lang="ts">
import { useStore } from 'vuex'
import { useVuelidate } from '@vuelidate/core'
import { email } from '@vuelidate/validators'
import { NEW_USER } from '~/constants/urls'

const emit = defineEmits<{
  (e: 'hide-manager-onboarding'): void
  (e: 'set-email', email: string): void
}>()
const props = defineProps({
  allAddedGroup: {
    type: Object,
    default: null,
  },
  groundedFeed: {
    type: Object,
    default: null,
  },
  clearField: {
    type: Boolean,
    default: false,
  },
  addGroup: {
    type: Boolean,
    default: false,
  },
  createdNewUserData: {
    type: Object,
    default: null,
  },
})
const config = useRuntimeConfig()
const store = useStore()
const router = useRouter()
const { fetch } = useFetched()

const showNewGroup = ref<boolean>(false)
const setCreateNewGroup = ($event: boolean) => {
  showNewGroup.value = $event
}
const showSelectUser = ref<boolean>(false)
const toggleSelectUser = ($event: boolean) => {
  showSelectUser.value = $event
}
interface GuestUsers {
  id: number
  value: number
  text: string
}
const guestUsers = ref<UserType[]>([])
//   [
//   {
//     id: 1,
//     value: 1,
//     text: 'SharpArchive',
//   },
//   {
//     id: 2,
//     value: 2,
//     text: 'GreenStar',
//   },
// ])
const selectedUser = ref<string>('Select User')
const setSelectUser = ($event: string) => {
  console.log(selectedUser.value, 'selectedUser')
  console.log($event, '$event')
}
const guestEmail = ref<string>('')
const rules = {
  guestEmail: {
    email,
  },
}
const v$ = useVuelidate(rules, { guestEmail })

const accountItems = computed(() => store.state.social.accountItems)
const sideBarAccountItems = computed(
  () => store.state.social.sideBarAccountItems,
)
const accountType = computed(() => store.state.social.accountType)
const accountItem = computed(() => store.state.social.accountItem)
const { getRouteOnProvider } = useSourceApi()
const launchSelectedAccountItems = () => {
  store.commit('social/CHECK_ACCOUNT_TYPE_BEFORE_LUNCH')
  store.commit('social/SET_SIDEBAR_ACCOUNT_ITEMS_AT_LAUNCH')
  getRouteOnProvider(
    accountItem.value.provider,
    accountType.value,
    accountItem.value.username,
  )
  emit('hide-manager-onboarding')
}
const addNewAccountItem = () => {
  store.commit('social/SET_NEW_ACCOUNT_ITEM', {
    id: accountItems.value.length + 1,
    type: 'socials',
    profilePic: `/social/greenstar.png`,
    provider: 'LinkedIn',
    name: 'GreenStar Advisors',
    username: '@greenstar',
    count: 0,
    toggleSelect: true,
    select: false,
  })
}
onMounted(() => {
  getAllUsers()
})
interface UserType {
  alertPermission: string
  avatar: string
  city: string
  country: string
  device: {}
  email: string
  firstName: string
  id: number
  isExpired: boolean
  lastLogin: string
  lastName: string
  phone: string
  state: string
  streetAddress: string
  userPermission: string
  zipCode: string
  value?: number
  text?: string
}
interface getAllUsersType {
  data: { friends: [UserType]; owner: UserType }
  success: boolean
}
const getAllUsers = async () => {
  try {
    const { success, data } = (await fetch(NEW_USER)) as getAllUsersType
    if (success) {
      data.friends.forEach((user) => {
        user.value = user.id
        user.text = `${user.firstName} ${user.lastName}`
      })
      guestUsers.value = data.friends
    }
  } catch (err) {
    console.log(err)
  }
}
watch(
  () => props.createdNewUserData,
  (data) => {
    data.value = data.id
    data.text = `${data.firstName} ${data.lastName}`
    guestUsers.value.unshift(data)
  },
)
</script>

<template>
  <div
    class="lg:w-[652px] w-[90%] max-h-[720px] h-full z-[99] rounded-2xl bg-white overflow-hidden absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
  >
    <div
      class="px-6 py-4 border-b border-[#F1F2F6] flex justify-between items-center"
    >
      <h2 class="text-[#505050] text-lg font-semibold">
        Social Media Manager Onboarding
      </h2>
      <ClientOnly>
        <fa
          class="text-base cursor-pointer"
          :icon="['fas', 'times']"
          @click="emit('hide-manager-onboarding')"
        />
      </ClientOnly>
    </div>
    <div class="p-6 pr-0 w-full h-full">
      <div
        class="pr-6 w-full h-[calc(100%-61px)] overflow-y-auto custom-scroll"
      >
        <div class="w-full flex flex-col space-y-4 text-lg text-[#333333]">
          <div class="flex flex-col space-y-4">
            <div class="flex flex-col space-y-1.5">
              <p>
                Would you like to add a guest to your account who will be
                posting for you?
              </p>
              <InputsYesNoToggle
                left-text="Yes"
                right-text="No"
                bg-color="#F1F2F6"
                active-bg-color="#4A71D4"
                text-color="#525252"
                active-text-color="#fff"
                @show-select-user="toggleSelectUser"
              />
            </div>
            <div v-if="showSelectUser" class="flex space-x-4">
              <div class="w-full max-w-[221px] text-lg font-bold selectSearch">
                <InputsSelectInput
                  id="pastMonth1"
                  v-model="selectedUser"
                  class="selectSearch"
                  class-style-name="searchPageScrollStyle searchPageScrollWidth search-select-input"
                  :options="guestUsers"
                  place-holder="Select User"
                  :place-holder-disabled="true"
                  color="#FFFFFF"
                  background="#4A71D4"
                  caret-bg="#4A71D4"
                  scroll-color="#4A71D4"
                  @update:modelValue="setSelectUser($event)"
                >
                </InputsSelectInput>
              </div>
              <div class="flex-grow h-10 max-h-[40px]">
                <input
                  v-model="guestEmail"
                  type="text"
                  placeholder="Or, Enter their Email"
                  autocomplete="off"
                  class="w-full h-full rounded-full py-2 pl-6 pr-2.5 outline-none focus:outline-none bg-[#F1F2F6] text-[#707070] placeholder-[#707070] text-left align-start"
                  @keyup="v$.guestEmail.$touch()"
                  @keypress.enter.prevent="
                    store.commit('setting/SET_SHOW_PROFILE', true),
                      emit('set-email', guestEmail)
                  "
                />
                <div
                  v-if="v$.guestEmail.$error && v$.guestEmail.email.$invalid"
                  class="text-red-400 text-xs mt-0 pl-2"
                >
                  The Email is Invalid
                </div>
              </div>
            </div>
          </div>
          <div class="flex flex-col space-y-4">
            <div class="flex flex-col space-y-1.5">
              <p>Will you be posting for multiple people or groups?</p>
              <InputsYesNoToggle
                left-text="Yes"
                right-text="No"
                bg-color="#F1F2F6"
                active-bg-color="#4A71D4"
                text-color="#525252"
                active-text-color="#fff"
                @show-new-group="setCreateNewGroup"
              />
            </div>
            <div v-if="showNewGroup">
              <SourceHubManagerNewGroup
                :addGroup="addGroup"
                :allAddedGroup="allAddedGroup"
                :groundedFeed="groundedFeed"
                :clearField="clearField"
              />
            </div>
          </div>
        </div>
        <div class="mt-4">
          <div class="w-full">
            <p>
              We have these as your social media accounts, would you like to add
              more or hide any of these?
            </p>
            <div class="mt-1 flex flex-col space-y-2">
              <SourceHubManagerAccountItem
                v-for="accountItem in accountItems"
                :key="accountItem.id"
                :account-item="accountItem"
              />
            </div>
            <div class="flex justify-end mt-4">
              <!-- @click="addNewAccountItem" -->
              <BaseButton
                class="w-[165px] h-[39px] text-base bg-[#F1F2F6] text-[#4A71D4] font-semibold"
                text="Add More Feeds"
                @click="
                  store.commit('socialFeed/SHOW_ADD_FEED_MODAL', true),
                    store.commit('social/SET_MANAGER_ONBOARDING', false)
                "
              />
            </div>
          </div>
          <div class="flex justify-center space-x-4 mt-[32px]">
            <BaseButton
              class="w-[104px] h-[39px] text-base bg-transparent border-2 border-[#4A71D4] text-[#4A71D4] font-semibold"
              text="Cancel"
              @click="emit('hide-manager-onboarding')"
            />
            <BaseButton
              class="w-[104px] h-[39px] text-base bg-[#4A71D4] text-[#FFFFFF] font-semibold"
              text="Launch"
              @click="launchSelectedAccountItems"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
