<script setup lang="ts">
import { useStore } from 'vuex'
import SharedIconHubEmailsCalenderIcon from '~/components/shared/icon/hub/emails/CalenderIcon.vue'

interface MoreMenuOptions {
  id: number
  label: string
  icon?: Component | string
  checked?: boolean
  action?: () => void
}

const store = useStore()
const moreMenuOptions = ref<MoreMenuOptions[]>([
  {
    id: 1,
    label: 'Default to full screen',
    checked: false,
    action: () => toggleOption(1),
  },
  {
    id: 2,
    label: 'Label',
    action: () => toggleLabel(),
  },
  {
    id: 3,
    label: 'Plain text mode',
    checked: false,
    action: () => toggleOption(3),
  },
  {
    id: 4,
    label: 'Print',
    action: () => {},
  },
  {
    id: 5,
    label: 'Check spelling',
    action: () => showCheckSpelling(),
  },
  {
    id: 6,
    label: 'Set up a time to meet',
    icon: markRaw(SharedIconHubEmailsCalenderIcon),
    action: () => {},
  },
])

const toggleOption = (id: number) => {
  const option = moreMenuOptions.value.find((item) => item.id === id)
  if (option && option?.checked !== undefined) {
    option.checked = !option.checked
  }
  if (id === 1) {
    store.commit('emails/SET_GMAIL_COMPOSE_MORE_OPTION', {
      defaultFullScreen: option?.checked,
    })
  } else if (id === 3) {
    store.commit('emails/SET_GMAIL_COMPOSE_MORE_OPTION', {
      plainTextMode: option?.checked,
    })
  }
  showLabel.value = false
}

const showLabel = ref(false)
const toggleLabel = () => {
  showLabel.value = !showLabel.value
}

const gmailComposeMoreOption = computed(
  () => store.state.emails.gmailComposeMoreOption,
)

const showCheckSpelling = () => {
  store.commit('emails/SET_SHOW_SPELLING_CHECK_MODAL')
}

onMounted(() => {
  moreMenuOptions.value.forEach((item) => {
    if (item.id === 1) {
      item.checked = gmailComposeMoreOption.value.defaultFullScreen
    } else if (item.id === 3) {
      item.checked = gmailComposeMoreOption.value.plainTextMode
    }
  })
})
</script>

<template>
  <div
    class="menu-box absolute xl:left-0 right-0 top-[-233px] w-[240px] py-[1px] text-[#525252] shadow-[2px_2px_4px_#22283114]"
  >
    <div
      v-for="moreMenuOption in moreMenuOptions"
      :key="moreMenuOption.id"
      class="grid grid-cols-[16px_1fr] gap-x-2.5 items-center px-[18px] py-[7px] hover:bg-[#F1F2F6] cursor-pointer"
      :class="
        moreMenuOption.id === 1 ||
        moreMenuOption.id === 3 ||
        moreMenuOption.id === 5
          ? 'border-b border-[#C2C2C2] border-opacity-50'
          : ''
      "
      @click="moreMenuOption.action"
    >
      <SharedIconCheckIcon
        v-if="moreMenuOption?.checked"
        class="w-[18px] h-4"
      />

      <component v-else-if="moreMenuOption?.icon" :is="moreMenuOption.icon" />

      <div v-else class="w-3.5"></div>

      <div class="flex justify-between items-center">
        <p class="text-[#525252] whitespace-nowrap">
          {{ moreMenuOption.label }}
        </p>
        <SharedIconHubEmailsDownArrow
          v-if="moreMenuOption.id === 2 || moreMenuOption.id === 6"
          class="transform rotate-[-90deg] w-[8px] h-2"
        />
      </div>
    </div>
    <SourceHubEmailsLabelAsMenu
      v-if="showLabel"
      class="bottom-0 left-[96%] z-1 single-message-label-as-menu"
      :showAddStar="true"
      @click.stop=""
      @hide-labelasmenu="toggleLabel()"
    />
  </div>
</template>

<style scoped>
.menu-box {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
</style>
