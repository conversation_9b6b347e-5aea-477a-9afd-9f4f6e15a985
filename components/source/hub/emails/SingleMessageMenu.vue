<script setup lang="ts">
import { useStore } from 'vuex'
import MoveToIcon from '~/components/shared/icon/hub/emails/MoveToIcon.vue'
import Reply from '~/components/shared/icon/hub/emails/Reply.vue'
import ReplyAll from '~/components/shared/icon/hub/emails/ReplyAll.vue'
import Forward from '~/components/shared/icon/hub/emails/Forward.vue'
import Attachment from '~/components/shared/icon/hub/emails/Attachment.vue'
import ArchiveIcon from '~/components/shared/icon/hub/emails/ArchiveIcon.vue'
import DeleteIcon from '~/components/shared/icon/hub/emails/DeleteIcon.vue'
import UnreadMessageIcon from '~/components/shared/icon/hub/emails/UnreadMessageIcon.vue'
import ReadMessageIcon from '~/components/shared/icon/hub/emails/ReadMessageIcon.vue'
import Snooze from '~/components/shared/icon/hub/emails/Snooze.vue'
import AddTasks from '~/components/shared/icon/hub/emails/AddTasks.vue'
import Label from '~/components/shared/icon/hub/emails/Label.vue'
import Mute from '~/components/shared/icon/hub/emails/Mute.vue'
import NewWindow from '~/components/shared/icon/hub/emails/NewWindow.vue'

const store = useStore()

interface Props {
  toggleSingleCheckBox?: Function
  checkedAll?: boolean
  showMinus?: boolean
  messageRead?: boolean
  specificId?: number
}

const props = withDefaults(defineProps<Props>(), {
  toggleSingleCheckBox: () => {},
  checkedAll: false,
  showMinus: false,
  messageRead: false,
  specificId: 0,
})

interface SingleMessageMenuOptions {
  id: number
  image: string | Component
  image1: string | Component
  title: string
  title1: string
  function: (payload: MouseEvent) => void
}

const singleMessageMenuOptions = ref<SingleMessageMenuOptions[]>([
  {
    id: 1,
    image: markRaw(MoveToIcon),
    title: 'Move to tab',
    image1: '',
    title1: '',
    function: () => {
      settoggleMoveToTabMenu()
    },
  },
  {
    id: 2,
    image: markRaw(Reply),
    title: 'Reply',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 3,
    image: markRaw(ReplyAll),
    title: 'Reply all',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 4,
    image: markRaw(Forward),
    title: 'Forward',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 5,
    image: markRaw(Attachment),
    title: 'Forward as attachment',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 6,
    image: markRaw(ArchiveIcon),
    title: 'Archive',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 7,
    image: markRaw(DeleteIcon),
    title: 'Delete',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 8,
    image: markRaw(UnreadMessageIcon),
    title: 'Mark as unread',
    image1: markRaw(ReadMessageIcon),
    title1: 'Mark as read',
    function: () =>
      store.commit('emails/READ_UNREAD_A_SPECIFIC_MESSAGE', props.specificId),
  },
  {
    id: 9,
    image: markRaw(Snooze),
    title: 'Snooze',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 10,
    image: markRaw(AddTasks),
    title: 'Add to Tasks',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 11,
    image: markRaw(MoveToIcon),
    title: 'Move to',
    image1: '',
    title1: '',
    function: () => {
      settoggleMoveToMenu()
    },
  },
  {
    id: 12,
    image: markRaw(Label),
    title: 'Label as',
    image1: '',
    title1: '',
    function: () => {
      settoggleLabelMenu()
    },
  },
  {
    id: 13,
    image: markRaw(Mute),
    title: 'Mute',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 14,
    image: markRaw(NewWindow),
    title: 'Open in new window',
    image1: '',
    title1: '',
    function: () => {},
  },
])

const handleOptionClick = () => {
  store.commit('emails/READ_ALL_MESSAGE')
}
const toggleMoveToMenu = ref<boolean>(false)
const settoggleMoveToMenu = () => {
  toggleMoveToMenu.value = !toggleMoveToMenu.value
  toggleLabelMenu.value = false
  toggleMoveToTabMenu.value = false
}
const toggleLabelMenu = ref<boolean>(false)
const settoggleLabelMenu = () => {
  toggleLabelMenu.value = !toggleLabelMenu.value
  toggleMoveToMenu.value = false
  toggleMoveToTabMenu.value = false
}
const toggleMoveToTabMenu = ref<boolean>(false)
const settoggleMoveToTabMenu = () => {
  toggleMoveToTabMenu.value = !toggleMoveToTabMenu.value
  toggleLabelMenu.value = false
  toggleMoveToMenu.value = false
}
</script>

<template>
  <div
    class="flex flex-col select-extend-menu-box space-y-3.5 bg-white py-2 fixed"
  >
    <div class="flex flex-col">
      <div
        v-for="singleMessageMenuOption in singleMessageMenuOptions"
        :key="singleMessageMenuOption.id"
        class="relative flex justify-between items-center !space-x-2 px-3.5 py-2 hover:bg-[#F1F2F6] cursor-pointer"
        :class="
          singleMessageMenuOption.id === 1 ||
          singleMessageMenuOption.id === 5 ||
          singleMessageMenuOption.id === 10 ||
          singleMessageMenuOption.id === 13
            ? 'border-b border-[#C2C2C2]'
            : ''
        "
        @click.stop="singleMessageMenuOption.function"
      >
        <div class="flex items-center !space-x-2">
          <component
            v-if="singleMessageMenuOption.id !== 8"
            :is="singleMessageMenuOption.image"
            :class="singleMessageMenuOption.id === 10 ? ' opacity-50' : ''"
          />
          <component
            v-else-if="singleMessageMenuOption.id === 8"
            :is="
              messageRead
                ? singleMessageMenuOption.image
                : singleMessageMenuOption.image1
            "
          />
          <p
            v-if="singleMessageMenuOption.id !== 8"
            class="text-[#525252]"
            :class="singleMessageMenuOption.id === 10 ? ' opacity-50' : ''"
          >
            {{ singleMessageMenuOption.title }}
          </p>
          <p
            v-else-if="singleMessageMenuOption.id === 8"
            class="text-[#525252]"
          >
            {{
              messageRead
                ? singleMessageMenuOption.title
                : singleMessageMenuOption.title1
            }}
          </p>
        </div>
        <SharedIconHubEmailsDownArrow
          v-if="
            singleMessageMenuOption.id === 1 ||
            singleMessageMenuOption.id === 11 ||
            singleMessageMenuOption.id === 12
          "
          class="transform rotate-[-90deg] w-[10px] h-4"
        />
        <SourceHubEmailsMoveToMenu
          v-if="toggleMoveToMenu && singleMessageMenuOption.id === 11"
          class="bottom-0 left-[96%] z-1 single-message-move-to-menu"
          @click.stop="toggleMoveToMenu = true"
        />
        <SourceHubEmailsLabelAsMenu
          v-if="toggleLabelMenu && singleMessageMenuOption.id === 12"
          class="bottom-0 left-[96%] z-1 single-message-label-as-menu"
          @click.stop="toggleLabelMenu = true"
        />
        <SourceHubEmailsMoveToTabMenu
          v-if="toggleMoveToTabMenu && singleMessageMenuOption.id === 1"
          class="top-[-23%] left-[96%] z-1 single-message-move-to-tab-menu"
          @click.stop="toggleMoveToTabMenu = true"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.select-menu-box {
  min-width: 176px;
  max-width: 176px;
  height: fit-content;
  z-index: 1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
.select-extend-menu-box {
  min-width: 244px;
  max-width: 244px;
  height: fit-content;
  z-index: 1000;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
</style>
