<script setup lang="ts">
import type { Editor } from '@tiptap/vue-3'
import { useStore } from 'vuex'

interface Props {
  editor?: Editor | null
}

const props = withDefaults(defineProps<Props>(), {
  editor: null,
})

const store = useStore()
interface SignatureMenuOptions {
  id: number
  label: string
  text: string
  description: string
  selected: boolean
}
const localCreatedSignatures = ref([])
const createdSignatures = computed(() => {
  store.state.emails.createdSignature.forEach((element) => {
    if (element.id === 1) {
      element.selected = true
    } else {
      element.selected = false
    }
  })
  console.log(store.state.emails.createdSignature)
  return store.state.emails.createdSignature
})
const signatureMenuOptions = ref<SignatureMenuOptions[]>([
  {
    id: 0,
    label: 'Manage signature',
    selected: false,
  },
  // {
  //   id: 2,
  //   label: 'No signature',
  //   selected: true,
  // },
])
const start = ref(0)
const end = ref(0)
const docSize = ref('')
const lastNode = ref('')
const emit = defineEmits(['selected-signature'])
const setSignatureSelect = (id: number) => {
  signatureMenuOptions.value.forEach(
    (signatureMenuOption: SignatureMenuOptions) => {
      if (signatureMenuOption.id === id) {
        signatureMenuOption.selected = true
      } else {
        signatureMenuOption.selected = false
      }
    },
  )
  createdSignatures.value.forEach((createdSignature: SignatureMenuOptions) => {
    console.log(id)
    if (createdSignature.id === id) {
      if (!createdSignature.selected) {
        createdSignature.selected = true
        if (id === 1) {
          // docSize.value = props.editor?.state.doc.content.size
          // lastNode.value = props.editor?.state.doc.lastChild
          // if (lastNode.value) {
          //   const pos = docSize.value - lastNode.value.nodeSize
          //   props.editor?.commands.command(({ tr, dispatch }) => {
          //     tr.delete(pos, docSize.value)
          //     dispatch(tr)
          //     return true
          //   })
          // }

          // const { state, view } = props.editor!
          // const tr = state.tr

          // state.doc.descendants((node, pos) => {
          //   if (node.attrs['data-external-id'] === 'signature') {
          //     tr.delete(pos, pos + node.nodeSize)
          //   }
          // })

          // view.dispatch(tr)
          // updateSignature('')
          removeSignature()
        } else {
          updateSignature(createdSignature.description)
          // // Move the cursor to the end before inserting
          // props.editor?.commands.focus('end')
          // start.value = props.editor?.state.doc.content.size
          // // Insert raw HTML
          // props.editor?.commands.insertContent(
          //   `<p id="signature">${createdSignature.description}</p>`,
          // )
          // docSize.value = props.editor?.state.doc.content.size
          // lastNode.value = props.editor?.state.doc.lastChild
          // if (lastNode.value) {
          //   const pos = docSize.value - lastNode.value.nodeSize
          //   props.editor?.commands.command(({ tr, dispatch }) => {
          //     tr.delete(pos, docSize.value)
          //     dispatch(tr)
          //     return true
          //   })
          // }
          // end.value = props.editor?.state.doc.content.size
          // console.log(start.value, end.value, createdSignature, 'world')
        }
      }
      // emit('selected-signature', createdSignature.description)
    } else {
      createdSignature.selected = false
    }
  })
}

function updateSignature(html: string) {
  const { state, view } = props.editor!
  const tr = state.tr

  // Remove old signature node(s)
  state.doc.descendants((node, pos) => {
    if (node.type.name === 'signature') {
      tr.delete(pos, pos + node.nodeSize)
    }
  })

  // Add new signature node at the end
  props.editor?.commands.focus('end')
  // 4. Insert new signature node
  tr.insert(
    tr.doc.content.size,
    state.schema.nodes.signature.create({ content: html }),
  )
  // 5. Apply changes
  view.dispatch(tr)
}
function removeSignature() {
  const { state, view } = props.editor!
  const tr = state.tr

  // Remove all signature nodes in the document
  state.doc.descendants((node, pos) => {
    if (node.type.name === 'signature') {
      tr.delete(pos, pos + node.nodeSize)
    }
  })

  view.dispatch(tr)

  // Manually remove the DOM node created by addNodeView() containing '-- '
  const signatureElements = view.dom.querySelectorAll('[data-signature]')
  signatureElements.forEach((el) => el.remove())
}
</script>

<template>
  <div
    class="menu-box absolute xl:left-0 right-0 bottom-[100%] w-[188px] py-[1px]"
  >
    <div
      class="grid grid-cols-[16px_1fr] gap-x-2.5 items-center px-[18px] py-[7px] border-opacity-50 border-b border-[#C2C2C2] last:border-none hover:bg-[#F1F2F6] cursor-pointer"
      v-for="signatureMenuOption in signatureMenuOptions"
      :key="signatureMenuOption.id"
      @click="setSignatureSelect(signatureMenuOption.id)"
    >
      <fa
        class="text-[#707070]"
        :class="signatureMenuOption.selected ? 'opacity-100' : 'opacity-0'"
        :icon="['fa', 'fa-check']"
      />
      <p class="#525252 whitespace-nowrap">
        {{ signatureMenuOption.label }}
      </p>
    </div>
    <div
      class="grid grid-cols-[16px_1fr] gap-x-2.5 items-center px-[18px] py-[7px] border-opacity-50 border-b border-[#C2C2C2] last:border-none hover:bg-[#F1F2F6] cursor-pointer"
      v-for="createdSignature in createdSignatures"
      :key="createdSignature.id"
      @click="setSignatureSelect(createdSignature.id)"
    >
      <fa
        class="text-[#707070]"
        :class="createdSignature.selected ? 'opacity-100' : 'opacity-0'"
        :icon="['fa', 'fa-check']"
      />
      <p class="#525252 whitespace-nowrap">
        {{ createdSignature.text }}
      </p>
    </div>
  </div>
</template>

<style scoped>
.menu-box {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
</style>
