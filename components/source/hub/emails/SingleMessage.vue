<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const selectedEmailMessage = computed(
  () => store.state.emails.selectedEmailMessage,
)
const accountItem = computed(() => {
  return store.state.social.accountItem
})
const showReplyBox = ref<boolean>(false)
const setShowReplyBox = () => {
  showReplyBox.value = true
}
const showEmojiPicker = ref<boolean>(false)
const toggleEmojiPicker = () => {
  showEmojiPicker.value = !showEmojiPicker.value
  showMessageMenu.value = false
}
const showMessageMenu = ref<boolean>(false)
const toggleMessageMenu = () => {
  showMessageMenu.value = !showMessageMenu.value
  showEmojiPicker.value = false
}
interface Emoji {
  count: number
  emoji: string
}
const selectedEmojis = ref<Emoji[]>([])
const addEmoji = (emoji: string) => {
  if (selectedEmojis.value && selectedEmojis.value.length > 0) {
    const existingEmoji = selectedEmojis.value.find(
      (e: Emoji) => e.emoji === emoji,
    )
    if (existingEmoji) {
      existingEmoji.count += 1
    } else {
      selectedEmojis.value.push({
        count: 1,
        emoji,
      })
    }
  } else {
    selectedEmojis.value.push({
      count: 1,
      emoji,
    })
  }
  showEmojiPicker.value = false
}
onMounted(() => {
  document.addEventListener('click', (event) => {
    showEmojiPicker.value = false
    showMessageMenu.value = false
  })
})
onUnmounted(() => {
  document.removeEventListener('click', () => {
    showEmojiPicker.value = false
    showMessageMenu.value = false
  })
})
const showForwardBox = ref<boolean>(false)
const forwardText = ref<string>('')
const setShowForwardBox = () => {
  const subject = selectedEmailMessage.value.subject
    .replace(/<[^>]*>/g, '')
    .trim()
  forwardText.value = `<p>---------- Forwarded message ---------</p><p>From: &lt;<EMAIL>&gt;</p><p>Date: Mon, May 13, 2024 at 2:35 AM</p> <p>Subject: ${subject}</p><p>To: &lt;${accountItem.value.username}&gt;</p>`
  showForwardBox.value = true
}
const hideReplyBox = () => {
  showReplyBox.value = false
  showForwardBox.value = false
}
</script>

<template>
  <div class="w-full h-auto p-4 bg-white">
    <div class="grid grid-cols-[40px,1fr] gap-x-4">
      <div></div>
      <div>
        <h2
          class="text-xl text-[#333333] font-semibold"
          v-html="selectedEmailMessage.subject"
        ></h2>
      </div>
    </div>
    <div class="grid grid-cols-[40px,1fr] gap-x-4 mt-5">
      <SourceAccountLogo
        :account-profile-pic="selectedEmailMessage.profileUrl"
      />
      <div class="flex justify-between">
        <div>
          <p>
            <span class="text-[#333333] font-semibold">{{
              selectedEmailMessage.from
            }}</span>
            <span class="text-sm text-[#707070]"
              >&lt;<EMAIL>&gt;</span
            >
          </p>
          <div class="flex space-x-2 items-center">
            <p class="">to me</p>
            <SharedIconHubEmailsDownArrow class="w-2 h-1.5" />
          </div>
        </div>
        <div class="flex space-x-6 items-center">
          <p class="text-sm text-[#707070]">
            Nov 27, 2024, 7:30 PM (8 hours ago)
          </p>
          <div class="size-9 flex justify-center items-center">
            <SharedIconHubEmailsStarIcon
              v-if="!selectedEmailMessage.favourite"
              class="cursor-pointer min-w-4 min-h-4"
              color="#525252"
              @click.stop="
                store.commit(
                  'emails/SET_FAVOURITE_MESSAGE',
                  selectedEmailMessage.id,
                )
              "
            />
            <ClientOnly v-else>
              <fa
                class="w-4 h-4 min-w-4 min-h-4 text-yellow-midlight cursor-pointer"
                :icon="['fas', 'star']"
                @click.stop="
                  store.commit(
                    'emails/SET_FAVOURITE_MESSAGE',
                    selectedEmailMessage.id,
                  )
                "
              />
            </ClientOnly>
          </div>
          <div class="relative !ml-0">
            <div
              class="relative z-[10] cursor-pointer size-9 flex justify-center items-center rounded-full"
              :class="showEmojiPicker ? 'bg-[#F1F2F6]' : ''"
              @click.stop="toggleEmojiPicker"
            >
              <SharedIconHubEmailsEmojiIcon class="w-4 h-4 min-w-4 min-h-4" />
            </div>
            <EmojiPicker
              v-if="showEmojiPicker"
              @click.stop=""
              class="emoji-picker absolute top-[36%] right-0 z-1"
              @selected-emoji="addEmoji"
            />
          </div>
          <div class="!ml-0 size-9 flex justify-center items-center">
            <SharedIconHubEmailsReply
              class="w-4 h-4 min-w-4 min-h-4"
              color="#525252"
            />
          </div>
          <div class="relative !ml-0">
            <div
              class="!ml-0 cursor-pointer size-9 flex justify-center items-center rounded-full"
              :class="showMessageMenu ? 'bg-[#F1F2F6]' : ''"
              @click.stop="toggleMessageMenu"
            >
              <SharedIconHubEmailsThreeDotMenuIcon color="#525252" />
              <SourceHubEmailsMessageMenu
                v-if="showMessageMenu"
                class="absolute top-full right-0 z-1"
                :specificId="selectedEmailMessage.id"
                :messageRead="selectedEmailMessage.read"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-[40px,1fr] gap-x-4 mt-10">
      <div></div>
      <div>
        <p
          class="text-base text-[#979797] font-semibold"
          v-html="selectedEmailMessage.description"
        ></p>
      </div>
      <div class="flex space-x-2 mt-4">
        <span
          v-for="(emoji, index) in selectedEmojis"
          :key="index"
          class="flex items-center space-x-1 bg-[#F1F2F6] px-2 py-1 rounded-full text-sm text-[#333333]"
        >
          <span>{{ emoji.emoji }}</span>
          <span class="text-xs text-[#707070] font-semibold">{{
            emoji.count
          }}</span>
        </span>
      </div>
    </div>
    <div
      v-if="!showReplyBox && !showForwardBox"
      class="grid grid-cols-[40px,1fr] gap-x-4 mt-10"
    >
      <div></div>
      <div class="flex space-x-2">
        <button
          class="flex !space-x-2 justify-center items-center px-6 py-1.5 rounded-full text-sm border border-[#525252] text-[#979797] font-semibold"
          @click="setShowReplyBox"
        >
          <SharedIconHubEmailsReply
            class="w-4 h-[11px] min-w-4 min-h-[11px]"
            color="#525252"
          />
          <p>Reply</p>
        </button>
        <button
          class="flex !space-x-2 justify-center items-center px-6 py-1.5 rounded-full text-sm border border-[#525252] text-[#979797] font-semibold"
          @click="setShowForwardBox"
        >
          <SharedIconHubEmailsForward
            class="w-4 h-[11px] min-w-4 min-h-[11px]"
            color="#525252"
          />
          <p>Forward</p>
        </button>
      </div>
    </div>
    <div
      v-if="showReplyBox || showForwardBox"
      class="grid grid-cols-[40px,1fr] gap-x-4 mt-10"
    >
      <SourceAccountLogo :account-profile-pic="accountItem.profilePic" />
      <SourceHubEmailsReplyBox
        class="reply-box px-4 py-[18px]"
        :hideReplyBox="hideReplyBox"
        :showForwardBox="showForwardBox"
        :forwardText="forwardText"
      />
    </div>
  </div>
</template>

<style scoped>
.reply-box {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 1px 2px 8px #********;
  border-radius: 8px;
}
</style>
