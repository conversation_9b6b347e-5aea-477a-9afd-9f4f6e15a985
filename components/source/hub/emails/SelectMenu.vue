<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()

interface Props {
  toggleSingleCheckBox?: Function
}

const props = withDefaults(defineProps<Props>(), {
  toggleSingleCheckBox: () => {},
})

interface SelectMenuOptions {
  id: number
  title: string
}

const selectMenuOptions = ref<SelectMenuOptions[]>([
  {
    id: 1,
    title: 'All',
  },
  {
    id: 2,
    title: 'None',
  },
  {
    id: 3,
    title: 'Read',
  },
  {
    id: 4,
    title: 'Unread',
  },
  {
    id: 5,
    title: 'Starred',
  },
  {
    id: 6,
    title: 'Unstarred',
  },
])

const handleOptionClick = (option: SelectMenuOptions) => {
  store.commit('emails/SET_CHECKED_READ_EMAIL_MESSAGES', option)
  props.toggleSingleCheckBox()
}
</script>

<template>
  <div
    class="select-menu-box flex flex-col space-y-3.5 bg-white py-2 absolute top-10 left-0"
  >
    <div
      v-for="selectMenuOption in selectMenuOptions"
      :key="selectMenuOption.id"
      class="px-6 hover:bg-[#F1F2F6] cursor-pointer"
      @click="handleOptionClick(selectMenuOption)"
    >
      <p class="text-[#525252]">{{ selectMenuOption.title }}</p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.select-menu-box {
  height: fit-content;
  z-index: 1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
</style>
