<script setup lang="ts">
import { useStore } from 'vuex'
import AddTasks from '~/components/shared/icon/hub/emails/AddTasks.vue'
import Attachment from '~/components/shared/icon/hub/emails/Attachment.vue'
import Filter from '~/components/shared/icon/hub/emails/Filter.vue'
import Label from '~/components/shared/icon/hub/emails/Label.vue'
import Mute from '~/components/shared/icon/hub/emails/Mute.vue'
import ReadMessageIcon from '~/components/shared/icon/hub/emails/ReadMessageIcon.vue'
import Snooze from '~/components/shared/icon/hub/emails/Snooze.vue'
import Switch from '~/components/shared/icon/hub/emails/Switch.vue'
import UnreadMessageIcon from '~/components/shared/icon/hub/emails/UnreadMessageIcon.vue'
import Starred from '~/components/shared/icon/hub/emails/sidebar/Starred.vue'
import MarkAsImportant from '~/components/shared/icon/hub/emails/MarkAsImportant.vue'

const store = useStore()
const { saToast, toast } = useCustomToast()
const emit = defineEmits<{
  (e: 'hide-move-labelasmenu', value: boolean): void
  (e: 'openSnooze'): void
  (e: 'closeMenu'): void
}>()

interface Props {
  toggleSingleCheckBox?: Function
  checkedAll?: boolean
  showMinus?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  toggleSingleCheckBox: () => {},
  checkedAll: false,
  showMinus: false,
})
const readUnreadAll = computed(() => store.state.emails.readUnreadAll)
const showhideAdvanceMenu = computed(
  () => store.state.emails.showhideAdvanceMenu,
)
interface MarkAllReadMenu {
  id: number
  image: string | Component
  title: string
  description: string
}

interface ExtendMenu {
  id: number
  image: string | Component
  title: string
  function: (payload: MouseEvent) => void
}
interface AdvanceMenu {
  id: number
  image: string | Component
  title: string
  image1: string | Component
  title1: string
  function: (payload: MouseEvent) => void
}

const markAllReadMenu = ref<MarkAllReadMenu>({
  id: 1,
  image: markRaw(ReadMessageIcon),
  title: 'Make all as read',
  description: 'Select messages to see more actions',
})

const extendMenu = ref<ExtendMenu[]>([
  {
    id: 1,
    image: markRaw(Snooze),
    title: 'Snooze',
    function: () => {
      handleOpenSnooze()
    },
  },
  {
    id: 2,
    image: markRaw(AddTasks),
    title: 'Add to Tasks',
    function: () => {},
  },
  {
    id: 3,
    image: markRaw(Label),
    title: 'Label as',
    function: () => {
      settoggleLabelMenu()
    },
  },
  {
    id: 4,
    image: markRaw(Attachment),
    title: 'Forward as attachment',
    function: () => {
      handleFileSelect()
    },
  },
  {
    id: 5,
    image: markRaw(Filter),
    title: 'Filter message like these',
    function: () => {
      filterMessageLikeThese()
    },
  },
  {
    id: 6,
    image: markRaw(Mute),
    title: 'Mute',
    function: () => {
      muteConversation()
    },
  },
  {
    id: 7,
    image: markRaw(Switch),
    title: 'Switch to advance toolbar',
    function: () => {
      toggleAdvanceSimple(true)
    },
  },
])
const advanceMenu = ref<AdvanceMenu[]>([
  {
    id: 1,
    image: markRaw(ReadMessageIcon),
    title: 'Mark as read',
    image1: markRaw(UnreadMessageIcon),
    title1: 'Mark as unread ',
    function: () => handleSelectedReadUnread(),
  },
  {
    id: 2,
    image: markRaw(MarkAsImportant),
    title: 'Mark as important',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 3,
    image: markRaw(Starred),
    title: 'Add star',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 4,
    image: markRaw(Filter),
    title: 'Filter message like these',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 5,
    image: markRaw(Mute),
    title: 'Mute',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 6,
    image: markRaw(Attachment),
    title: 'Forward as attachment',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 7,
    image: markRaw(Switch),
    title: 'Switch to simple toolbar',
    image1: '',
    title1: '',
    function: () => {
      toggleAdvanceSimple(false)
    },
  },
])
const toggleAdvanceSimple = (value: boolean) => {
  store.commit('emails/SET_SHOW_HIDE_ADVANCE_MENU', value)
}
const handleOptionClick = () => {
  if (!readUnreadAll.value) {
    store.commit('emails/READ_ALL_MESSAGE_WITH_ALL_UNCHECK')
  } else {
    store.commit('emails/UNREAD_ALL_MESSAGE_WITH_ALL_UNCHECK')
  }
}
const handleSelectedReadUnread = () => {
  if (!readUnreadAll.value) {
    store.commit('emails/READ_ALL_MESSAGE')
  } else {
    store.commit('emails/UNREAD_ALL_MESSAGE')
  }
}
const emailMessages = computed(() => store.state.emails.emailMessages)
const {
  handleFileChange,
  removeFile,
  formatSize,
  attachedFiles,
  fileUrls,
  uploadProgress,
} = FileUpload()
const handleFileSelect = () => {
  const selectedFiles = []
  emailMessages.value.forEach((emailMessage) => {
    if (emailMessage.checked) {
      selectedFiles.push({
        name: emailMessage.fileName,
        size: emailMessage.size,
        from: emailMessage.from,
        subject: emailMessage.subject,
        description: emailMessage.description,
      })
    }
  })
  handleFileChange(selectedFiles)
  store.commit('emails/SET_FORWARD_MESSAGE_EVENTS', {
    attachedFiles: attachedFiles.value,
    fileUrls: fileUrls.value,
    formatSize: formatSize,
    uploadProgress: uploadProgress.value,
    removeFile: removeFile,
  })
  store.commit('emails/SET_SHOW_COMPOSE_SECTION')
}
const toggleLabelMenu = ref<boolean>(false)
const settoggleLabelMenu = () => {
  toggleLabelMenu.value = !toggleLabelMenu.value
}
const handleOpenSnooze = () => {
  emit('openSnooze')
}

const muteConversation = () => {
  toast.clear()
  saToast({
    message: 'Conversation muted',
    onUndo: undoMuteConversation,
  })
}

const undoMuteConversation = () => {
  toast.clear()
  saToast({
    message: 'Action undone.',
  })
}

const filterMessageLikeThese = () => {
  const filter = {
    from: '<EMAIL>',
  }
  store.commit('emails/SET_TEMP_FILTERS', filter)
  store.commit('emails/SET_CREATE_NEW_FILTER_MODAL', {
    isOpen: true,
    type: 'create',
    for: 'menu',
  })
  emit('closeMenu')
}
</script>

<template>
  <div
    class="flex flex-col space-y-3.5 bg-white py-1 absolute top-10 left-0"
    :class="
      checkedAll || showMinus ? 'select-extend-menu-box' : 'select-menu-box'
    "
  >
    <div v-if="checkedAll || showMinus" class="flex flex-col">
      <div
        v-for="item in showhideAdvanceMenu ? advanceMenu : extendMenu"
        :key="item.id"
        class="relative flex justify-between items-center !space-x-2 px-3.5 py-[6px] hover:bg-[#F1F2F6] cursor-pointer"
        :class="
          item.id === 2 || item.id === 6 ? 'border-b border-[#C2C2C2]' : ''
        "
        @click.stop="item.function"
      >
        <div class="flex items-center !space-x-2">
          <component
            v-if="
              !showhideAdvanceMenu || (showhideAdvanceMenu && item.id !== 1)
            "
            :is="item.image"
            color="#707070"
          />
          <component
            v-else-if="item.id === 1 && showhideAdvanceMenu"
            :is="readUnreadAll ? item.image : item.image1"
            color="#707070"
          />
          <p
            v-if="
              !showhideAdvanceMenu || (showhideAdvanceMenu && item.id !== 1)
            "
            class="text-[#525252]"
          >
            {{ item.title }}
          </p>
          <p
            v-else-if="showhideAdvanceMenu && item.id === 1"
            class="text-[#525252]"
          >
            {{ item.title1 }}
          </p>
        </div>
        <SharedIconHubEmailsDownArrow
          v-if="!showhideAdvanceMenu && item.id === 3"
          class="transform rotate-[-90deg]"
        />
        <SourceHubEmailsLabelAsMenu
          v-if="toggleLabelMenu && item.id === 3"
          class="top-0 labelasMenu-position left-[96%] z-1 single-message-label-as-menu"
          @click.stop=""
          @hide-labelasmenu="
            (event) => (
              (toggleLabelMenu = event), emit('hide-move-labelasmenu', event)
            )
          "
        />
      </div>
    </div>
    <div v-else class="flex flex-col">
      <div
        class="flex !space-x-2 px-3.5 py-3 hover:bg-[#F1F2F6] cursor-pointer border-b border-[#C2C2C2]"
        @click="handleOptionClick"
      >
        <component :is="markAllReadMenu.image" />
        <p class="text-[#525252]">{{ markAllReadMenu.title }}</p>
      </div>
      <p class="px-4 pt-2 text-[#707070] text-sm italic">
        {{ markAllReadMenu.description }}
      </p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.select-menu-box {
  min-width: 176px;
  max-width: 176px;
  height: fit-content;
  z-index: 1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 8px #2228313d;
  border-radius: 8px;
}
.select-extend-menu-box {
  min-width: 244px;
  max-width: 244px;
  height: fit-content;
  z-index: 1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 8px #2228313d;
  border-radius: 8px;
}
@media (max-height: 829px) {
  .labelasMenu-position {
    bottom: 0 !important;
  }
}
</style>
