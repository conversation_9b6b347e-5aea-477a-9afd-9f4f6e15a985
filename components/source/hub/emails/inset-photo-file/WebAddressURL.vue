<script setup lang="ts">
import useVuelidate from '@vuelidate/core'
import { helpers } from '@vuelidate/validators'

const emit = defineEmits<{
  (e: 'insertImage', url: string): void
}>()

const state = reactive({
  url: '',
})

const imageLoadingState = ref<'idle' | 'loading' | 'loaded' | 'error'>('idle')

const urlValidator = helpers.withMessage(
  'The URL is not valid. Make sure it starts with http:// or https://.',
  (value: string) => {
    if (!value) return true

    if (!value.startsWith('http://') && !value.startsWith('https://')) {
      return false
    }

    try {
      new URL(value)
      return true
    } catch {
      return false
    }
  },
)

const imageUrlValidator = helpers.withMessage(
  'Please enter a valid image URL (jpg, jpeg, png, gif, webp, svg)',
  (value: string) => {
    if (!value) return true

    // Only validate format if URL is valid first
    try {
      new URL(value)
    } catch {
      return true
    }

    const imageExtensions = /\.(jpg|jpeg|png|gif|webp|svg)(\?.*)?$/i
    const imageUrlPatterns = [imageExtensions]

    return imageUrlPatterns.some((pattern) => pattern.test(value))
  },
)

const imageAccessibilityValidator = helpers.withMessage(
  'The image URL is not accessible or the image is broken',
  () => {
    return imageLoadingState.value !== 'error'
  },
)

const rules = {
  url: {
    urlValidator,
    imageUrlValidator,
    imageAccessibilityValidator,
  },
}

const v$ = useVuelidate(rules, state)

const inputRef = ref<HTMLInputElement | null>(null)

const isInsertDisabled = computed(() => {
  return (
    !state.url.trim() ||
    v$.value.url.$error ||
    imageLoadingState.value === 'loading'
  )
})

const firstError = computed(() => {
  if (!v$.value.url.$error || !v$.value.url.$errors.length) {
    return null
  }
  return v$.value.url.$errors[0].$message
})

const validateImageLoad = (url: string) => {
  if (!url.trim()) {
    imageLoadingState.value = 'idle'
    return
  }

  if (
    v$.value.url.urlValidator.$invalid ||
    v$.value.url.imageUrlValidator.$invalid
  ) {
    imageLoadingState.value = 'idle'
    return
  }

  imageLoadingState.value = 'loading'

  const img = new Image()

  img.onload = () => {
    imageLoadingState.value = 'loaded'
    nextTick(() => {
      v$.value.url.$touch()
    })
  }

  img.onerror = () => {
    imageLoadingState.value = 'error'
    nextTick(() => {
      v$.value.url.$touch()
    })
  }

  img.src = url
}

watch(
  () => state.url,
  (newUrl) => {
    if (newUrl) {
      nextTick(() => {
        validateImageLoad(newUrl)
      })
    } else {
      imageLoadingState.value = 'idle'
    }
  },
)

const clearInput = () => {
  state.url = ''
  imageLoadingState.value = 'idle'
  v$.value.$reset()
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleBlur = () => {
  if (state.url.trim()) {
    v$.value.url.$touch()
  } else {
    imageLoadingState.value = 'idle'
    v$.value.$reset()
  }
}

const insertImage = () => {
  if (!isInsertDisabled.value) {
    emit('insertImage', state.url)
  }
}
</script>

<template>
  <div class="px-6 pb-6 pt-12 flex flex-col w-full h-[calc(100%-136px)]">
    <div
      class="flex-1 h-full max-h-[calc(100%-35px)] flex flex-col items-center"
    >
      <div
        class="relative max-w-[424px] px-3 w-full focus-within:rounded-full focus-within:bg-[#F1F2F6]"
      >
        <input
          ref="inputRef"
          v-model="state.url"
          type="text"
          placeholder="Paste URL of image..."
          class="w-full h-[38px] pl-1 pr-7 py-[7px] border-0 border-b-2 border-b-[#F1F2F6] focus:bg-[#F1F2F6] outline-none text-lg leading-4 text-[#333333]"
          @blur="handleBlur"
          @input="handleBlur"
        />
        <SharedIconXmarkCircle
          @click="clearInput"
          class="absolute right-4 top-1/2 -translate-y-1/2 text-[#C2C2C2] cursor-pointer hover:text-[#999999] transition-colors"
        />
      </div>

      <div v-if="firstError" class="w-full max-w-[424px] px-3 pt-1">
        <p class="text-xs text-red-500">
          {{ firstError }}
        </p>
      </div>

      <p class="text-sm text-[#707070] pt-1.5">
        Only select images that you've confirmed you have the license to use
      </p>

      <div
        v-if="state.url && !v$.url.$error && imageLoadingState === 'loaded'"
        class="max-w-[536px] max-h-[calc(100%-66px)] py-6"
      >
        <img
          :src="state.url"
          alt="Image preview"
          class="max-h-full max-w-full"
        />
      </div>

      <div
        v-else-if="
          state.url && !v$.url.$error && imageLoadingState === 'loading'
        "
        class="max-w-[536px] max-h-[calc(100%-66px)] py-6 flex items-center justify-center"
      >
        <div class="text-[#707070] text-sm">Loading image...</div>
      </div>
    </div>

    <div class="w-full flex justify-end">
      <button
        @click="insertImage"
        :disabled="isInsertDisabled"
        :class="[
          'h-[35px] px-6 flex justify-center items-center text-white font-semibold rounded-full transition-colors',
          isInsertDisabled ? 'bg-[#E3E3E3]' : 'bg-[#4A71D4] hover:bg-[#3A61C4]',
        ]"
      >
        Insert Image
      </button>
    </div>
  </div>
</template>
