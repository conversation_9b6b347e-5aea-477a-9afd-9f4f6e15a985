<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()

interface MoveToMenuOptions {
  id: number
  title: string
}

const searchText = ref<string>('')
const moveToMenuOptions = ref<MoveToMenuOptions[]>([
  {
    id: 1,
    title: 'Product',
  },
  {
    id: 2,
    title: 'Social',
  },
  {
    id: 3,
    title: 'Updates',
  },
  {
    id: 4,
    title: 'Forums',
  },
  {
    id: 5,
    title: 'Promotions',
  },
  {
    id: 6,
    title: 'Spam',
  },
  {
    id: 7,
    title: 'Trash',
  },
])

const searchLabels = computed(() => {
  return moveToMenuOptions.value.filter((moveToMenuOption: MoveToMenuOptions) =>
    moveToMenuOption.title
      .toLowerCase()
      .startsWith(searchText.value.toLowerCase()),
  )
})
</script>

<template>
  <div
    class="select-extend-menu-box flex flex-col space-y-1 bg-white py-1 absolute custom-scroll"
  >
    <div class="px-3.5 py-[5px]">
      <p class="text-[#525252]">Move To:</p>
    </div>
    <div class="px-3.5 h-[36px] !mt-0">
      <div
        class="flex space-x-3 justify-between items-center border-b-2 border-[#4A71D4] h-[36px] pb-1.5"
      >
        <input
          class="flex-grow outline-none border-none"
          type="text"
          v-model="searchText"
        />
        <ClientOnly>
          <fa class="text-[#707070]" :icon="['fa', 'magnifying-glass']" />
        </ClientOnly>
      </div>
    </div>
    <ul class="flex flex-col">
      <li
        v-for="moveToMenuOption in searchLabels"
        :key="moveToMenuOption.id"
        class="flex justify-between items-center !space-x-2 px-3.5 py-[5px] hover:bg-[#F1F2F6] cursor-pointer"
        :class="
          moveToMenuOption.id === 5 || moveToMenuOption.id === 7
            ? 'border-b border-[#C2C2C2]'
            : ''
        "
      >
        <div class="flex items-center !space-x-2">
          <p class="text-[#525252]">{{ moveToMenuOption.title }}</p>
        </div>
      </li>
    </ul>
    <ul class="flex flex-col px-3.5">
      <li class="py-[5px]">
        <p class="text-[#525252]">
          {{ searchText ? `"${searchText}"` : '' }} Create new
        </p>
      </li>
      <li class="py-[5px]"><p class="text-[#525252]">Manage Labels</p></li>
    </ul>
  </div>
</template>

<style lang="scss" scoped>
.select-menu-box {
  min-width: 176px;
  max-width: 176px;
  height: fit-content;
  z-index: 1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
.select-extend-menu-box {
  min-width: 240px;
  max-width: 240px;
  height: fit-content;
  z-index: 1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
.single-message-move-to-menu {
  box-shadow: 0px 0px 8px #2228313d !important;
}
@media (max-height: 819px) and (min-width: 1536px) {
  .header-menu {
    top: 0%;
    right: 100%;
    left: auto;
  }
}
@media (max-height: 719px) and (min-width: 1536px) {
  .header-menu {
    top: 0%;
    right: 100%;
    left: auto;
  }
}
@media (max-height: 919px) and (max-width: 1535px) {
  .header-menu {
    top: 0%;
    right: 100%;
    left: auto;
  }
}
@media (max-height: 819px) and (max-width: 1535px) {
  .header-menu {
    top: 0%;
    right: 100%;
    left: auto;
    max-height: 300px;
  }
}
@media (max-height: 750px) and (max-width: 1535px) {
  .header-menu {
    top: 0%;
    right: 100%;
    left: auto;
    max-height: 250px;
  }
}
@media (max-height: 690px) and (max-width: 1535px) {
  .header-menu {
    top: 0%;
    right: 100%;
    left: auto;
    max-height: 200px;
  }
}
</style>
