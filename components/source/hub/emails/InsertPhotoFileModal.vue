<script setup lang="ts">
import { format, isToday, isYesterday, parse } from 'date-fns'
import { useStore } from 'vuex'
import WebAddressURL from './inset-photo-file/WebAddressURL.vue'

const store = useStore()
const composeArray = computed(() => store.state.emails.composeArray)
interface ButtonOptions {
  id: number
  text: string
  select: boolean
}
const buttonOptions = ref<ButtonOptions[]>([
  {
    id: 1,
    text: 'Google Photos',
    select: true,
  },
  {
    id: 2,
    text: 'Upload',
    select: false,
  },
  {
    id: 3,
    text: 'Web Address (URL)',
    select: false,
  },
])
const sidebarOptions = ref<ButtonOptions[]>([
  {
    id: 1,
    text: 'Photos',
    select: true,
  },
  {
    id: 2,
    text: 'Album',
    select: false,
  },
])
const selectedSidebarOption = ref<string>('Photos')
const setSidebarOptionsSelect = (id: number) => {
  sidebarOptions.value.forEach((sidebarOption: ButtonOptions) => {
    if (sidebarOption.id === id) {
      sidebarOption.select = true
      selectedSidebarOption.value = sidebarOption.text
    } else {
      sidebarOption.select = false
    }
  })
}
const selectedButton = ref('Google Photos')
const setButtonOptionsSelect = (id: number) => {
  buttonOptions.value.forEach((buttonOption: ButtonOptions) => {
    if (buttonOption.id === id) {
      buttonOption.select = !buttonOption.select
      selectedButton.value = buttonOption.text
    } else {
      buttonOption.select = false
    }
  })
}

interface InsertFiles {
  id: number
  img: string
  type: string
  title: string
  name: string
  date: string
  select: boolean
  size: number
}
const insertFiles = ref<InsertFiles[]>([
  {
    id: 1,
    img: '/social/email/1.png',
    type: 'image/png',
    title: 'Minecraft-1',
    name: 'Minecraft-1.png',
    date: '03-03-2025',
    select: false,
    size: 300,
  },
  {
    id: 2,
    img: '/social/email/2.png',
    type: 'image/png',
    title: 'Minecraft-thumbnail',
    name: 'Minecraft-thumbnail.png',
    date: '02-03-2025',
    select: false,
    size: 350,
  },
  {
    id: 3,
    img: '/social/email/3.png',
    type: 'image/png',
    title: 'Minecraft-thumbnail2',
    name: 'Minecraft-thumbnail2.png',
    date: '28-02-2025',
    select: false,
    size: 310,
  },
  {
    id: 4,
    img: '/social/email/4.png',
    type: 'image/png',
    title: 'Minecraft-2',
    name: 'Minecraft-2.png',
    date: '28-02-2025',
    select: false,
    size: 320,
  },
  {
    id: 5,
    img: '/social/email/5.png',
    type: 'image/png',
    title: 'Minecraft-thumbnail3',
    name: 'Minecraft-thumbnail3.png',
    date: '27-02-2025',
    select: false,
    size: 400,
  },
  {
    id: 6,
    img: '/social/email/1.png',
    type: 'image/png',
    title: 'Minecraft-thumbnail4',
    name: 'Minecraft-thumbnail4.png',
    date: '27-02-2025',
    select: false,
    size: 410,
  },
  {
    id: 7,
    img: '/social/email/2.png',
    type: 'image/png',
    title: 'Minecraft-thumbnail5',
    name: 'Minecraft-thumbnail5.png',
    date: '27-02-2025',
    select: false,
    size: 450,
  },
  {
    id: 8,
    img: '/social/email/3.png',
    type: 'image/png',
    title: 'Minecraft-thumbnail6',
    name: 'Minecraft-thumbnail6.png',
    date: '26-02-2025',
    select: false,
    size: 420,
  },
  {
    id: 9,
    img: '/social/email/4.png',
    type: 'image/png',
    title: 'Minecraft-thumbnail7',
    name: 'Minecraft-thumbnail7.png',
    date: '22-02-2025',
    select: false,
    size: 500,
  },
  {
    id: 10,
    img: '/social/email/3.png',
    type: 'image/png',
    title: 'Minecraft-thumbnail6',
    name: 'Minecraft-thumbnail6.png',
    date: '15-02-2025',
    select: false,
    size: 510,
  },
  {
    id: 11,
    img: '/social/email/4.png',
    type: 'image/png',
    title: 'Minecraft-2',
    name: 'Minecraft-2.png',
    date: '10-01-2025',
    select: false,
    size: 550,
  },
])
interface AlbumFiles {
  id: number
  img: string
  type: string
  title: string
  name: string
  date: string
  items: string
  select: boolean
  size: number
}
const albumFiles = ref<AlbumFiles[]>([
  {
    id: 1,
    img: '/social/email/thumbnail_1.png',
    type: 'image/png',
    title: 'Minecraft-1',
    name: 'Minecraft-1.png',
    date: 'September 28, 2024',
    items: '5 items',
    select: false,
    size: 300,
  },
  {
    id: 2,
    img: '/social/email/thumbnail_2.png',
    type: 'image/png',
    title: 'Minecraft-thumbnail',
    name: 'Minecraft-thumbnail.png',
    date: 'July 5, 2024',
    items: '19 items',
    select: false,
    size: 350,
  },
])
const itemSelect = ref<boolean>(false)
// Helper function to parse date
const parseDate = (dateStr: string) => {
  return parse(dateStr, 'dd-MM-yyyy', new Date())
}
// Grouped files
const groupedFiles = computed(() => {
  const grouped: Record<string, InsertFiles[]> = {}

  insertFiles.value.forEach((file: InsertFiles) => {
    const fileDate = parseDate(file.date)

    let label = format(fileDate, 'MMMM dd, yyyy') // Default format like "Feb 22, 2025"

    if (isToday(fileDate)) label = 'Today'
    else if (isYesterday(fileDate)) label = 'Yesterday'

    if (!grouped[label]) grouped[label] = []
    grouped[label].push(file)
  })

  return grouped
})
const selctedDriveFiles = ref<any>([])
const selectedInsertFile = (files: InsertFiles[], id: number) => {
  files.forEach((file: InsertFiles) => {
    if (file.id === id) {
      file.select = !file.select
    }
    if (file.select) {
      convertToFile(file).then((item) => {
        selctedDriveFiles.value.push(item)
      })
    } else {
      const removeIndex = selctedDriveFiles.value.findIndex(
        (f: InsertFiles) => f.name === file.name && f.type === file.type,
      )
      if (removeIndex !== -1) {
        selctedDriveFiles.value.splice(removeIndex, 1)
      }
    }
  })
  itemSelect.value = insertFiles.value.some((file: InsertFiles) => file.select)
}
// Convert it into a File
async function convertToFile(obj: InsertFiles): Promise<File> {
  // fetch the file (works if `img` is a URL or relative path in /public folder)
  const response = await fetch(obj.img)
  const blob = await response.blob()

  // return File
  return new File([blob], obj.name, { type: obj.type })
}
const resetAllSeletedFiles = () => {
  insertFiles.value.forEach((insertFile: InsertFiles) => {
    insertFile.select = false
  })
  itemSelect.value = false
  selctedDriveFiles.value = []
}
const MAX_FILE_SIZE_MB = 25
const isDragging = ref(false)
const fileInput = ref<HTMLInputElement | null>(null)
const { $toast } = useNuxtApp()
// Trigger hidden file input
const triggerFileInput = () => {
  fileInput.value?.click()
}

// File validation
const validateFile = (file: File): string | null => {
  if (!file.type.startsWith('image/')) return 'Only image files are allowed.'
  if (file.size > MAX_FILE_SIZE_MB * 1024 * 1024)
    return 'Image must be 25MB or less.'
  return null
}

const totalImages = ref(0)
const completedImages = ref(0)

const insertMultipleImages = async (files: File[]) => {
  totalImages.value = 0
  const validFiles = files.filter((file) => !validateFile(file))
  totalImages.value = validFiles.length
  completedImages.value = 0
  for (const file of files) {
    const error = validateFile(file)
    if (error) {
      alert(error)
      continue
    }
    const base64 = await readFileAsBase64(file)
    completedImages.value++
    console.log(composeArray.value, 'InsertPhotoFileModal')
    if (composeArray.value && composeArray.value.length > 0) {
      store.commit('emails/SET_FILE_FROM_PHOTO_INSERT', {
        url: base64,
        alt: file.name,
      })
    } else {
      store.commit('emails/SET_FILE_CHANGED', {
        fileChanged: true,
        fileUrl: base64,
      })
    }
  }
  // Optional: Reset after short delay
  setTimeout(() => {
    totalImages.value = 0
    completedImages.value = 0
    store.commit('emails/SET_PHOTO_INSERT_FILE_MODAL', false)
  }, 2000)
}
const progress = computed(() => {
  return totalImages.value === 0
    ? 0
    : Math.round((completedImages.value / totalImages.value) * 100)
})
// const progress = computed(() =>
//   totalImages.value === 0
//     ? 0
//     : Math.round((completedImages.value / totalImages.value) * 100),
// )

// Utility to read file as base64 using Promise
const readFileAsBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}

const onFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement
  handleFiles(input.files)
}

const handleFiles = (fileList: FileList | null) => {
  if (!fileList) return
  insertMultipleImages(Array.from(fileList))
  // for (let i = 0; i < fileList.length; i++) {
  // insertImage(fileList[i])
  // }
}

const onDrop = (event: DragEvent) => {
  isDragging.value = false
  const files = event.dataTransfer?.files
  if (files) insertMultipleImages(Array.from(files))
}

const onDragOver = () => {
  isDragging.value = true
}
const onDragLeave = () => {
  isDragging.value = false
}
const handleAttachmentAction = () => {
  store.commit('emails/SET_PHOTO_INSERT_FILE_MODAL', false)
  store.commit('emails/SET_PHOTO_ATTACHMENT_FILES', selctedDriveFiles.value)
}
const handleInsertAction = () => {
  store.commit('emails/SET_PHOTO_INSERT_FILE_MODAL', false)
  insertMultipleImages(selctedDriveFiles.value)
}
const handleInsertWebUrlImage = (url: string) => {
  const urlArray = url.split('/')
  const alt = urlArray[urlArray.length - 1]
  store.commit('emails/SET_PHOTO_INSERT_FILE_MODAL', false)
  store.commit('emails/SET_FILE_FROM_PHOTO_INSERT', {
    url: url,
    alt: alt,
  })
}
</script>

<template>
  <div
    class="overflow-hidden max-w-[1280px] w-[90%] max-h-[622px] h-full rounded-2xl bg-white fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[999999999]"
  >
    <div class="max-h-[622px] h-full rounded-2xl bg-white overflow-hidden">
      <div @click="resetAllSeletedFiles">
        <!-- <div
          class="flex items-center justify-between px-6 py-3.5 border-b border-[#f1f1f2]"
        >
          <p class="text-[#505050] text-lg font-semibold">Insert File</p>
          <SharedIconHubEmailsCrossIcon
            class="cursor-pointer"
            @click.stop="
              store.commit('emails/SET_PHOTO_INSERT_FILE_MODAL', false)
            "
          />
        </div> -->
        <div class="px-6 py-4 border-b border-[#f1f1f2]">
          <div class="grid grid-cols-[268px_1fr_36px] gap-[108px] items-center">
            <div class="text-left flex !space-x-2">
              <p class="text-[#525252] text-lg font-semibold whitespace-nowrap">
                Insert Photo
              </p>
            </div>
            <div
              class="flex items-center justify-between space-x-3 bg-[#F1F2F6] px-[26px] py-[2px] rounded-full max-w-[480px]"
            >
              <div class="flex-grow">
                <div class="flex space-x-2 items-center">
                  <fa class="text-[#707070]" :icon="['fas', 'search']" />
                  <input
                    class="text-[#707070] bg-[#F1F2F6] flex-grow border-none outline-none"
                    type="text"
                    placeholder="Search for photos"
                  />
                </div>
              </div>
            </div>
            <SharedIconHubEmailsCrossIcon
              class="cursor-pointer"
              @click.stop="
                store.commit('emails/SET_PHOTO_INSERT_FILE_MODAL', false)
              "
            />
          </div>
          <div class="mt-8 flex space-x-4 w-full">
            <button
              v-for="buttonOption in buttonOptions"
              :key="buttonOption.id"
              class="px-6 w-fit h-[35px] rounded-full font-semibold flex justify-center items-center"
              :class="
                buttonOption.select
                  ? 'text-white bg-[#4A71D4]'
                  : 'bg-[#f1f2f6] text-[#525252]'
              "
              @click="setButtonOptionsSelect(buttonOption.id)"
            >
              {{ buttonOption.text }}
            </button>
          </div>
        </div>
        <!-- <div
          class="px-6 py-4 border-b border-[#f1f1f2] flex justify-between items-center"
        >
          <p class="text-[#707070]">Recent</p>
          <SharedIconHubEmailsListIcon />
        </div> -->
      </div>
      <div
        v-if="selectedButton === 'Google Photos'"
        class="grid grid-cols-[184px_1fr] h-[calc(100%-201px)]"
      >
        <div class="flex flex-col py-4">
          <div
            v-for="sidebarOption in sidebarOptions"
            :key="sidebarOption.id"
            class="pl-6 py-1.5 cursor-pointer"
            :class="
              sidebarOption.select
                ? 'bg-[#E3EFFF] text-[#4A71D4] rounded-se-full rounded-ee-full'
                : 'text-[#525252]'
            "
            @click="
              setSidebarOptionsSelect(sidebarOption.id), resetAllSeletedFiles()
            "
          >
            <p>{{ sidebarOption.text }}</p>
          </div>
        </div>
        <div
          v-if="selectedSidebarOption === 'Photos'"
          class="px-4 py-4 flex flex-col space-y-6 h-full custom-scroll"
          @click="resetAllSeletedFiles"
        >
          <div
            v-for="(files, dateLabel) in groupedFiles"
            :key="dateLabel"
            class="flex flex-col space-y-4"
            :class="files.length > 0 ? '' : '!mt-0'"
          >
            <h3 v-if="files.length > 0" class="text-[#707070]">
              {{ dateLabel }}
            </h3>
            <div
              v-if="files.length > 0"
              class="grid grid-cols-[repeat(auto-fit,minmax(min(126px,100%),126px))] gap-2"
            >
              <div
                class="file-box h-[168px] transform transition-all duration-200 ease-in-out max-w-[126px] max-h-[168px] cursor-pointer relative"
                :class="file.select ? 'scale-90' : ''"
                v-for="file in files"
                :key="file.id"
                @click.stop="selectedInsertFile(files, file.id)"
              >
                <img
                  v-if="file.img"
                  class="w-[126px] h-[168px] object-cover"
                  :src="file.img"
                  :alt="file.title"
                />
                <div
                  class="flex justify-center items-center absolute top-[-6px] left-[-6px] w-5 h-5 rounded-full bg-[#4A71D4] z-[9999999]"
                  :class="file.select ? 'block' : 'hidden'"
                >
                  <fa class="text-white text-base" :icon="['fa', 'check']" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          v-else-if="selectedSidebarOption === 'Album'"
          class="px-4 py-4 grid grid-cols-[repeat(auto-fit,minmax(min(212px,100%),212px))] gap-2 h-full custom-scroll"
          @click="resetAllSeletedFiles"
        >
          <div
            v-for="(file, dateLabel) in albumFiles"
            :key="dateLabel"
            class="flex flex-col rounded bg-[#F1F2F6] overflow-hidden max-h-[272px]"
          >
            <img
              class="w-full max-w-[212px]"
              :src="file.img"
              :alt="file.title"
            />
            <div class="px-2 py-1.5 flex flex-col space-y-1">
              <p class="text-[#525252] text-base font-semibold">
                {{ file.date }}
              </p>
              <p class="text-sm text-[#525252]">{{ file.items }}</p>
            </div>
          </div>
        </div>
      </div>
      <div
        v-else-if="selectedButton === 'Upload'"
        class="flex flex-col w-full h-[calc(100%-201px)] justify-center items-center"
        @dragover.prevent="onDragOver"
        @dragleave="onDragLeave"
        @drop.prevent="onDrop"
      >
        <div
          class="flex flex-col w-full h-full justify-center items-center custom-scroll"
        >
          <img
            class="max-h-[200px]"
            src="/images/png/upload_background.png"
            alt="upload_background"
          />
          <!-- v-if="progress > 0 && progress < 100" -->
          <div
            v-if="totalImages !== 0"
            class="w-[80%] bg-gray-200 rounded h-2 mt-2 mx-auto"
          >
            <div
              class="bg-blue-500 h-full transition-all duration-300"
              :style="{ width: `${progress}%` }"
            ></div>
          </div>
          <div class="flex flex-col justify-center items-center" v-else>
            <button
              class="mt-5 px-6 text-white bg-[#4A71D4] w-fit h-[35px] rounded-full font-semibold flex justify-center items-center"
              @click="triggerFileInput"
            >
              Browse
            </button>
            <p class="!mt-1.5 text-2xl text-[#525252] text-center">
              or drag files here
            </p>
          </div>
          <!-- File input -->
          <input
            ref="fileInput"
            type="file"
            multiple
            accept="image/*"
            class="hidden"
            @change="onFileChange"
          />
        </div>
      </div>
      <WebAddressURL
        v-else-if="selectedButton === 'Web Address (URL)'"
        @insertImage="handleInsertWebUrlImage"
      />

      <Transition name="slide-up">
        <div
          v-if="itemSelect"
          class="w-full bg-[#E3EFFF] py-4 px-6 flex justify-between items-center space-x-2 border-t border-[#f1f1f2] sticky bottom-0 left-0 z-1"
        >
          <div class="flex space-x-[26px] items-center">
            <SharedIconHubEmailsCrossIcon
              class="cursor-pointer"
              color="#525252"
              @click.stop="resetAllSeletedFiles"
            />
            <p class="text-[#525252] text-lg">
              {{ selctedDriveFiles.length }} selected
            </p>
          </div>
          <div class="flex space-x-2 items-center">
            <button
              class="w-fit h-[35px] px-6 flex justify-center items-center border border-[#4A71D4] text-[#4A71D4] font-semibold rounded-full"
              @click.stop="handleAttachmentAction"
            >
              Add as attachment
            </button>
            <button
              class="w-fit h-[35px] px-6 flex justify-center items-center bg-[#4A71D4] text-white font-semibold rounded-full"
              @click.stop="handleInsertAction"
            >
              Insert
            </button>
          </div>
        </div>
      </Transition>
    </div>
  </div>
</template>

<style scoped>
.file-box {
  background: white;
  opacity: 1;
}
.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.5s ease;
}
.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(100%);
}
</style>
