<script setup lang="ts">
import { useStore } from 'vuex'
import type {
  GeneralSettings,
  VacationResponder,
} from '~/types/hubEmailsSettings'

const vacationResponderOptions = [
  {
    label: 'Vacation responder off',
    value: 'off',
    description: '',
  },
  {
    label: 'Vacation responder on',
    value: 'on',
    description: '',
  },
]

const emit = defineEmits<{
  onVacationResponder: [vacationResponder: VacationResponder]
}>()

const store = useStore()

const planText = ref(false)

const {
  selectedDate,
  selectedFirstDay,
  selectedLastDay,
  currentTab,
  currentTitle,
  currentMonthYear,
  weekRange,
  isToday,
  isThisWeek,
  isThisMonth,
  handleGoToPrev,
  handleGoToNext,
  handleGoToToday,
  handleTabChange,
  setMonthYear,
  synchronizeCalendarRange,
  synchronizeCalendarMonth,
} = useCalendarManager()

const vacationResponder = ref<VacationResponder>({
  status: 'off',
  firstDay: '2025-06-18T00:00:00.000Z',
  lastDay: '2025-06-20T00:00:00.000Z',
  enableLastDay: false,
  subject: '',
  message: '',
  myContent: false,
})

const handleSetFirstDay = (date: Date) => {
  selectedFirstDay.value = date
  vacationResponder.value.firstDay = date.toISOString()
}
const handleSetLastDay = (date: Date) => {
  selectedLastDay.value = date
  vacationResponder.value.lastDay = date.toISOString()
}

const generalSettings = computed<GeneralSettings>(
  () => store.state.emails.gmailGeneral,
)

const togglePlainOrRichText = () => {
  if (!planText.value) {
    vacationResponder.value.message = vacationResponder.value.message.replace(
      /<[^>]*>/g,
      '',
    )
  }
  planText.value = !planText.value
}

const toggleEnableLastDay = () => {
  if (!vacationResponder.value.enableLastDay) {
    selectedLastDay.value = new Date(
      new Date().setDate(new Date().getDate() + 7),
    )
    vacationResponder.value.lastDay = selectedLastDay.value.toISOString()
  }
}

watch(
  vacationResponder,
  () => {
    emit('onVacationResponder', vacationResponder.value)
  },
  { deep: true },
)

onMounted(async () => {
  await nextTick()
  const tempData = _cloneDeep(generalSettings.value.vacationResponder)
  if (tempData) {
    selectedFirstDay.value = tempData?.firstDay
      ? new Date(utcToLocalTime(tempData.firstDay))
      : new Date()
    selectedLastDay.value = tempData?.lastDay
      ? new Date(utcToLocalTime(tempData.lastDay))
      : new Date(new Date().setDate(new Date().getDate() + 7))
    vacationResponder.value = tempData
  }
})
</script>

<template>
  <div>
    <SourceHubEmailsSettingsGeberalFeaturesRadioOptions
      v-model="vacationResponder.status"
      label="Vacation responder:"
      description="(sends an automated reply to incoming messages. If a contact sends you several messages, this automated reply will be sent at most once every 4 days)"
      :options="vacationResponderOptions"
    >
      <template v-slot:learn-more>
        <p class="text-[#4A71D4] !mt-1.5">Learn more</p>
      </template>
      <template v-slot:vacation-responder>
        <div class="mt-[26px]">
          <div class="grid grid-cols-[1fr_1fr] max-w-[680px] gap-x-9">
            <div class="flex space-x-3">
              <p class="text-[#333333] font-semibold">First day:</p>
              <SourcePostListWeekMonthDatePicker
                class="date-select-box"
                calender-height="h-[27px] pl-[16px] !justify-start bg-white !text-[#333333] !font-normal border border-[#C2C2C2]"
                :currentTab="currentTab"
                :selectedDate="selectedFirstDay || new Date()"
                :weekRange="weekRange"
                :monthYear="currentMonthYear"
                :currentTitle="currentTitle"
                @update:date="handleSetFirstDay"
                @update:range="
                  (range: WeekRange) => {
                    weekRange = range
                    synchronizeCalendarRange()
                  }
                "
                @update:monthYear="setMonthYear"
                ><template v-slot:button>
                  <button
                    @click.stop="selectedFirstDay = new Date()"
                    class="text-sm text-[#525252] pl-8"
                  >
                    Today
                  </button>
                </template></SourcePostListWeekMonthDatePicker
              >
            </div>
            <div class="flex space-x-3">
              <label
                class="relative flex space-x-2 items-start cursor-pointer"
                for="last_day"
                @click.stop="toggleEnableLastDay"
              >
                <div class="w-4 h-4 relative mt-0.5">
                  <input
                    ref="last_day"
                    id="last_day"
                    type="checkbox"
                    v-model="vacationResponder.enableLastDay"
                    :checked="vacationResponder.enableLastDay"
                    class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                    :class="
                      vacationResponder.enableLastDay
                        ? ''
                        : 'border border-[#333333]'
                    "
                  />
                  <ClientOnly>
                    <fa
                      class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                      :icon="['fas', 'check']"
                    />
                  </ClientOnly>
                </div>
                <div class="inline-block font-semibold">
                  <p class="text-[#333333] font-semibold">Last day:</p>
                </div>
              </label>
              <div
                v-if="!vacationResponder.enableLastDay"
                class="text-[#707070] bg-[#F1F2F6] border-[1px] border-[#C2C2C2] rounded-full min-w-[160px] h-[27px] flex items-center px-4"
              >
                (optional)
              </div>
              <SourcePostListWeekMonthDatePicker
                v-else
                class="date-select-box"
                calender-height="h-[27px] pl-[16px] !justify-start bg-white !text-[#333333] !font-normal border border-[#C2C2C2]"
                :currentTab="currentTab"
                :selectedDate="
                  selectedLastDay ||
                  new Date(new Date().setDate(new Date().getDate() + 7))
                "
                :weekRange="weekRange"
                :monthYear="currentMonthYear"
                :currentTitle="currentTitle"
                @update:date="handleSetLastDay"
                @update:range="
                  (range: WeekRange) => {
                    weekRange = range
                    synchronizeCalendarRange()
                  }
                "
                @update:monthYear="setMonthYear"
                ><template v-slot:button>
                  <button
                    @click.stop="selectedLastDay = new Date()"
                    class="text-sm text-[#525252] pl-8"
                  >
                    Today
                  </button>
                </template></SourcePostListWeekMonthDatePicker
              >
            </div>
          </div>
          <div class="flex space-x-[28px] mt-3">
            <p class="text-[#333333] font-semibold">Subject:</p>
            <input
              v-model="vacationResponder.subject"
              type="text"
              class="outline-none px-4 border border-[#C2C2C2] w-full max-w-[534px] rounded-full"
            />
          </div>
          <div class="flex space-x-4 mt-3">
            <p class="text-[#333333] font-semibold">Message:</p>
            <div class="flex flex-col">
              <button
                v-if="planText"
                class="text-left text-[#3964D0]"
                @click.stop="togglePlainOrRichText"
              >
                {{ !planText ? '« Plain Text' : 'Rich formatting »' }}
              </button>
              <textarea
                v-if="planText"
                v-model="vacationResponder.message"
                class="mt-1.5 rounded-lg border border-[#C2C2C2] outline-none px-4 py-2 w-[100vw] min-h-[120px] max-w-[707px]"
              />
              <SourceHubEmailsSettingsTextEditor
                v-else
                class="max-w-[707] text-editor-box"
                :signature="{
                  id: 0,
                  text: '',
                  description: vacationResponder.message,
                }"
                @set-text="vacationResponder.message = $event"
                ><template v-slot:text>
                  <button
                    class="text-left my-1.5 text-[#3964D0]"
                    @click.stop="togglePlainOrRichText"
                  >
                    {{ !planText ? '« Plain Text' : 'Rich formatting »' }}
                  </button></template
                ></SourceHubEmailsSettingsTextEditor
              >
              <div class="flex flex-col items-start mt-[6px]">
                <label
                  class="w-full relative flex space-x-2 items-start cursor-pointer"
                  for="my_contact"
                >
                  <div class="w-4 h-4 relative mt-0.5">
                    <input
                      ref="my_contact"
                      id="my_contact"
                      type="checkbox"
                      v-model="vacationResponder.myContent"
                      :checked="vacationResponder.myContent"
                      class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                      :class="
                        vacationResponder.myContent
                          ? ''
                          : 'border border-[#333333]'
                      "
                    />
                    <ClientOnly>
                      <fa
                        class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                        :icon="['fas', 'check']"
                      />
                    </ClientOnly>
                  </div>
                  <div class="inline-block font-semibold">
                    <p class="text-[#333333]">
                      Only send a response to people in my Contacts
                    </p>
                  </div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </template>
    </SourceHubEmailsSettingsGeberalFeaturesRadioOptions>
  </div>
</template>

<style lang="scss" scoped>
.toggle-check-1 {
  &:checked {
    @apply bg-[#4A71D4];
  }
  &:checked + .check-1 {
    @apply opacity-100;
    color: white;
  }
}
</style>
