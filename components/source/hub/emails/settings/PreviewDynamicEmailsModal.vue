<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()

const enableDynamicEmail = ref(false)
const enableDynamicEmailSender = ref(false)
</script>

<template>
  <div
    class="bg-white max-w-[678px] rounded-2xl fixed z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
  >
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="flex justify-between items-center px-6 py-3.5">
        <p class="text-lg font-semibold text-[#505050]">
          Preview dynamic emails
        </p>
        <SharedIconHubEmailsCrossIcon
          @click.stop="store.commit('emails/SHOW_DYNAMIC_EMAILS_MODAL', false)"
          class="w-4 h-4 cursor-pointer"
        />
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="px-6 py-3.5">
        <p class="text-[#525252]">
          Use the settings below to display dynamic emails during development.
          This is only recommended for test accounts and in private browsing
          mode. <span class="text-[#3964D0]">Learn more</span>
        </p>
        <div class="flex space-x-2 items-start mt-5">
          <label
            class="w-full h-5 relative flex space-x-2 items-start cursor-pointer"
            for="dynamic_email_modal"
          >
            <div class="w-4 h-4 relative mt-0.5">
              <input
                ref="dynamic_email_modal"
                id="dynamic_email_modal"
                type="checkbox"
                v-model="enableDynamicEmail"
                :checked="enableDynamicEmail ? true : false"
                class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                :class="enableDynamicEmail ? '' : 'border border-[#707070]'"
              />
              <ClientOnly>
                <fa
                  class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                  :icon="['fas', 'check']"
                />
              </ClientOnly>
            </div>
            <div class="inline-block whitespace-nowrap">
              <p class="text-[#333333]">
                <span>Always allow dynamic emails from</span
                ><span class="text-[#333333] font-semibold">
                  <EMAIL></span
                >
              </p>
            </div>
          </label>
        </div>
        <div class="flex flex-col space-x-[22px] items-start mt-3.5">
          <label
            class="w-full h-5 relative flex space-x-2 items-start cursor-pointer"
            for="dynamic_email_sender"
          >
            <div class="w-4 h-4 relative mt-0.5">
              <input
                ref="dynamic_email_sender"
                id="dynamic_email_sender"
                type="checkbox"
                v-model="enableDynamicEmailSender"
                :checked="enableDynamicEmailSender ? true : false"
                class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                :class="
                  enableDynamicEmailSender
                    ? ''
                    : 'border border-[#707070] border-opacity-30'
                "
              />
              <ClientOnly>
                <fa
                  class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                  :icon="['fas', 'check']"
                />
              </ClientOnly>
            </div>
            <div class="inline-block whitespace-nowrap">
              <p class="text-[#333333]">
                Always allow dynamic emails from this sender:
              </p>
            </div>
          </label>
          <div class="w-[96%] mt-[6px]">
            <input
              class="w-full bg-[#F1F2F6] text-[#333333] rounded-full border-none outline-none px-6 py-3"
              type="text"
            />
            <p class="text-[#707070] !mt-[6px]">
              Emails must be SPF, DKIM and DMARC-authenticated.
            </p>
          </div>
        </div>
        <!-- <input
          class="w-full bg-[#F1F2F6] text-[#333333] rounded-full border-none outline-none px-6 py-3"
          type="text"
        />
        <p class="text-[#707070]">
          Emails must be SPF, DKIM and DMARC-authenticated.
        </p> -->
      </div>
    </div>
    <div class="flex justify-end items-center space-x-2 px-6 py-3.5">
      <button
        class="flex justify-center items-center rounded-full font-semibold text-[#4A71D4] border-[1px] border-[#4A71D4] w-[104px] h-[35px]"
      >
        Cancel
      </button>
      <button
        class="flex justify-center items-center rounded-full font-semibold text-[#FFFFFF] bg-[#4A71D4] w-[104px] h-[35px]"
      >
        Save
      </button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.toggle-check-1 {
  &:checked {
    @apply bg-[#4A71D4];
  }
  &:checked + .check-1 {
    @apply opacity-100;
    color: white;
  }
}
</style>
