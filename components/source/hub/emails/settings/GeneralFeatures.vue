<script setup lang="ts">
import { useStore } from 'vuex'
import type {
  GeneralSettings,
  VacationResponder,
} from '~/types/hubEmailsSettings'

const store = useStore()
const router = useRouter()

interface SendCancellationPeriod {
  id: number
  text: string
}

const sendCancellationPeriods: SendCancellationPeriod[] = [
  { id: 1, text: '5' },
  { id: 2, text: '10' },
  { id: 3, text: '20' },
  { id: 4, text: '30' },
]

const selectedReplyBehavior = ref('')

const replyBehavior = [
  {
    label: 'Reply',
    value: 'reply',
    description: '',
  },
  {
    label: 'Reply all',
    value: 'replyall',
    description: '',
  },
]

const selectedHoverAction = ref('enable')

const hoverActions = [
  {
    label: 'Enable hover actions',
    value: 'enable',
    description:
      ' - Quickly gain access to archive, delete, mark as read, and snooze controls on hover.',
  },
  {
    label: 'Disable hover actions',
    value: 'disable',
    description: '',
  },
]

const selectedSendAndArchive = ref('hide send & archive')

const sendAndArchive = [
  {
    label: 'Show "Send & Archive" button in reply',
    value: 'show send & archive',
    description: '',
  },
  {
    label: 'Hide "Send & Archive" button in reply',
    value: 'hide send & archive',
    description: '',
  },
]

const fontFamilies = [
  { label: 'Sans Serif', value: 'sans-serif' },
  { label: 'Serif', value: 'serif' },
  { label: 'Fixed Width', value: 'monospace' },
  { label: 'Wide', value: 'Arial Black' },
  { label: 'Narrow', value: 'Arial Narrow' },
  { label: 'Comic Sans MS', value: 'Comic Sans MS' },
  { label: 'Garamond', value: 'Garamond' },
  { label: 'Georgia', value: 'Georgia' },
  { label: 'Tahoma', value: 'Tahoma' },
  { label: 'Trebuchet MS', value: 'Trebuchet MS' },
  { label: 'Verdana', value: 'Verdana' },
]
const selectedFont = ref('Sans Serif')
const updatedFontFamily = (label: string, value: string) => {
  selectedFont.value = label
  // props.editor?.chain().focus().setFontFamily(value).run()
}

const headingArray = ref([
  {
    id: 4,
    label: 'Small',
    fontSize: 14,
    selected: false,
  },
  {
    id: 3,
    label: 'Normal',
    fontSize: 16,
    selected: true,
  },
  {
    id: 2,
    label: 'Large',
    fontSize: 18,
    selected: false,
  },
  {
    id: 1,
    label: 'Huge',
    fontSize: 22,
    selected: false,
  },
])
const setHeading = (id: any, fontSize: number) => {
  // props.editor?.chain().focus().setHeading({ level: id }).run()
  // props.editor?.chain().focus().setFontSize(fontSize).run()
  headingArray.value.forEach((heading) => {
    if (heading.id === id) {
      heading.selected = true
    } else {
      heading.selected = false
    }
  })
}
const applyTextColor = (color: any) => {
  // props.editor?.chain().focus().setColor(color).run()
}

const applyBackgroundColor = (color: any) => {
  // props.editor?.chain().focus().setHighlight({ color: color }).run()
}
const showFontFamilyMenu = ref(false)
const showColorPicker = ref(false)
const showHeaderModal = ref(false)

const selectedImagesOption = ref('2')

const imagesOption = [
  {
    label: 'Always display external images',
    value: '1',
    description: ' - Learn more',
    className: '!text-[#3964D0]',
  },
  {
    label: 'Ask before displaying external images',
    value: '2',
    description: ' - This option also disables dynamic email.',
  },
]

const enableDynamicEmail = ref(false)

const selectGrammer = ref('suggestions-on')
const grammarOption = [
  {
    label: 'Grammar suggestions on',
    value: 'suggestions-on',
    description: '',
  },
  {
    label: 'Grammar suggestions off',
    value: 'suggestions-off',
    description: '',
  },
]

const selectSpelling = ref('suggestions-on')
const spellingOption = [
  {
    label: 'Spelling suggestions on',
    value: 'suggestions-on',
    description: '',
  },
  {
    label: 'Spelling suggestions off',
    value: 'suggestions-off',
    description: '',
  },
]

const selectAutocorrect = ref('on')
const autocorrectOption = [
  {
    label: 'Autocorrect on',
    value: 'on',
    description: '',
  },
  {
    label: 'Autocorrect off',
    value: 'off',
    description: '',
  },
]

const selectSmartCompose = ref('suggestions-on')
const smartComposeOption = [
  {
    label: 'Writing suggestions on',
    value: 'suggestions-on',
    description: '',
  },
  {
    label: 'Writing suggestions off',
    value: 'suggestions-off',
    description: '',
  },
]

const selectPersonalization = ref('on')
const personalizationOption = [
  {
    label: 'Personalization on',
    value: 'on',
    description: '',
  },
  {
    label: 'Personalization off',
    value: 'off',
    description: '',
  },
]

const selectConversation = ref('on')
const conversationOption = [
  {
    label: 'Conversation view on',
    value: 'on',
    description: '',
  },
  {
    label: 'Conversation view off',
    value: 'off',
    description: '',
  },
]

const emailToReply = ref(true)
const emailToFollow = ref(true)

const selectSmartReply = ref('on')
const smartReplyOption = [
  {
    label: 'Smart Reply on',
    value: 'on',
    description: '',
  },
  {
    label: 'Smart Reply off',
    value: 'off',
    description: '',
  },
]

const smartFeature = ref(true)
const packageTracking = ref(false)

const selectDesktopNotifications = ref('mail-notifications')
const desktopNotificationsOption = [
  {
    label: 'New mail notifications on',
    value: 'new-mail',
    description:
      ' - Notify me when any new message arrives in my inbox or primary tab',
  },
  {
    label: 'Important mail notifications on',
    value: 'important-mail',
    description:
      ' - Notify me only when an important message arrives in my inbox',
  },
  {
    label: 'Mail notifications off',
    value: 'mail-notifications',
    description: '',
  },
]
interface NotificationSounds {
  id: number
  text: string
}

const notificationSounds: NotificationSounds[] = [
  { id: 1, text: 'None' },
  { id: 2, text: 'Welcome' },
  { id: 3, text: 'Nudge' },
  { id: 4, text: 'Snappy' },
  { id: 5, text: 'Sweet' },
  { id: 6, text: 'Sweet' },
  { id: 7, text: 'Whistle' },
  { id: 8, text: 'Tennis' },
  { id: 9, text: 'Music box' },
  { id: 10, text: 'Tones' },
  { id: 11, text: 'Calm' },
  { id: 12, text: 'Treasure' },
  { id: 13, text: 'Piggyback' },
  { id: 14, text: 'Shrink ray' },
]
const selectShortcuts = ref('off')
const ShortcutsOptions = [
  {
    label: 'Keyboard shortcuts off',
    value: 'off',
    description: '',
  },
  {
    label: 'Keyboard shortcuts on',
    value: 'on',
    description: '',
  },
]
const selectButtonLabels = ref('icons')
const buttonLabelsOptions = [
  {
    label: 'Icons',
    value: 'icons',
    description: '',
  },
  {
    label: 'Text',
    value: 'text',
    description: '',
  },
]
const selectContacts = ref('next-time')
const contactsOptions = [
  {
    label:
      'When I send a message to a new person, add them to Other Contacts so that I can auto-complete to them next time',
    value: 'next-time',
    description: '',
  },
  {
    label: `I'll add contacts myself`,
    value: 'myself',
    description: '',
  },
]
const selectPersonalLevelIndicators = ref('no')
const personalLevelIndicatorsOptions = [
  {
    label: 'No indicators',
    value: 'no',
    description: '',
  },
  {
    label: 'Show indicators',
    value: 'show',
    description:
      ' - Display an arrow ( › ) by messages sent to my address (not a mailing list), and a double arrow ( » ) by messages sent only to me.',
  },
]
const selectSnippets = ref('show')
const snippetsOptions = [
  {
    label: 'Show snippets',
    value: 'show',
    description: ' - Show snippets of the message (like Google web search!).',
  },
  {
    label: 'No snippets',
    value: 'no',
    description: ' - Show subject only.',
  },
]
const signature = ref(null)

const generalSettings = computed<GeneralSettings>(
  () => store.state.emails.gmailGeneral,
)

const generalSettingsLocal = ref<GeneralSettings>({
  vacationResponder: null,
})

const isGeneralSettingsChanged = computed(() => {
  return !_isEqual(generalSettings.value, generalSettingsLocal.value)
})

const handleVacationResponderChange = (
  vacationResponder: VacationResponder,
) => {
  generalSettingsLocal.value.vacationResponder = vacationResponder
}

onMounted(() => {
  generalSettingsLocal.value = _cloneDeep(generalSettings.value)
})

const saveChanges = () => {
  store.commit(
    'emails/SET_GMAIL_GENERAL',
    _cloneDeep(generalSettingsLocal.value),
  )
}
const previousPath = computed(() => store.state.emails.previousPath)
const cancel = () => {
  router.push(previousPath.value)
}
</script>

<template>
  <div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <p class="text-[#333333] font-semibold">Maximum page size:</p>
        <div class="">
          <div class="flex space-x-2">
            <p class="text-[#333333] font-semibold">
              Send cancellation period:
            </p>
            <BaseDropsDown
              class="border-[#C2C2C2] border rounded-full language-dropdown"
              :options="sendCancellationPeriods"
              labelKey="text"
              placeholder="5"
              :menuWidth="69"
              :menuHeight="27"
              :dropdownWidth="69"
              :dropdownMaxHeight="290"
              menuBgColor="#FFFFFF"
              menuTextColor="#333333"
              dropsdownTextColor="#333333"
              scrollbarTrackColor="#a1cdff50"
              scrollbarThumbColor="#a1cdff"
              hoverColor="#4A71D4"
              hoverTextColor="#FFFFFF"
            />
            <p class="text-[#333333] font-semibold">seconds</p>
            <!-- @change="handleSelectStatus" -->
          </div>
        </div>
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <div>
          <p class="text-[#333333] font-semibold">Default reply behavior:</p>
          <p class="text-[#3964D0]">Learn more</p>
        </div>
        <InputsRadioOptionGroup
          v-model="selectedReplyBehavior"
          :options="replyBehavior"
        />
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <div>
          <p class="text-[#333333] font-semibold">Hover actions:</p>
        </div>
        <InputsRadioOptionGroup
          v-model="selectedHoverAction"
          :options="hoverActions"
        />
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <div>
          <p class="text-[#333333] font-semibold">Send and Archive:</p>
          <p class="text-[#3964D0]">Learn more</p>
        </div>
        <InputsRadioOptionGroup
          v-model="selectedSendAndArchive"
          :options="sendAndArchive"
        />
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <div>
          <p class="text-[#333333] font-semibold">Default text style:</p>
          <p class="text-[#525252] text-sm !mt-2">
            (Use the 'Remove formatting' button on the toolbar to reset the
            default text style)
          </p>
        </div>
        <div class="text-style-box max-w-[298px] p-4">
          <div class="flex items-center">
            <div
              id="font-family-menu"
              class="relative hover:bg-[#F1F2F6] w-[113px] h-8 rounded flex justify-between items-center px-2 py-0 cursor-pointer"
              :class="showFontFamilyMenu ? 'bg-[#F1F2F6]' : ''"
              @click.stop="
                (showFontFamilyMenu = !showFontFamilyMenu),
                  (showColorPicker = false),
                  (showHeaderModal = false)
              "
            >
              <p
                class="whitespace-nowrap text-[#525252] font-semibold text-ellipsis overflow-hidden"
              >
                {{ selectedFont }}
              </p>
              <div class="">
                <SharedIconHubEmailsDownArrow class="w-2 h-1" />
              </div>
              <SourceHubEmailsFontFamilyMenu
                v-if="showFontFamilyMenu"
                :options="fontFamilies"
                :updatedFontFamily="updatedFontFamily"
                :label="selectedFont"
                class="!left-0 !top-[-407px]"
              />
            </div>
            <div class="w-[1px] h-[20px] bg-[#707070] bg-opacity-25"></div>
            <div
              class="mx-2 flex space-x-2 items-center justify-center hover:bg-[#F1F2F6] w-[42px] h-8 rounded cursor-pointer px-2 py-0 relative"
              :class="showHeaderModal ? 'bg-[#F1F2F6]' : ''"
              @click.stop="
                (showHeaderModal = !showHeaderModal),
                  (showColorPicker = false),
                  (showFontFamilyMenu = false)
              "
            >
              <div><SharedIconHubEmailsSizeButton /></div>
              <div><SharedIconHubEmailsDownArrow class="w-2 h-1" /></div>
              <SourceHubEmailsHeadingMenu
                v-if="showHeaderModal"
                @click.stop=""
                :headingArray="headingArray"
                :setHeading="setHeading"
              />
            </div>
            <div class="w-[1px] h-[20px] bg-[#707070] bg-opacity-25"></div>
            <div
              class="mx-1.5 hover:bg-[#F1F2F6] w-10 h-8 rounded cursor-pointer flex items-center space-x-0.5 px-2 py-0 relative"
              :class="showColorPicker ? 'bg-[#F1F2F6]' : ''"
              @click.stop="
                (showColorPicker = !showColorPicker),
                  (showHeaderModal = false),
                  (showFontFamilyMenu = false)
              "
            >
              <SharedIconHubEmailsTextColorButton />
              <SharedIconHubEmailsDownArrow class="w-2 h-1" />
              <div
                v-show="showColorPicker"
                class="color-box max-w-[396px] min-w-[396px] max-h-[252px] p-4 pt-3 color-box absolute top-[-254px] min-[1490px]:left-0 min-[1310px]:-left-[180px] right-0"
                @click.stop=""
              >
                <div class="grid grid-cols-[1fr_1fr] gap-3">
                  <div class="flex flex-col space-y-3.5">
                    <p class="text-[#525252]">Background color</p>
                    <SourceColorPicker
                      :applyColor="applyBackgroundColor"
                      title="Background color"
                      selectedColorCode="#FFFFFF"
                    />
                  </div>
                  <div class="flex flex-col space-y-3.5">
                    <p class="text-[#525252]">Text color</p>
                    <SourceColorPicker
                      :applyColor="applyTextColor"
                      title="Text color"
                      selectedColorCode="#000000"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div class="w-[1px] h-[20px] bg-[#707070] bg-opacity-25"></div>
            <div class="flex items-center">
              <!-- editor.chain().focus().unsetAllMarks().run(),
                    editor.chain().focus().clearNodes().run(), -->
              <button
                class="px-2 py-0 hover:bg-[#F1F2F6] w-8 h-8 rounded cursor-pointer flex justify-center items-center"
                @click="
                  ((showColorPicker = false),
                  (showHeaderModal = false),
                  (showFontFamilyMenu = false)),
                    applyTextColor('#000000'),
                    applyBackgroundColor('#ffffff')
                "
              >
                <SharedIconHubEmailsRemoveFormattingButton />
              </button>
            </div>
          </div>
          <p class="text-sm text-[#525252]">
            This is what your body text will look like.
          </p>
        </div>
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <div>
          <p class="text-[#333333] font-semibold">Images:</p>
        </div>
        <InputsRadioOptionGroup
          v-model="selectedImagesOption"
          :options="imagesOption"
        />
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <div>
          <p class="text-[#333333] font-semibold">Dynamic email:</p>
          <p class="text-[#4A71D4] !mt-1.5">Learn more</p>
        </div>
        <div class="flex flex-col items-start">
          <label
            class="w-full relative flex space-x-2 items-start cursor-pointer"
            for="dynamic_email"
          >
            <div class="w-4 h-4 relative mt-0.5">
              <input
                ref="dynamic_email"
                id="dynamic_email"
                type="checkbox"
                v-model="enableDynamicEmail"
                :checked="enableDynamicEmail ? true : false"
                class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                :class="enableDynamicEmail ? '' : 'border border-[#333333]'"
              />
              <ClientOnly>
                <fa
                  class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                  :icon="['fas', 'check']"
                />
              </ClientOnly>
            </div>
            <div class="inline-block font-semibold">
              <p
                :class="
                  enableDynamicEmail
                    ? 'text-[#333333]'
                    : 'text-[#333333] text-opacity-30'
                "
              >
                <span>Enable dynamic email</span
                ><span class="text-[#525252]">
                  - Display dynamic email content when available.</span
                >
              </p>
            </div>
          </label>
          <p
            class="text-[#3964D0] !mt-2 font-normal cursor-pointer"
            @click="store.commit('emails/SHOW_DYNAMIC_EMAILS_MODAL', true)"
          >
            Developer settings
          </p>
        </div>
      </div>
    </div>
    <SourceHubEmailsSettingsGeberalFeaturesRadioOptions
      v-model="selectGrammer"
      label="Grammar:"
      description=""
      :options="grammarOption"
    />
    <SourceHubEmailsSettingsGeberalFeaturesRadioOptions
      v-model="selectSpelling"
      label="Spelling:"
      description=""
      :options="spellingOption"
    />
    <SourceHubEmailsSettingsGeberalFeaturesRadioOptions
      v-model="selectAutocorrect"
      label="Autocorrect:"
      description=""
      :options="autocorrectOption"
    />
    <SourceHubEmailsSettingsGeberalFeaturesRadioOptions
      v-model="selectSmartCompose"
      label="Smart Compose:"
      description="(predictive writing suggestions appear as you compose an email)"
      :options="smartComposeOption"
    />
    <SourceHubEmailsSettingsGeberalFeaturesRadioOptions
      v-model="selectPersonalization"
      label="Smart Compose personalization:"
      description="(Smart Compose is personalized to your writing style)"
      :options="personalizationOption"
    />
    <SourceHubEmailsSettingsGeberalFeaturesRadioOptions
      v-model="selectConversation"
      label="Conversation View:"
      description="(sets whether emails of the same topic are grouped together)"
      :options="conversationOption"
    />
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <div>
          <p class="text-[#333333] font-semibold">Nudges:</p>
          <p class="text-[#4A71D4] !mt-1.5">Learn more</p>
        </div>
        <div>
          <div class="flex flex-col items-start">
            <label
              class="w-full relative flex space-x-2 items-start cursor-pointer"
              for="email_to_reply"
            >
              <div class="w-4 h-4 relative mt-0.5">
                <input
                  ref="email_to_reply"
                  id="email_to_reply"
                  type="checkbox"
                  v-model="emailToReply"
                  :checked="emailToReply ? true : false"
                  class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                  :class="emailToReply ? '' : 'border border-[#333333]'"
                />
                <ClientOnly>
                  <fa
                    class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                    :icon="['fas', 'check']"
                  />
                </ClientOnly>
              </div>
              <div class="inline-block font-semibold">
                <p class="text-[#333333]">
                  <span>Suggest emails to reply to</span
                  ><span class="text-[#525252] font-normal">
                    - Emails you might have forgotten to respond to will appear
                    at the top of your inbox</span
                  >
                </p>
              </div>
            </label>
          </div>
          <div class="flex flex-col items-start mt-[6px]">
            <label
              class="w-full relative flex space-x-2 items-start cursor-pointer"
              for="email_to_follow"
            >
              <div class="w-4 h-4 relative mt-0.5">
                <input
                  ref="email_to_follow"
                  id="email_to_follow"
                  type="checkbox"
                  v-model="emailToFollow"
                  :checked="emailToFollow ? true : false"
                  class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                  :class="emailToFollow ? '' : 'border border-[#333333]'"
                />
                <ClientOnly>
                  <fa
                    class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                    :icon="['fas', 'check']"
                  />
                </ClientOnly>
              </div>
              <div class="inline-block font-semibold">
                <p class="text-[#333333]">
                  <span>Suggest emails to follow up on</span
                  ><span class="text-[#525252] font-normal">
                    - Sent emails you might need to follow up on will appear at
                    the top of your inbox</span
                  >
                </p>
              </div>
            </label>
          </div>
        </div>
      </div>
    </div>
    <SourceHubEmailsSettingsGeberalFeaturesRadioOptions
      v-model="selectSmartReply"
      label="Smart Reply:"
      description="(Show suggested replies when available.)"
      :options="smartReplyOption"
    />
    <SourceHubEmailsSettingsPreviewPane />
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <div>
          <p class="text-[#333333] font-semibold">Smart features:</p>
          <p class="text-[#4A71D4] !mt-1.5">Learn more</p>
        </div>
        <div>
          <div class="flex flex-col items-start">
            <label
              class="w-full relative flex space-x-2 items-start cursor-pointer"
              for="smart_feature"
            >
              <div class="w-4 h-4 relative mt-0.5">
                <input
                  ref="smart_feature"
                  id="smart_feature"
                  type="checkbox"
                  v-model="smartFeature"
                  :checked="smartFeature ? true : false"
                  class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                  :class="smartFeature ? '' : 'border border-[#333333]'"
                />
                <ClientOnly>
                  <fa
                    class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                    :icon="['fas', 'check']"
                  />
                </ClientOnly>
              </div>
              <div class="inline-block font-semibold">
                <p class="text-[#333333]">
                  <span>Turn on smart features in Gmail, Chat, and Meet</span
                  ><span class="text-[#525252] font-normal">
                    - When you turn this setting on, you agree to let Gmail,
                    Chat, and Meet use your content and activity in these
                    products to provide smart features and</span
                  >
                </p>
              </div>
            </label>
          </div>
        </div>
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <div>
          <p class="text-[#333333] font-semibold">Package tracking:</p>
          <p class="text-[#4A71D4] !mt-1.5">Learn more</p>
        </div>
        <div>
          <div class="flex flex-col items-start">
            <label
              class="w-full relative flex space-x-2 items-start cursor-pointer"
              for="package_tracking"
            >
              <div class="w-4 h-4 relative mt-0.5">
                <input
                  ref="package_tracking"
                  id="package_tracking"
                  type="checkbox"
                  v-model="packageTracking"
                  :checked="packageTracking ? true : false"
                  class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                  :class="packageTracking ? '' : 'border border-[#333333]'"
                />
                <ClientOnly>
                  <fa
                    class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                    :icon="['fas', 'check']"
                  />
                </ClientOnly>
              </div>
              <div class="inline-block font-semibold">
                <p class="text-[#333333]">
                  <span>Turn on package tracking</span
                  ><span class="text-[#525252] font-normal">
                    - Google will share the tracking numbers for your parcels
                    with delivery companies. You'll receive status updates here
                    in Gmail.</span
                  >
                </p>
              </div>
            </label>
          </div>
        </div>
      </div>
    </div>
    <SourceHubEmailsSettingsGeberalFeaturesRadioOptions
      v-model="selectDesktopNotifications"
      label="Desktop notifications:"
      description="(allows Gmail to display pop-up notifications on your desktop when new email messages arrive)"
      :options="desktopNotificationsOption"
    >
      <template v-slot:learn-more>
        <p class="text-[#4A71D4] !mt-1.5">Learn more</p>
      </template>
      <template v-slot:text>
        <p class="text-[#4A71D4] !mb-1.5">
          Click here to enable desktop notifications for Gmail.
        </p>
      </template>
      <template
        v-if="
          selectDesktopNotifications === 'new-mail' ||
          selectDesktopNotifications === 'important-mail'
        "
        v-slot:notification-sound
      >
        <div class="flex space-x-1 items-center mt-[26px]">
          <p class="text-[#333333] font-semibold">Email notification sounds:</p>
          <BaseDropsDown
            class="border-[#C2C2C2] border rounded-full language-dropdown"
            :options="notificationSounds"
            labelKey="text"
            placeholder="Welcome"
            :menuWidth="125"
            :menuHeight="27"
            :dropdownWidth="125"
            :dropdownMaxHeight="290"
            menuBgColor="#FFFFFF"
            menuTextColor="#333333"
            dropsdownTextColor="#333333"
            scrollbarTrackColor="#a1cdff50"
            scrollbarThumbColor="#a1cdff"
            hoverColor="#4A71D4"
            hoverTextColor="#FFFFFF"
          />
        </div>
      </template>
    </SourceHubEmailsSettingsGeberalFeaturesRadioOptions>
    <SourceHubEmailsSettingsStarDrag />
    <SourceHubEmailsSettingsGeberalFeaturesRadioOptions
      v-model="selectShortcuts"
      label="Keyboard shortcuts:"
      description=""
      :options="ShortcutsOptions"
    >
      <template v-slot:learn-more>
        <p class="text-[#4A71D4] !mt-1.5">Learn more</p>
      </template>
    </SourceHubEmailsSettingsGeberalFeaturesRadioOptions>
    <SourceHubEmailsSettingsGeberalFeaturesRadioOptions
      v-model="selectButtonLabels"
      label="Button labels:"
      description=""
      :options="buttonLabelsOptions"
      ><template v-slot:learn-more>
        <p class="text-[#4A71D4] !mt-1.5">Learn more</p>
      </template></SourceHubEmailsSettingsGeberalFeaturesRadioOptions
    >
    <SourceHubEmailsSettingsGeberalFeaturesRadioOptions
      v-model="selectContacts"
      label="Create contacts for auto-complete:"
      description=""
      :options="contactsOptions"
    ></SourceHubEmailsSettingsGeberalFeaturesRadioOptions>
    <SourceHubEmailsSettingsSignature ref="signature" />
    <SourceHubEmailsSettingsGeberalFeaturesRadioOptions
      v-model="selectPersonalLevelIndicators"
      label="Personal level indicators:"
      description=""
      :options="personalLevelIndicatorsOptions"
    />
    <SourceHubEmailsSettingsGeberalFeaturesRadioOptions
      v-model="selectSnippets"
      label="Snippets:"
      description=""
      :options="snippetsOptions"
    />
    <SourceHubEmailsSettingsVacationResponder
      @onVacationResponder="handleVacationResponderChange"
    />
    <div class="w-full flex justify-center items-center space-x-2 pt-4">
      <button
        class="flex justify-center items-center w-26 h-[35px] rounded-full border border-[#4A71D4] text-[#4A71D4]"
        @click="cancel"
      >
        Cancel
      </button>
      <button
        class="flex justify-center items-center w-[149px] h-[35px] rounded-full bg-[#4A71D4] text-[#FFFFFF]"
        @click.stop="($refs as any).signature.saveChanges(), saveChanges()"
      >
        Save Changes
      </button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.text-style-box {
  background: var(--unnamed-color-ffffff) 0% 0% no-repeat padding-box;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 8px #2228313d;
  border-radius: 8px;
}
.color-box {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px rgba(34, 40, 49, 0.0784313725);
  border-radius: 8px;
}
.toggle-check-1 {
  &:checked {
    @apply bg-[#4A71D4];
  }
  &:checked + .check-1 {
    @apply opacity-100;
    color: white;
  }
}
</style>
