<script setup lang="ts">
const props = defineProps<{
  label: string
  description: string
  options: {
    label: string
    value: string
    description?: string
    className?: string
  }[]
}>()
const model = defineModel({ type: String })
</script>

<template>
  <div class="border-b-[2px] border-[#F1F2F6]">
    <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
      <div>
        <p class="text-[#333333] font-semibold">{{ label }}</p>
        <p v-if="description" class="text-[#525252] text-sm !mt-2">
          {{ description }}
        </p>
        <slot name="learn-more"></slot>
      </div>
      <div>
        <slot name="text"></slot>
        <InputsRadioOptionGroup v-model="model" :options="options" />
        <slot name="notification-sound"></slot>
        <slot name="vacation-responder"></slot>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
