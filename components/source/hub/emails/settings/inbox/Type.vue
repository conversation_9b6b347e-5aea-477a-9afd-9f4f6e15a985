<script setup lang="ts">
import { useStore } from 'vuex'

interface InboxType {
  id: number
  text: string
}
interface Category {
  id: number
  text: string
  description: string
  examples: string[]
  onHover: boolean
  checked: boolean
  disabled: boolean
}

interface CategoryText {
  id: number
  description: string
  image: string
}

const router = useRouter()
const store = useStore()

const inboxType: InboxType[] = [
  { id: 1, text: 'Default' },
  { id: 2, text: 'Important first' },
  { id: 3, text: 'Unread first' },
  { id: 4, text: 'Starred first' },
  { id: 4, text: 'Priority Inbox' },
  { id: 4, text: 'Multiple Inboxes' },
]
const categoriesLists = ref([
  {
    id: 1,
    text: 'Primary',
    description: `Person-to-person conversations and messages that don't appear in other tabs.`,
    examples: ['Sharparchive', 'Yahoo'],
    onHover: false,
    checked: true,
    disabled: true,
  },
  {
    id: 2,
    text: 'Promotions',
    description: `Marketing, interests, social and political causes, and other promotional emails.`,
    examples: ['Grammarly Insights', 'Canva', 'CORSAIR'],
    onHover: false,
    checked: true,
    disabled: false,
  },
  {
    id: 3,
    text: 'Social',
    description: `Messages from social networks, media-sharing sites, online dating services and other social websites.`,
    examples: [
      'Hasan on Facebook',
      'Facebook Friend Suggestion',
      'Dec Factor on Facebook',
    ],
    onHover: false,
    checked: true,
    disabled: false,
  },
  {
    id: 4,
    text: 'Updates',
    description: `Personal, auto-generated updates including confirmations, receipts, bills and statements.`,
    examples: ['Google', 'Grammarly Insights', 'Facebook'],
    onHover: false,
    checked: true,
    disabled: false,
  },
  {
    id: 5,
    text: 'Forums',
    description: `Messages from online groups, discussion boards and mailing lists.`,
    examples: [
      'Lily at Cultivated Culture',
      'Eric from Viralstyle',
      'Leticia from HolaBrief',
    ],
    onHover: false,
    checked: true,
    disabled: false,
  },
])
const showBuildingPromotion = ref(true)
const starredMessages = ref(true)
const bundlingPromotions = ref(true)
const checkUncheckCategories = (id: number) => {
  categoriesLists.value.forEach((categoriesList) => {
    if (categoriesList.id === id) {
      categoriesList.checked = !categoriesList.checked
      if (categoriesList.text === 'Promotions') {
        showBuildingPromotion.value = categoriesList.checked
      }
    }
  })
}
const showonHoverText = ref<Category | null>(null)
const showCategoryText = ref(false)
const checkOnHoverCategories = (id: number) => {
  categoriesLists.value.forEach((categoriesList) => {
    if (categoriesList.id === id) {
      showonHoverText.value = categoriesList
    }
  })
}
const categoriRelatedText = {
  id: 1,
  description: `Choose which message categories to show as Inbox tabs. Other messages will appear in the Primary tab.<br><br> Deselect all categories to go back to your old Inbox.`,
  image: '/images/icon/io_tabbed_inbox1.png',
}
const starredMessageText = {
  id: 2,
  description:
    'Select to show starred messages from all tabs in the Primary tab.<br><br> Use this option to track important messages that you want to follow up on.',
  image: '/images/icon/primary_tab.png',
}
const buildPromotionText = {
  id: 3,
  description:
    'Highlight high-value email in bundles and show rich previews (i.e. deal badges, logos, images).',
  image: '/images/icon/highlight.png',
}
const categoriesText = ref<CategoryText | null>(null)
const changeCategoriRelatedText = (id: number) => {
  if (id === 2) {
    categoriesText.value = starredMessageText
  } else if (id === 3) {
    categoriesText.value = buildPromotionText
  } else {
    categoriesText.value = categoriRelatedText
  }
}
const replyBehavior = [
  {
    label: 'Reply',
    value: 'reply',
    description: '',
  },
  {
    label: 'Reply all',
    value: 'replyall',
    description: '',
  },
]
const selectedMarker = ref('show')
const markersList = ref([
  {
    label: 'Show markers',
    value: 'show',
    description: ' - Show a marker ( ) by messages marked as important.',
  },
  {
    label: 'No markers',
    value: 'no',
    description: '',
  },
])
const selectedPastActions = ref('yes')
const pastActions = ref([
  {
    label: '',
    value: 'yes',
    description:
      'Use my past actions to predict which messages are important to me.',
  },
  {
    label: '',
    value: 'no',
    description: `Don't use my past actions to predict which messages are important.`,
  },
])
const selectedFilter = ref('no_override')
const filters = ref([
  {
    label: 'Override filters',
    value: 'override',
    description:
      ' - Include important messages in the inbox that may have been filtered out.',
  },
  {
    label: `Don't override filters`,
    value: 'no_override',
    description: '',
  },
])
const enableReadingPane = ref(false)
const selectedReadingPositions = ref('no_split')
const readingPositions = ref([
  {
    label: 'No split',
    value: 'no_split',
    description: '',
  },
  {
    label: `Right of inbox`,
    value: 'right_of_inbox',
    description: '',
  },
  {
    label: `Below inbox`,
    value: 'below_inbox',
    description: '',
  },
])

onMounted(() => {
  const gmailInbox = store.state.emails.gmailInbox
  categoriesLists.value.forEach((category) => {
    category.checked = gmailInbox.checkedCategories.includes(category.text)
  })
  changeCategoriRelatedText(1)
})
const previousPath = computed(() => store.state.emails.previousPath)
const handleSaveChanges = () => {
  const selectedCategories = categoriesLists.value
    .filter((category) => category.checked)
    .map((category) => category.text)

  store.commit('emails/SET_CHECKED_INBOX_CATEGORIES', selectedCategories)

  router.push(previousPath.value)
}
const cancel = () => {
  router.push(previousPath.value)
}
</script>

<template>
  <div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <p class="text-[#333333] font-semibold">Inbox type:</p>
        <div class="">
          <div class="flex space-x-2">
            <BaseDropsDown
              class="border-[#C2C2C2] border rounded-full language-dropdown"
              :options="inboxType"
              labelKey="text"
              placeholder="Default"
              :menuWidth="220"
              :menuHeight="27"
              :dropdownWidth="220"
              :dropdownMaxHeight="290"
              menuBgColor="#FFFFFF"
              menuTextColor="#333333"
              dropsdownTextColor="#333333"
              scrollbarTrackColor="#a1cdff50"
              scrollbarThumbColor="#a1cdff"
              hoverColor="#4A71D4"
              hoverTextColor="#FFFFFF"
            />
            <!-- <p class="text-[#333333] font-semibold">seconds</p> -->
            <!-- @change="handleSelectStatus" -->
          </div>
        </div>
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <div>
          <p class="text-[#333333] font-semibold">Categories:</p>
          <p class="text-[#4A71D4] mt-1.5">Learn more</p>
        </div>
        <div class="">
          <div class="flex flex-col space-y-1.5 items-start">
            <label
              v-for="categoriesList in categoriesLists"
              :key="categoriesList.id"
              class="w-full relative flex space-x-2 items-start cursor-pointer"
              :for="categoriesList.id.toString()"
              @mouseover="
                checkOnHoverCategories(categoriesList.id),
                  (showCategoryText = true)
              "
              @mouseleave="
                checkOnHoverCategories(0), (showCategoryText = false)
              "
            >
              <div class="w-4 h-4 relative mt-0.5">
                <input
                  :ref="categoriesList.id.toString()"
                  :id="categoriesList.id.toString()"
                  :disabled="categoriesList.disabled ? true : false"
                  type="checkbox"
                  :checked="categoriesList.checked ? true : false"
                  class="appearance-none w-4 h-4 rounded-sm"
                  :class="[
                    categoriesList.checked ? '' : 'border border-[#333333]',
                    categoriesList.disabled
                      ? 'toggle-check-1 disable'
                      : 'toggle-check-1',
                  ]"
                  @input="checkUncheckCategories(categoriesList.id)"
                />
                <ClientOnly>
                  <fa
                    class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                    :icon="['fas', 'check']"
                  />
                </ClientOnly>
              </div>
              <div class="inline-block font-semibold">
                <p class="text-[#333333] font-semibold">
                  {{ categoriesList.text }}
                </p>
              </div>
            </label>
            <div class="!mt-[22px]">
              <p class="text-[#333333] font-semibold">Starred messages</p>
              <label
                class="w-full relative flex space-x-2 items-start cursor-pointer mt-1.5"
                for="Starred_Messages"
                @mouseover="changeCategoriRelatedText(2)"
                @mouseleave="changeCategoriRelatedText(1)"
              >
                <div class="w-4 h-4 relative mt-0.5">
                  <input
                    ref="Starred_Messages"
                    id="Starred_Messages"
                    type="checkbox"
                    v-model="starredMessages"
                    :checked="starredMessages ? true : false"
                    class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                    :class="starredMessages ? '' : 'border border-[#333333]'"
                  />
                  <ClientOnly>
                    <fa
                      class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                      :icon="['fas', 'check']"
                    />
                  </ClientOnly>
                </div>
                <div class="inline-block font-semibold">
                  <p class="text-[#333333] font-semibold">
                    Include starred in Primary
                  </p>
                </div>
              </label>
            </div>
            <div v-if="showBuildingPromotion" class="!mt-[22px]">
              <p class="text-[#333333] font-semibold">Bundling in Promotions</p>
              <label
                class="w-full relative flex space-x-2 items-start cursor-pointer mt-1.5"
                for="Bundling_Promotions"
                @mouseover="changeCategoriRelatedText(3)"
                @mouseleave="changeCategoriRelatedText(1)"
              >
                <div class="w-4 h-4 relative mt-0.5">
                  <input
                    ref="Bundling_Promotions"
                    id="Bundling_Promotions"
                    type="checkbox"
                    v-model="bundlingPromotions"
                    :checked="bundlingPromotions ? true : false"
                    class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                    :class="bundlingPromotions ? '' : 'border border-[#333333]'"
                  />
                  <ClientOnly>
                    <fa
                      class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                      :icon="['fas', 'check']"
                    />
                  </ClientOnly>
                </div>
                <div class="inline-block font-semibold">
                  <p class="text-[#333333] font-semibold">
                    Enable bundling of top promo emails in Promotions
                  </p>
                </div>
              </label>
            </div>
            <div
              v-if="!showCategoryText"
              class="!mt-[22px] w-full flex justify-between"
            >
              <div class="max-w-[780px]">
                <p
                  class="text-[#525252]"
                  v-html="categoriesText?.description"
                ></p>
              </div>
              <img
                class="w-[343px] h-[143px]"
                :src="categoriesText?.image"
                :alt="categoriesText?.description"
              />
            </div>
            <div
              v-else
              class="!mt-[22px] w-full grid grid-cols-[1fr_1fr] gap-x-[56px]"
            >
              <div>
                <p class="text-[#333333] font-semibold">
                  {{ showonHoverText?.text }}
                </p>
                <p class="text-[#525252] !mt-3">
                  {{ showonHoverText?.description }}
                </p>
              </div>
              <div class="">
                <p class="text-[#333333] font-semibold">Examples</p>
                <ul
                  class="grid [grid-template-columns:repeat(3,minmax(0,max-content))] max-w-[630px] gap-[64px] !mt-3"
                >
                  <li
                    class="text-[#525252] username-clamp"
                    v-for="example in showonHoverText?.examples"
                    :key="example"
                  >
                    {{ example }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <div>
          <p class="text-[#333333] font-semibold">Reading pane:</p>
        </div>
        <div>
          <label
            class="w-full relative flex space-x-2 items-start cursor-pointer mt-1.5"
            for="enable_pane"
          >
            <div class="w-4 h-4 relative mt-0.5">
              <input
                ref="enable_pane"
                id="enable_pane"
                type="checkbox"
                v-model="enableReadingPane"
                :checked="enableReadingPane ? true : false"
                class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                :class="enableReadingPane ? '' : 'border border-[#333333]'"
              />
              <ClientOnly>
                <fa
                  class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                  :icon="['fas', 'check']"
                />
              </ClientOnly>
            </div>
            <div class="inline-block font-semibold">
              <p class="text-[#333333] font-semibold">
                Enable reading pane
                <span class="text-[#525252] font-normal">
                  - provides a way to read emails right beside your list of
                  conversations, making it faster to read and write emails and
                  adding more context.</span
                >
              </p>
            </div>
          </label>
          <p
            class="!mt-[22px] font-semibold"
            :class="
              enableReadingPane ? 'text-[text-[#333333]]' : 'text-[#C2C2C2]'
            "
          >
            Reading pane position
          </p>
          <InputsRadioOptionGroup
            class="mt-1.5"
            v-model="selectedReadingPositions"
            :enableReadingPane="enableReadingPane"
            :options="readingPositions"
          />
        </div>
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <div>
          <p class="text-[#333333] font-semibold">Importance markers:</p>
        </div>
        <div>
          <InputsRadioOptionGroup
            v-model="selectedMarker"
            :options="markersList"
          />
          <p class="text-[#525252] !mt-[24px]">
            Gmail analyses your new incoming messages to predict what's
            important, considering things like how you've treated similar
            messages in the past, how directly the message is addressed to you
            and many other factors.
            <span class="text-[#3964D0]">Learn more</span>
          </p>
          <InputsRadioOptionGroup
            class="mt-[18px]"
            v-model="selectedPastActions"
            :options="pastActions"
          />
          <p class="text-[#333333] pl-[28px]">
            Note: This will erase action history and is likely to reduce the
            accuracy of importance predictions.
          </p>
        </div>
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <div>
          <p class="text-[#333333] font-semibold">Filtered mail:</p>
        </div>
        <div>
          <InputsRadioOptionGroup v-model="selectedFilter" :options="filters" />
        </div>
      </div>
    </div>
    <div class="w-full flex justify-center items-center space-x-2 pt-4">
      <button
        class="flex justify-center items-center w-26 h-[35px] rounded-full border border-[#4A71D4] text-[#4A71D4]"
        @click="cancel"
      >
        Cancel
      </button>
      <button
        class="flex justify-center items-center w-[149px] h-[35px] rounded-full bg-[#4A71D4] text-[#FFFFFF]"
        @click="handleSaveChanges"
      >
        Save Changes
      </button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.toggle-check-1 {
  &:checked {
    @apply bg-[#4A71D4];
  }
  &:checked + .check-1 {
    @apply opacity-100;
    color: white;
  }
}
.toggle-check-1.disable {
  &:checked {
    @apply bg-[#E3E3E3];
  }
  &:checked + .check-1 {
    @apply opacity-100;
    color: white;
  }
}
.username-clamp {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
