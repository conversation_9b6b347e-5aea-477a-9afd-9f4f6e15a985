<template>
  <div class="border-b-[2px] border-[#F1F2F6]">
    <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
      <p class="text-[#333333] font-semibold">Stars:</p>
      <div>
        <p class="font-semibold text-[#333333]">
          Drag the stars between the lists.
          <span class="text-[#525252] font-normal"
            >The stars will rotate in the order shown below when you click
            successively. To learn the name of a star for search, hover your
            mouse over the image.</span
          >
        </p>
        <div class="mt-[22px] text-[#333333]">
          <div class="grid grid-cols-[80px_1fr] gap-x-4">
            <p>Presets:</p>
            <div class="flex space-x-4 items-center">
              <p
                :class="
                  oneStarSelected
                    ? 'text-[#333333] font-semibold'
                    : 'text-[#3964D0]'
                "
                class="cursor-pointer"
                @click="setOneStarInuse"
              >
                1 star
              </p>
              <p
                :class="
                  fourStarSelected
                    ? 'text-[#333333] font-semibold'
                    : 'text-[#3964D0]'
                "
                class="cursor-pointer"
                @click="setFourStarInuse"
              >
                4 stars
              </p>
              <p
                :class="
                  allStarSelected
                    ? 'text-[#333333] font-semibold'
                    : 'text-[#3964D0]'
                "
                class="cursor-pointer"
                @click="setAllStarInuse"
              >
                all stars
              </p>
            </div>
          </div>
          <div class="grid grid-cols-[80px_1fr] gap-x-4 mt-[7px]">
            <p class="font-semibold">In use:</p>
            <ul ref="inUseRef" class="flex gap-2 flex-wrap">
              <li
                v-for="star in inUse"
                :key="star"
                :data-id="star"
                class="cursor-move select-none"
              >
                <img :src="star" :alt="star" />
              </li>
            </ul>
          </div>
          <div class="grid grid-cols-[80px_1fr] gap-x-4 mt-[7px]">
            <p class="font-semibold">Not in use:</p>
            <ul ref="notInUseRef" class="flex gap-2 flex-wrap">
              <li
                v-for="star in notInUse"
                :key="star"
                :data-id="star"
                class="cursor-move select-none"
              >
                <img :src="star" :alt="star" />
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import { useSortable } from '@vueuse/integrations/useSortable'

const allStars = [
  '/images/email-general-settings/star1.svg',
  '/images/email-general-settings/star2.svg',
  '/images/email-general-settings/star3.svg',
  '/images/email-general-settings/star4.svg',
  '/images/email-general-settings/star5.svg',
  '/images/email-general-settings/star6.svg',
  '/images/email-general-settings/star7.svg',
  '/images/email-general-settings/star8.svg',
  '/images/email-general-settings/star9.svg',
  '/images/email-general-settings/star10.svg',
  '/images/email-general-settings/star11.svg',
  '/images/email-general-settings/star12.svg',
]

const inUse = ref(['/images/email-general-settings/star1.svg'])
const notInUse = ref(allStars.filter((s) => !inUse.value.includes(s)))
const originalNotInUseOrder = ref([...allStars])

const inUseRef = ref(null)
const notInUseRef = ref(null)

function removeFromArray(array, item) {
  const index = array.indexOf(item)
  if (index !== -1) array.splice(index, 1)
}

function insertInArrayAtIndex(array, item, index) {
  if (!array.includes(item)) {
    array.splice(index, 0, item)
  }
}

function insertInOriginalPosition(array, item, referenceOrder) {
  oneStarSelected.value = false
  fourStarSelected.value = false
  allStarSelected.value = false
  const originalIndex = referenceOrder.indexOf(item)
  if (originalIndex === -1) return
  let insertIndex = array.findIndex((existingItem) => {
    return referenceOrder.indexOf(existingItem) > originalIndex
  })
  if (insertIndex === -1) {
    array.push(item)
  } else {
    array.splice(insertIndex, 0, item)
  }
}

// Sortable for "In use" with sorting enabled
useSortable(inUseRef, inUse, {
  group: {
    name: 'stars',
    pull: true,
    put: true,
  },
  animation: 150,
  sort: true,
  onAdd(evt) {
    oneStarSelected.value = false
    fourStarSelected.value = false
    allStarSelected.value = false
    const item = evt.item.dataset.id
    const newIndex = evt.newIndex
    nextTick(() => {
      removeFromArray(notInUse.value, item)
      insertInArrayAtIndex(inUse.value, item, newIndex)
    })
  },
})

// Sortable for "Not in use" with sorting disabled
useSortable(notInUseRef, notInUse, {
  group: {
    name: 'stars',
    pull: true,
    put: true,
  },
  animation: 150,
  sort: false,
  onAdd(evt) {
    const item = evt.item.dataset.id
    nextTick(() => {
      removeFromArray(inUse.value, item)
      if (!notInUse.value.includes(item)) {
        insertInOriginalPosition(
          notInUse.value,
          item,
          originalNotInUseOrder.value,
        )
      }
    })
  },
})
const oneStar = ['/images/email-general-settings/star1.svg']
const oneStarSelected = ref(false)
const fourStarSelected = ref(false)
const allStarSelected = ref(false)
const setOneStarInuse = () => {
  inUse.value = JSON.parse(JSON.stringify(oneStar))
  oneStarSelected.value = true
  fourStarSelected.value = false
  allStarSelected.value = false
  notInUse.value = allStars.filter((s) => !inUse.value.includes(s))
}
const fourStar = [
  '/images/email-general-settings/star1.svg',
  '/images/email-general-settings/star5.svg',
  '/images/email-general-settings/star10.svg',
  '/images/email-general-settings/star7.svg',
]
const setFourStarInuse = () => {
  inUse.value = JSON.parse(JSON.stringify(fourStar))
  oneStarSelected.value = false
  fourStarSelected.value = true
  allStarSelected.value = false
  notInUse.value = allStars.filter((s) => !inUse.value.includes(s))
}
const setAllStarInuse = () => {
  inUse.value = JSON.parse(JSON.stringify(allStars))
  oneStarSelected.value = false
  fourStarSelected.value = false
  allStarSelected.value = true
  notInUse.value = allStars.filter((s) => !inUse.value.includes(s))
}
</script>

<style scoped></style>
