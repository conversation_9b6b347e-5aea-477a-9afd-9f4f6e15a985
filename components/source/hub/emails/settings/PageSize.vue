<script setup lang="ts">
interface PageSize {
  id: number
  text: string
}

const pageSizes: PageSize[] = [
  { id: 1, text: '10' },
  { id: 2, text: '15' },
  { id: 3, text: '20' },
  { id: 4, text: '25' },
  { id: 5, text: '50' },
  { id: 6, text: '100' },
]
</script>

<template>
  <div class="border-b-[2px] border-[#F1F2F6]">
    <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
      <p class="text-[#333333] font-semibold">Maximum page size:</p>
      <div class="">
        <div class="flex space-x-2">
          <p class="text-[#333333] font-semibold">Show</p>
          <BaseDropsDown
            class="border-[#C2C2C2] border rounded-full language-dropdown"
            :options="pageSizes"
            labelKey="text"
            placeholder="50"
            :menuWidth="78"
            :menuHeight="27"
            :dropdownWidth="78"
            :dropdownMaxHeight="390"
            menuBgColor="#FFFFFF"
            menuTextColor="#333333"
            dropsdownTextColor="#333333"
            scrollbarTrackColor="#a1cdff50"
            scrollbarThumbColor="#a1cdff"
            hoverColor="#4A71D4"
            hoverTextColor="#FFFFFF"
          />
          <p class="text-[#333333] font-semibold">conversations per page</p>
          <!-- @change="handleSelectStatus" -->
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
