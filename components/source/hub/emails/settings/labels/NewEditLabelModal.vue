<script setup lang="ts">
import { useStore } from 'vuex'

interface CreatedLabelOptions {
  id: number
  name: string
  selected: string
  showText: string
  hideText: string
}

interface NestLabelOptions {
  id: number
  name: string
  // children:
}

const store = useStore()
const labelName = ref<string>('')
const nestedValueOption = ref('')
const nestLabel = ref<boolean>(false)
const nestLabelOptions = ref<NestLabelOptions[]>([])
const labelsItems = computed(() => store.state.emails.labelsItems)
const newLabel = computed(() => store.state.emails.newLabel)
const showEditLabelModal = computed(() => store.state.emails.showEditLabelModal)
const showRemoveConfirmationModal = computed(
  () => store.state.emails.showRemoveConfirmationModal,
)
const createdLabelOptions = computed(
  () => store.state.emails.createdLabelOptions,
)
onMounted(() => {
  nextTick(() => {
    if (Object.keys(newLabel.value).length === 0) {
      labelName.value = ''
      nestLabel.value = false
    } else {
      labelName.value = newLabel.value.name
      nestLabel.value = newLabel.value.nested
      if (
        newLabel.value.parent &&
        newLabel.value.parent.name !== newLabel.value.name
      ) {
        // If the label has a parent, set the nested value option to the parent's name
        nestedValueOption.value = newLabel.value.parent.name || ''
        nestedValue.value.id = newLabel.value.parent.id
        nestedValue.value.name = newLabel.value.parent.name || ''
      } else {
        // If no parent, set it to an empty string
        nestedValueOption.value = ''
        nestedValue.value = null
        // nestedValue.value.name = ''
      }
    }
    getNestLabelOptions(labelsItems.value, newLabel.value.id)
  })
})
const getNestLabelOptions = (items, targetId) => {
  if (targetId) {
    nestLabelOptions.value = removeLabelById(items, targetId)
  } else {
    nestLabelOptions.value = items
  }
}

const removeLabelById = (labels, targetId) => {
  return labels
    .filter((item) => item.id !== targetId)
    .map((item) => ({
      ...item,
      children: item.children ? removeLabelById(item.children, targetId) : [],
    }))
}
const nestedValue = ref<NestLabelOptions>({
  id: 0,
  name: '',
})
const selectOption = (option: NestLabelOptions) => {
  nestLabel.value = true
  nestedValue.value = option
  // store.commit('emails/SET_NEST_LABEL', option)
}
</script>

<template>
  <div
    class="bg-white w-full max-w-[678px] rounded-2xl fixed z-20 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
  >
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="flex justify-between items-center px-6 py-3.5">
        <p class="text-lg font-semibold text-[#505050]">
          {{
            !showEditLabelModal && !showRemoveConfirmationModal
              ? 'New label'
              : showRemoveConfirmationModal
                ? 'Remove label'
                : 'Edit label'
          }}
        </p>
        <SharedIconHubEmailsCrossIcon
          @click.stop="
            store.commit('emails/SET_NEW_LABEL_MODAL', false),
              store.commit('emails/SET_EDIT_LABEL_MODAL', false),
              store.commit('emails/SET_REMOVE_CONFIRMATION_MODAL', false)
          "
          class="w-4 h-4 cursor-pointer"
        />
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div
        class="px-6 pt-4"
        :class="showRemoveConfirmationModal ? 'pb-4' : 'pb-0'"
      >
        <p
          :class="
            showRemoveConfirmationModal ? 'text-[#333333]' : 'text-[#525252]'
          "
        >
          {{
            !showEditLabelModal && !showRemoveConfirmationModal
              ? 'Please enter a new label name:'
              : showRemoveConfirmationModal
                ? 'Delete the label "Product"?'
                : 'Label name:'
          }}
        </p>
        <input
          v-if="!showRemoveConfirmationModal"
          type="text"
          v-model="labelName"
          class="w-full h-[43px] rounded-full px-4 py-1 mt-2 border-none outline-none bg-[#F1F2F6]"
        />
      </div>
      <div v-if="!showRemoveConfirmationModal" class="px-6 mt-[23px] pb-4">
        <label
          class="w-full relative flex space-x-2 items-center cursor-pointer"
          for="nest_label"
        >
          <div class="w-4 h-4 relative">
            <input
              ref="nest_label"
              id="nest_label"
              type="checkbox"
              v-model="nestLabel"
              :checked="nestLabel ? true : false"
              class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
              :class="nestLabel ? '' : 'border border-[#333333]'"
            />
            <ClientOnly>
              <fa
                class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                :icon="['fas', 'check']"
              />
            </ClientOnly>
          </div>
          <div class="inline-block pt-[3px]">
            <p :class="nestLabel ? 'text-[#525252]' : 'text-[#525252]'">
              Nest label under:
            </p>
          </div>
        </label>
        <BaseDropsDown
          class="bg-[#F1F2F6] rounded-full language-dropdown mt-2 dropdown"
          :options="nestLabelOptions"
          labelKey="name"
          :placeholder="nestLabel ? nestedValueOption : ''"
          :menuWidth="220"
          :menuHeight="43"
          :dropdownWidth="220"
          :dropdownMaxHeight="390"
          menuBgColor="#F1F2F6"
          menuTextColor="#333333"
          dropsdownTextColor="#333333"
          scrollbarTrackColor="#a1cdff50"
          scrollbarThumbColor="#a1cdff"
          hoverColor="#4A71D4"
          hoverTextColor="#FFFFFF"
          @change="selectOption"
        />
      </div>
    </div>
    <div class="flex justify-end items-center space-x-2 px-6 py-3.5">
      <button
        class="flex justify-center items-center rounded-full font-semibold text-[#4A71D4] border-[1px] border-[#4A71D4] w-[104px] h-[35px]"
        @click.stop="
          store.commit('emails/SET_NEW_LABEL_MODAL', false),
            store.commit('emails/SET_EDIT_LABEL_MODAL', false),
            store.commit('emails/SET_REMOVE_CONFIRMATION_MODAL', false)
        "
      >
        Cancel
      </button>
      <button
        v-if="!showEditLabelModal && !showRemoveConfirmationModal"
        :disabled="
          labelName && !nestLabel
            ? false
            : labelName && nestedValue.name
              ? false
              : true
        "
        class="flex justify-center items-center rounded-full font-semibold w-[104px] h-[35px]"
        :class="
          labelName && !nestLabel
            ? 'text-[#FFFFFF] bg-[#4A71D4]'
            : labelName && nestedValue.name
              ? 'text-[#FFFFFF] bg-[#4A71D4]'
              : 'text-[#C2C2C2] bg-[#F1F2F6]'
        "
        @click.stop="
          store.commit('emails/SET_CREATED_LABEL_OPTIONS', {
            text: labelName,
            nested: nestedValue,
          }),
            store.commit('emails/SET_NEW_LABEL_MODAL', false)
        "
      >
        Create
      </button>
      <button
        v-if="!showEditLabelModal && showRemoveConfirmationModal"
        class="flex justify-center items-center rounded-full font-semibold text-[#FFFFFF] bg-[#4A71D4] w-[104px] h-[35px]"
        @click.stop="
          store.commit('emails/SET_REMOVE_EXITING_LABEL', newLabel.id),
            store.commit('emails/SET_REMOVE_CONFIRMATION_MODAL', false)
        "
      >
        Delete
      </button>
      <button
        v-else-if="showEditLabelModal && !showRemoveConfirmationModal"
        :disabled="
          labelName && !nestLabel
            ? false
            : labelName && nestedValue.name
              ? false
              : true
        "
        class="flex justify-center items-center rounded-full font-semibold w-[104px] h-[35px]"
        :class="
          labelName && !nestLabel
            ? 'text-[#FFFFFF] bg-[#4A71D4]'
            : labelName && nestedValue.name
              ? 'text-[#FFFFFF] bg-[#4A71D4]'
              : 'text-[#C2C2C2] bg-[#F1F2F6]'
        "
        @click.stop="
          store.commit('emails/SET_EDIT_LABEL_MODAL', false),
            store.commit('emails/SET_CREATED_LABEL_EDIT_OPTIONS', {
              targetId: newLabel.id,
              text: labelName,
              nested: nestedValue,
            })
        "
      >
        Done
      </button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.toggle-check-1 {
  &:checked {
    @apply bg-[#4A71D4];
  }
  &:checked + .check-1 {
    @apply opacity-100;
    color: white;
  }
}
</style>
