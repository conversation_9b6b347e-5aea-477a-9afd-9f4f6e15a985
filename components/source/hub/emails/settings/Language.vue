<script setup lang="ts">
interface Languages {
  id: number
  text: string
}

const languages: Languages[] = [
  { id: 1, text: 'Afrikaans' },
  { id: 2, text: 'Azerbaycanca' },
  { id: 3, text: 'Bahasa Indonesia' },
  { id: 4, text: 'Bahasa Melayu' },
  { id: 5, text: 'Cat<PERSON>à' },
  { id: 6, text: '<PERSON><PERSON><PERSON><PERSON>' },
  { id: 6, text: '<PERSON><PERSON><PERSON><PERSON>' },
  { id: 6, text: 'Dansk' },
  { id: 6, text: '<PERSON><PERSON><PERSON>' },
  { id: 6, text: '<PERSON><PERSON><PERSON> keel' },
  { id: 6, text: 'English (UK)' },
  { id: 6, text: 'English (US)' },
  { id: 6, text: 'Español' },
  { id: 6, text: 'Español (Latinoamérica)' },
]
const selectLanguage = ref('off')
const languageOptions = [
  {
    label: 'Right-to-left editing support off',
    value: 'off',
    description: '',
  },
  {
    label: 'Right-to-left editing support on',
    value: 'on',
    description: '',
  },
]
</script>

<template>
  <div class="border-b-[2px] border-[#F1F2F6]">
    <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
      <p class="text-[#333333] font-semibold">Language:</p>
      <div class="">
        <div class="flex space-x-2">
          <p class="text-[#333333] font-semibold">Gmail display language:</p>
          <BaseDropsDown
            class="border-[#C2C2C2] border rounded-full language-dropdown"
            :options="languages"
            labelKey="text"
            placeholder="English (US)"
            :menuWidth="220"
            :menuHeight="27"
            :dropdownWidth="220"
            :dropdownMaxHeight="390"
            menuBgColor="#FFFFFF"
            menuTextColor="#333333"
            dropsdownTextColor="#333333"
            scrollbarTrackColor="#a1cdff50"
            scrollbarThumbColor="#a1cdff"
            hoverColor="#4A71D4"
            hoverTextColor="#FFFFFF"
          />
          <!-- @change="handleSelectStatus" -->
        </div>
        <p class="text-[#3964D0] !mt-1.5">Show all language options</p>
        <InputsRadioOptionGroup
          class="mt-4"
          v-model="selectLanguage"
          :options="languageOptions"
        />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
