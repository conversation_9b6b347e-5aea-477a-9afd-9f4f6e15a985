<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const signatureName = ref('')
const newsignatureName = computed(() => store.state.emails.signatureName)
const editSignatureName = computed(() => store.state.emails.editSignatureName)
onMounted(() => {
  nextTick(() => {
    console.log(newsignatureName.value, 'newsignatureName.value')
    if (newsignatureName.value) {
      signatureName.value = newsignatureName.value.text
    }
  })
})
</script>

<template>
  <div
    class="bg-white w-full max-w-[678px] rounded-2xl fixed z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
  >
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="flex justify-between items-center px-6 py-3.5">
        <p class="text-lg font-semibold text-[#505050]">
          {{
            !editSignatureName ? 'Name new signature' : 'Edit signature name'
          }}
        </p>
        <SharedIconHubEmailsCrossIcon
          @click.stop="
            store.commit('emails/SHOW_HIDE_EMAIL_SIGNATURE', false),
              store.commit('emails/SET_EDIT_SIGNATURE_NAME', { show: false })
          "
          class="w-4 h-4 cursor-pointer"
        />
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="px-6 py-4">
        <p class="text-[#525252]">Signature name</p>
        <input
          type="text"
          v-model="signatureName"
          class="w-full h-[43px] rounded-full px-4 py-1 mt-2 border-none outline-none bg-[#F1F2F6]"
        />
      </div>
    </div>
    <div class="flex justify-end items-center space-x-2 px-6 py-3.5">
      <button
        class="flex justify-center items-center rounded-full font-semibold text-[#4A71D4] border-[1px] border-[#4A71D4] w-[104px] h-[35px]"
        @click.stop="
          store.commit('emails/SHOW_HIDE_EMAIL_SIGNATURE', false),
            store.commit('emails/SET_EDIT_SIGNATURE_NAME', { show: false })
        "
      >
        Cancel
      </button>
      <button
        v-if="!editSignatureName"
        class="flex justify-center items-center rounded-full font-semibold text-[#FFFFFF] bg-[#4A71D4] w-[104px] h-[35px]"
        @click.stop="
          store.commit('emails/SET_NEW_EMAIL_SIGNATURE', signatureName),
            store.commit('emails/SHOW_HIDE_EMAIL_SIGNATURE', false)
        "
      >
        Create
      </button>
      <button
        v-else
        class="flex justify-center items-center rounded-full font-semibold text-[#FFFFFF] bg-[#4A71D4] w-[104px] h-[35px]"
        @click.stop="
          store.commit('emails/SET_UPDATED_EMAIL_SIGNATURE_NAME', {
            id: newsignatureName.id,
            name: signatureName,
          })
        "
      >
        Done
      </button>
    </div>
  </div>
</template>

<style scoped></style>
