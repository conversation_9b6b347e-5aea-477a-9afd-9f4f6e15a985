<script setup lang="ts">
interface PreviewPane {
  id: number
  text: string
}

const previewPane: Languages[] = [
  { id: 1, text: 'After 1 seconds' },
  { id: 2, text: 'After 2 seconds' },
  { id: 3, text: 'After 3 seconds' },
  { id: 4, text: 'After 4 seconds' },
  { id: 5, text: 'After 5 seconds' },
  // { id: 6, text: '<PERSON><PERSON><PERSON><PERSON>' },
  // { id: 6, text: '<PERSON><PERSON>rae<PERSON>' },
  // { id: 6, text: 'Dansk' },
  // { id: 6, text: '<PERSON>uts<PERSON>' },
  // { id: 6, text: '<PERSON><PERSON><PERSON> keel' },
  // { id: 6, text: 'English (UK)' },
  // { id: 6, text: 'English (US)' },
  // { id: 6, text: 'Español' },
  // { id: 6, text: 'Español (Latinoamérica)' },
]
</script>

<template>
  <div class="border-b-[2px] border-[#F1F2F6]">
    <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
      <p class="text-[#333333] font-semibold">Preview Pane:</p>
      <div class="">
        <div class="flex space-x-2">
          <p class="text-[#333333] font-semibold">
            Mark a conversation as read:
          </p>
          <BaseDropsDown
            class="border-[#C2C2C2] border rounded-full language-dropdown"
            :options="previewPane"
            labelKey="text"
            placeholder="After 3 seconds"
            :menuWidth="169"
            :menuHeight="27"
            :dropdownWidth="169"
            :dropdownMaxHeight="390"
            menuBgColor="#FFFFFF"
            menuTextColor="#333333"
            dropsdownTextColor="#333333"
            scrollbarTrackColor="#a1cdff50"
            scrollbarThumbColor="#a1cdff"
            hoverColor="#4A71D4"
            hoverTextColor="#FFFFFF"
          />
          <!-- @change="handleSelectStatus" -->
        </div>
        <!-- <p class="text-[#3964D0] !mt-1.5">Show all language options</p> -->
      </div>
    </div>
  </div>
</template>

<style scoped></style>
