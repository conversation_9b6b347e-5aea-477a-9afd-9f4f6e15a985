<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const router = useRouter()
const createdSignature = computed(() => store.state.emails.createdSignature)
// const insertSignature = computed(() => store.state.emails.insertSignature)
const insertSignature = computed({
  get: () => store.state.emails.insertSignature,
  set: (value) => {
    store.commit('emails/SET_INSERT_SIGNATURE', value)
  },
})
const signatureTextOptions = ref([])
const setText = ($event: string, signature) => {
  if (signatureTextOptions.value && signatureTextOptions.value.length > 0) {
    signatureTextOptions.value.forEach((item) => {
      if (
        signatureTextOptions.value.some(
          (element) => element.id === signature.id,
        )
      ) {
        if (item.id === signature.id) {
          item.description = $event
        }
      } else {
        signatureTextOptions.value.push({
          id: signature.id,
          description: $event,
        })
      }
    })
  } else {
    signatureTextOptions.value.push({
      id: signature.id,
      description: $event,
    })
  }

  console.log(
    $event,
    signature,
    '$event, signature',
    signatureTextOptions.value,
  )
}
const previousPath = computed(() => store.state.emails.previousPath)
const saveChanges = () => {
  store.commit(
    'emails/SET_DESCRIPTION_TO_SIGNATURE',
    signatureTextOptions.value,
  )
  router.push(previousPath.value)
}
defineExpose({
  saveChanges,
})
</script>

<template>
  <div class="border-b-[2px] border-[#F1F2F6]">
    <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
      <div>
        <p class="text-[#333333] font-semibold">Signature:</p>
        <p class="text-[#525252] text-sm !mt-2">
          (appended at the end of all outgoing messages)
        </p>
        <p class="text-[#4A71D4] !mt-1.5">Learn more</p>
      </div>
      <div>
        <div v-if="createdSignature.length === 1">
          <p class="text-[#333333] font-semibold">No signatures</p>
          <button
            class="w-[124px] h-[33px] mt-4 rounded-full bg-[#F1F2F6] text-[#3964D0] text-sm flex space-x-2 justify-center items-center"
            @click.stop="store.commit('emails/SHOW_HIDE_EMAIL_SIGNATURE', true)"
          >
            <SharedIconHubEmailsSidebarPlusIcon
              class="w-3.5 h-3.5"
              color="#3964D0"
            />
            <p>Create new</p>
          </button>
        </div>
        <div v-else>
          <div
            class="grid grid-cols-[240px_minmax(0px,707px)] max-w-[947px] h-[238px] max-h-[238px] border-[2px] border-[#F1F2F6] rounded-[7px] mt-6"
          >
            <div>
              <div
                class="border-r-[1px] h-[238px] border-[#F1F2F6] py-1 overflow-y-auto"
              >
                <div v-for="signature in createdSignature" :key="signature.id">
                  <div
                    v-if="signature.id !== 1"
                    class="px-5 py-1.5 flex justify-between items-center cursor-pointer"
                    :class="
                      signature.selected
                        ? 'bg-[#F1F2F6]'
                        : 'hover:bg-[#F1F2F6] hover:bg-opacity-40'
                    "
                    @click.stop="
                      store.commit(
                        'emails/SET_SELECTED_EMAIL_SIGNATURE',
                        signature.id,
                      )
                    "
                  >
                    <p class="text-[#525252] line-clamp-1 mr-10">
                      {{ signature.text }}
                    </p>
                    <div
                      v-if="signature.selected"
                      class="flex space-x-2 items-center"
                    >
                      <!-- <SharedIconPencil
                        @click.stop="
                          store.commit('emails/SET_EDIT_SIGNATURE_NAME', {
                            show: true,
                            name: signature.text,
                            id: signature.id,
                          })
                        "
                      /> -->
                      <img
                        @click.stop="
                          store.commit('emails/SET_EDIT_SIGNATURE_NAME', {
                            show: true,
                            name: signature.text,
                            id: signature.id,
                          })
                        "
                        src="/images/icon/pen-icon.svg"
                        alt="penIcon"
                      />
                      <SharedIconHubEmailsDeleteIcon
                        @click.stop="
                          store.commit(
                            'emails/REMOVE_EMAIL_SIGNATURE',
                            signature.id,
                          )
                        "
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <template
              v-for="signature in createdSignature"
              :key="signature.id"
              class="flex flex-col h-[238px]"
            >
              <SourceHubEmailsSettingsTextEditor
                v-show="signature.selected && signature.id !== 1"
                class="h-[238px]"
                :signature="signature"
                @set-text="setText($event, signature)"
              />
            </template>
          </div>
          <button
            class="w-[240px] h-[33px] mt-4 rounded-full bg-[#F1F2F6] text-[#3964D0] text-sm flex space-x-2 justify-center items-center"
            @click.stop="store.commit('emails/SHOW_HIDE_EMAIL_SIGNATURE', true)"
          >
            <SharedIconHubEmailsSidebarPlusIcon
              class="w-3.5 h-3.5"
              color="#3964D0"
            />
            <p>Create new</p>
          </button>
          <div class="mt-[22px]">
            <p class="text-[#333333] font-semibold">Signature defaults</p>
            <div class="grid grid-cols-[210px_210px] gap-x-4 mt-3.5">
              <div>
                <p class="text-xs text-[#707070]">FOR NEW EMAILS USE</p>
                <BaseDropsDown
                  class="border-[#C2C2C2] border rounded-full language-dropdown mt-2"
                  :options="createdSignature"
                  labelKey="text"
                  placeholder="No signature"
                  :menuWidth="200"
                  :menuHeight="27"
                  :dropdownWidth="200"
                  :dropdownMaxHeight="290"
                  menuBgColor="#FFFFFF"
                  menuTextColor="#333333"
                  dropsdownTextColor="#333333"
                  scrollbarTrackColor="#a1cdff50"
                  scrollbarThumbColor="#a1cdff"
                  hoverColor="#4A71D4"
                  hoverTextColor="#FFFFFF"
                />
              </div>
              <div>
                <p class="text-xs text-[#707070]">ON REPLY/FORWARD USE</p>
                <BaseDropsDown
                  class="border-[#C2C2C2] border rounded-full language-dropdown mt-2"
                  :options="createdSignature"
                  labelKey="text"
                  placeholder="No signature"
                  :menuWidth="200"
                  :menuHeight="27"
                  :dropdownWidth="200"
                  :dropdownMaxHeight="290"
                  menuBgColor="#FFFFFF"
                  menuTextColor="#333333"
                  dropsdownTextColor="#333333"
                  scrollbarTrackColor="#a1cdff50"
                  scrollbarThumbColor="#a1cdff"
                  hoverColor="#4A71D4"
                  hoverTextColor="#FFFFFF"
                />
              </div>
            </div>
            <div class="flex flex-col items-start mt-3.5">
              <label
                class="w-full relative flex space-x-2 items-start cursor-pointer"
                for="insert_signature"
              >
                <div class="w-4 h-4 relative mt-0.5">
                  <input
                    ref="insert_signature"
                    id="insert_signature"
                    type="checkbox"
                    v-model="insertSignature"
                    :checked="insertSignature ? true : false"
                    class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                    :class="insertSignature ? '' : 'border border-[#333333]'"
                  />
                  <ClientOnly>
                    <fa
                      class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                      :icon="['fas', 'check']"
                    />
                  </ClientOnly>
                </div>
                <div class="inline-block">
                  <p class="text-[##525252]">
                    Insert signature before the quoted text in replies, and
                    remove the '--' line that precedes it.
                  </p>
                </div>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.toggle-check-1 {
  &:checked {
    @apply bg-[#4A71D4];
  }
  &:checked + .check-1 {
    @apply opacity-100;
    color: white;
  }
}
</style>
