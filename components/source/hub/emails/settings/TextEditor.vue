<script setup lang="ts">
import { useStore } from 'vuex'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import TextAlign from '@tiptap/extension-text-align'
import Highlight from '@tiptap/extension-highlight'
import TextStyle from '@tiptap/extension-text-style'
import Color from '@tiptap/extension-color'
import FontFamily from '@tiptap/extension-font-family'
import Underline from '@tiptap/extension-underline'
import Link from '@tiptap/extension-link'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableCell from '@tiptap/extension-table-cell'
import TableHeader from '@tiptap/extension-table-header'
import { CustomIndent } from '~/composables/tiptap-extension-indent'
import { FontSize } from '~/composables/FontSize'
import Image from '@tiptap/extension-image'

interface Signature {
  id: number
  text: string
  description: string
}

const props = withDefaults(
  defineProps<{
    signature?: Signature
  }>(),
  {
    signature: () => ({
      id: 0,
      text: '',
      description: '',
    }),
  },
)
const store = useStore()
const editor = ref<Editor | null>(null)

const emit = defineEmits(['set-text'])

watch(
  () => props.signature.description,
  (newDescription) => {
    if (editor.value && newDescription !== editor.value.getHTML()) {
      editor.value.commands.setContent(newDescription)
    }
  },
  { immediate: true },
)

onMounted(() => {
  editor.value = new Editor({
    content: props.signature.description,
    onSelectionUpdate({ editor }) {
      nextTick(() => {
        const { from, to } = editor.state.selection
        // isTextSelected.value = from !== to && !editor.state.selection.empty
      })
    },
    extensions: [
      StarterKit.configure({
        // disable default table to use tiptap one
        table: false,
        blockquote: {
          HTMLAttributes: {
            class: 'border-l-2 border-gray-300 pl-2',
          },
        },
        bulletList: {
          HTMLAttributes: {
            class: 'list-disc ml-3',
          },
        },
        orderedList: {
          HTMLAttributes: {
            class: 'list-decimal ml-3',
          },
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      TextStyle.configure({ mergeNestedSpanStyles: true }),
      Color,
      Highlight.configure({
        multicolor: true,
      }),
      FontFamily.configure({
        types: ['textStyle'],
      }),
      Underline,
      CustomIndent.configure({
        types: ['heading', 'paragraph'],
        minIndent: 0,
        maxIndent: 1000,
        indentLevel: 20,
      }),
      FontSize,
      Link.configure({
        openOnClick: true,
        autolink: true,
        HTMLAttributes: {
          class: 'tiptap-link',
          style: 'color: #2563eb; text-decoration: underline; cursor: pointer;',
        },
      }), // Prevents auto-opening links
      Image,
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableCell,
      TableHeader,
    ],
    editorProps: {
      attributes: {
        class:
          'w-full h-auto no-scrollbar resize-none textarea-style outline-none border-none bg-white text-[#333333]',
      },
    },
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      const text = editor.getText()
      emit('set-text', html)
      console.log('Updated HTML:', html)
      console.log('Updated Text:', text)
    },
  })
  editor.value?.setOptions({
    editorProps: {
      handleDOMEvents: {
        mouseup: () => {
          updatePopupPosition()
        },
      },
    },
  })
  document.addEventListener('click', () => {
    closelinkPopUp()
    // linkPopupVisible.value = false
    // showLinkOptions.value = false
  })
})
onUnmounted(() => {
  document.removeEventListener('click', () => {
    closelinkPopUp()
    // linkPopupVisible.value = false
    // showLinkOptions.value = false
  })
})
const fileUrl = computed(() => store.state.emails.fileUrl)
watch(
  () => fileUrl.value,
  (newValue) => {
    console.log(newValue, 'newValue')
    if (newValue) {
      console.log('newValue12')
      editor.value
        ?.chain()
        .focus('end')
        .setImage({ src: newValue })
        .createParagraphNear() // 🪄 Like pressing "Enter"
        .selectNodeForward()
        .run()
      setTimeout(() => {
        store.commit('emails/SET_FILE_CHANGED', {
          fileChanged: false,
          fileUrl: '',
        })
      }, 1000)
    }
  },
)

// Show Link Popup
const showLinkPopup = () => {
  updatePopupPosition()
  linkPopupVisible.value = true
}
const hideLinkPopup = () => {
  linkPopupVisible.value = false
}
const selectedLink = ref(null)
const showLinkOptions = ref(false) // Shows the first popup (Go to Link, Change, Remove)
const showEditLink = ref(false) // Shows the second popup (Link input)
const selectedLinks = ref(null) // Stores the selected link
const selectedText = ref<string | null | undefined>('')
const linkPopupVisible = ref(false)
const currentSelection = ref(null)
const linkMenuPosition = ref({ top: 0, left: 0 })
const textSelected = ref(false)
const updatePopupPosition = () => {
  nextTick(() => {
    const selection = editor.value?.state.selection
    if (!selection) return

    const { from, to, empty } = selection
    textSelected.value = !empty // True if text is selected
    // Get link if the cursor is inside one
    const linkMark = editor.value?.getAttributes('link')
    selectedLink.value = linkMark?.href || null

    // if (textSelected.value || selectedLink.value) {
    const coords = editor.value?.view.coordsAtPos(from)
    const editorRect = editor.value?.view.dom.getBoundingClientRect()

    // const node = selection.anchorNode?.parentElement
    // const link = node?.closest('a') // Find nearest <a> tag
    // console.log('link', link, node, linkMark)
    if (linkMark?.href && !empty) {
      selectedLinks.value = linkMark.href
      selectedText.value = empty
        ? ''
        : editor.value?.state.doc.textBetween(from, to, ' ')
      showLinkOptions.value = true // Show first popup
      showEditLink.value = false
    } else {
      selectedLinks.value = null
      showLinkOptions.value = false
    }
    if (coords && editorRect) {
      linkMenuPosition.value = {
        top: coords.top - editorRect.top + 30,
        left: coords.left - editorRect.left,
      }
    }
    // linkPopupVisible.value = true
    // } else {
    //   linkPopupVisible.value = false
    // }
  })
}

const editLink = () => {
  showLinkOptions.value = false
  linkPopupVisible.value = true
  textSelected.value = false
}

const removeLink = () => {
  editor.value?.chain().focus().extendMarkRange('link').unsetLink().run()
  showLinkOptions.value = false
}
const closelinkPopUp = () => {
  linkPopupVisible.value = false
  showLinkOptions.value = false
  showEditLink.value = false
  selectedLinks.value = null
  selectedText.value = null
}

// ✅ Handle keydown to unset link before typing
const onKeydown = (event) => {
  console.log(event, 'event')
  if (!editor.value) return

  // Only handle Backspace key
  if (event.key === 'Backspace') return

  // We only want to unset the link if we're right after a link
  const { state } = editor.value
  const { from, empty } = state.selection
  if (!empty) return // only when caret, not selection

  const marks = state.storedMarks || state.doc.resolve(from).marks()
  const hasLink = marks.some((mark) => mark.type.name === 'link')

  if (hasLink) {
    console.log('hasLink', hasLink)
    editor.value.chain().focus().unsetMark('link').run()
  }
}
</script>

<template>
  <div class="flex flex-col">
    <div class="h-[48px] min-h-[48px] responder-text-formatter hidden">
      <SourceHubEmailsTextFormatterMenu
        :editor="editor"
        :hide-undo-button="true"
        :hide-strike-button="true"
        :hide-box-shadow="false"
      >
        <template v-slot:link-image>
          <div class="w-[1px] h-[20px] bg-[#707070] bg-opacity-25"></div>
          <button
            class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
            @click.stop="showLinkPopup()"
          >
            <SharedIconHubEmailsLinkIcon />
          </button>
          <div
            class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
            @click="store.commit('emails/SET_PHOTO_INSERT_FILE_MODAL', true)"
          >
            <SharedIconHubEmailsPhotoIcon />
          </div>
        </template>
      </SourceHubEmailsTextFormatterMenu>
    </div>
    <slot name="text"></slot>
    <div
      class="editor flex-grow border-b-[1px] border-[#F1F2F6] py-2.5 px-4 relative overflow-y-auto"
    >
      <editor-content :editor="editor" @keydown="onKeydown" @click.stop="" />
      <LinkInputPopup
        v-if="linkPopupVisible"
        :editor="editor"
        :selection="currentSelection"
        :position="linkMenuPosition"
        :textSelected="textSelected"
        :selectedLinks="selectedLinks"
        :selectedText="selectedText"
        @close="closelinkPopUp()"
        @click.stop=""
      />
      <div
        v-if="showLinkOptions"
        @click.stop=""
        :style="`position: absolute; top: ${linkMenuPosition.top}px; left: ${linkMenuPosition.left}px; z-index: 100; background: #ffffff; border: 1px solid; border-color: #bbb #bbb #a8a8a8; border-radius: 2px; padding: 2px 10px; box-shadow: 0 1px 3px rgba(0, 0, 0, .2);`"
      >
        <span class="text-[13px] font-medium"
          >Go to link:
          <a
            :href="selectedLinks"
            target="_blank"
            class="text-[#15c] font-medium hover:underline"
            >{{ selectedLinks }}</a
          >
        </span>
        |
        <button
          @click="editLink"
          class="text-[#15c] text-[13px] font-medium hover:underline"
        >
          Change
        </button>
        |
        <button
          @click="removeLink"
          class="text-[#15c] text-[13px] font-medium hover:underline"
        >
          Remove
        </button>
      </div>
    </div>
    <div class="h-[48px] min-h-[48px] text-formatter">
      <SourceHubEmailsTextFormatterMenu
        :editor="editor"
        :hide-undo-button="true"
        :hide-strike-button="true"
        :hide-box-shadow="true"
      >
        <template v-slot:link-image>
          <div class="w-[1px] h-[20px] bg-[#707070] bg-opacity-25"></div>
          <button
            class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
            @click.stop="showLinkPopup()"
          >
            <SharedIconHubEmailsLinkIcon />
          </button>
          <div
            class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
            @click="store.commit('emails/SET_PHOTO_INSERT_FILE_MODAL', true)"
          >
            <SharedIconHubEmailsPhotoIcon />
          </div>
        </template>
      </SourceHubEmailsTextFormatterMenu>
    </div>
  </div>
</template>

<style scoped>
.text-editor-box > .responder-text-formatter {
  display: block;
}
.text-editor-box > .text-formatter {
  display: none;
}
.text-editor-box > .editor {
  @apply h-[120px] max-h-[120px] border border-[#C2C2C2] rounded-lg;
}
</style>
