<script setup lang="ts">
import { useStore } from 'vuex'
import { Editor } from '@tiptap/vue-3'
import { Node as ProseMirrorNode } from '@tiptap/pm/model'

interface Props {
  visible: boolean
  position: { top: number; left: number }
  editor: Editor | null
  node: ProseMirrorNode | null
  getPos: number
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  position: () => ({ top: 0, left: 0 }),
  editor: null,
  node: null,
  getPos: 0,
})

const emit = defineEmits(['close'])
const store = useStore()

const currentSize = computed(() => {
  return props.node?.attrs?.size || 'bestfit'
})

const handleSmall = () => {
  if (props.editor && props.node) {
    const pos = props.getPos
    props.editor.view.dispatch(
      props.editor.view.state.tr.setNodeMarkup(pos, undefined, {
        ...props.node.attrs,
        size: 'small',
        width: '260px',
        height: 'auto',
      }),
    )
  }
  emit('close')
}

const handleBestFit = () => {
  if (props.editor && props.node) {
    const pos = props.getPos
    props.editor.view.dispatch(
      props.editor.view.state.tr.setNodeMarkup(pos, undefined, {
        ...props.node.attrs,
        size: 'bestfit',
        width: '100%',
        height: 'auto',
      }),
    )
  }
  emit('close')
}

const handleOriginalSize = () => {
  if (props.editor && props.node) {
    const pos = props.getPos
    props.editor.view.dispatch(
      props.editor.view.state.tr.setNodeMarkup(pos, undefined, {
        ...props.node.attrs,
        size: 'original',
        width: null,
        height: null,
      }),
    )
  }
  emit('close')
}

const handleEditAltText = () => {
  // Store the image data in the store before closing the context menu
  store.commit('emails/SET_IMAGE_ALT_TEXT_MODAL', {
    show: true,
    currentAlt: props.node?.attrs?.alt || '',
    node: props.node,
    getPos: props.getPos,
  })
  emit('close')
}

const handleRemove = () => {
  if (props.editor && props.node && props.getPos >= 0) {
    const pos = props.getPos
    const tr = props.editor.view.state.tr

    // Verify the node at this position is actually an image
    const nodeAtPos = props.editor.view.state.doc.nodeAt(pos)

    if (nodeAtPos && nodeAtPos.type.name === 'image') {
      const nodeSize = nodeAtPos.nodeSize
      tr.delete(pos, pos + nodeSize)
      props.editor.view.dispatch(tr)
    } else {
      console.error(
        'No image found at position:',
        pos,
        'Found node:',
        nodeAtPos,
      )
    }
  } else {
    console.error('Cannot remove image - missing data:', {
      editor: !!props.editor,
      node: !!props.node,
      getPos: props.getPos,
    })
  }
  emit('close')
}

// Close menu when clicking outside
const menuRef = ref<HTMLElement | null>(null)

const handleClickOutside = (event: MouseEvent) => {
  if (menuRef.value && !menuRef.value.contains(event.target as Node)) {
    emit('close')
  }
}
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div
    v-if="visible"
    ref="menuRef"
    class="absolute z-50 bg-white rounded-[4px] border shadow-[0px_0px_8px_#00000029] py-[7px] flex items-center whitespace-nowrap text-[#707070]"
    :style="{
      left: `${position.left}px`,
      top: `${position.top}px`,
    }"
    @click.stop=""
  >
    <button
      :disabled="currentSize === 'small'"
      :class="[
        'px-2 text-sm',
        currentSize === 'small'
          ? 'text-[#525252]'
          : 'text-[#4A71D4] hover:underline',
      ]"
      @click="handleSmall"
    >
      Small
    </button>
    |
    <button
      :disabled="currentSize === 'bestfit'"
      :class="[
        'px-2 text-sm',
        currentSize === 'bestfit'
          ? 'text-[#525252]'
          : 'text-[#4A71D4] hover:underline',
      ]"
      @click="handleBestFit"
    >
      Best fit
    </button>
    |
    <button
      :disabled="currentSize === 'original'"
      :class="[
        'px-2 text-sm',
        currentSize === 'original'
          ? 'text-[#525252]'
          : 'text-[#4A71D4] hover:underline',
      ]"
      @click="handleOriginalSize"
    >
      Original size
    </button>
    |
    <button
      class="px-2 text-sm text-[#4A71D4] hover:underline"
      @click="handleEditAltText"
    >
      Edit alt text
    </button>
    |
    <button
      class="px-2 text-sm text-[#4A71D4] hover:underline"
      @click="handleRemove"
    >
      Remove
    </button>
  </div>
</template>

<style scoped></style>
