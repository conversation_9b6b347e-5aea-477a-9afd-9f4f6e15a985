<script setup lang="ts">
import { useStore } from 'vuex'

import Inbox from '~/components/shared/icon/hub/emails/sidebar/Inbox.vue'
import Starred from '~/components/shared/icon/hub/emails/sidebar/Starred.vue'
import Snoozed from '~/components/shared/icon/hub/emails/sidebar/Snoozed.vue'
import Sent from '~/components/shared/icon/hub/emails/sidebar/Sent.vue'
import Drafts from '~/components/shared/icon/hub/emails/sidebar/Drafts.vue'
import Spam from '~/components/shared/icon/hub/emails/sidebar/Spam.vue'
import Important from '~/components/shared/icon/hub/emails/sidebar/Important.vue'
import Scheduled from '~/components/shared/icon/hub/emails/sidebar/Scheduled.vue'
import AllMail from '~/components/shared/icon/hub/emails/sidebar/AllMail.vue'
import Trash from '~/components/shared/icon/hub/emails/sidebar/Trash.vue'
import SettingsIcon from '~/components/shared/icon/hub/emails/sidebar/SettingsIcon.vue'
import PlusIcon from '~/components/shared/icon/hub/emails/sidebar/PlusIcon.vue'
import ChatIcon from '~/components/shared/icon/hub/emails/sidebar/ChatIcon.vue'

const store = useStore()
const route = useRoute()

const menuItems = computed(() => store.state.emails.menuItems)
const labelsItems = computed(() => {
  return sortLabelsByName(store.state.emails.labelsItems)
})
const sortLabelsByName = (labels) => {
  return labels
    .map((label) => ({
      ...label,
      children: label.children ? sortLabelsByName(label.children) : [],
    }))
    .sort((a, b) => a.name.localeCompare(b.name))
}
const localMenuItems = ref([
  {
    id: 1,
    image: markRaw(Inbox),
    name: 'Inbox',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/inbox`,
    unread: 2,
    selected: true,
  },
  {
    id: 2,
    image: markRaw(Starred),
    name: 'Starred',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/starred`,
    unread: 0,
    selected: true,
  },
  {
    id: 3,
    image: markRaw(Snoozed),
    name: 'Snoozed',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/snoozed`,
    unread: 0,
    selected: false,
  },
  {
    id: 4,
    image: markRaw(Sent),
    name: 'Sent',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/sent`,
    unread: 0,
    selected: false,
  },
  {
    id: 5,
    image: markRaw(Drafts),
    name: 'Drafts',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/drafts`,
    unread: 0,
    selected: true,
  },
  {
    id: 6,
    image: markRaw(Spam),
    name: 'Spam',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/spam`,
    unread: 0,
    selected: false,
  },
  {
    id: 7,
    image: markRaw(Important),
    name: 'Important',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/important`,
    unread: 0,
    selected: true,
  },
  {
    id: 8,
    image: markRaw(ChatIcon),
    name: 'Chats',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/chats`,
    unread: 0,
    selected: false,
  },
  {
    id: 9,
    image: markRaw(Scheduled),
    name: 'Scheduled',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/scheduled`,
    unread: 0,
    selected: true,
  },
  {
    id: 10,
    image: markRaw(AllMail),
    name: 'All Mail',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/allmail`,
    unread: 0,
    selected: false,
  },
  {
    id: 11,
    image: markRaw(Trash),
    name: 'Trash',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/trash`,
    unread: 0,
    selected: false,
  },
  {
    id: 12,
    image: markRaw(SettingsIcon),
    name: 'Manage Labels',
    path: `/source/hub/emails/settings/labels`,
    unread: 0,
    selected: true,
  },
  {
    id: 13,
    image: markRaw(PlusIcon),
    name: 'Create new label',
    path: ``,
    unread: 0,
    selected: true,
  },
])
const localLabelsItems = ref(null)
onMounted(async () => {
  if (menuItems.value) {
    localMenuItems.value = menuItems.value
  }
  // if (labelsItems.value && labelsItems.value.length > 0) {
  //   localLabelsItems.value = labelsItems.value
  //   if (localLabelsItems.value && localLabelsItems.value.length > 0) {
  //     localLabelsItems.value.forEach((item: any) => {
  //       labelsItems.value.forEach((labelItem: any) => {
  //         if (item.name === labelItem.name) {
  //           item.path = `/source/hub/emails/${route.params.slug}/${route.params.categories}/${item.name}`
  //         }
  //       })
  //     })
  //   }
  // }
  if (!menuItems.value) {
    store.commit('emails/SET_MENU_ITEMS', localMenuItems.value)
  }
  // if (!labelsItems.value) {
  //   store.commit('emails/SET_LABEL_ITEMS', localLabelsItems.value)
  // }
  // setTimeout(async () => {
  //   await nextTick
  //   const NuxtLink = document.querySelector('.nuxt-link')
  //   if (NuxtLink) {
  //     NuxtLink.click()
  //   }
  // }, 100)
})
</script>

<template>
  <div class="flex flex-col p-4">
    <div class="flex flex-col">
      <template v-for="menuItem in menuItems">
        <NuxtLink
          v-if="menuItem.selected"
          :key="menuItem.id"
          :to="menuItem.path"
          class="nuxt-link cursor-pointer whitespace-nowrap flex justify-between px-4 py-2 items-center rounded-full"
          @click.native="
            store.commit('emails/RESET_SHOW_COMPOSE_SECTION'),
              menuItem.path === ''
                ? store.commit('emails/SET_NEW_LABEL_MODAL', true)
                : ''
          "
        >
          <div class="menu-container flex !space-x-[18px] items-center">
            <component v-if="menuItem.image" :is="menuItem.image"></component>
            <p>{{ menuItem.name }}</p>
          </div>
          <p v-if="menuItem.unread > 0" class="text-sm">
            {{ menuItem.unread }}
          </p>
        </NuxtLink>
      </template>
    </div>
    <div class="mt-[26px]">
      <div class="w-full flex justify-between items-center">
        <label class="text-lg text-[#525252] font-semibold">Labels</label>
        <SharedIconHubEmailsSidebarPlusIcon class="w-3 h-3" />
      </div>
      <div
        v-if="labelsItems && labelsItems.length > 0"
        class="flex flex-col mt-[12px]"
      >
        <SourceHubEmailsLabelItem
          v-for="menuItem in labelsItems"
          :key="menuItem.id"
          :item="menuItem"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.router-link-exact-active,
.router-link-active {
  @apply bg-blue-200 text-white;
}
</style>
