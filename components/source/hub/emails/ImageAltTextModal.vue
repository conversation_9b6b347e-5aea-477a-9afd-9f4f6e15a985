<script setup lang="ts">
import type { Editor } from '@tiptap/vue-3'
import { useStore } from 'vuex'

interface Props {
  editor: Editor | null
}

const props = withDefaults(defineProps<Props>(), {
  editor: null,
})

const emit = defineEmits(['close'])
const store = useStore()
const altTextInput = ref<HTMLInputElement | null>(null)

const showModal = computed(() => store.state.emails.showImageAltTextModal)
const modalData = computed(() => store.state.emails.imageAltTextModalData)

const altText = ref('')

watch(
  () => modalData.value,
  (newData) => {
    if (newData && newData.currentAlt !== undefined) {
      altText.value = newData.currentAlt || ''
    }
  },
  { immediate: true },
)

const closeModal = () => {
  store.commit('emails/SET_IMAGE_ALT_TEXT_MODAL', {
    show: false,
    currentAlt: '',
    node: null,
    getPos: 0,
  })
  altText.value = ''
  emit('close')
}

const saveAltText = () => {
  const data = modalData.value
  if (
    data &&
    data.node &&
    data.getPos !== undefined &&
    data.getPos >= 0 &&
    props.editor
  ) {
    const pos = data.getPos
    const tr = props.editor.view.state.tr

    // Verify the node at this position
    const nodeAtPos = props.editor.view.state.doc.nodeAt(pos)

    // Update the node with new alt text
    tr.setNodeMarkup(pos, undefined, {
      ...data.node.attrs,
      alt: altText.value,
    })

    // Dispatch the transaction
    props.editor.view.dispatch(tr)
  } else {
    console.error('Failed to save alt text - missing data:', {
      modalData: !!data,
      node: !!data?.node,
      getPos: data?.getPos,
      editor: !!props.editor,
    })
  }
  closeModal()
}

// Handle escape key
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    closeModal()
  }
}

watch(
  () => showModal.value,
  (newVal) => {
    if (newVal) {
      nextTick(() => {
        altTextInput.value?.focus()
      })
    }
  },
)

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<template>
  <Transition name="modal">
    <div
      v-if="showModal"
      class="fixed inset-0 z-50 flex items-center justify-center bg-gray-600/75"
      @click.self="closeModal"
    >
      <div
        class="bg-white rounded-lg shadow-xl max-w-[678px] w-full mx-4"
        @click.stop=""
      >
        <div class="border-b-[2px] border-[#F1F2F6]">
          <div class="flex justify-between items-center px-6 py-3.5">
            <p class="text-lg font-semibold text-[#505050]">Alt text</p>
            <SharedIconHubEmailsCrossIcon
              @click="closeModal"
              class="w-4 h-4 cursor-pointer"
            />
          </div>
        </div>

        <div class="px-6 py-4 text-[#525252] text-base leading-[19px]">
          <p>
            Alt text is accessed by screen readers for people who might have
            trouble seeing your content.
          </p>
          <div class="mt-3 flex items-center gap-4">
            <span>Description</span>
            <input
              ref="altTextInput"
              type="text"
              v-model="altText"
              placeholder="Alter text"
              class="w-full h-[38px] px-6 border-none outline-none bg-[#F1F2F6] rounded-full text-[#333333]"
            />
          </div>
        </div>
        <div
          class="flex justify-end items-center space-x-2 px-6 py-3.5 border-t-[2px] border-[#F1F2F6]"
        >
          <button
            @click="closeModal"
            class="rounded-full font-semibold text-[#4A71D4] border-[1px] border-[#4A71D4] min-w-[108px] h-[35px] px-6"
          >
            Cancel
          </button>

          <button
            @click="saveAltText"
            class="rounded-full font-semibold text-[#FFFFFF] bg-[#4A71D4] min-w-[108px] h-[35px] px-6"
          >
            Apply
          </button>
        </div>
      </div>
    </div>
  </Transition>
</template>

<style scoped>
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-active .bg-white,
.modal-leave-active .bg-white {
  transition: transform 0.3s ease;
}

.modal-enter-from .bg-white,
.modal-leave-to .bg-white {
  transform: scale(0.95);
}
</style>
