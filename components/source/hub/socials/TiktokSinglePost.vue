<template>
  <div v-if="singlePost" class="w-full bg-white mb-3 whole-card scroll">
    <div
      :id="singlePost.id"
      class="card h-auto tiktok-card"
      :style="{ '--scrollColor': scrollColor }"
    >
      <div class="flex items-center px-3">
        <SharedIconToktokIcon
          v-if="!singlePost.profileImageUrl"
          class="w-13 h-13 rounded-full border border-gray-400"
        ></SharedIconToktokIcon>
        <img
          v-else
          height="42"
          width="42"
          :src="singlePost.profileImageUrl"
          class="rounded-full profile-image"
          alt="dp"
          srcset=""
        />
        <div class="ml-2 lineHeight-1">
          <h2 class="profile-name leading-6">
            {{ singlePost.username }}
          </h2>
          <span class="text-ash-primary text-xs">{{ singlePost.name }} </span>
          <span class="text-ash-primary text-xs">· </span>
          <span class="text-ash-primary text-xs"
            >{{ $diffForHumans(singlePost.createdAt) }} ago</span
          >
        </div>
      </div>
      <div class="w-full mt-2">
        <!-- v-see-more.right="200" -->
        <p
          v-if="singlePost.title"
          v-html="addLineBreak(singlePost.title, singlePost.provider)"
          class="comment break-words px-3 text-lg"
        ></p>
        <!-- v-see-more.right="200" -->
        <p
          v-if="singlePost.embedHtml"
          v-html="singlePost.embedHtml"
          class="comment break-words px-3 text-base"
        ></p>
      </div>
      <div class="w-full py-3.5 px-3">
        <video
          v-if="singlePost.archivedVideoUrl"
          class="w-full rounded"
          height="400"
          controls
        >
          <source :src="singlePost.archivedVideoUrl" />
          <source :src="singlePost.archivedVideoUrl" />
        </video>
        <iframe
          v-else
          class="w-full rounded"
          height="400"
          :src="singlePost.embedLink"
        >
        </iframe>
      </div>
      <div class="flex flex-nowrap space-x-4 items-center px-3.5 text-ash-dark">
        <div class="flex flex-nowrap space-x-2 items-center text-base">
          <SharedIconTiktokLove class="w-8 h-8" />
          <span>{{ singlePost.likeCount }}</span>
        </div>
        <div class="flex flex-nowrap space-x-2 items-center text-base">
          <SharedIconTiktokComment class="w-8 h-8" />
          <span>{{ singlePost.commentCount }}</span>
        </div>
        <div class="flex flex-nowrap space-x-2 items-center text-base">
          <SharedIconTiktokShare class="w-6 h-6" />
          <span>{{ singlePost.shareCount }}</span>
        </div>
      </div>
      <div class="w-full h-px px-3 my-5">
        <div class="w-full h-full bg-gray-1700"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// import { useStore } from 'vuex'
import { useFeedComment } from '~/composables/feeds/useComment'

const props = defineProps({
  scrollColor: {
    type: String,
    default: '',
  },
  singlePost: {
    type: Object,
    default: () => {},
  },
})

// const vueStore = useStore()
const { loadComment } = useFeedComment()
const { addLineBreak } = useLineBreak()
const feedComments = ref(null)
const collapseable = ref(false)
// const articles = computed(() => vueStore.state.search.articles)
// const article = computed(() => vueStore.state.search.article)
// const searchComments = computed(() => vueStore.state.search.searchComments)
interface ArticlesItem {
  id: number | string
  provider: string
  socialId: string
  createdAt: string
  title: string
  text: string
  duration: number
  height: number
  width: number
  likeCount: number
  commentCount: number
  shareCount: number
  viewCount: number
  embedHtml: string
  embedLink: string
  coverImageUrl: string
  videoUrl: string
  archivedVideoUrl: string
}
const loadFeedComment = async (article: ArticlesItem) => {
  if (collapseable.value) {
    feedComments.value = null
    collapseable.value = false
  } else {
    try {
      const { data } = (await loadComment({
        provider: article.provider,
        id: article.id,
      })) as any
      if (data.length) {
        feedComments.value = data
        collapseable.value = true
      }
    } catch (error) {
      console.log(error)
    }
  }
}
</script>

<style lang="scss" scoped>
.whole-card {
  box-shadow: 0px 1px 2px #22283126;
  border-radius: 10px;
}
.card {
  /* md:w-1/2 */
  @apply bg-white py-3 my-0 w-full z-1;
  box-shadow: 0px 1px 2px #22283126;
  border-radius: 10px;
  /* // min-width: 400px;
  // max-width: 690px; */
}
.imagePopup-enter-active,
.imagePopup-leave-active {
  transition: opacity 0.5s;
}
.imagePopup-enter,
.imagePopup-leave-to {
  opacity: 0;
}
.profile-name {
  @apply text-lg;
  color: #222831;
}

.profile-image {
  @apply w-10.1 h-10.1 rounded-full;
}

.lineHeight-1 {
  line-height: 1px;
}

.profile-date {
  @apply text-xs text-gray-light;
}

.comment {
  @apply mt-4 mb-2;
  color: #222831;
}

.reaction-conter {
  @apply text-base ml-1.5 text-gray-light;
}

.comments-conter {
  @apply inline-block text-gray-light;
}
.scroll {
  overflow-y: auto;
  overflow-x: hidden;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-color: var(--scrollColor) #ececec; /* Firefox 64 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background: #ececec;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: var(--scrollColor);
    border-radius: 3px;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: var(--scrollColor);
  }
}
</style>
