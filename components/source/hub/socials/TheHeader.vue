<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const route = useRoute()
const accountItem = computed(() => {
  return store.state.social.accountItem
})
const accountType = computed(() => store.state.social.accountType)
const isGmailMessageRefreshing = computed(
  () => store.state.emails.isGmailMessageRefreshing,
)
const notificationType = ref('all')
const selectedType = (type: string) => {
  notificationType.value = type
}
const showProfileModal = ref(false)
const showHelpMenu = ref(false)
const toggleHelpMenu = () => {
  showHelpMenu.value = !showHelpMenu.value
  console.log('Help menu toggled:', showHelpMenu.value)
}
const toggleProfileModal = () => {
  showProfileModal.value = !showProfileModal.value
  console.log('Profile modal toggled:', showProfileModal.value)
}
const gmailComposeMoreOption = computed(
  () => store.state.emails.gmailComposeMoreOption,
)
const handleComposeMail = () => {
  store.commit('emails/SET_SHOW_COMPOSE_SECTION', {
    provider: accountItem.value.provider,
  })
}
onMounted(() => {
  document.addEventListener('click', () => {
    showProfileModal.value = false
    showHelpMenu.value = false
  })
})
onUnmounted(() => {
  document.removeEventListener('click', () => {
    showProfileModal.value = false
    showHelpMenu.value = false
  })
})
</script>

<template>
  <div class="w-full px-4 pt-4 pb-6 border-b-[3px] border-[#F1F2F6] relative">
    <div class="w-full flex flex-col space-y-[37px]">
      <div class="w-full flex justify-between">
        <div class="flex space-x-2.5 relative pr-4">
          <SourceAccountLogo
            class="cursor-pointer"
            :account-profile-pic="accountItem.profilePic"
            :account-provider="accountItem.provider"
            width="54px"
            height="54px"
            @click.stop="toggleProfileModal"
          />
          <div
            @click.stop="toggleProfileModal"
            class="flex flex-col space-y-[2px] cursor-pointer"
          >
            <p class="text-[#525252] text-xl font-bold">
              {{ accountItem.name }}
            </p>
            <p class="text-[#707070]">
              {{
                accountType === 'text'
                  ? accountItem.username
                  : accountItem.username
              }}
            </p>
          </div>
          <SourceHubEmailsProfileModal
            v-if="showProfileModal"
            @click.stop=""
            @close-modal="() => (showProfileModal = false)"
          />
        </div>
        <div v-if="accountType === 'emails'" class="flex space-x-4">
          <div
            v-if="accountItem.provider === 'Google'"
            class="relative h-[35px]"
          >
            <SharedIconHubEmailsButtonGoogleHelp
              class="cursor-pointer"
              :color="showHelpMenu ? '#FFFFFF' : '#525252'"
              :BgColor="showHelpMenu ? '#4A71D4' : '#F1F2F6'"
              @click.stop="toggleHelpMenu"
            />
            <div v-if="showHelpMenu">
              <SourceHubEmailsHelpMenu />
            </div>
          </div>
          <div
            v-if="accountItem.provider === 'Microsoft'"
            class="w-[35px] h-[35px] rounded-full bg-[#F1F2F6] flex justify-center items-center"
          >
            <SharedIconHubMicrosoftLightIcon class="w-3 h-4" />
          </div>
          <NuxtLink
            :to="`/source/hub/emails/settings/${accountItem.provider === 'Google' ? 'general' : 'microsoft/account'}`"
            @click.native="
              () => {
                store.commit('emails/SET_PREVIOUS_PATH', route.fullPath)
                console.log('Settings clicked', route.fullPath)
              }
            "
          >
            <SharedIconHubEmailsButtonSettings />
          </NuxtLink>
          <SharedIconHubEmailsButtonApps
            v-if="accountItem.provider === 'Google'"
          />
          <BaseButton
            class="w-[116px] h-[35px] rounded-full flex justify-center items-center bg-[#4A71D4] text-white font-semibold"
            :text="accountItem.provider === 'Google' ? 'Compose' : 'New Mail'"
            @click="handleComposeMail"
          />
        </div>
        <BaseButton
          v-else
          class="w-[132px] h-[35px] rounded-full flex justify-center items-center bg-[#4A71D4] text-white font-semibold"
          :text="
            notificationType === 'messages' ||
            route.fullPath.includes('messages') ||
            accountType === 'text'
              ? 'New Chat'
              : 'Create Post'
          "
          @click="$router.push('/source/create-post')"
        />
      </div>
      <SourceHubSocialsHeaderTab @seleted-type="selectedType" />
    </div>
    <div
      v-if="isGmailMessageRefreshing"
      class="w-[104px] bg-[#FFEEBF] border border-[#DEA300] shadow-[0px_2px_4px_#0000003D] rounded-sm absolute top-0 right-1/2 -translate-1/2 px-4 py-[7px] text-base leading-[21px] font-semibold text-[#525252]"
    >
      Loading...
    </div>
  </div>
</template>
