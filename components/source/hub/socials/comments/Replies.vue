<template>
  <div class="w-full">
    <SourceHubSocialsCommentsReply
      v-for="(comment, index) in comments"
      :key="index"
      :comment="comment"
      :main-comment="mainComment"
      @selected-comment-reply="selectedCommentReply"
      :hideFeatures="hideFeatures"
    >
    </SourceHubSocialsCommentsReply>
    <div v-if="mainComment.selected" class="flex mt-2">
      <img
        class="w-6 min-w-6 h-6 min-h-6 mr-1 rounded-full"
        src="/social/profile-picture.png"
        alt="sharparchive"
      />
      <div class="w-full">
        <div class="w-full px-4 py-2 bg-white rounded-lg">
          <div class="flex space-x-1">
            <!-- <div class="bg-[#DCEAFF] text-[#333333]">{{ replyPersonName }}</div> -->
            <!-- <textarea
              class="w-full h-auto no-scrollbar resize-none textarea-style outline-none border-none bg-white text-[#333333]"
              name=""
              id=""
              :value="replyPersonName"
              @input="getInputData"
            >
            </textarea> -->
            <editor-content
              class="w-full max-h-[100px] pr-4 overflow-y-auto overflow-x-hidden comment-text-editor"
              :editor="editor"
              @click.stop=""
            />
          </div>
          <div class="flex space-x-2.5 mt-4 relative">
            <img
              src="/social/comments/icon-emoji.svg"
              alt="icon-emoji"
              class="cursor-pointer"
              @click.stop="toggleEmojiPicker"
            />
            <img
              v-if="!avatarPreview && !gifPreview"
              src="/social/comments/icon-photos.svg"
              alt="icon-photos"
              class="cursor-pointer"
              @click="fileInput.click()"
            />
            <input
              ref="fileInput"
              class="hidden"
              type="file"
              accept="image/*"
              @change="onFileSelected"
            />
            <img
              v-if="!avatarPreview && !gifPreview"
              src="/social/comments/icon-gif.svg"
              alt="icon-gif"
              @click.stop="
                setShowPicker(),
                  store.commit('emails/SET_CLOSE_ALL_MENUS', true)
              "
            />
            <img
              v-if="!avatarPreview && !gifPreview"
              src="/social/comments/icon-clipboard.svg"
              alt="icon-clipboard"
              class="cursor-pointer"
              @click.stop="showClipBoardMenuHandler(true)"
            />
            <EmojiPicker
              v-if="showEmojiPicker"
              @click.stop=""
              :editor="editor"
              class="absolute top-[-344px] left-[-14px]"
            />
            <SourceHubSocialsClipBoardMenu
              v-if="showClipBoardMenu"
              class="absolute w-full h-[360px] max-h-[360px] top-[-360px] left-0"
              v-model:showClipBoardMenu="showClipBoardMenu"
              @click.stop=""
            />
            <GifPicker
              v-show="showPicker && closeAllMenus"
              ref="gifPicker"
              class="absolute top-[-304px] left-[-14px]"
              @select="(gif) => selectGif(gif)"
            />
          </div>
        </div>
        <div v-if="avatarPreview" class="px-4 w-full flex space-x-2 mt-2.5">
          <img class="max-w-[80px]" :src="avatarPreview" alt="comment-image" />
          <div
            class="w-6 h-6 rounded-full bg-[#ebedf5] flex items-center justify-center cursor-pointer"
            @click="avatarPreview = ''"
          >
            <ClientOnly>
              <fa
                class="text-lg font-semibold text-[#525252]"
                :icon="['fas', 'times']"
              />
            </ClientOnly>
          </div>
        </div>
        <div
          v-if="gifPreview"
          class="px-4 w-full flex space-x-2 mt-2.5"
        >
          <img
            class="max-w-[80px]"
            :src="gifPreview"
            alt="comment-image"
          />
          <div
            class="w-6 h-6 rounded-full bg-[#ebedf5] flex items-center justify-center cursor-pointer"
            @click="selectGif('')"
          >
            <ClientOnly>
              <fa
                class="text-lg font-semibold text-[#525252]"
                :icon="['fas', 'times']"
              />
            </ClientOnly>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Link from '@tiptap/extension-link'

const emit = defineEmits<{
  (
    e: 'selected-comment-reply',
    name: string,
    facebookUrl: string,
    id: number,
  ): void
}>()
const props = defineProps({
  comments: {
    type: Array,
    default: null,
  },
  mainComment: {
    type: Object,
    default: () => {},
  },
  replyPersonName: {
    type: String,
    default: '',
  },
  hideFeatures: {
    type: Boolean,
    default: false,
  },
})
// const getInputData = (e: any) => {
//   console.log(e.target.value)
// }
const store = useStore()
const editor = ref<Editor | null>(null)
onMounted(() => {
  editor.value = new Editor({
    // content: `<p></p>`,
    extensions: [
      // StarterKit.configure({}),
      StarterKit,
      Link.configure({
        openOnClick: true,
        autolink: true,
        HTMLAttributes: {
          class: 'tiptap-link',
          style:
            'color: #333333; text-decoration: none; cursor: pointer; background-color: #DCEAFF;',
        },
      }),
    ],
    editorProps: {
      attributes: {
        class:
          'w-full h-auto no-scrollbar resize-none textarea-style outline-none border-none bg-white text-[#333333]',
      },
    },
  })
  document.addEventListener('click', () => {
    showEmojiPicker.value = false
    showClipBoardMenu.value = false
  })
})
onUnmounted(() => {
  document.removeEventListener('click', () => {
    showEmojiPicker.value = false
    showClipBoardMenu.value = false
  })
})
const closeAllMenus = computed(() => store.state.emails.closeAllMenus)
watch(closeAllMenus, (data) => {
  if (!data) {
    showPicker.value = false
  }
})
const { gifs, loadGifs, selectGif, gifPreview } = useGiphy()
// const gifPreview = computed(() => store.state.social.gifPreview)
const showPicker = ref<boolean>(false)
const setShowPicker = () => {
  showPicker.value = !showPicker.value
  showEmojiPicker.value = false
  showClipBoardMenu.value = false
  if (showPicker.value && gifs.value.length === 0) {
    loadGifs()
  }
}
const selectedCommentReply = (
  name: string,
  facebookUrl: string,
  id: number,
) => {
  emit('selected-comment-reply', name, facebookUrl, id)
}
const showEmojiPicker = ref<boolean>(false)
const toggleEmojiPicker = () => {
  showEmojiPicker.value = !showEmojiPicker.value
  showClipBoardMenu.value = false
  showPicker.value = false
}
const hideAllMenus = () => {
  showEmojiPicker.value = false
  showClipBoardMenu.value = false
  showPicker.value = false
}
const showClipBoardMenu = ref<boolean>(false)
const showClipBoardMenuHandler = (value: boolean) => {
  showClipBoardMenu.value = value
  showEmojiPicker.value = false
  showPicker.value = false
}
const fileInput = ref<HTMLInputElement | null>(null)
const { avatarPreview, onFileSelected } = useFileUpload()
defineExpose({
  hideAllMenus,
  editor,
})
</script>

<style scoped>
.textarea-style {
  field-sizing: content;
  width: 100%;
  /* min-height: 3rem; */
  max-height: 6rem;
}
</style>
