<template>
  <div class="flex mt-0">
    <img
      class="w-6 min-w-6 h-6 min-h-6 mr-1 rounded-full"
      :src="
        comment.profileImageUrl
          ? comment.profileImageUrl
          : store.state.defaultImg
      "
      alt=""
    />
    <div class="w-full">
      <div class="flex space-x-2 items-center">
        <div
          v-if="comment.name || comment.text"
          :id="comment.id"
          class="px-4 py-2 font-medium rounded-lg"
          :class="[
            mainComment.selected ? 'bg-white' : 'bg-[#EBEDF5]',
            hideFeatures && comment.showAlert ? 'bg-[#FFE0E0]' : '',
          ]"
        >
          <a
            v-if="comment.facebookUrl && comment.name"
            :href="comment.facebookUrl"
            target="_blank"
            class="text-[#333333] text-sm font-semibold hover:text-blue-500 hover:underline"
            >{{ comment.name }}</a
          >
          <!-- <span class="font-bold"> - </span>
        <span v-if="comment.createdAt" class="text-gray-600">
          <date-time
            :show-time="false"
            :datetime="comment.createdAt"
          ></date-time>
        </span> -->
          <p
            v-if="comment.text"
            v-nameIndicator="messageTagNames(comment.messageTags)"
            v-see-more.right="200"
            class="font-normal break-all text-[#525252]"
            v-html="comment.text"
          ></p>
          <!-- <div id="fb-root"></div>
        <script
          async
          defer
          crossorigin="anonymous"
          src="https://connect.facebook.net/en_GB/sdk.js#xfbml=1&version=v13.0&appId=482095120000076&autoLogAppEvents=1"
          nonce="ms5VurmK"
        ></script> -->
        </div>
        <SharedIconAlertIcon
          v-if="hideFeatures && comment.showAlert"
          class="size-5"
        />
      </div>
      <img
        v-if="
          (comment.type === 'photo' ||
            comment.type === 'animated_image_share') &&
          (comment.archivedSourceUrl || comment.sourceUrl)
        "
        class="w-38 h-auto rounded-2xl mt-1 border shadow-sm"
        :src="
          comment.archivedSourceUrl
            ? comment.archivedSourceUrl
            : comment.sourceUrl
        "
        alt=""
      />
      <img
        v-if="comment.type === 'sticker' && comment.attachmentUrl"
        class="w-38 h-auto rounded-2xl mt-1 border shadow-sm"
        :src="comment.attachmentUrl"
        alt=""
      />
      <iframe
        v-else-if="comment.type === 'video_inline'"
        class="rounded-2xl mt-1"
        :src="`https://www.facebook.com/plugins/video.php?height=200&href=${comment.attachmentUrl}%2F&show_text=false&width=300&t=0`"
        width="300"
        height="200"
        style="border: none; overflow: hidden"
        scrolling="no"
        frameborder="0"
        allowfullscreen="true"
        allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
      ></iframe>
      <div class="flex !space-x-4 text-[#707070] text-xs my-2">
        <p>1h</p>
        <p
          v-if="!hideFeatures"
          class="font-bold"
          @click.stop="
            emit(
              'selected-comment-reply',
              comment.name,
              comment.facebookUrl,
              mainComment.id,
            )
          "
        >
          Reply
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'

const emit = defineEmits<{
  (
    e: 'selected-comment-reply',
    name: string,
    facebookUrl: string,
    id: number,
  ): void
}>()

const props = defineProps({
  comment: {
    type: Object,
    default: null,
  },
  mainComment: {
    type: Object,
    default: null,
  },
  hideFeatures: {
    type: Boolean,
    default: false,
  },
})

const store = useStore()
const messageTagNames = (payload: any) => {
  let names = []
  if (payload) {
    const profile = JSON.parse(payload)
    names = profile.map((item: any) => {
      return item.name
    })
  }
  return names
}
</script>
