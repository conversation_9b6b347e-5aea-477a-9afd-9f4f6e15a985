<template>
  <div
    v-if="comments"
    class="w-full"
    @click="store.commit('social/SET_SELECTED_COMMENT_FOR_REPLY_FALSE')"
  >
    <template v-for="(comment, index) in comments">
      <SourceHubSocialsCommentsComment
        v-if="index <= seeMoreCommentIndex"
        :key="index"
        :comment="comment"
        :hideFeatures="hideFeatures"
        @click.stop="emit('hideMenus')"
      ></SourceHubSocialsCommentsComment>
    </template>
    <p
      v-if="comments.length - 1 > seeMoreCommentIndex && !hideFeatures"
      class="pb-4 text-[#707070] cursor-pointer"
      @click.stop="seeMore"
    >
      View {{ comments.length - (seeMoreCommentIndex + 1) }} more comments
    </p>
    <p
      v-if="seeMoreCommentIndex === comments.length && comments.length > 2"
      class="pb-4 text-[#707070] cursor-pointer"
      @click.stop="lessMore"
    >
      Less more comments
    </p>
  </div>
</template>

<script setup lang="ts">
import store from '~/store'

const emit = defineEmits(['hideMenus'])

const props = defineProps({
  comments: {
    type: Array,
    default: null,
  },
  hideFeatures: {
    type: Boolean,
    default: false,
  },
})

const seeMoreCommentIndex = ref<number>(1)
const seeMore = () => {
  seeMoreCommentIndex.value += 2
}
const lessMore = () => {
  seeMoreCommentIndex.value = 1
}
</script>
