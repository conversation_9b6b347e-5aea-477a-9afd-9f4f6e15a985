<template>
  <div
    v-if="singlePost"
    class="card scroll"
    :style="{ '--scrollColor': scrollColor }"
  >
    <div class="flex flex-col cursor-pointer">
      <div
        v-if="previousSinglePost.length > 0 && isLive"
        class="w-9 h-9 rounded-full bg-purple-midlight flex justify-center cursor-pointer items-center ml-3 mb-4"
        @click="backToPreviousPost()"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 384 512"
          width="24"
          height="24"
        >
          <path
            class="back-arrow"
            d="M41.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l192 192c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.3 256 278.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-192 192z"
          />
        </svg>
      </div>

      <div
        v-if="singlePost?.referencedTweetsId"
        class="flex flex-row space-x-0 ml-12"
        @click="
          singlePost.referencedTweetsType !== 'replied_to'
            ? [
                loadFeedComment(
                  singlePost.id,
                  singlePost.socialId,
                  singlePost.referencedTweetsId,
                  singlePost.referencedTweetsType,
                ),
              ]
            : ''
        "
      >
        <img
          class="retweet"
          src="@/assets/img/icon/TwitterIcon/retweet.svg"
          alt="Twitter Retweet Icon"
        />

        <div class="text-gray-1700 font-semibold">You Retweeted</div>
      </div>

      <div class="flex px-3 w-full">
        <div
          class="post-profile"
          @click="
            singlePost.referencedTweetsType !== 'replied_to' && isLive
              ? [
                  loadFeedComment(
                    singlePost.id,
                    singlePost.socialId,
                    singlePost.referencedTweetsId,
                    singlePost.referencedTweetsType,
                  ),
                ]
              : ''
          "
        >
          <!-- <twitter-icon
                v-if="
                  !singlePost.profileImageUrl &&
                  !singlePost.referencedTweetsProfileImageUrl
                "
                class="w-14 h-14 rounded-full border border-gray-400"
              ></twitter-icon> -->
          <div
            v-if="
              !singlePost.profileImageUrl &&
              !singlePost.referencedTweetsProfileImageUrl
            "
            class="w-14 h-14 rounded-full border border-gray-400 twitter-icon"
          ></div>
          <template v-else>
            <img
              v-if="!singlePost.profileImageUrl"
              :src="singlePost.referencedTweetsProfileImageUrl"
              class="rounded-full w-14 h-14"
              :alt="`${singlePost.referencedTweetsName} Profile Picture`"
            />
            <img
              v-else
              :src="singlePost.profileImageUrl"
              class="rounded-full w-14 h-14"
              :alt="`${singlePost.name} Profile Picture`"
            />
          </template>
        </div>

        <div class="w-full">
          <div
            @click="
              singlePost.referencedTweetsType !== 'replied_to' && isLive
                ? [
                    loadFeedComment(
                      singlePost.id,
                      singlePost.socialId,
                      singlePost.referencedTweetsId,
                      singlePost.referencedTweetsType,
                    ),
                  ]
                : ''
            "
          >
            <div class="flex justify-between items-start">
              <p class="text-ash-primary space-x-1">
                <span v-if="singlePost.name" class="font-bold text-offgray">{{
                  singlePost.name
                }}</span>

                <span v-else class="font-bold text-offgray">{{
                  singlePost.referencedTweetsName
                }}</span>

                <img
                  src="@/assets/img/svg/twitter/Verify.svg"
                  class="inline-block h-4"
                  alt="Twitter Verify Icon"
                />

                <span v-if="singlePost.username" class="text-gray-1700"
                  >@{{ singlePost.username }} ·</span
                >

                <span v-else class="text-gray-1700"
                  >@{{ singlePost.referencedTweetsUsername }} ·</span
                >

                <span v-if="singlePost.createdAt" class="text-gray-1700">{{
                  $diffForHumansShortFlag(singlePost.createdAt)
                }}</span>

                <span v-else class="text-gray-1700">
                  {{
                    $diffForHumansShortFlag(
                      singlePost.referencedTweetsCreatedAt,
                    )
                  }}
                </span>
              </p>

              <!-- <fa
                        class="cursor-pointer text-gray-600 text-lg mt-1 mr-0.5"
                        :icon="['fas', 'ellipsis-h']"
                      />-->
            </div>

            <!-- <p v-if="singlePost.replyToUsername" class="">
                    <span class="text-gray-1700">Replying to</span>
                    <span class="text-blue-600"
                      >@{{ singlePost.replyToUsername }}</span
                    >
                    </p>-->

            <!-- v-html="linkify(singlePost.text, singlePost.entities.urls)" -->

            <p
              v-if="singlePost.text"
              class="text-ash-primary"
              v-html="addLineBreak(singlePost.text, singlePost.provider)"
            ></p>

            <div
              v-for="(poll, pollIndex) in singlePost.polls"
              :key="pollIndex"
              class="flex flex-col my-4"
            >
              <div
                v-for="(option, optionIndex) in poll.options"
                :key="optionIndex"
                class="flex justify-between space-x-2 items-center w-full p-3 relative"
                :class="[
                  optionIndex !== 0 ? 'mt-1' : '',
                  (poll.voting_status === 'closed' ||
                    totalTimeLeft(poll.end_datetime) === 'Final results') &&
                  maxValueIndex(poll.options, optionIndex) === optionIndex
                    ? 'font-bold'
                    : '',
                ]"
              >
                <div
                  class="absolute top-0 left-0 h-full"
                  :class="[
                    votingPercentage(poll.options, option.votes) === 0
                      ? 'rounded-lg'
                      : 'rounded-md',
                    (poll.voting_status === 'closed' ||
                      totalTimeLeft(poll.end_datetime) === 'Final results') &&
                    maxValueIndex(poll.options, optionIndex) === optionIndex
                      ? 'poll_bgColor_closed'
                      : 'poll_bgColor',
                  ]"
                  :style="{
                    '--bgValue':
                      votingPercentage(poll.options, option.votes) === 0
                        ? '7px'
                        : `${votingPercentage(poll.options, option.votes)}%`,
                  }"
                ></div>

                <p class="text-ash-primary z-1 break-all">
                  {{ option.label }}
                </p>

                <p class="text-ash-primary z-1">
                  {{ votingPercentage(poll.options, option.votes) }}%
                </p>
              </div>

              <div class="flex space-x-2 items-center">
                <span
                  v-if="totalVotes(poll.options) === 1"
                  class="text-gray-1700 mt-4 inline-block"
                  >{{ totalVotes(poll.options) }} vote</span
                >

                <span v-else class="text-gray-1700 mt-4 inline-block"
                  >{{ totalVotes(poll.options) }} votes</span
                >

                <span class="text-gray-1700 mt-4 inline-block">·</span>

                <span class="text-gray-1700 mt-4 inline-block">
                  {{
                    poll.voting_status === 'closed'
                      ? 'Final results'
                      : totalTimeLeft(poll.end_datetime)
                  }}
                </span>
              </div>
            </div>
          </div>

          <!-- <img
            v-if="singlePost.mediaUrl"
            :src="singlePost.mediaUrl"
            :alt="`${singlePost.name}'s Posted Image in Twitter Profile`"
            class="mt-3 rounded-xl w-full"
          /> -->

          <div
            v-if="
              singlePost.mediaUrl.length > 0 &&
              singlePost.mediaType === 'photo' &&
              singlePost.mediaUrl.length !== 3
            "
            class="flex justify-center flex-wrap relative mt-3 overflow-hidden rounded-3xl border border-solid border-gray-2100 space-x-0.5"
          >
            <div
              v-for="(feature, imageIndex) in singlePost.mediaUrl.slice(0, 5)"
              :key="feature"
              class="flex justify-center items-center cursor-pointer"
              :class="[
                imageIndex < 2 ? 'column-2' : 'column-3',
                imageIndex === 2 ? 'margin_left' : '',
                singlePost.mediaUrl.length === 4 &&
                (imageIndex === 0 || imageIndex === 1)
                  ? 'margin_top'
                  : '',
              ]"
              @click="
                singlePost.referencedTweetsType !== 'replied_to' && isLive
                  ? [
                      loadFeedComment(
                        singlePost.id,
                        singlePost.socialId,
                        singlePost.referencedTweetsId,
                        singlePost.referencedTweetsType,
                      ),
                    ]
                  : ''
              "
            >
              <img
                class="w-full"
                :class="[singlePost.mediaUrl.length > 1 ? 'hover-border' : '']"
                :src="feature"
              />

              <div
                v-if="imageIndex === 4"
                class="w-full absolute z-0 post-overlay"
              >
                <div class="sub-overlay"></div>

                <p
                  class="xl:text-5xl md:text-3xl text-xl relative opacity-100 text-white font-bold"
                >
                  + {{ singlePost.mediaUrl.length - 4 }}
                </p>
              </div>
            </div>
          </div>

          <div
            v-else-if="
              singlePost.mediaUrl.length > 0 &&
              singlePost.mediaType === 'photo' &&
              singlePost.mediaUrl.length === 3
            "
            class="flex justify-center flex-nowrap mt-3 overflow-hidden rounded-3xl border border-solid border-gray-2100 space-x-0.5"
          >
            <div class="flex flex-col w-1/2">
              <div
                v-for="(feature, imageIndex) in singlePost.mediaUrl.slice(0, 1)"
                :key="feature"
                class="flex justify-center items-center cursor-pointer"
                :class="imageIndex < 2 ? 'column-2' : 'column-3'"
                @click="
                  singlePost.referencedTweetsType !== 'replied_to' && isLive
                    ? [
                        loadFeedComment(
                          singlePost.id,
                          singlePost.socialId,
                          singlePost.referencedTweetsId,
                          singlePost.referencedTweetsType,
                        ),
                      ]
                    : ''
                "
              >
                <img class="w-full h-full three-image-show" :src="feature" />
              </div>
            </div>

            <div class="flex flex-col w-1/2">
              <div
                v-for="(feature, imageIndex) in singlePost.mediaUrl.slice(1, 5)"
                :key="feature"
                class="flex justify-center items-center cursor-pointer"
                :class="[
                  imageIndex < 3 ? 'column-2' : 'column-3',
                  singlePost.mediaUrl.length === 3 && imageIndex === 0
                    ? 'margin_top'
                    : '',
                ]"
                @click="
                  singlePost.referencedTweetsType !== 'replied_to' && isLive
                    ? [
                        loadFeedComment(
                          singlePost.id,
                          singlePost.socialId,
                          singlePost.referencedTweetsId,
                          singlePost.referencedTweetsType,
                        ),
                      ]
                    : ''
                "
              >
                <img class="w-full h-full hover-border" :src="feature" />

                <div
                  v-if="imageIndex === 2"
                  class="w-full h-full absolute z-0 post-overlay"
                >
                  <div class="sub-overlay"></div>

                  <p
                    class="xl:text-5xl md:text-3xl text-xl relative opacity-100 text-white font-bold"
                  >
                    {{ `+${singlePost.mediaUrl.length - 4}` }}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div
            v-else-if="
              (singlePost.mediaType === 'video' ||
                singlePost.mediaType === 'animated_gif') &&
              singlePost.videoUrl.length > 0
            "
            class="w-full pt-6"
            @click="
              singlePost.referencedTweetsType !== 'replied_to' && isLive
                ? [
                    loadFeedComment(
                      singlePost.id,
                      singlePost.socialId,
                      singlePost.referencedTweetsId,
                      singlePost.referencedTweetsType,
                    ),
                    // singlePostShow(singlePost),
                  ]
                : ''
            "
          >
            <video class="w-full h-auto" height="400" controls>
              <source :src="singlePost.videoUrl ? singlePost.videoUrl : ''" />
            </video>
          </div>

          <div v-if="isLive" class="reaction">
            <div
              @click="
                singlePost.referencedTweetsType !== 'replied_to'
                  ? [
                      loadFeedComment(
                        singlePost.id,
                        singlePost.socialId,
                        singlePost.referencedTweetsId,
                        singlePost.referencedTweetsType,
                      ),
                    ]
                  : ''
              "
            >
              <TwitterCommentIcon class="inline-block w-4 mr-4" />

              <span>{{ singlePost.replyCount }}</span>
            </div>

            <div>
              <img
                src="@/assets/img/icon/TwitterIcon/retweet.svg"
                alt="Twitter Retweet Icon"
              />
              <span>{{ singlePost.retweetCount }}</span>
            </div>

            <div>
              <TwitterReactionIcon class="inline-block w-4 mr-4" />

              <span>{{ singlePost.likeCount }}</span>
            </div>
          </div>
        </div>
      </div>

      <template v-if="isLive">
        <div class="mb-4 h-px w-full bg-gray-1500"></div>
        <comments v-if="comments" :comments="comments"></comments>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
import Comments from '~/components/home/<USER>/twitter/Comments.vue'
import TwitterCommentIcon from '~/components/shared/icon/TwitterCommentIcon.vue'
import TwitterReactionIcon from '~/components/shared/icon/TwitterReactionIcon.vue'
import { useFeedComment } from '~/composables/feeds/useComment'
import { useLineBreak } from '~/composables/useLineBreak'

defineProps({
  scrollColor: {
    type: String,
    default: '',
  },
  singlePost: {
    type: Object,
    default: () => {},
  },
  isLive: {
    type: Boolean,
    default: true,
  },
})

const store = useStore()
const { loadComment } = useFeedComment()
const { addLineBreak } = useLineBreak()
const feedComments = ref(null)
// const collapseable = ref(true)

const comments = computed(() => store.state.browse.twitterComments)
const previousSinglePost = computed(
  () => store.state.socialFeed.previousSinglePost,
)
const previousTwitterComment = computed(
  () => store.state.home.previousTwitterComment,
)
// const singlePost = computed(() => store.state.browse.twitterPosts)

const loadFeedComment = async (
  id: any,
  socialId: any,
  referencedTweetsId: any,
  referencedTweetsType: any,
  event?: any,
) => {
  // if (collapseable.value) {
  //   feedComments.value = null
  //   collapseable.value = false
  // } else {
  try {
    const { data } = (await loadComment({
      provider: 'Twitter',
      id: id | event,
    })) as any
    if (data.length) {
      feedComments.value = data
      store.dispatch('home/setTwitterComments', {
        data,
        socialId,
        referencedTweetsId,
        referencedTweetsType,
      })
      // collapseable.value = true
    }
  } catch (error) {
    console.log(error)
  }
}

const votingPercentage = (value: any[], singleVote: number) => {
  let totalValue = 0
  value.forEach((item: any) => {
    totalValue = totalValue + item.votes
  })
  let votePercentage = 0
  if (totalValue > 0) {
    votePercentage = (singleVote * 100) / totalValue
  }

  return votePercentage === 0
    ? 0
    : votePercentage === 100
      ? 100
      : votePercentage.toFixed(1)
}

const totalVotes = (value: any[]) => {
  let totalValue = 0
  value.forEach((item: any) => {
    totalValue = totalValue + item.votes
  })
  return totalValue
}

const totalTimeLeft = (endTime: string | number | Date) => {
  const date1 = new Date()
  const date2 = new Date(endTime)

  const diff = date2.getTime() - date1.getTime()

  let msec = diff
  const hh = Math.floor(msec / 1000 / 60 / 60)
  msec -= hh * 1000 * 60
  const mm = Math.floor(msec / 1000 / 60)

  return hh < 0 && mm < 0
    ? 'Final results'
    : hh === 0
      ? mm + ' minutes left'
      : hh === 1
        ? hh + ' hour left'
        : hh + ' hours left'
}

const maxValueIndex = (array: any[], currentIndex: any) => {
  const tempArray: any[] = []
  array.forEach((item: any) => {
    tempArray.push(item.votes)
  })

  const max = Math.max(...tempArray)
  const index: any[] = []
  array.forEach((item: any, itemIndex: any) => {
    if (max === item.votes) {
      index.push(itemIndex)
    }
  })

  if (index.includes(currentIndex)) {
    return currentIndex
  }
}

const backToPreviousPost = () => {
  if (
    previousSinglePost.value[0].mediaUrl.length === 0 ||
    previousSinglePost.value[0].mediaType !== 'photo'
  ) {
    store.commit('socialFeed/SINGLE_POST_SHOW', true)
    store.commit(
      'search/SET_TWITTER_SEARCH_DETAILS',
      previousSinglePost.value[0],
    )
    store.commit(
      'socialFeed/BACK_TO_PREVIOUS_POST',
      previousSinglePost.value[0],
    )
    store.commit(
      'home/BACK_TO_PREVIOUS_COMMENT',
      previousTwitterComment.value[0],
    )
  } else {
    store.dispatch('socialFeed/singlePostClose', false)
    setTimeout(() => {
      store.commit('socialFeed/SINGLE_IMAGE_POST_SHOW', true)
      store.commit(
        'search/SET_TWITTER_SEARCH_DETAILS',
        previousSinglePost.value[0],
      )
      store.commit(
        'socialFeed/BACK_TO_PREVIOUS_POST',
        previousSinglePost.value[0],
      )
      store.commit(
        'home/BACK_TO_PREVIOUS_COMMENT',
        previousTwitterComment.value[0],
      )
    }, 450)
  }
}
</script>

<style scoped>
.back-arrow {
  fill: #ffffff;
}
.twitter-icon {
  background-size: cover;
  animation: twitter 6s ease-in-out;
  animation-iteration-count: infinite;
}
@keyframes twitter {
  0% {
    background-image: url('~/assets/img/icon/TwitterIcon/twitter.svg');
  }
  50% {
    background-image: url('~/assets/img/icon/TwitterIcon/X_logo.png');
  }
  100% {
    background-image: url('~/assets/img/icon/TwitterIcon/twitter.svg');
  }
}
.noDataContainer {
  height: calc(100% - 56px);
}
.noData {
  top: 42%;
  left: 50%;
  transform: translate(-50%, -42%);
}
.media-enter-active,
.media-leave-active {
  transition: opacity 0.5s;
}
.media-enter,
.media-leave-to {
  opacity: 0;
}
.verticle-line {
  @apply absolute h-full border-l-2 border-red-700 top-2 left-6;
  border-color: #f1f2f6;
}
.post-profile {
  @apply pr-3 py-3 -mt-3;
  z-index: 20;
}
.post-profile img {
  min-width: 56px;
}
.link {
  color: #1263cc;
}
.posted-image {
  @apply max-h-116;
}
.column-2 {
  flex: 48%;
  /* margin: 2px; */
  overflow: hidden;
  text-align: center;
  clear: both;
  position: relative;
}
.column-3 {
  /* margin: 2px; */
  flex: 32%;
  overflow: hidden;
  clear: both;
  text-align: center;
  position: relative;
}
.hover-border {
  /* border: 1px solid transparent; */
  width: 297px;
  height: 197px;
}
.three-image-show {
  /* width: 296px; */
  height: 396px;
}
.post-overlay {
  align-items: center;
  border-radius: inherit;
  display: flex;
  justify-content: center;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: auto;
  z-index: 150;
  color: #fff;
  transition:
    0.3s cubic-bezier(0.25, 0.8, 0.5, 1),
    z-index 1ms;
}
.sub-overlay {
  opacity: 0.46;
  background-color: rgb(33, 33, 33);
  border-color: rgb(33, 33, 33);
  border-radius: inherit;
  bottom: 0;
  height: 100%;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  transition: inherit;
  width: 100%;
  will-change: opacity;
}
.margin_left {
  margin-left: 0px !important;
}
.margin_top {
  margin-bottom: 2px;
}
@media (max-width: 425px) {
  .hover-border {
    /* border: 1px solid transparent; */
    width: 297px;
    height: 125px;
  }
  .three-image-show {
    /* width: 296px; */
    height: 252px;
  }
}
.whole-card {
  box-shadow: 0px 1px 2px #22283126;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
.card {
  @apply bg-white py-3 w-full z-1;
  /* box-shadow: 0px 1px 2px #22283126; */
  /* border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px; */
  /* // min-width: 400px;
  // max-width: 690px; */
}
.imagePopup-enter-active,
.imagePopup-leave-active {
  transition: opacity 0.5s;
}
.imagePopup-enter,
.imagePopup-leave-to {
  opacity: 0;
}
.reaction {
  @apply my-2 flex justify-around text-gray-1700 mr-16;
}
.reaction img {
  @apply inline-block w-4 mr-4;
  fill: #8e8e8e;
}
.retweet {
  @apply inline-block w-4 mr-4;
  fill: #8e8e8e;
}
.poll_bgColor {
  background: #cfd9de;
  width: var(--bgValue);
}
.poll_bgColor_closed {
  background: #1d9bf094;
  width: var(--bgValue);
}

.fade-in {
  animation: fadeIn 2s ease-in-out;
  -webkit-animation: fadeIn 2s ease-in-out;
  -moz-animation: fadeIn 2s ease-in-out;
  -o-animation: fadeIn 2s ease-in-out;
  -ms-animation: fadeIn 2s ease-in-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@-moz-keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@-webkit-keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@-o-keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@-ms-keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.fade-out {
  animation: fadeOut 2s ease-in-out;
  -webkit-animation: fadeOut 2s ease-in-out;
  -moz-animation: fadeOut 2s ease-in-out;
  -o-animation: fadeOut 2s ease-in-out;
  -ms-animation: fadeOut 2s ease-in-out;
}

@keyframes fadeOut {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@-moz-keyframes fadeOut {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@-webkit-keyframes fadeOut {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@-o-keyframes fadeOut {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@-ms-keyframes fadeOut {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.scroll {
  overflow-y: auto;
  overflow-x: hidden;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-color: var(--scrollColor) #ececec; /* Firefox 64 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background: #ececec;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: var(--scrollColor);
    border-radius: 3px;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: var(--scrollColor);
  }
}
</style>
