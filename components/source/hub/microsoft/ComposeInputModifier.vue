<script setup lang="ts">
import { useStore } from 'vuex'
import { Editor, EditorContent } from '@tiptap/vue-3'
import SharedIconHubEmailsAlignButton from '~/components/shared/icon/hub/emails/AlignButton.vue'
import SharedIconHubEmailsMiddleAlignIcon from '~/components/shared/icon/hub/emails/MiddleAlignIcon.vue'
import SharedIconHubEmailsRightAlignIcon from '~/components/shared/icon/hub/emails/RightAlignIcon.vue'

interface Props {
  editor?: Editor | null
  isPlainTextMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  editor: null,
  isPlainTextMode: false,
})
const store = useStore()
const finalMessageFormat = computed(() => store.state.emails.finalMessageFormat)

const showAlignModal = ref(false)
const fontFamilies = [
  { label: 'Arial', value: 'Arial' },
  { label: 'Sans Serif', value: 'sans-serif' },
  { label: 'Serif', value: 'serif' },
  { label: 'Fixed Width', value: 'monospace' },
  { label: 'Wide', value: 'Arial Black' },
  { label: 'Narrow', value: 'Arial Narrow' },
  { label: 'Comic Sans MS', value: 'Comic Sans MS' },
  { label: 'Garamond', value: 'Garamond' },
  { label: 'Georgia', value: 'Georgia' },
  { label: 'Tahoma', value: 'Tahoma' },
  { label: 'Trebuchet MS', value: 'Trebuchet MS' },
  { label: 'Verdana', value: 'Verdana' },
]

// [
//   { label: 'Arial', value: 'Arial' },
//   { label: 'Aptos', value: 'Aptos' },
//   { label: 'Aptos Display', value: 'Aptos Display' },
//   { label: 'Aptos Mono', value: 'Aptos Mono' },
//   { label: 'Aptos Narrow', value: 'Aptos Narrow' },
//   { label: 'Aptos Serif', value: 'Aptos Serif' },
//   { label: 'Arial Black', value: 'Arial Black' },
//   { label: 'Calibri', value: 'Calibri' },
//   { label: 'Calibri Light', value: 'Calibri Light' },
//   { label: 'Cambria', value: 'Cambria' },
//   { label: 'Candara', value: 'Candara' },
//   { label: 'Century Gothic', value: 'Century Gothic' },
//   { label: 'Comic Sans MS', value: 'Comic Sans MS' },
//   { label: 'Consolas', value: 'Consolas' },
//   { label: 'Constantia', value: 'Constantia' },
//   { label: 'Corbel', value: 'Corbel' },
//   { label: 'Courier New', value: 'Courier New' },
//   { label: 'Franklin Gothic Book', value: 'Franklin Gothic Book' },
//   { label: 'Franklin Gothic Demi', value: 'Franklin Gothic Demi' },
//   { label: 'Franklin Gothic Medium', value: 'Franklin Gothic Medium' },
//   { label: 'Garamond', value: 'Garamond' },
//   { label: 'Georgia', value: 'Georgia' },
//   { label: 'Impact', value: 'Impact' },
//   { label: 'Lucida Console', value: 'Lucida Console' },
//   { label: 'Lucida Handwriting', value: 'Lucida Handwriting' },
//   { label: 'Lucida Sans Unicode', value: 'Lucida Sans Unicode' },
//   { label: 'Palatino Linotype', value: 'Palatino Linotype' },
//   { label: 'Segoe UI', value: 'Segoe UI' },
//   { label: 'Sitka Heading', value: 'Sitka Heading' },
//   { label: 'Sitka Text', value: 'Sitka Text' },
//   { label: 'Tahoma', value: 'Tahoma' },
//   { label: 'Times', value: 'Times' },
//   { label: 'Times New Roman', value: 'Times New Roman' },
//   { label: 'Trebuchet MS', value: 'Trebuchet MS' },
//   { label: 'TW Cen MT', value: 'TW Cen MT' },
//   { label: 'Verdana', value: 'Verdana' },
//   { label: '微软雅黑', value: '微软雅黑' },
//   { label: '黑体', value: '黑体' },
//   { label: '新宋体', value: '新宋体' },
//   { label: '仿宋', value: '仿宋' },
//   { label: '隶书', value: '隶书' },
//   { label: '楷体', value: '楷体' },
//   { label: '微軟正黑體', value: '微軟正黑體' },
//   { label: '新細明體', value: '新細明體' },
//   { label: '標楷體', value: '標楷體' },
//   { label: 'メイリオ', value: 'メイリオ' },
//   { label: 'ＭＳ Ｐゴシック', value: 'ＭＳ Ｐゴシック' },
//   { label: 'ＭＳ Ｐ明朝', value: 'ＭＳ Ｐ明朝' },
//   { label: '游ゴシック', value: '游ゴシック' },
//   { label: '游明朝', value: '游明朝' },
//   { label: '맑은 고딕', value: '맑은 고딕' },
//   { label: '굴림', value: '굴림' },
//   { label: '돋움', value: '돋움' },
//   { label: '바탕', value: '바탕' },
//   { label: '바탕체', value: '바탕체' },
//   { label: '궁서', value: '궁서' },
//   { label: 'Leelawadee UI', value: 'Leelawadee UI' },
//   { label: 'Angsana New', value: 'Angsana New' },
//   { label: 'Cordia New', value: 'Cordia New' },
//   { label: 'DaunPenh', value: 'DaunPenh' },
//   { label: 'Nirmala UI', value: 'Nirmala UI' },
//   { label: 'Gautami', value: 'Gautami' },
//   { label: 'Iskoola Pota', value: 'Iskoola Pota' },
//   { label: 'Kalinga', value: 'Kalinga' },
//   { label: 'Kartika', value: 'Kartika' },
//   { label: 'Latha', value: 'Latha' },
//   { label: 'Mangal', value: 'Mangal' },
//   { label: 'Raavi', value: 'Raavi' },
//   { label: 'Shruti', value: 'Shruti' },
//   { label: 'Tunga', value: 'Tunga' },
//   { label: 'Vrinda', value: 'Vrinda' },
//   { label: 'Nyala', value: 'Nyala' },
//   { label: 'Sylfaen', value: 'Sylfaen' },
// ]
const selectedFont = ref('Arial')
const updatedFontFamily = (label: string, value: string) => {
  selectedFont.value = label
  props.editor?.chain().focus().setFontFamily(value).run()
}
const alignIcons = ref([
  {
    id: 1,
    label: 'LeftAlign',
    value: markRaw(SharedIconHubEmailsAlignButton),
  },
  {
    id: 2,
    label: 'MiddleAlign',
    value: markRaw(SharedIconHubEmailsMiddleAlignIcon),
  },
  {
    id: 3,
    label: 'RightAlign',
    value: markRaw(SharedIconHubEmailsRightAlignIcon),
  },
])
const selectedAlignIcon = ref<string | Component>(
  markRaw(SharedIconHubEmailsAlignButton),
)
const setSelectedIcon = (icon: string | Component, label: string) => {
  selectedAlignIcon.value = icon
  console.log(label, 'selectedAlignIcon')
  if (label === 'LeftAlign') {
    console.log('left', props.editor)
    props.editor?.chain().focus().setTextAlign('left').run()
  } else if (label === 'MiddleAlign') {
    props.editor?.chain().focus().setTextAlign('center').run()
  } else if (label === 'RightAlign') {
    props.editor?.chain().focus().setTextAlign('right').run()
  }
}
const headingArray = ref([
  { id: 1, label: '8', fontSize: 8, selected: false },
  { id: 2, label: '9', fontSize: 9, selected: false },
  { id: 3, label: '10', fontSize: 10, selected: false },
  { id: 4, label: '11', fontSize: 11, selected: false },
  { id: 5, label: '12', fontSize: 12, selected: true }, // default selected
  { id: 6, label: '14', fontSize: 14, selected: false },
  { id: 7, label: '16', fontSize: 16, selected: false },
  { id: 8, label: '18', fontSize: 18, selected: false },
  { id: 9, label: '20', fontSize: 20, selected: false },
  { id: 10, label: '22', fontSize: 22, selected: false },
  { id: 11, label: '24', fontSize: 24, selected: false },
  { id: 12, label: '26', fontSize: 26, selected: false },
  { id: 13, label: '28', fontSize: 28, selected: false },
  { id: 14, label: '36', fontSize: 36, selected: false },
  { id: 15, label: '48', fontSize: 48, selected: false },
  { id: 16, label: '72', fontSize: 72, selected: false },
])
const selectingHeaderSize = ref(12) // default selected font size

const setHeading = (id: any, fontSize: number) => {
  // props.editor?.chain().focus().setHeading({ level: id }).run()
  selectingHeaderSize.value = fontSize
  props.editor?.chain().focus().setFontSize(fontSize).run()
  headingArray.value.forEach((heading) => {
    if (heading.id === id) {
      heading.selected = true
    } else {
      heading.selected = false
    }
  })
}

const setFontFamily = (event: Event) => {
  const target = event.target as HTMLSelectElement
  const fontFamily = target.value
  props.editor?.chain().focus().setFontFamily(fontFamily).run()
}
const selectedColor = ref('#000000')
const applyTextColor = (color: string) => {
  selectedColor.value = color
  props.editor?.chain().focus().setColor(color).run()
}

const applyBackgroundColor = (color: string) => {
  props.editor?.chain().focus().setHighlight({ color: color }).run()
}

const showColorPicker = ref(false)
const showBackgroundColorPicker = ref(false)
const showFontFamilyMenu = ref(false)
const showHeaderModal = ref(false)
const closeAllMenus = computed(() => store.state.emails.closeAllMenus)
watch(closeAllMenus, (data) => {
  if (!data) {
    showAlignModal.value = false
    showColorPicker.value = false
    showBackgroundColorPicker.value = false
    showFontFamilyMenu.value = false
    showHeaderModal.value = false
  }
})
watch(
  finalMessageFormat,
  (data) => {
    if (data) {
      fontFamilies.forEach((font) => {
        if (font.value === data.selectedFontFamily) {
          selectedFont.value = font.label
        }
      })
      selectingHeaderSize.value = data.selectedHeaderSize || 12 // default to 12 if not set
      selectedColor.value = data.colorText || '#000000' // default to black if not set
      console.log('Final message format updated:', data)
    }
  },
  { immediate: true },
)
</script>

<template>
  <div class="flex items-center bg-white py-[5px]">
    <button
      class="px-2 py-1.5 flex space-x-1.5 items-center cursor-pointer rounded-md hover:bg-[#F1F2F6]"
    >
      <SharedIconHubMicrosoftUndoIcon class="w-4 h-4 min-w-4 min-h-4" />
      <SharedIconHubMicrosoftArrowIcon class="w-2 h-1.5" />
    </button>
    <button
      class="px-[11px] py-1.5 flex justify-center cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :class="isPlainTextMode ? 'opacity-50 cursor-not-allowed' : ''"
      :disabled="isPlainTextMode"
    >
      <SharedIconHubMicrosoftClearIcon class="w-3.5 h-4 min-w-3.5 min-h-4" />
    </button>
    <div class="mx-2 w-[1px] h-7 bg-[#707070] bg-opacity-25"></div>
    <div
      class="flex justify-between border border-[#707070] px-2 rounded text-[#525252] text-sm"
      :class="isPlainTextMode ? 'opacity-50 cursor-not-allowed' : ''"
      :disabled="isPlainTextMode"
    >
      <div
        class="relative cursor-pointer flex-grow flex justify-between items-center min-w-[126px] pr-2 py-1"
        :class="isPlainTextMode ? 'opacity-50 cursor-not-allowed' : ''"
        @click.stop="
          (showFontFamilyMenu = !showFontFamilyMenu),
            store.commit('emails/SET_CLOSE_ALL_MENUS', true),
            (showColorPicker = false),
            (showAlignModal = false),
            (showHeaderModal = false),
            (showBackgroundColorPicker = false)
        "
      >
        <p>{{ selectedFont }}</p>
        <SharedIconHubMicrosoftArrowIcon class="w-2 h-1.5" />
        <SourceHubEmailsFontFamilyMenu
          v-if="showFontFamilyMenu && closeAllMenus && !isPlainTextMode"
          class="absolute !top-[30px] !left-[-8px] !ml-0 !z-1 !min-h-[294px] !h-[294px] custom-scroll overflow-y-auto"
          :options="fontFamilies"
          :updatedFontFamily="updatedFontFamily"
          :label="selectedFont"
        />
      </div>
      <div
        class="relative cursor-pointer flex justify-center !space-x-2 items-center pl-2 py-1 border-l border-[#707070]"
        :class="isPlainTextMode ? 'opacity-50 cursor-not-allowed' : ''"
        @click.stop="
          (showHeaderModal = !showHeaderModal),
            store.commit('emails/SET_CLOSE_ALL_MENUS', true),
            (showAlignModal = false),
            (showColorPicker = false),
            (showFontFamilyMenu = false),
            (showBackgroundColorPicker = false)
        "
      >
        <p>{{ selectingHeaderSize }}</p>
        <SharedIconHubMicrosoftArrowIcon class="w-2 h-1.5" />
        <SourceHubMicrosoftModalFontSizeMenu
          v-if="showHeaderModal && closeAllMenus && !isPlainTextMode"
          class="absolute !w-[60px] !min-w-[60px] !top-[30px] !left-[-8px] !ml-0 !z-1 !min-h-[294px] !h-[294px] custom-scroll overflow-y-auto"
          @click.stop=""
          :headingArray="headingArray"
          :setHeading="setHeading"
        />
      </div>
    </div>
    <button
      class="ml-3 px-2 py-1.5 flex justify-center cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :disabled="
        !editor.can().chain().focus().toggleBold().run() || isPlainTextMode
      "
      :class="{
        'is-active': editor.isActive('bold'),
        'opacity-50 cursor-not-allowed': isPlainTextMode,
      }"
      @click="
        editor.chain().focus().toggleBold().run(),
          ((showColorPicker = false),
          (showAlignModal = false),
          (showHeaderModal = false),
          (showFontFamilyMenu = false),
          (showBackgroundColorPicker = false))
      "
    >
      <SharedIconHubMicrosoftBoldIcon class="w-3 h-4 min-w-3 min-h-4" />
    </button>
    <button
      class="ml-3 px-2 py-1.5 flex justify-center cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :disabled="
        !editor.can().chain().focus().toggleItalic().run() || isPlainTextMode
      "
      :class="{
        'is-active': editor.isActive('italic'),
        'opacity-50 cursor-not-allowed': isPlainTextMode,
      }"
      @click="
        editor.chain().focus().toggleItalic().run(),
          ((showColorPicker = false),
          (showAlignModal = false),
          (showHeaderModal = false),
          (showFontFamilyMenu = false),
          (showBackgroundColorPicker = false))
      "
    >
      <SharedIconHubMicrosoftItalicIcon class="w-3.5 h-4 min-w-3.5 min-h-4" />
    </button>
    <button
      class="ml-3 px-2 py-1.5 flex justify-center cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :disabled="
        !editor.can().chain().focus().toggleUnderline().run() || isPlainTextMode
      "
      :class="{
        'is-active': editor.isActive('underline'),
        'opacity-50 cursor-not-allowed': isPlainTextMode,
      }"
      @click="
        editor.chain().focus().toggleUnderline().run(),
          ((showColorPicker = false),
          (showAlignModal = false),
          (showHeaderModal = false),
          (showFontFamilyMenu = false),
          (showBackgroundColorPicker = false))
      "
    >
      <SharedIconHubMicrosoftUnderlineIcon class="w-3 h-4 min-w-3 min-h-4" />
    </button>
    <button
      class="ml-3 px-2 py-1.5 flex justify-center cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :disabled="
        !editor.can().chain().focus().toggleStrike().run() || isPlainTextMode
      "
      :class="{
        'is-active': editor.isActive('strike'),
        'opacity-50 cursor-not-allowed': isPlainTextMode,
      }"
      @click="
        editor.chain().focus().toggleStrike().run(),
          ((showColorPicker = false),
          (showAlignModal = false),
          (showHeaderModal = false),
          (showFontFamilyMenu = false),
          (showBackgroundColorPicker = false))
      "
    >
      <SharedIconHubMicrosoftStrickIcon class="w-5 h-4 min-w-5 min-h-4" />
    </button>
    <div
      class="relative ml-3 px-2 py-1.5 flex justify-center items-center space-x-2 cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :class="[
        showBackgroundColorPicker ? 'bg-[#F1F2F6]' : '',
        isPlainTextMode ? 'opacity-50 cursor-not-allowed' : '',
      ]"
      @click.stop="
        (showBackgroundColorPicker = !showBackgroundColorPicker),
          store.commit('emails/SET_CLOSE_ALL_MENUS', true),
          (showAlignModal = false),
          (showColorPicker = false),
          (showHeaderModal = false),
          (showFontFamilyMenu = false)
      "
    >
      <SharedIconHubMicrosoftWriteIcon class="w-3.5 h-4 min-w-3.5 min-h-4" />
      <SharedIconHubMicrosoftArrowIcon class="w-2 h-1.5" />
      <div
        v-show="showBackgroundColorPicker && closeAllMenus && !isPlainTextMode"
        class="z-1 max-w-[220px] !ml-0 min-w-[220px] max-h-[252px] p-4 pt-3 color-box absolute top-[28px] min-[1490px]:left-0 min-[1310px]:-left-[180px] right-0"
        @click.stop=""
      >
        <div class="grid grid-cols-[1fr] gap-3">
          <div class="flex flex-col space-y-3.5">
            <p class="text-[#525252]">Background color</p>
            <SourceColorPicker
              :applyColor="applyBackgroundColor"
              title="Background color"
              selectedColorCode="#FFFFFF"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="relative ml-3 px-2 py-1.5 flex justify-center items-center space-x-2 cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :class="[
        showColorPicker ? 'bg-[#F1F2F6]' : '',
        isPlainTextMode ? 'opacity-50 cursor-not-allowed' : '',
      ]"
      @click.stop="
        (showColorPicker = !showColorPicker),
          store.commit('emails/SET_CLOSE_ALL_MENUS', true),
          (showAlignModal = false),
          (showBackgroundColorPicker = false),
          (showHeaderModal = false),
          (showFontFamilyMenu = false)
      "
    >
      <SharedIconHubMicrosoftColorIcon class="w-3.5 h-4 min-w-3.5 min-h-4" />
      <SharedIconHubMicrosoftArrowIcon class="w-2 h-1.5" />
      <div
        v-show="showColorPicker && closeAllMenus && !isPlainTextMode"
        class="z-1 max-w-[220px] !ml-0 min-w-[220px] max-h-[252px] p-4 pt-3 color-box absolute top-[28px] min-[1490px]:left-0 min-[1310px]:-left-[180px] right-0"
        @click.stop=""
      >
        <div class="grid grid-cols-[1fr] gap-3">
          <div class="flex flex-col space-y-3.5">
            <p class="text-[#525252]">Text color</p>
            <SourceColorPicker
              :applyColor="applyTextColor"
              title="Text color"
              :selectedColorCode="selectedColor"
            />
          </div>
        </div>
      </div>
    </div>
    <button
      class="ml-3 px-2 py-1.5 flex justify-center cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :class="isPlainTextMode ? 'opacity-50 cursor-not-allowed' : ''"
      :disabled="isPlainTextMode"
      @click="
        editor.chain().focus().unsetAllMarks().run(),
          editor.chain().focus().clearNodes().run(),
          ((showColorPicker = false),
          (showBackgroundColorPicker = false),
          (showAlignModal = false),
          (showHeaderModal = false),
          (showFontFamilyMenu = false),
          applyTextColor('#000000'),
          applyBackgroundColor('#ffffff'))
      "
    >
      <SharedIconHubMicrosoftCrossStrickIcon class="w-4 h-4 min-w-4 min-h-4" />
    </button>
    <button
      class="ml-3 px-2 py-1.5 flex justify-center cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :class="{
        'is-active': editor.isActive('bulletList'),
        'opacity-50 cursor-not-allowed': isPlainTextMode,
      }"
      @click="editor.chain().focus().toggleBulletList().run()"
      :disabled="isPlainTextMode"
    >
      <SharedIconHubMicrosoftUnderorderListIcon
        class="w-[18px] h-4 min-w-[18px] min-h-4"
      />
    </button>
    <button
      class="ml-3 px-2 py-1.5 flex justify-center cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :class="{
        'is-active': editor.isActive('orderedList'),
        'opacity-50 cursor-not-allowed': isPlainTextMode,
      }"
      :disabled="isPlainTextMode"
      @click="editor.chain().focus().toggleOrderedList().run()"
    >
      <SharedIconHubMicrosoftOrderListIcon
        class="w-3.5 h-4 min-w-3.5 min-h-4"
      />
    </button>
    <button
      class="ml-3 px-2 py-1.5 flex justify-center cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :class="isPlainTextMode ? 'opacity-50 cursor-not-allowed' : ''"
      :disabled="isPlainTextMode"
      @click="editor.chain().focus().decreaseIndent().run()"
    >
      <SharedIconHubMicrosoftShiftLeftIcon class="w-4 h-4 min-w-4 min-h-4" />
    </button>
    <button
      class="ml-3 px-2 py-1.5 flex justify-center cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :class="isPlainTextMode ? 'opacity-50 cursor-not-allowed' : ''"
      :disabled="isPlainTextMode"
      @click="editor.chain().focus().increaseIndent().run()"
    >
      <SharedIconHubMicrosoftShiftRightIcon class="w-4 h-4 min-w-4 min-h-4" />
    </button>
    <div
      class="relative ml-3 px-2 py-1.5 flex justify-center items-center space-x-2 cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :class="[
        showAlignModal ? 'bg-[#F1F2F6]' : '',
        isPlainTextMode ? 'opacity-50 cursor-not-allowed' : '',
      ]"
      @click.stop="
        (showAlignModal = !showAlignModal),
          store.commit('emails/SET_CLOSE_ALL_MENUS', true)
      "
    >
      <component :is="selectedAlignIcon" />
      <!-- <SharedIconHubMicrosoftAlignIcon class="w-4 h-4 min-w-4 min-h-4" /> -->
      <SharedIconHubMicrosoftArrowIcon class="w-2 h-1.5" />
      <SourceHubEmailsTextAlignModal
        v-if="showAlignModal && closeAllMenus && !isPlainTextMode"
        class="absolute !top-[28px] !left-0 !ml-1 !z-1"
        @click.stop=""
        :icons="alignIcons"
        :setSelectedIcon="setSelectedIcon"
        :editor="editor"
      />
    </div>
     
    <div
      class="ml-3 px-2 py-1.5 flex justify-center items-center space-x-2 cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :class="isPlainTextMode ? 'opacity-50 cursor-not-allowed' : ''"
    >
      <SharedIconHubMicrosoftUpDownAlignIcon class="w-5 h-4 min-w-5 min-h-4" />
      <SharedIconHubMicrosoftArrowIcon class="w-2 h-1.5" />
    </div>
    <button
      class="ml-3 px-2 py-1.5 flex justify-center cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :class="isPlainTextMode ? 'opacity-50 cursor-not-allowed' : ''"
      :disabled="isPlainTextMode"
      @click="editor.chain().focus().setTextAlign('left').run()"
    >
      <SharedIconHubMicrosoftLeftAlignIcon class="w-4 h-4 min-w-4 min-h-4" />
    </button>
    <button
      class="ml-3 px-2 py-1.5 flex justify-center cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :class="isPlainTextMode ? 'opacity-50 cursor-not-allowed' : ''"
      :disabled="isPlainTextMode"
      @click="editor.chain().focus().setTextAlign('left').run()"
    >
      <SharedIconHubMicrosoftRightAlignIcon class="w-4 h-4 min-w-4 min-h-4" />
    </button>
    <button
      class="ml-3 px-2 py-1.5 flex justify-center space-x-2 cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :class="{
        'is-active': editor.isActive('blockquote'),
        'opacity-50 cursor-not-allowed': isPlainTextMode,
      }"
      :disabled="isPlainTextMode"
      @click="editor.chain().focus().toggleBlockquote().run()"
    >
      <SharedIconHubMicrosoftCotationIcon
        class="w-4 h-[11px] min-w-4 min-h-[11px]"
      />
    </button>
    <div class="mx-2 w-[1px] h-7 bg-[#707070] bg-opacity-25"></div>
    <div
      class="ml-3 px-2 py-1.5 flex justify-center cursor-pointer rounded-md hover:bg-[#F1F2F6]"
    >
      <SharedIconHubEmailsThreeDotMenuIcon
        class="transform rotate-90"
        color="#525252"
      />
    </div>
    <div class="mx-2 w-[1px] h-7 bg-[#707070] bg-opacity-25"></div>
    <div
      class="ml-3 px-2 py-1.5 flex justify-center items-center space-x-2 cursor-pointer rounded-md hover:bg-[#F1F2F6]"
    >
      <SharedIconHubMicrosoftAttachmentIcon
        class="w-2.5 h-4 min-w-2.5 min-h-4"
      />
      <SharedIconHubMicrosoftArrowIcon class="w-2 h-1.5" />
    </div>
    <button
      class="ml-3 px-2 py-1.5 flex justify-center items-center space-x-2 cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :class="isPlainTextMode ? 'opacity-50 cursor-not-allowed' : ''"
      :disabled="isPlainTextMode"
    >
      <SharedIconHubMicrosoftLinkIcon class="w-4 h-2 min-w-4 min-h-2" />
      <SharedIconHubMicrosoftArrowIcon class="w-2 h-1.5" />
    </button>
    <div
      class="ml-3 px-2 py-1.5 flex justify-center items-center space-x-2 cursor-pointer rounded-md hover:bg-[#F1F2F6]"
    >
      <SharedIconHubMicrosoftNoteIcon class="w-4 h-4 min-w-4 min-h-4" />
      <SharedIconHubMicrosoftArrowIcon class="w-2 h-1.5" />
    </div>
    <div class="mx-2 w-[1px] h-7 bg-[#707070] bg-opacity-25"></div>
    <div
      class="ml-3 px-2 py-1.5 flex justify-center space-x-2 cursor-pointer rounded-md hover:bg-[#F1F2F6]"
    >
      <SharedIconHubMicrosoftMenuIcon class="w-4 h-4 min-w-4 min-h-4" />
    </div>
    <div class="mx-2 w-[1px] h-7 bg-[#707070] bg-opacity-25"></div>
    <button
      class="ml-3 px-2 py-1.5 flex justify-center space-x-2 cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      :class="isPlainTextMode ? 'opacity-50 cursor-not-allowed' : ''"
      :disabled="isPlainTextMode"
    >
      <SharedIconHubMicrosoftVoiceIcon class="w-3 h-4 min-w-3 min-h-4" />
    </button>
    <div class="mx-2 w-[1px] h-7 bg-[#707070] bg-opacity-25"></div>
    <div
      class="ml-3 px-2 py-1.5 flex justify-center items-center space-x-2 cursor-pointer rounded-md hover:bg-[#F1F2F6]"
    >
      <SharedIconHubMicrosoftQueryIcon class="w-3 h-4 min-w-3 min-h-4" />
      <SharedIconHubMicrosoftArrowIcon class="w-2 h-1.5" />
    </div>
    <div class="mx-2 w-[1px] h-7 bg-[#707070] bg-opacity-25"></div>
    <div
      class="ml-3 px-2 py-1.5 flex justify-center space-x-2 cursor-pointer rounded-md hover:bg-[#F1F2F6]"
    >
      <SharedIconHubMicrosoftPrintingIcon class="w-4 h-3.5 min-w-4 min-h-3.5" />
    </div>
  </div>
</template>

<style scoped>
.is-active {
  background-color: #f1f2f6;
}
.color-box {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
</style>
