<script setup lang="ts">
import { useStore } from 'vuex'

import Inbox from '~/components/shared/icon/hub/microsoft/sidebar/InboxIcon.vue'
import JunkEmail from '~/components/shared/icon/hub/microsoft/sidebar/JunkEmailIcon.vue'
import Drafts from '~/components/shared/icon/hub/microsoft/sidebar/DraftsIcon.vue'
import Sent from '~/components/shared/icon/hub/microsoft/sidebar/SentItemsIcon.vue'
import Delete from '~/components/shared/icon/hub/microsoft/sidebar/DeleteItemsIcon.vue'
import Archive from '~/components/shared/icon/hub/microsoft/sidebar/ArchiveIcon.vue'
import Notes from '~/components/shared/icon/hub/microsoft/sidebar/NoteIcon.vue'

const store = useStore()
const route = useRoute()

const menuItems = ref([
  {
    id: 1,
    image: markRaw(Inbox),
    name: 'Inbox',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/inbox`,
    unread: 1,
  },
  {
    id: 2,
    image: markRaw(JunkEmail),
    name: 'Junk Email',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/junk-email`,
    unread: 0,
  },
  {
    id: 3,
    image: markRaw(Drafts),
    name: 'Drafts',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/drafts`,
    unread: 0,
  },
  {
    id: 4,
    image: markRaw(Sent),
    name: 'Sent Items',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/sent-items`,
    unread: 0,
  },
  {
    id: 5,
    image: markRaw(Delete),
    name: 'Delete Items',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/delete-items`,
    unread: 0,
  },
  {
    id: 6,
    image: markRaw(Archive),
    name: 'Archive',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/archive`,
    unread: 0,
  },
  {
    id: 7,
    image: markRaw(Notes),
    name: 'Notes',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/notes`,
    unread: 0,
  },
])
onMounted(async () => {
  // setTimeout(async () => {
  //   await nextTick
  //   const NuxtLink = document.querySelector('.nuxt-link')
  //   if (NuxtLink) {
  //     NuxtLink.click()
  //   }
  // }, 100)
})
</script>

<template>
  <div class="flex flex-col p-4">
    <NuxtLink
      :to="menuItem.path"
      class="nuxt-link flex justify-between px-4 py-2 items-center rounded-full"
      v-for="menuItem in menuItems"
      :key="menuItem.id"
      @click.native="store.commit('emails/RESET_SHOW_COMPOSE_SECTION')"
    >
      <div
        class="menu-container flex !space-x-[18px] items-center whitespace-nowrap"
      >
        <component v-if="menuItem.image" :is="menuItem.image"></component>
        <p>{{ menuItem.name }}</p>
      </div>
      <p v-if="menuItem.unread > 0" class="text-sm">{{ menuItem.unread }}</p>
    </NuxtLink>
  </div>
</template>

<style lang="scss" scoped>
.router-link-exact-active,
.router-link-active {
  @apply bg-blue-200 text-white;
}
</style>
