<script setup lang="ts">
import { Editor } from '@tiptap/vue-3'
import { useStore } from 'vuex'
import type {
  DefaultSignatures,
  LanguageAndTime,
} from '~/types/hubEmailsSettings'

interface Props {
  editor?: Editor | null
}

const props = withDefaults(defineProps<Props>(), {
  editor: null,
})

const store = useStore()
const defaultSignature = computed<DefaultSignatures>(
  () => store.getters['outlook/getDefaultSignature'],
)
const languageAndTime = computed<LanguageAndTime>(
  () => store.state.outlook.languageAndTime,
)

const subjectText = ref<string>('')

function updateSignature(html: string) {
  const { state, view } = props.editor!
  const tr = state.tr

  // Remove old signature node(s)
  state.doc.descendants((node, pos) => {
    if (node.type.name === 'signature') {
      tr.delete(pos, pos + node.nodeSize)
    }
  })

  // Add new signature node at the end
  props.editor?.commands.focus('end')
  // 4. Insert new signature node
  tr.insert(
    tr.doc.content.size,
    state.schema.nodes.signature.create({ content: html, dash: false }),
  )
  // 5. Apply changes
  view.dispatch(tr)
}

onMounted(async () => {
  await nextTick()
  if (defaultSignature.value?.newMessageSignature) {
    updateSignature(defaultSignature.value.newMessageSignature.value)
  }
})
onUnmounted(() => {
  if (props.editor) {
    props.editor.commands.clearContent()
  }
})
</script>

<template>
  <div
    class="w-full h-[calc(100%-56px)] overflow-hidden flex flex-col px-2 pt-2 bg-[#F1F2F6]"
  >
    <div
      class="py-2 px-4 bg-white rounded-lg flex-grow h-[calc(100%-86px)] custom-scroll"
    >
      <div class="flex justify-between items-center">
        <button
          class="flex justify-center items-center rounded h-[33px] bg-[#0F6CBC] text-white text-sm"
        >
          <div
            class="flex justify-center items-center px-4 border-r border-white !space-x-2"
          >
            <SharedIconHubMicrosoftSidebarSentItemsIcon color="#fff" />
            <p>Send</p>
          </div>
          <div class="flex justify-center items-center px-2">
            <SharedIconHubMicrosoftArrowIcon class="w-2 h-1" color="#fff" />
          </div>
        </button>
        <SharedIconHubMicrosoftSidebarDeleteItemsIcon
          class="cursor-pointer"
          @click.stop="store.commit('emails/RESET_SHOW_COMPOSE_SECTION')"
        />
      </div>
      <div class="mt-6 flex flex-col">
        <div class="flex space-x-4">
          <div
            class="w-13 h-8 flex justify-center items-center border-[0.4px] border-[#707070] text-[#333333] cursor-pointer"
            @click.stop="store.commit('emails/SET_SHOW_ADD_RECIPIENTS', true)"
          >
            To
          </div>
          <div
            class="flex-grow flex items-center justify-between space-x-6 pb-[7px] border-b border-[#707070] text-[#333333]"
          >
            <input
              class="flex-grow text-sm border-none outline-none"
              type="text"
            />
            <div class="flex !space-x-4 text-sm">
              <p>Cc</p>
              <p>Bcc</p>
            </div>
          </div>
        </div>
        <div
          class="flex justify-between items-center mt-[22px] border-b border-[#707070]"
        >
          <input
            v-model="subjectText"
            class="flex-grow border-none outline-none placeholder:text-[#707070] text-[#333333]"
            type="text"
            placeholder="Add a Subject"
            maxlength="90"
          />
          <p class="text-[#707070] text-xs">
            Draft saved at
            {{
              zonedDateTime(
                new Date().toISOString(),
                languageAndTime.timeZone,
                languageAndTime.timeFormat,
              )
            }}
          </p>
        </div>
        <div class="mt-[30px] flex-grow">
          <SourceHubMicrosoftTextInput :editor="editor" />
          <!-- <textarea
            class="w-full h-auto no-scrollbar resize-none textarea-style outline-none border-none bg-white text-[#333333] text-lg"
            name=""
            id=""
          ></textarea> -->
        </div>
      </div>
    </div>
    <div class="flex space-x-2 mt-2">
      <button
        class="px-4 py-[7px] w-[200px] rounded-t flex whitespace-nowrap items-center justify-center border border-[#707070] text-[#707070] font-semibold"
      >
        Select an item to read
      </button>
      <button
        :data-title="subjectText ? subjectText : '(No subject)'"
        class="px-4 py-[7px] bg-white w-[200px] rounded-t flex items-center justify-center !space-x-2 border border-[#707070] text-[#0F6BBE] font-semibold"
      >
        <SharedIconHubMicrosoftEditIcon />
        <p class="line-clamp-1 w-[calc(100%-40px)] text-left">
          {{ subjectText ? subjectText : '(No subject)' }}
        </p>
      </button>
    </div>
  </div>
</template>

<style scoped>
.textarea-style {
  field-sizing: content;
  width: 100%;
}
[data-title]:after {
  color: #0f6cbc;
  /* right: 100%; */
  z-index: 99999999;
}
[data-title]:after {
  content: attr(data-title);
  position: absolute;
  top: -30px;
  left: 0px;
  /* max-width: 500px; */
  width: 300px;
  height: fit-content;
  word-break: break-all;
  padding: 2px;
  white-space: wrap;
  border-radius: 5px;
  box-shadow: 0px 0px 4px #666;
  background-color: white;
  background-image: -o-linear-gradient(top, #dbc9c9, #958f8f);
  opacity: 0;
  z-index: 100000000000000000000000;
  visibility: hidden;
  font-size: 12px;
}
[data-title]:hover:after {
  opacity: 1;
  transition: all 0.1s ease 0.5s;
  visibility: visible;
}
</style>
