<script setup lang="ts">
import { Editor } from '@tiptap/vue-3'
import { useStore } from 'vuex'
import type { DefaultSignatures } from '~/types/hubEmailsSettings'

const store = useStore()

interface Props {
  setShowReplyBox?: (payload: MouseEvent) => void
  deleteSpecificComposeItem?: Function
  itemId?: number
  toPersonName?: string
  editor?: Editor | null
}

const props = withDefaults(defineProps<Props>(), {
  setShowReplyBox: () => {},
  deleteSpecificComposeItem: (id: number) => {},
  itemId: 0,
  toPersonName: '',
  editor: null,
})
const accountItem = computed(() => {
  return store.state.social.accountItem
})
const showComposeSection = computed(() => store.state.emails.showComposeSection)

const defaultSignature = computed<DefaultSignatures>(
  () => store.getters['outlook/getDefaultSignature'],
)

function updateSignature(html: string) {
  const { state, view } = props.editor!
  const tr = state.tr

  // Remove old signature node(s)
  state.doc.descendants((node, pos) => {
    if (node.type.name === 'signature') {
      tr.delete(pos, pos + node.nodeSize)
    }
  })

  // Add new signature node at the end
  props.editor?.commands.focus('end')
  // 4. Insert new signature node
  tr.insert(
    tr.doc.content.size,
    state.schema.nodes.signature.create({ content: html, dash: false }),
  )
  // 5. Apply changes
  view.dispatch(tr)
}

onMounted(async () => {
  await nextTick()
  if (defaultSignature.value?.replyForwardMessageSignature) {
    updateSignature(defaultSignature.value.replyForwardMessageSignature.value)
  }
})

onUnmounted(() => {
  if (props.editor) {
    props.editor.commands.clearContent()
  }
})
</script>

<template>
  <div class="flex flex-col min-h-[264px]">
    <nav class="flex space-x-2 items-center">
      <div class="grid grid-cols-[40px,1fr] gap-x-4 items-center">
        <SourceAccountLogo :account-profile-pic="accountItem.profilePic" />
        <div v-if="!showComposeSection">
          <p class="text-[#333333] text-sm">
            To:
            <span v-if="toPersonName">{{ toPersonName }}</span>
          </p>
        </div>
      </div>
      <div v-if="showComposeSection" class="w-full text-[#707070]">
        <div class="w-full py-2 pr-4 border-b border-[#F1F2F6]">
          <input
            class="w-full outline-none border-none"
            type="text"
            placeholder="Recipients"
          />
        </div>
        <div class="w-full py-2 pr-4 mt-2 border-b border-[#F1F2F6]">
          <input
            class="w-full outline-none border-none"
            type="text"
            placeholder="Subject"
          />
        </div>
      </div>
    </nav>
    <div class="mt-3 flex-grow">
      <!-- <textarea
        class="w-full h-auto no-scrollbar resize-none textarea-style outline-none border-none bg-white text-[#333333]"
        name=""
        id=""
      ></textarea> -->
      <SourceHubMicrosoftTextInput :editor="editor" />
      <!-- <editor-content :editor="editor" @click.stop="" /> -->
    </div>
    <div
      v-if="!showComposeSection"
      class="w-[30px] h-3.5 bg-[#F1F2F6] rounded-[5px] flex justify-center items-center"
    >
      <SharedIconHubEmailsThreeDotMenuIcon
        class="transform rotate-90"
        color="#525252"
      />
    </div>
    <div class="flex space-x-2 mt-4">
      <button
        class="flex justify-center items-center rounded h-[33px] bg-[#0F6CBC] text-white text-sm"
      >
        <div
          class="flex justify-center items-center px-4 border-r border-white !space-x-2"
        >
          <SharedIconHubMicrosoftSidebarSentItemsIcon color="#fff" />
          <p>Send</p>
        </div>
        <div class="flex justify-center items-center px-2">
          <SharedIconHubMicrosoftArrowIcon class="w-2 h-1" color="#fff" />
        </div>
      </button>
      <button
        class="flex items-center !space-x-2 border border-[#525252] h-[33px] px-4 py-2 rounded"
        @click="
          store.commit('emails/SET_SHOW_REPLY_BOX', false),
            deleteSpecificComposeItem(itemId)
        "
      >
        <SharedIconHubMicrosoftSidebarDeleteItemsIcon />
        <p class="text-[#333333] text-sm">Discard</p>
      </button>
    </div>
    <!-- <div class="mt-4">
      <SourceHubEmailsInputModifier
        :setShowReplyBox="setShowReplyBox"
        :deleteSpecificComposeItem="deleteSpecificComposeItem"
        :itemId="itemId"
      />
    </div> -->
  </div>
</template>

<style lang="scss" scoped>
// .reply-box {
//   background: #ffffff 0% 0% no-repeat padding-box;
//   box-shadow: 1px 2px 8px #00000029;
//   border-radius: 8px;
// }
.textarea-style {
  field-sizing: content;
  width: 100%;
}
</style>
