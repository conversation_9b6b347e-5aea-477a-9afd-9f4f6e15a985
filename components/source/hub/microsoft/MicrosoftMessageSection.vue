<script setup lang="ts">
// import juice from 'juice'
// import inlineCss from 'inline-css'
import Color from '@tiptap/extension-color'
import FontFamily from '@tiptap/extension-font-family'
import Highlight from '@tiptap/extension-highlight'
import Image from '@tiptap/extension-image'
import Link from '@tiptap/extension-link'
import TextAlign from '@tiptap/extension-text-align'
import TextStyle from '@tiptap/extension-text-style'
import Underline from '@tiptap/extension-underline'
import StarterKit from '@tiptap/starter-kit'
import { Editor } from '@tiptap/vue-3'
import { useStore } from 'vuex'
import ArchiveIcon from '~/components/shared/icon/hub/emails/ArchiveIcon.vue'
import ForwardIcon from '~/components/shared/icon/hub/emails/Forward.vue'
import ReplyIcon from '~/components/shared/icon/hub/emails/Reply.vue'
import BlockIcon from '~/components/shared/icon/hub/microsoft/BlockIcon.vue'
import IgnoreIcon from '~/components/shared/icon/hub/microsoft/IgnoreIcon.vue'
import ReplyAllIcon from '~/components/shared/icon/hub/microsoft/ReplyAllIcon.vue'
import ReportIcon from '~/components/shared/icon/hub/microsoft/ReportIcon.vue'
import DeleteIcon from '~/components/shared/icon/hub/microsoft/sidebar/DeleteItemsIcon.vue'
import { FontSize } from '~/composables/FontSize'
import { Signature } from '~/composables/Signature'
// import { RawHTML } from '~/composables/TextStyleWithExtras'
import { CustomIndent } from '~/composables/tiptap-extension-indent'
import Strike from '@tiptap/extension-strike'
import { DOMParser as ProseMirrorDOMParser } from 'prosemirror-model'
// import { DOMPurify } from 'vue-dompurify-html'
// import DOMPurify from 'dompurify'
import type { LanguageAndTime } from '~/types/hubEmailsSettings'

const store = useStore()
const route = useRoute()
const router = useRouter()

interface EmailMessage {
  id: number
  subject: string
  snippet: string
  description: string
  createdAt: string
  read: boolean
  checked: boolean
  favourite: boolean
}

const emailMessages = computed(() => store.state.emails.emailMessages)
const showComposeSection = computed(() => store.state.emails.showComposeSection)
const showReplyBox = computed(() => store.state.emails.showReplyBox)
const checkedAll = ref<boolean>(false)
const showMinus = ref<boolean>(false)
const toggleCheckedAll = ($event: any) => {
  if (!showMinus.value) {
    checkedAll.value = !checkedAll.value
    store.commit('emails/SET_CHECKED_ALL_EMAIL_MESSAGES')
  } else {
    showMinus.value = false
    checkedAll.value = false
    store.commit('emails/SET_UNCHECKED_ALL_EMAIL_MESSAGES')
  }
}
const toggleSingleCheckBox = () => {
  showMinus.value = emailMessages.value.some(
    (emailMessage: EmailMessage) => emailMessage.checked,
  )
  if (showMinus.value) {
    checkedAll.value = true
  }
  const allChecked = emailMessages.value.every(
    (emailMessage: EmailMessage) => emailMessage.checked,
  )
  if (allChecked) {
    showMinus.value = false
    checkedAll.value = true
  }
  const allUnchecked = emailMessages.value.every(
    (emailMessage: EmailMessage) => !emailMessage.checked,
  )
  if (allUnchecked) {
    showMinus.value = false
    checkedAll.value = false
  }
}
const toggleDeleteMenu = ref<boolean>(false)
const deleteMenuOptions = ref([
  {
    id: 1,
    title: 'Delete',
    image: markRaw(DeleteIcon),
    border: false,
  },
  {
    id: 2,
    title: 'Ignore',
    image: markRaw(IgnoreIcon),
    border: false,
  },
])
const toggleMoveMenu = ref<boolean>(false)
const moveMenuOptions = ref([
  {
    id: 1,
    title: 'Delete Items',
    image: markRaw(DeleteIcon),
    border: false,
  },
  {
    id: 2,
    title: 'Archive',
    image: markRaw(ArchiveIcon),
    border: true,
  },
  {
    id: 3,
    title: 'Create new folder',
    image: '',
    emptyDiv: true,
    button: 'Save',
    border: true,
  },
  {
    id: 4,
    title: 'Move to Focused inbox',
    image: '',
    emptyDiv: true,
    border: false,
  },
  {
    id: 5,
    title: 'Always move to Focused inbox',
    image: '',
    emptyDiv: true,
    border: false,
  },
  {
    id: 6,
    title: 'Move to a different folder...',
    image: '',
    emptyDiv: true,
    border: false,
  },
])
const toggleReportMenu = ref<boolean>(false)
const reportMenuOptions = ref([
  {
    id: 1,
    title: 'Report phishing',
    image: markRaw(ReportIcon),
    border: false,
  },
  {
    id: 2,
    title: 'Report junk',
    image: markRaw(BlockIcon),
    border: false,
  },
])
const toggleReplyMenu = ref<boolean>(false)
const replyMenuOptions = ref([
  {
    id: 1,
    title: 'Reply',
    image: markRaw(ReplyIcon),
    border: false,
  },
  {
    id: 2,
    title: 'Reply all',
    image: markRaw(ReplyAllIcon),
    border: false,
  },
  {
    id: 3,
    title: 'Forward',
    image: markRaw(ForwardIcon),
    border: false,
  },
])
const toggleFlagMenu = ref<boolean>(false)
const flagMenuOptions = ref([
  {
    id: 1,
    title: 'Today',
    image: '',
    border: false,
  },
  {
    id: 2,
    title: 'Tomorrow',
    image: '',
    border: false,
  },
  {
    id: 3,
    title: 'This week',
    image: '',
    border: false,
  },
  {
    id: 4,
    title: 'Next week',
    image: '',
    border: false,
  },
  {
    id: 5,
    title: 'No date',
    image: '',
    border: true,
  },
  {
    id: 6,
    title: 'Mark complete',
    image: '',
    border: false,
  },
  {
    id: 7,
    title: 'Clear flag',
    image: '',
    border: false,
    opacity: true,
  },
])
const toggleMarkAllReadMenu = ref<boolean>(false)
const toggleReadUnreadButton = ref<boolean>(false)
const setReadUnreadToggleButton = () => {
  toggleReadUnreadButton.value = !toggleReadUnreadButton.value
  if (toggleReadUnreadButton.value) {
    store.commit('emails/READ_ALL_MESSAGE')
  } else {
    store.commit('emails/UNREAD_ALL_MESSAGE')
  }
}
const currentRoute = ref<string>('')
const isTextSelected = ref(false)
const finalMessageFormat = computed(() => store.state.emails.finalMessageFormat)
const finalCopyPasteFormat = computed(
  () => store.state.emails.finalCopyPasteFormat,
)
const editor = ref<Editor | null>(null)
const hasInsertedZWS = ref(false) // flag to track \u200b insertion
const enterPressed = ref(false) // flag to track if Enter was pressed
const isApplyingFormat = ref(false) // flag to prevent multiple format applications
const isCleaningZWS = ref(false) // flag to track if ZWS cleaning is in progress
const isPlainTextMode = ref(false)
const isPasting = ref(false)
watch(
  () => finalMessageFormat.value.selectedMessageFormat,
  (options) => {
    console.log(options, 'finalMessageFormat.value.selectedMessageFormat')
    isPlainTextMode.value = options.includes('Plain text')
    console.log(isPlainTextMode.value, 'isPlainTextMode.value')
    if (isPlainTextMode.value) {
      stripAllFormatting()
    }
  },
  { immediate: true },
)

function stripAllFormatting() {
  if (!editor.value) return
  editor.value.chain().focus().unsetAllMarks().clearNodes().run()
}

const languageAndTime = computed<LanguageAndTime>(
  () => store.state.outlook.languageAndTime,
)

const formattedZonedDate = (utcDate: string) => {
  const { timeZone, dateFormat, timeFormat } = languageAndTime.value
  const formatString = isToday(utcDate, timeZone) ? timeFormat : `${dateFormat}`

  return zonedDateTime(utcDate, timeZone, formatString)
}
onMounted(() => {
  editor.value = new Editor({
    content: '',
    onSelectionUpdate({ editor }) {
      nextTick(() => {
        const { from, to } = editor.state.selection
        isTextSelected.value = from !== to && !editor.state.selection.empty
      })
    },
    extensions: [
      StarterKit.configure({
        blockquote: {
          HTMLAttributes: {
            class: 'border-l-2 border-gray-300 pl-2',
          },
        },
        bulletList: {
          HTMLAttributes: {
            class: 'list-disc ml-3',
          },
        },
        orderedList: {
          HTMLAttributes: {
            class: 'list-decimal ml-3',
          },
        },
      }),
      Signature,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      TextStyle.configure({ mergeNestedSpanStyles: true }),
      Color,
      Highlight.configure({
        multicolor: true,
      }),
      FontFamily.configure({
        types: ['textStyle'],
      }),
      Underline,
      CustomIndent.configure({
        types: ['heading', 'paragraph'],
        minIndent: 0,
        maxIndent: 1000,
        indentLevel: 20,
      }),
      FontSize,
      Link.configure({
        openOnClick: true,
        autolink: true,
        HTMLAttributes: {
          class: 'tiptap-link',
          style: 'color: #2563eb; text-decoration: underline; cursor: pointer;',
        },
      }), // Prevents auto-opening links
      Image,
    ],
    onCreate({ editor }) {
      console.log('editor')
      applyDefaultFormatting(editor, finalMessageFormat.value)
      // ✅ Add transaction listener here
      editor.on('transaction', ({ editor, transaction }) => {
        const { state } = editor
        const selection = state.selection

        const isNewParagraph =
          transaction.docChanged &&
          transaction.steps.some(
            (step: any) =>
              step.slice?.content?.content?.[0]?.type?.name === 'paragraph',
          )

        const isEmpty = state.doc.textContent === ''
        // If it's a new paragraph or everything was deleted
        if (isNewParagraph || isEmpty) {
          applyDefaultFormatting(editor, finalMessageFormat.value)

          // Check if selection is inside an empty paragraph
          const $from = selection.$from
          const parentNode = $from.node($from.depth)
          if (
            parentNode.type.name === 'paragraph' &&
            parentNode.content.size === 0
          ) {
            // Insert zero-width space if it's truly empty
            editor.commands.insertContent('\u200b')
            hasInsertedZWS.value = true
            console.log('1', hasInsertedZWS.value)
          }
        }
      })
      editor.view.setProps({
        handleKeyDown(view, event) {
          if (event.key === 'Enter') {
            enterPressed.value = true
            console.log(enterPressed.value, '4')
            return false
          }

          return false
        },
        handlePaste(view, event) {
          isPasting.value = true
          const html = event.clipboardData?.getData('text/html') || ''
          const plain = event.clipboardData?.getData('text/plain') || ''
          const mode = finalCopyPasteFormat.value.selectedPastingFromAppsOption

          const schema = editor.schema // ✅ get schema from current editor

          if (mode === 'Keep source formatting' && html) {
            console.log('Keep Source Formatting', html)
            event.preventDefault()
            // const mergedHTML = mergeFormattingWithDefault(html)
            // editor.commands.insertContent(mergedHTML)
            cleanTrailingZWSParagraph(editor)
            return true
          }

          if (mode === 'Keep text only') {
            console.log('Keep text only')
            editor.commands.insertContent(plain)
            nextTick(() => {
              applyDefaultFormatting(editor, finalMessageFormat.value)
            })
            return true
          }

          if (mode === 'Merge formatting' && html) {
            // console.log('Merge formatting')
            // const parser = ProseMirrorDOMParser.fromSchema(schema)
            // const tempEl = document.createElement('div')
            // tempEl.innerHTML = html
            // console.log(tempEl, tempEl.innerHTML, 'tempEl')
            // // ❌ Remove unneeded tags
            // tempEl
            //   .querySelectorAll('style, meta, link')
            //   .forEach((n) => n.remove())
            // // ❌ Strip class/style from all tags, unwrap unsupported ones
            // tempEl.querySelectorAll('*').forEach((el) => {
            //   el.removeAttribute('style')
            //   el.removeAttribute('class')
            //   const tag = el.tagName.toLowerCase()
            //   if (
            //     !['b', 'strong', 'i', 'em', 'u', 'a', 'br', 'p'].includes(tag)
            //   ) {
            //     el.replaceWith(...el.childNodes)
            //   }
            // })
            // // ✅ Parse cleaned HTML into ProseMirror nodes
            // const slice = parser.parseSlice(tempEl, {
            //   preserveWhitespace: true,
            // })
            // // ✅ Insert parsed content directly into document
            // const tr = view.state.tr.replaceSelection(slice)
            // view.dispatch(tr)
            // // ✅ Reapply formatting AFTER inserting
            // nextTick(() => {
            //   applyDefaultFormatting(editor, finalMessageFormat.value)
            // })
            // return true

            // event.preventDefault()
            // const mergedHTML = mergeFormattingWithDefault(html)
            // editor.commands.insertContent(mergedHTML)

            event.preventDefault()

            const plainText =
              event.clipboardData
                ?.getData('text/plain')
                ?.replace(/\n/g, '<br>') || ''

            // 1️⃣ Insert raw text
            editor.commands.insertContent(plainText)

            // 2️⃣ Select the inserted text range
            const { from, to } = editor.state.selection
            editor.commands.setTextSelection({
              from: from - plainText.length,
              to: from,
            })

            // 3️⃣ Apply default formatting on selected text
            applyDefaultFormatting(editor, finalMessageFormat.value)

            // 4️⃣ Move cursor to end
            editor.commands.setTextSelection(to)
            return true
          }
          isPasting.value = false
          return false
        },
      })
    },
    onUpdate({ editor }) {
      const text = editor.getText()
      console.log(enterPressed.value, '5')

      if (
        hasInsertedZWS.value &&
        text.replace(/\s/g, '') !== '' &&
        !enterPressed.value &&
        !isCleaningZWS.value &&
        !isPasting.value
      ) {
        console.log('Cleaning ZWS')
        isCleaningZWS.value = true

        const tr = editor.view.state.tr
        let shouldDispatch = false

        editor.view.state.doc.descendants((node, pos) => {
          if (node.isText && node.text?.includes('\u200b')) {
            tr.insertText('', pos, pos + 1)
            shouldDispatch = true
          }
        })

        if (shouldDispatch) {
          editor.view.dispatch(tr)
          hasInsertedZWS.value = false
          console.log('✅ Removed ZWS')
        }

        isCleaningZWS.value = false
      }

      setTimeout(() => {
        enterPressed.value = false
        console.log(enterPressed.value, '6')
      }, 1000)

      if (!isApplyingFormat.value && editor.state.doc.textContent === '') {
        applyDefaultFormatting(editor, finalMessageFormat.value)
      }
    },
    editorProps: {
      attributes: {
        class:
          'w-full h-auto no-scrollbar resize-none textarea-style outline-none border-none bg-white text-[#333333]',
      },
    },
  })
  editor.value.commands.clearContent()
  currentRoute.value = route.fullPath
  document.addEventListener('click', () => {
    toggleDeleteMenu.value = false
    toggleMoveMenu.value = false
    toggleReportMenu.value = false
    toggleReplyMenu.value = false
    toggleFlagMenu.value = false
    toggleMarkAllReadMenu.value = false
    // store.commit('emails/SET_CHECK_SINGLE_MESSAGE', 0)
    // toggleSingleCheckBox()
  })
  window.addEventListener('popstate', function (event) {
    listenWindowBackForwardEvent(event.state.current)
  })
})
onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})
onUnmounted(() => {
  document.removeEventListener('click', () => {
    toggleDeleteMenu.value = false
    toggleMoveMenu.value = false
    toggleReportMenu.value = false
    toggleReplyMenu.value = false
    toggleFlagMenu.value = false
    toggleMarkAllReadMenu.value = false
    // store.commit('emails/SET_CHECK_SINGLE_MESSAGE', 0)
    // toggleSingleCheckBox()
  })
  document.removeEventListener('wheel', () => {
    if (showMenu.value) {
      store.commit('emails/SET_CHECK_SINGLE_MESSAGE', 0)
      showMenu.value = false
      toggleSingleCheckBox()
      position.value.x = 0
      position.value.y = 0
    }
  })
  window.removeEventListener('popstate', function (event) {
    listenWindowBackForwardEvent(event.state.current)
  })
})
function mergeFormattingWithDefault(html: string): string {
  const juiced = juice(html) // Inline all CSS
  const container = document.createElement('div')
  container.innerHTML = juiced

  container.querySelectorAll('*').forEach((el) => {
    const style = el.getAttribute('style')
    if (style) {
      const keepStyles = extractAllowedStyles(style)
      el.setAttribute('style', keepStyles)
    }
  })
  console.log('Merged HTML with default formatting:', container.innerHTML)
  // 👇 Remove extra <p><br></p> or <p>&ZeroWidthSpace;</p>
  const cleaned = container.innerHTML.replace(
    /<p>(<br\s*\/?>|&#8203;|\u200B)?<\/p>/gi,
    '',
  )
  return container.innerHTML
}
function extractAllowedStyles(style: string): string {
  const allowed = [
    'color',
    'background-color',
    'font-size',
    'text-decoration',
    'font-weight',
    'font-style',
  ]
  return style
    .split(';')
    .map((s) => s.trim())
    .filter((s) => allowed.some((a) => s.startsWith(a)))
    .join('; ')
}
const initialEditor = () => {
  if (editor.value) {
    editor.value.commands.clearContent()
    applyDefaultFormatting(editor.value, finalMessageFormat.value)
  }
}
function applyDefaultFormatting(editor, settings) {
  if (isPlainTextMode.value || isApplyingFormat.value) return // ⛔ Prevent recursive loop
  isApplyingFormat.value = true
  try {
    console.log('Applying default formatting:')
    // Check if doc is empty
    const isEmpty = editor.state.doc.textContent === ''
    let chain = editor.chain().focus()

    if (settings.bold) chain = chain.setMark('bold')
    if (settings.italic) chain = chain.setMark('italic')
    if (settings.underline) chain = chain.setMark('underline')
    if (settings.colorText) chain = chain.setColor(settings.colorText)
    if (settings.selectedHeaderSize)
      chain = chain.setFontSize(settings.selectedHeaderSize)
    if (settings.selectedFontFamily)
      chain = chain.setMark('textStyle', {
        fontFamily: settings.selectedFontFamily,
      })

    chain.run()
    // Insert a zero-width space to reflect styles at cursor visually
    if (isEmpty && !hasInsertedZWS.value) {
      editor.commands.insertContent('\u200b')
      hasInsertedZWS.value = true
      console.log('3', hasInsertedZWS.value)
    }
  } finally {
    isApplyingFormat.value = false
  }
}
function cleanTrailingZWSParagraph(editor: Editor) {
  nextTick(() => {
    const tr = editor.view.state.tr
    let shouldDispatch = false

    editor.view.state.doc.descendants((node, pos) => {
      if (node.type.name === 'paragraph' && node.textContent === '\u200b') {
        tr.delete(pos, pos + node.nodeSize)
        shouldDispatch = true
      }
    })

    if (shouldDispatch) editor.view.dispatch(tr)
  })
}

const setToggleDeleteMenu = () => {
  toggleDeleteMenu.value = !toggleDeleteMenu.value
  toggleMoveMenu.value = false
  toggleReportMenu.value = false
  toggleReplyMenu.value = false
  toggleFlagMenu.value = false
  toggleMarkAllReadMenu.value = false
}
const setToggleMoveMenu = () => {
  toggleMoveMenu.value = !toggleMoveMenu.value
  toggleDeleteMenu.value = false
  toggleReportMenu.value = false
  toggleReplyMenu.value = false
  toggleFlagMenu.value = false
  toggleMarkAllReadMenu.value = false
}
const setToggleReportMenu = () => {
  toggleReportMenu.value = !toggleReportMenu.value
  toggleDeleteMenu.value = false
  toggleMoveMenu.value = false
  toggleReplyMenu.value = false
  toggleFlagMenu.value = false
  toggleMarkAllReadMenu.value = false
}
const setToggleReplyMenu = () => {
  toggleReplyMenu.value = !toggleReplyMenu.value
  toggleReportMenu.value = false
  toggleDeleteMenu.value = false
  toggleMoveMenu.value = false
  toggleFlagMenu.value = false
  toggleMarkAllReadMenu.value = false
}
const setToggleFlagMenu = () => {
  toggleFlagMenu.value = !toggleFlagMenu.value
  toggleReportMenu.value = false
  toggleDeleteMenu.value = false
  toggleMoveMenu.value = false
  toggleReplyMenu.value = false
  toggleMarkAllReadMenu.value = false
}
const settoggleMarkAllReadMenu = () => {
  toggleMarkAllReadMenu.value = !toggleMarkAllReadMenu.value
  toggleFlagMenu.value = false
  toggleReportMenu.value = false
  toggleDeleteMenu.value = false
  toggleMoveMenu.value = false
  toggleReplyMenu.value = false
}

const showSpecificMessage = ref(false)
const setSpecificMessage = (emailMessage: EmailMessage) => {
  showSpecificMessage.value = true
  router.push(`${currentRoute.value}/${emailMessage.id}`)
  store.commit('emails/READ_A_SPECIFIC_MESSAGE', emailMessage.id)
  store.commit('emails/SET_SELECTED_EMAIL_MESSAGE', emailMessage)
  store.commit('emails/RESET_SHOW_COMPOSE_SECTION')
}
const listenWindowBackForwardEvent = (path: string) => {
  showSpecificMessage.value = false
}

watch(
  () => showReplyBox.value,
  (newValue) => {
    if (!newValue) {
      console.log(newValue)
      initialEditor()
    }
  },
)
</script>

<template>
  <div ref="messageMenu" class="w-full h-full overflow-hidden">
    <div
      class="flex justify-between items-center pl-2 pr-4 py-2 border-b border-[#E3E3E3]"
    >
      <div
        v-if="!showComposeSection && !showReplyBox"
        class="flex space-x-2 items-center"
      >
        <div
          class="flex items-center relative"
          :class="checkedAll || showMinus ? 'bg-[#F1F2F6] rounded-md' : ''"
        >
          <div
            class="hover:bg-[#F1F2F6] flex items-center h-10 px-2 rounded-md cursor-pointer"
          >
            <InputsCheckBoxInput
              :model-value="checkedAll"
              :show-minus="showMinus"
              @update:modelValue="toggleCheckedAll"
            />
          </div>
        </div>
        <div
          class="relative hover:bg-[#F1F2F6] px-2 w-fit h-7 flex space-x-[11px] justify-center items-center rounded cursor-pointer"
          :class="
            showMinus || checkedAll || showSpecificMessage
              ? ' opacity-100'
              : ' opacity-30'
          "
        >
          <SharedIconHubMicrosoftSidebarDeleteItemsIcon
            @click.stop="setToggleDeleteMenu"
          />
          <div
            class="flex space-x-2 items-center"
            @click.stop="setToggleDeleteMenu"
          >
            <p class="text-[#525252] text-sm max-[1210px]:hidden">Delete</p>
            <SharedIconHubMicrosoftArrowIcon class="w-2 h-1" />
          </div>
          <!--             v-if="(showMinus || checkedAll) && toggleDeleteMenu" -->
          <SourceHubMicrosoftModalDelete
            v-if="
              (showMinus || checkedAll || showSpecificMessage) &&
              toggleDeleteMenu
            "
            :modalOptions="deleteMenuOptions"
            width="105px"
            class="top-7 -left-[10px]"
          />
        </div>
        <div
          class="hover:bg-[#F1F2F6] px-2 w-fit h-7 flex space-x-[11px] justify-center items-center rounded cursor-pointer"
          :class="
            showMinus || checkedAll || showSpecificMessage
              ? ' opacity-100'
              : ' opacity-30'
          "
        >
          <SharedIconHubMicrosoftSidebarArchiveIcon />
          <div class="flex space-x-2 items-center max-[1272px]:hidden">
            <p class="text-[#525252] text-sm">Archive</p>
          </div>
        </div>
        <div
          class="hover:bg-[#F1F2F6] px-2 w-fit h-7 flex space-x-[11px] justify-center items-center rounded cursor-pointer relative"
          :class="
            showMinus || checkedAll || showSpecificMessage
              ? ' opacity-100'
              : ' opacity-30'
          "
        >
          <SharedIconHubEmailsMoveToIcon @click.stop="setToggleMoveMenu" />
          <div
            class="flex space-x-2 items-center"
            @click.stop="setToggleMoveMenu"
          >
            <p
              class="text-[#525252] text-sm whitespace-nowrap max-[1319px]:hidden"
            >
              Move to
            </p>
            <SharedIconHubMicrosoftArrowIcon class="w-2 h-1" />
          </div>
          <SourceHubMicrosoftModalDelete
            v-if="
              (showMinus || checkedAll || showSpecificMessage) && toggleMoveMenu
            "
            :modalOptions="moveMenuOptions"
            width="279px"
            class="top-7 -left-[10px]"
          />
        </div>
        <div
          class="w-[1px] min-w-[1px] h-[28px] bg-[#707070] bg-opacity-30"
        ></div>
        <div
          class="relative hover:bg-[#F1F2F6] px-2 w-fit h-7 flex space-x-[11px] justify-center items-center rounded cursor-pointer"
          :class="[
            showMinus || checkedAll || showSpecificMessage
              ? ' opacity-100'
              : ' opacity-30',
            (showMinus || checkedAll || showSpecificMessage) && toggleReportMenu
              ? 'bg-[#F1F2F6]'
              : '',
          ]"
        >
          <SharedIconHubMicrosoftReportIcon @click.stop="setToggleReportMenu" />
          <div
            class="flex space-x-2 items-center"
            @click.stop="setToggleReportMenu"
          >
            <p class="text-[#525252] text-sm max-[1374px]:hidden">Report</p>
            <SharedIconHubMicrosoftArrowIcon class="w-2 h-1" />
          </div>
          <SourceHubMicrosoftModalDelete
            v-if="
              (showMinus || checkedAll || showSpecificMessage) &&
              toggleReportMenu
            "
            :modalOptions="reportMenuOptions"
            width="172px"
            class="top-7 -left-[10px]"
          />
        </div>
        <div
          class="w-[1px] min-w-[1px] h-[28px] bg-[#707070] bg-opacity-30"
        ></div>
        <div
          class="relative hover:bg-[#F1F2F6] px-2 w-fit h-7 flex space-x-[11px] justify-center items-center rounded cursor-pointer"
          :class="[
            showMinus || checkedAll || showSpecificMessage
              ? ' opacity-100'
              : ' opacity-30',
            (showMinus || checkedAll || showSpecificMessage) && toggleReplyMenu
              ? 'bg-[#F1F2F6]'
              : '',
          ]"
        >
          <SharedIconHubMicrosoftReplyAllIcon
            @click.stop="setToggleReplyMenu"
          />
          <div
            class="flex space-x-2 items-center"
            @click.stop="setToggleReplyMenu"
          >
            <p
              class="text-[#525252] text-sm whitespace-nowrap max-[1422px]:hidden"
            >
              Reply All
            </p>
            <SharedIconHubMicrosoftArrowIcon class="w-2 h-1" />
          </div>
          <SourceHubMicrosoftModalDelete
            v-if="
              (showMinus || checkedAll || showSpecificMessage) &&
              toggleReplyMenu
            "
            :modalOptions="replyMenuOptions"
            width="119px"
            class="top-7 -left-[10px]"
          />
        </div>
        <div
          class="w-[1px] min-w-[1px] h-[28px] bg-[#707070] bg-opacity-30"
        ></div>
        <div
          class="hover:bg-[#F1F2F6] px-2 w-fit h-7 flex space-x-[11px] justify-center items-center rounded cursor-pointer"
          :class="
            showMinus || checkedAll || showSpecificMessage
              ? ' opacity-100'
              : ' opacity-100'
          "
          @click="setReadUnreadToggleButton"
        >
          <SharedIconHubEmailsReadMessageIcon />
          <div class="flex space-x-2 items-center max-[1517px]:hidden">
            <p class="text-[#525252] text-sm whitespace-nowrap">
              Read / Unread
            </p>
          </div>
        </div>
        <div
          class="relative hover:bg-[#F1F2F6] px-2 w-fit h-7 flex space-x-[11px] justify-center items-center rounded cursor-pointer"
          :class="[
            showMinus || checkedAll || showSpecificMessage
              ? ' opacity-100'
              : ' opacity-30',
            (showMinus || checkedAll || showSpecificMessage) && toggleFlagMenu
              ? 'bg-[#F1F2F6]'
              : '',
          ]"
        >
          <SharedIconHubMicrosoftFlagIcon @click.stop="setToggleFlagMenu" />
          <div
            class="flex space-x-2 items-center max-[1616px]:hidden"
            @click.stop="setToggleFlagMenu"
          >
            <p class="text-[#525252] text-sm whitespace-nowrap">
              Flag / Unflag
            </p>
            <SharedIconHubMicrosoftArrowIcon class="w-2 h-1" />
          </div>
          <SourceHubMicrosoftModalDelete
            v-if="
              (showMinus || checkedAll || showSpecificMessage) && toggleFlagMenu
            "
            :modalOptions="flagMenuOptions"
            width="139px"
            class="top-7 -left-[10px]"
          />
        </div>
        <div
          class="w-[1px] min-w-[1px] h-[28px] bg-[#707070] bg-opacity-30"
        ></div>
        <div
          class="hover:bg-[#F1F2F6] px-2 w-fit h-7 flex space-x-[11px] justify-center items-center rounded cursor-pointer"
          :class="
            showMinus || checkedAll || showSpecificMessage
              ? ' opacity-100'
              : ' opacity-30'
          "
        >
          <SharedIconHubMicrosoftUndoIcon />
          <div class="flex space-x-2 items-center max-[1656px]:hidden">
            <p class="text-[#525252] text-sm">Undo</p>
          </div>
        </div>
        <div
          class="w-[1px] min-w-[1px] h-[28px] bg-[#707070] bg-opacity-30"
        ></div>
        <div
          class="hover:bg-[#F1F2F6] w-7 min-w-[7] h-7 flex justify-center items-center rounded cursor-pointer relative"
          :class="toggleMarkAllReadMenu ? 'bg-[#F1F2F6]' : ''"
          @click.stop="settoggleMarkAllReadMenu"
        >
          <SharedIconHubEmailsThreeDotMenuIcon
            class="transform rotate-90"
            color="#525252"
          />
          <SourceHubMicrosoftModalSelect
            v-if="toggleMarkAllReadMenu"
            class="top-7 min-[1840px]:left-[0px] right-0"
          />
        </div>
      </div>
      <SourceHubMicrosoftComposeInputModifier
        v-else
        :editor="editor"
        :is-plain-text-mode="isPlainTextMode"
      />
    </div>
    <div class="w-full h-full grid grid-cols-[minmax(300px,0.4fr)_1fr]">
      <div class="h-[calc(100%-58px)] custom-scroll">
        <div
          class="px-3 py-3 flex space-x-2 border-b border-[#E3E3E3] cursor-pointer relative message-box"
          v-for="emailMessage in emailMessages"
          :key="emailMessage.id"
          :class="[
            emailMessage.checked && emailMessage.read
              ? 'bg-[#D6E7FF]'
              : emailMessage.checked && !emailMessage.read
                ? 'bg-[#D6E7FF] border-l-4 border-l-[#0F6CBC]'
                : '',
            emailMessage.read ? '' : 'border-l-4 border-l-[#0F6CBC]',
            emailMessage.selected ? 'bg-[#D6E7FF]' : '',
          ]"
          @contextmenu="(event) => checkSingleMessage(event, emailMessage.id)"
        >
          <div
            class="w-10 h-10 min-w-10 min-h-10 rounded-full flex items-center justify-start"
          >
            <InputsCheckBoxInput
              :id="emailMessage.id"
              class="checkbox"
              :class="emailMessage.checked ? '!block' : '!hidden'"
              v-model="emailMessage.checked"
              :model-value="emailMessage.checked"
              @update:modelValue="toggleSingleCheckBox"
            />
            <img
              class="profile-image"
              :class="emailMessage.checked ? '!hidden' : 'block'"
              :src="emailMessage.profileUrl"
              :alt="emailMessage.from"
            />
          </div>
          <div
            class="flex flex-col flex-grow space-y-1.5"
            @click.stop="setSpecificMessage(emailMessage)"
          >
            <h2
              class="text-[#333333]"
              :class="emailMessage.read ? '' : 'font-semibold'"
            >
              {{ emailMessage.from }}
            </h2>
            <div
              class="flex justify-between !space-x-2"
              :class="
                emailMessage.read
                  ? 'text-[#333333]'
                  : 'text-[#0F6CBC] font-semibold'
              "
            >
              <div class="line-clamp-1" v-html="emailMessage.subject"></div>
              <p class="whitespace-nowrap">
                {{ formattedZonedDate(emailMessage.createdAt) }}
              </p>
            </div>
            <div
              class="line-clamp-1 text-[#707070]"
              v-html="emailMessage.snippet"
            ></div>
          </div>
        </div>
      </div>
      <div
        v-if="!showSpecificMessage && !showComposeSection"
        class="w-full h-full bg-[#F1F2F6] flex justify-center items-center"
      >
        <div class="flex flex-col text-center items-center">
          <img
            width="96"
            height="96"
            src="/social/email-open.svg"
            alt="email-open"
          />
          <h1 class="text-lg font-semibold mt-[22px] text-[#333333]">
            Select an item to read
          </h1>
          <p class="text-lg mt-1.5 text-[#707070]">Nothing is selected</p>
        </div>
      </div>
      <div
        v-else-if="showSpecificMessage && !showComposeSection"
        class="w-full h-[calc(100%-58px)] bg-[#F1F2F6] custom-scroll"
      >
        <NuxtPage :editor="editor" />
      </div>
      <SourceHubMicrosoftComposeBox
        v-if="showComposeSection"
        :editor="editor"
      />
    </div>
  </div>
</template>

<style scoped>
.page-number:hover .show-hide {
  display: flex;
}
.message-box:hover .checkbox {
  display: block !important;
}
.message-box:hover .profile-image {
  display: none !important;
}
</style>
