<script setup lang="ts">
import { Editor } from '@tiptap/vue-3'
import { useStore } from 'vuex'
import type { LanguageAndTime } from '~/types/hubEmailsSettings'

interface Props {
  editor?: Editor | null
}

const props = withDefaults(defineProps<Props>(), {
  editor: null,
})

const store = useStore()
const selectedEmailMessage = computed(
  () => store.state.emails.selectedEmailMessage,
)
const accountItem = computed(() => {
  return store.state.social.accountItem
})
const showReplyBox = computed(() => store.state.emails.showReplyBox)

const languageAndTime = computed<LanguageAndTime>(
  () => store.state.outlook.languageAndTime,
)

const formattedZonedDate = (utcDate: string) => {
  const { timeZone, dateFormat, timeFormat } = languageAndTime.value
  const formatString = `EEE ${dateFormat} ${timeFormat}`

  return zonedDateTime(utcDate, timeZone, formatString)
}
</script>

<template>
  <div class="w-full h-auto p-2">
    <!-- <div class="grid grid-cols-[40px,1fr] gap-x-4">
      <div></div>
      <div>
        <h2
          class="text-xl text-[#333333] font-semibold"
          v-html="selectedEmailMessage.subject"
        ></h2>
      </div>
    </div> -->
    <div class="bg-white rounded-lg p-4">
      <div class="grid grid-cols-[40px,1fr] gap-x-4">
        <SourceAccountLogo
          :account-profile-pic="selectedEmailMessage.profileUrl"
        />
        <div class="flex justify-between">
          <div class="text-sm">
            <p>
              <span class="text-[#333333]">{{
                selectedEmailMessage.from
              }}</span>
              <span class="text-[#333333]"
                >&lt;<EMAIL>&gt;</span
              >
            </p>
            <div class="flex space-x-2 items-center text-[#333333] mt-[6px]">
              <p class="">To: You</p>
              <!-- <SharedIconHubEmailsDownArrow class="w-2 h-1.5" /> -->
            </div>
          </div>
          <div class="flex flex-col space-y-1.5">
            <div class="flex space-x-1.5 items-center transform -translate-x-2">
              <div class="p-2 pt-1">
                <SharedIconHubMicrosoftReplyColorIcon />
              </div>
              <div class="p-2 pt-1">
                <SharedIconHubMicrosoftReplyAllColorIcon />
              </div>
              <div class="p-2 pt-1">
                <SharedIconHubMicrosoftForwardColorIcon />
              </div>
              <div
                class="w-[1px] min-w-[1px] h-5 bg-[#707070] bg-opacity-25"
              ></div>
              <SharedIconHubEmailsThreeDotMenuIcon
                color="#525252"
                class="transform rotate-90 !ml-5"
              />
            </div>
            <div class="text-[#333333] text-xs text-right">
              {{ formattedZonedDate(selectedEmailMessage.createdAt) }}
            </div>
          </div>
        </div>
      </div>
      <div class="grid grid-cols-[40px,1fr] gap-x-4 mt-10">
        <div></div>
        <div>
          <p
            class="text-base text-[#979797] font-semibold"
            v-html="selectedEmailMessage.description"
          ></p>
        </div>
      </div>
      <div v-if="!showReplyBox" class="grid grid-cols-[40px,1fr] gap-x-4 mt-10">
        <div></div>
        <div class="flex space-x-2">
          <button
            class="flex !space-x-2 justify-center items-center px-4 py-1.5 rounded text-sm border border-[#525252] text-[#333333]"
            @click="store.commit('emails/SET_SHOW_REPLY_BOX', true)"
          >
            <SharedIconHubMicrosoftReplyColorIcon />
            <p>Reply</p>
          </button>
          <button
            class="flex !space-x-2 justify-center items-center px-4 py-1.5 rounded text-sm border border-[#525252] text-[#333333]"
          >
            <SharedIconHubMicrosoftForwardColorIcon />
            <p>Forward</p>
          </button>
        </div>
      </div>
    </div>
    <div
      v-if="showReplyBox"
      class="mt-2 bg-white rounded-lg p-4 pt-2 overflow-x-auto"
    >
      <SourceHubMicrosoftReplyBox
        class="px-0"
        :toPersonName="selectedEmailMessage.from"
        :editor="editor"
      />
    </div>
  </div>
</template>

<style scoped>
.reply-box {
  /* background: #ffffff 0% 0% no-repeat padding-box; */
  /* box-shadow: 1px 2px 8px #00000029; */
  /* border-radius: 8px; */
}
</style>
