<script setup lang="ts">
import BlockIcon from '~/components/shared/icon/hub/microsoft/BlockIcons.vue'
import SweepIcon from '~/components/shared/icon/hub/microsoft/SweepIcon.vue'
import RulesIcon from '~/components/shared/icon/hub/microsoft/RulesIcon.vue'
import CategorizeIcon from '~/components/shared/icon/hub/microsoft/CategorizeIcon.vue'
import PinIcon from '~/components/shared/icon/hub/microsoft/PinIcon.vue'
import SnoozeIcon from '~/components/shared/icon/hub/emails/Snooze.vue'
import PrintIcon from '~/components/shared/icon/hub/microsoft/PrintIcon.vue'

interface SubOption {
  id: number
  title: string
  image: string | Component
  opacity: boolean
}

interface Option {
  id: number
  title: string
  border: boolean
  image: string | Component
  subOptions: SubOption[]
}

interface MenuCategory {
  id: number
  title: string
  options: Option[]
}
const modalOptions = ref<MenuCategory[]>([
  {
    id: 1,
    title: 'Move & delete',
    options: [
      {
        id: 1,
        title: 'Block',
        border: false,
        image: markRaw(BlockIcon),
        subOptions: [
          {
            id: 1,
            title: 'Block',
            image: '',
            opacity: false,
          },
          {
            id: 2,
            title: 'Report spam',
            image: '',
            opacity: false,
          },
        ],
      },
      {
        id: 2,
        title: 'Sweep',
        border: false,
        image: markRaw(SweepIcon),
        subOptions: [],
      },
      {
        id: 3,
        title: 'Rules',
        border: true,
        image: markRaw(RulesIcon),
        subOptions: [
          {
            id: 1,
            title: 'Block',
            image: '',
            opacity: false,
          },
          {
            id: 2,
            title: 'Report spam',
            image: '',
            opacity: false,
          },
        ],
      },
    ],
  },
  {
    id: 2,
    title: 'Tags',
    options: [
      {
        id: 1,
        title: 'Categorize',
        border: false,
        image: markRaw(CategorizeIcon),
        subOptions: [
          {
            id: 1,
            title: 'Block',
            image: '',
            opacity: false,
          },
          {
            id: 2,
            title: 'Report spam',
            image: '',
            opacity: false,
          },
        ],
      },
      {
        id: 2,
        title: 'Pin / Unpin',
        border: false,
        image: markRaw(PinIcon),
        subOptions: [],
      },
      {
        id: 3,
        title: 'Snooze',
        border: true,
        image: markRaw(SnoozeIcon),
        subOptions: [
          {
            id: 1,
            title: 'Block',
            image: '',
            opacity: false,
          },
          {
            id: 2,
            title: 'Report spam',
            image: '',
            opacity: false,
          },
        ],
      },
    ],
  },
  {
    id: 3,
    title: 'Print',
    options: [
      {
        id: 1,
        title: 'Print',
        border: true,
        image: markRaw(PrintIcon),
        subOptions: [],
      },
    ],
  },
])
</script>

<template>
  <div class="box-modal py-1 absolute" :style="{ '--modal-width': '205px' }">
    <div
      v-for="modalOption in modalOptions"
      :key="modalOption.id"
      class="flex flex-col"
    >
      <h2 class="pl-[18px] pr-4 py-1.5 text-[#525252] font-semibold">
        {{ modalOption.title }}
      </h2>
      <div
        v-for="option in modalOption.options"
        :key="option.id"
        class="pl-4 pr-3.5 py-1.5 flex items-center !space-x-2 hover:bg-[#F1F2F6]"
        :class="option.border ? 'border-b border-[#E3E3E3]' : ''"
      >
        <component v-if="option.image" :is="option.image" />
        <div class="flex-grow flex justify-between items-center pr-1.5">
          <p class="flex-grow line-clamp-1 text-[#525252]">
            {{ option.title }}
          </p>
          <SharedIconHubEmailsDownArrow
            v-if="option.subOptions && option.subOptions.length > 0"
            class="transform rotate-[-90deg] w-[5px] h-2"
          />
        </div>
      </div>
    </div>
    <div class="w-full flex justify-center mt-2">
      <button class="text-[#525252]">Customize</button>
    </div>
  </div>
</template>

<style scoped>
.box-modal {
  width: var(--modal-width);
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
  z-index: 1;
}
</style>
