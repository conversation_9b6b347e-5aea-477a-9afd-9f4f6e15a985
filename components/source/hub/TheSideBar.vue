<script setup lang="ts">
import { useStore } from 'vuex'
const config = useRuntimeConfig()

const store = useStore()
const route = useRoute()
const router = useRouter()

interface accountItemsType {
  id: number
  type: string
  profilePic: string
  provider: string
  name: string
  username: string
  number: string
  count: number
  select: boolean
  toggleSelect: boolean
}
const accountItems = ref<accountItemsType[]>(
  JSON.parse(JSON.stringify(store.state.social.sideBarAccountItems)),
)
const sideBarAccountItems = computed(() => {
  accountItems.value = store.state.social.sideBarAccountItems
  return accountItems.value
})
console.log(accountItems.value, 'accountItems12')
const accountType = computed(() => store.state.social.accountType)
const showManagerOnboarding = computed(
  () => store.state.social.showManagerOnboarding,
)
const { getRouteOnProvider } = useSourceApi()
function escapeRegex(str: string) {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}
watch(
  () => accountType.value,
  (oldType, newType) => {
    if (oldType !== newType) {
      accountItems.value = JSON.parse(
        JSON.stringify(store.state.social.sideBarAccountItems),
      )
      console.log(oldType, newType, 'accountType changed', accountItems.value)
    }
  },
)
// watch(
//   () => accountType.value,
//   (data) => {
//     setTimeout(() => {
//       accountItems.value = JSON.parse(
//         JSON.stringify(store.state.social.sideBarAccountItems),
//       )

//       accountItems.value.forEach((accountItem: accountItemsType) => {
//         console.log(data, 'accountType', accountItem.username)

//         // username এবং data safe করে regex বানানো হলো
//         const safeData = escapeRegex(data)
//         const safeUsername = escapeRegex(accountItem.username)

//         const regex = new RegExp(`(?<=${safeData}/)${safeUsername}(?=/)`)
//         console.log(
//           regex,
//           route.fullPath.match(regex),
//           route.fullPath,
//           'route.fullPath',
//         )

//         if (route.fullPath.match(regex)) {
//           accountItem.select = true
//           store.commit('social/SET_PROFILE_CONTENT', accountItem)
//         } else {
//           accountItem.select = false
//         }
//       })
//     }, 100)
//     // setTimeout(() => {
//     //   accountItems.value = JSON.parse(
//     //     JSON.stringify(store.state.social.sideBarAccountItems),
//     //   )
//     //   accountItems.value.forEach((accountItem: accountItemsType) => {
//     //     console.log(data, 'accountType', accountItem.username)
//     //     const regex = new RegExp(`(?<=${data}/)\\${accountItem.username}(?=/)`)
//     //     console.log(
//     //       regex,
//     //       route.fullPath.match(regex),
//     //       route.fullPath,
//     //       'route.fullPath',
//     //     )
//     //     if (route.fullPath.match(regex)) {
//     //       accountItem.select = true
//     //       store.commit('social/SET_PROFILE_CONTENT', accountItem)
//     //     } else {
//     //       accountItem.select = false
//     //     }
//     //   })
//     // }, 2000)
//   },
// )
watch(
  () => showManagerOnboarding.value,
  (data) => {
    if (!data) {
      accountItems.value = JSON.parse(
        JSON.stringify(store.state.social.sideBarAccountItems),
      )
    }
  },
)

const getBgColor = (
  accountItem: accountItemsType,
  accountItems: accountItemsType[],
) => {
  return accountItem.select
    ? 'bg-[#4A71D4]'
    : accountItem.count > 0
      ? 'bg-[#D6E7FF]'
      : 'bg-[#F1F2F6]'
}

const changeAccountPath = (id: number, provider: string) => {
  accountItems.value.forEach((accountItem: accountItemsType) => {
    if (accountItem.id === id) {
      accountItem.select = true
      const username = accountItem.username.toLocaleLowerCase()
      getRouteOnProvider(
        accountItem.provider,
        accountType.value,
        accountItem.username,
      )
      setTimeout(() => {
      store.commit('social/SET_PROFILE_CONTENT', accountItem)
      }, 500)
    } else {
      accountItem.select = false
    }
  })
  store.commit('social/UPADTE_SIDEBAR_SELECT_VALUE', accountItems.value)
}
const openManagerOnboarding = () => {
  store.commit('social/SET_MANAGER_ONBOARDING', true)
}
onMounted(() => {
  console.log('mounted TheSideBar')
  window.addEventListener('popstate', function (event) {
    listenWindowBackForwardEvent(event.state.current)
  })
})
onUnmounted(() => {
  window.removeEventListener('popstate', function (event) {
    listenWindowBackForwardEvent(event.state.current)
  })
})
const listenWindowBackForwardEvent = (path: string) => {
  if (path.includes('socials')) {
    store.commit('social/SET_ACCOUNT_TYPE', 'socials')
    accountItems.value = JSON.parse(
      JSON.stringify(store.state.social.sideBarAccountItems),
    )
    accountItems.value.forEach((accountItem: accountItemsType) => {
      const regex = new RegExp(`(?<=socials/)\\${accountItem.username}(?=/)`)
      if (path.match(regex)) {
        accountItem.select = true
        store.commit('social/SET_PROFILE_CONTENT', accountItem)
      } else {
        accountItem.select = false
      }
    })
  } else if (path.includes('emails')) {
    store.commit('social/SET_ACCOUNT_TYPE', 'emails')
    accountItems.value = JSON.parse(
      JSON.stringify(store.state.social.sideBarAccountItems),
    )
    accountItems.value.forEach((accountItem: accountItemsType) => {
      const regex = new RegExp(`(?<=emails/)\\${accountItem.username}(?=/)`)
      if (path.match(regex)) {
        accountItem.select = true
        store.commit('social/SET_PROFILE_CONTENT', accountItem)
      } else {
        accountItem.select = false
      }
    })
  } else if (path.includes('text')) {
    store.commit('social/SET_ACCOUNT_TYPE', 'text')
    accountItems.value = JSON.parse(
      JSON.stringify(store.state.social.sideBarAccountItems),
    )
    accountItems.value.forEach((accountItem: accountItemsType) => {
      const regex = new RegExp(`(?<=text/)\\${accountItem.username}(?=/)`)
      if (path.match(regex)) {
        accountItem.select = true
        store.commit('social/SET_PROFILE_CONTENT', accountItem)
      } else {
        accountItem.select = false
      }
    })
  }
}
</script>

<template>
  <div class="w-full h-full flex flex-col space-y-[22px] overflow-hidden">
    <div class="flex justify-between items-center px-4">
      <p class="text-[#707070] font-semibold">Accounts</p>
      <fa
        class="text-sm text-[#525252] cursor-pointer"
        :icon="['fas', 'plus']"
        @click="openManagerOnboarding"
      />
    </div>
    <div class="w-full h-[calc(100vh-378px)] overflow-y-auto custom-scroll">
      <div class="flex flex-col space-y-3 px-4">
        <div
          class="flex justify-between items-center px-4 py-3 rounded-lg cursor-pointer"
          v-for="accountItem in accountItems"
          :key="accountItem.id"
          :class="getBgColor(accountItem, accountItems)"
          @click="changeAccountPath(accountItem.id, accountItem.provider)"
        >
          <div class="flex space-x-2.5 w-[calc(100%-40px)]">
            <SourceAccountLogo
              :account-profile-pic="accountItem.profilePic"
              :account-provider="accountItem.provider"
            />
            <div class="flex flex-col space-y-[2px] max-w-[134px] line-clamp-1">
              <p
                class="font-semibold overflow-hidden whitespace-nowrap text-ellipsis"
                :class="accountItem.select ? 'text-white' : 'text-[#333333]'"
              >
                {{ accountItem.name }}
              </p>
              <p
                class="text-sm overflow-hidden whitespace-nowrap text-ellipsis"
                :class="accountItem.select ? 'text-white' : 'text-[#525252]'"
              >
                {{
                  accountType === 'text'
                    ? accountItem.username
                    : accountItem.username
                }}
              </p>
            </div>
          </div>
          <div
            v-if="accountItem.count > 0"
            class="w-5 h-5 rounded-full text-xs font-bold flex justify-center items-center"
            :class="
              accountItem.select
                ? 'bg-[#FFFFFF] text-[#4A71D4]'
                : 'bg-[#E21F3F] text-white'
            "
          >
            <p>{{ accountItem.count }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
