<script setup lang="ts">
import { useStore } from 'vuex'
import { GROUPS, AVAILABLE_FEEDS } from '~/constants/urls'

const props = defineProps({
  allAddedGroup: {
    type: Object,
    default: null,
  },
  groupUpdate: {
    type: Object,
    default: null,
  },
  selectedGroupId: {
    type: Number,
    default: null,
  },
  groundedFeed: {
    type: Object,
    default: null,
  },
  clearField: {
    type: Boolean,
    default: false,
  },
})

const store = useStore()
interface availableItemsType {
  id: number
  profilePic: string
  provider: string
  name: string
  username: string
  selected: boolean
}
interface allAddedGroupsType {
  feedsInGroup: [availableItemsType]
  groupName: string
  id: number
}
const allAddedGroups = ref<allAddedGroupsType[]>([])
const availableItems = ref<availableItemsType[]>([])

watch(
  () => props.allAddedGroup,
  (data) => {
    allAddedGroups.value.unshift(data)
  },
)
watch(
  () => props.groundedFeed,
  (data) => {
    if (availableItems.value.length === 0) {
      availableItems.value.push(data.item1)
    } else if (availableItems.value.length > 0) {
      availableItems.value.push(data.item1)
    } else {
      availableItems.value.map((item) => {
        if (Number(item.id) === Number(data.item1.id)) {
          item.selected = data.item1.selected
        }
        // else if (Number(item.id) !== Number(data.item1.id)) {
        //   this.availableItems.push(data.item1)
        // }
        return item
      })
    }
    availableItems.value = [
      ...new Map(availableItems.value.map((item) => [item.id, item])).values(),
    ]
  },
)
watch(
  () => props.clearField,
  (data) => {
    if (data) {
      // setTimeout(() => {
      getavailableItemsAll()
      // }, 100)
    }
  },
)
onMounted(() => {
  getavailableItemsAll()
  getCreatedGroup()
})
const { fetch } = useFetched()
// allAddedGroupsType
interface getCreatedGroupResponseType {
  data: allAddedGroupsType
  message: string
  status: number
  success: boolean
}
const getCreatedGroup = async () => {
  try {
    const response = (await fetch(GROUPS)) as getCreatedGroupResponseType
    if (response.success) {
      allAddedGroups.value = response.data
    }
  } catch (error) {
    console.log(error)
  }
}
const getavailableItemsAll = async () => {
  try {
    const response = (await fetch(AVAILABLE_FEEDS)) as responseType
    if (response.success) {
      response.data.forEach((item: availableItemsType) => {
        item.selected = false
      })
      availableItems.value = response.data
    }
  } catch (error) {
    console.log(error)
  }
}
const unselectedFeeds = computed(() => {
  // eslint-disable-next-line array-callback-return
  return availableItems.value.filter((item) => {
    if (!item.selected) {
      return item
    }
  })
})
</script>

<template>
  <div class="flex flex-col space-y-4">
    <div
      class="flex flex-col rounded-lg bg-[#F1F2F6] p-3.5 min-h-[176px] w-full"
    >
      <h2 class="text-[#525252] font-semibold">Groups</h2>
      <div class="flex-grow w-full flex flex-col items-center py-4">
        <div
          v-if="allAddedGroups.length === 0"
          class="w-full h-full flex justify-center items-center"
        >
          <p class="text-[#707070] text-lg italic whitespace-nowrap">
            You haven't grouped any feeds yet.
          </p>
        </div>
        <table class="table-wrapper scroll min-w-full">
          <thead>
            <tr
              v-if="allAddedGroups.length > 0"
              class="bg-[#E3EFFF] sticky top-0 z-1 border-b"
            >
              <th class="table-th break-all text-left py-1 px-4">
                <span>Name</span>
              </th>
            </tr>
          </thead>
          <tbody v-if="allAddedGroups.length > 0" class="body">
            <tr
              v-for="(groupItem, gIndex) in allAddedGroups"
              :key="gIndex + 'group-web'"
              :class="gIndex % 2 === 0 ? 'bg-white' : 'bg-gray-default'"
            >
              <td
                class="py-1 px-4 flex items-center justify-start table-td action-icon"
              >
                <div
                  class="text-ash-primary text-left w-full break-all md:text-lg text-md"
                >
                  <span>{{ groupItem.groupName }}</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="w-full h-[39px] flex justify-end">
        <BaseButton
          class="w-[178px] h-full text-base bg-[#4A71D4] text-white font-semibold"
          text="Create New Group"
          @click="store.commit('setting/SET_SHOW_CREATE_GROUP', true)"
        />
      </div>
    </div>
    <div
      class="flex flex-col space-y-4 rounded-lg bg-[#F1F2F6] p-3.5 min-h-[176px] w-full"
    >
      <h2 class="text-[#525252] font-semibold">Available Feeds</h2>
      <div class="flex-grow w-full flex flex-col items-start">
        <div
          class="w-full h-full flex flex-col space-y-3.5 justify-start items-start"
        >
          <SourceHubManagerAvailableFeedsItem
            v-for="availableItem in unselectedFeeds"
            :key="availableItem.id"
            :available-item="availableItem"
          />
        </div>
        <div
          v-if="unselectedFeeds.length === 0"
          class="w-full h-full flex justify-center items-center text-gray-600 md:text-xl text-md font-semibold inner-body-height"
        >
          No feeds available
        </div>
      </div>
    </div>
  </div>
</template>
