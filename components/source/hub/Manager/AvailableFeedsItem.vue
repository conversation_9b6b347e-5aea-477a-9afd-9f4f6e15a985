<script setup lang="ts">
import YoutubeCircleIcon from '~/assets/img/png/youtube_social_circle_red.png'
import GoogleSvgIcon from '~/assets/img/svg/Google__G__Logo.svg'
import WebsiteIcon from '~/assets/img/svg/website.svg'

interface AvailableItem {
  id: number
  profilePic: string
  provider: string
  name: string
  username: string
}

interface Props {
  availableItem?: AvailableItem
}

const props = withDefaults(defineProps<Props>(), {
  availableItem: () => {},
})
</script>

<template>
  <div
    class="w-full max-h-[38px] px-4 py-1.5 bg-white rounded-lg flex !space-x-4 items-center"
  >
    <div class="h-full flex !space-x-2 items-center">
      <div class="w-5 min-w-5 h-5 min-h-5 rounded-full overflow-hidden">
        <template v-if="!availableItem.profilePic">
          <SharedIconFacebookIcon
            v-if="availableItem.provider === 'Facebook'"
            class="w-5 min-w-5 h-5 min-h-5"
          ></SharedIconFacebookIcon>
          <SharedIconLinkedinIcon
            v-if="availableItem.provider === 'LinkedIn'"
            class="w-5 min-w-5 h-5 min-h-5"
          ></SharedIconLinkedinIcon>
          <div
            v-if="availableItem.provider === 'Twitter'"
            class="w-5 min-w-5 h-5 min-h-5 twitter-icon"
          ></div>
          <SharedIconInstagramIcon
            v-if="availableItem.provider === 'Instagram'"
            class="w-5 min-w-5 h-5 min-h-5"
          ></SharedIconInstagramIcon>
          <SharedIconPinterestIcon
            v-if="availableItem.provider === 'Pinterest'"
            class="w-5 min-w-5 h-5 min-h-5"
          ></SharedIconPinterestIcon>
          <SharedIconRedditIcon
            v-if="availableItem.provider === 'Reddit'"
            class="w-5 min-w-5 h-5 min-h-5"
          ></SharedIconRedditIcon>
          <SharedIconTiktokIcon
            v-if="availableItem.provider === 'TikTok'"
            class="w-5 min-w-5 h-5 min-h-5"
          ></SharedIconTiktokIcon>
          <SharedIconMicrosoftIcon
            v-if="availableItem.provider === 'Microsoft'"
            class="w-5 min-w-5 h-5 min-h-5"
          ></SharedIconMicrosoftIcon>
          <img
            v-if="availableItem.provider === 'YouTube'"
            class="w-5 min-w-5 h-5 min-h-5"
            :src="YoutubeCircleIcon"
            alt="Youtube Circle Icon"
          />
          <img
            v-if="availableItem.provider === 'Google'"
            :src="GoogleSvgIcon"
            class="w-5 min-w-5 h-5 min-h-5"
            alt="Google Icon"
          />
          <img
            v-if="availableItem.provider === 'Web'"
            :src="WebsiteIcon"
            class="w-5 min-w-5 h-5 min-h-5"
            alt="Web Icon"
          />
        </template>
        <img
          v-else
          cwidth="20"
          height="20"
          :src="availableItem.profilePic"
          :alt="`${availableItem.name} Profile Picture`"
        />
      </div>
      <p class="whitespace-nowrap text-[#525252] font-semibold text-lg">
        {{ availableItem.name }}
      </p>
    </div>
    <p class="line-clamp-1">{{ availableItem.username }}</p>
  </div>
</template>

<style lang="scss" scoped>
.twitter-icon {
  background-size: cover;
  animation: twitter 6s ease-in-out;
  animation-iteration-count: infinite;
}
@keyframes twitter {
  0% {
    background-image: url('~/assets/img/icon/TwitterIcon/twitter.svg');
  }
  50% {
    background-image: url('~/assets/img/icon/TwitterIcon/X_logo.png');
  }
  100% {
    background-image: url('~/assets/img/icon/TwitterIcon/twitter.svg');
  }
}
</style>
