<script setup lang="ts">
interface Tab {
  name: string
}

defineProps<{
  tabs: Tab[]
}>()

const model = defineModel<string>({ required: true })
</script>

<template>
  <div
    class="w-full h-12 flex items-center justify-start px-6 py-[13px] border-b space-x-6"
  >
    <button
      v-for="tab in tabs"
      :key="tab.name"
      @click="model = tab.name"
      class="font-semibold text-base leading-[21px]"
      :class="[model === tab.name ? 'text-[#525252]' : 'text-[#C2C2C2]']"
    >
      {{ tab.name }}
    </button>
  </div>
</template>

<style scoped></style>
