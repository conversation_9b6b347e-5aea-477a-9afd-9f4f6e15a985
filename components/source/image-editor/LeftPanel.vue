<script setup lang="ts">
import AdjustmentIcon from '~/components/shared/icon/editor/Adjustment.vue'
import ElementsIcon from '~/components/shared/icon/editor/Elements.vue'
import FilterIcon from '~/components/shared/icon/editor/Filters.vue'
import MediaIcon from '~/components/shared/icon/editor/Media.vue'
import StickersIcon from '~/components/shared/icon/editor/Stickers.vue'
// import TemplatesIcon from '~/components/shared/icon/editor/Templates.vue'
import TextIcon from '~/components/shared/icon/editor/Text.vue'

const menues = [
  {
    name: 'Media',
    icon: MediaIcon,
  },
  {
    name: 'Filters',
    icon: FilterIcon,
  },
  {
    name: 'Adjustment',
    icon: AdjustmentIcon,
  },
  {
    name: 'Text',
    icon: TextIcon,
  },
  {
    name: 'Elements',
    icon: ElementsIcon,
  },
  {
    name: 'Stickers',
    icon: StickersIcon,
  },
  // {
  //   name: 'Templates',
  //   icon: TemplatesIcon,
  // },
]
const model = defineModel<string>({ default: 'Media' })

const handleSelectMenu = (menu: string) => {
  model.value = menu
}
</script>

<template>
  <ul
    class="flex flex-col space-y-4 px-6 py-4 border-r overflow-y-auto custom-scroll"
  >
    <li
      v-for="menu in menues"
      :key="menu.name"
      class="flex items-center space-x-4 px-4 py-3 cursor-pointer text-base rounded-lg hover:bg-[#4A71D4] hover:text-white transition-all duration-200 ease-in-out"
      :class="[
        model === menu.name
          ? 'bg-[#4A71D4] text-white'
          : 'bg-[#EBEDF5] text-[#525252] ',
      ]"
      @click="handleSelectMenu(menu.name)"
    >
      <Component :is="menu.icon" class="size-5" />
      <span class="">{{ menu.name }}</span>
    </li>
  </ul>
</template>

<style scoped></style>
