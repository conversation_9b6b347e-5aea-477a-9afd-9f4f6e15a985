<script setup lang="ts">
import Adjustment from './adjustment/Adjustment.vue'
import Elements from './elements/Elements.vue'
import Filters from './filters/Filters.vue'
import Media from './media/Media.vue'
import Stickers from './stickers/Stickers.vue'
import Templates from './templates/Templates.vue'
import Text from './text/Text.vue'

interface ComponentsMap {
  [key: string]: ReturnType<typeof defineComponent>
}

defineProps<{
  currentMenu: string
}>()

const componentsMap: ComponentsMap = {
  Media,
  Text,
  Adjustment,
  Elements,
  Filters,
  Stickers,
  Templates,
}
</script>

<template>
  <div class="size-full flex flex-col border-l overflow-y-auto custom-scroll">
    <Transition name="page" mode="out-in">
      <Component :is="componentsMap[currentMenu]" />
    </Transition>
  </div>
</template>

<style scoped></style>
