<script setup lang="ts">
import { defineProps } from 'vue'
import { useImage } from 'vue-konva'
import type { VImageConfig } from '~/types/imageEditor'

const { handleStickerTransformEnd, handleStickerDragEnd, handleStickerClick } =
  useSharedImageEditor()

interface Props {
  config: VImageConfig
}

const props = defineProps<Props>()

const [editorImage, imageStatus]: readonly [
  Ref<HTMLImageElement | null>,
  Ref<'error' | 'loading' | 'loaded'>,
] = useImage(props.config.src || '', 'Anonymous')

const newConfig = computed(() => {
  return {
    ...props.config,
    image: editorImage.value,
  }
})
</script>

<template>
  <v-image
    :config="newConfig"
    @click="handleStickerClick(config)"
    @dragend="handleStickerDragEnd(config.id)"
    @transformend="handleStickerTransformEnd(config.id)"
  />
</template>

<style scoped></style>
