<script setup lang="ts">
import type Konva from 'konva'
import ColorPickerModal from '../ColorPickerModal.vue'

const {
  group,
  layer,
  history,
  textItems,
  selectedText,
  selectedSticker,
  selectedElement,
  handleTextClick,
  transformerTextRef,
} = useSharedImageEditor()

// Available fonts
const fontFamilies = [
  'Arial',
  'Helvetica',
  'Times New Roman',
  'Courier New',
  'Georgia',
  'Verdana',
]

const addNewText = () => {
  if (!group.value || !history.value.present) return
  const groupNode = group.value.getNode()
  const box = groupNode.getClientRect()
  const groupWidth = box.width
  const groupHeight = box.height
  const newText: Konva.TextConfig = {
    id: `text_${Date.now()}`,
    text: 'Click to edit',
    x: groupWidth / 2,
    y: groupHeight / 2,
    fontSize: 20,
    fontFamily: 'Arial',
    fill: '#FFFFFF',
    draggable: true,
    opacity: 1,
    rotation: 0,
  }
  textItems.value.push(newText)
  selectedElement.value = null
  selectedSticker.value = null
  handleTextClick(newText)
}
const deleteSelectedText = () => {
  if (!selectedText.value) return
  const index = textItems.value.findIndex(
    (item) => item.id === selectedText.value?.id,
  )
  if (index !== -1) {
    textItems.value = textItems.value.filter(
      (item) => item.id !== selectedText.value?.id,
    )
    selectedText.value = null
    transformerTextRef.value?.getNode().nodes([])
    layer.value?.getNode().batchDraw()
  }
}
// @click="updateTextProperty('fill', color)"
const updateTextProperty = (
  property: keyof Konva.TextConfig,
  value: Konva.TextConfig[typeof property],
) => {
  if (!selectedText.value) return
  const index = textItems.value.findIndex(
    (item) => item.id === selectedText.value?.id,
  )
  if (index !== -1) {
    const oldValue = textItems.value[index][property]
    if (oldValue !== value) {
      textItems.value[index] = {
        ...textItems.value[index],
        [property]: value,
      }
      selectedText.value = textItems.value[index]
      // saveTextToHistory()
    }
  }
}
</script>

<template>
  <div class="flex flex-col size-full p-4">
    <div class="w-full h-full flex flex-col">
      <p class="text-base font-semibold text-[#525252] leading-[21px] pb-2">
        Insert Text
      </p>

      <div class="flex flex-col gap-4 mt-2">
        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <h3 class="font-medium text-lg">Text</h3>
            <button
              @click="addNewText"
              class="bg-[#4A71D4] text-white px-3 py-1 rounded-lg text-sm"
            >
              + Add Text
            </button>
          </div>

          <div v-if="selectedText" class="space-y-4">
            <div class="space-y-2">
              <label class="text-sm text-gray-600">Text Content</label>
              <input
                type="text"
                v-model="selectedText.text"
                placeholder="Input text"
                class="w-full px-3 py-2 border rounded-lg outline-none focus:ring-1"
              />
            </div>

            <div
              v-if="typeof selectedText.fill === 'string'"
              class="flex items-center justify-between"
            >
              <label class="text-sm text-gray-600">Color</label>
              <ColorPickerModal v-model="selectedText.fill" class="size-6" />
            </div>

            <div class="space-y-2">
              <label class="text-sm text-gray-600">Font Size</label>
              <BaseRangeSlider
                v-model="selectedText.fontSize"
                :min="8"
                :max="100"
                :thumbWidth="8"
                :thumbHeight="20"
                :trackHeight="5"
                thumbColor="#4A71D4"
                thumbBorderRadius="12px"
                trackActiveColor="#4A71D4"
                trackInactiveColor="#D6E7FF"
                class="w-full"
              />
              <div class="flex justify-between text-xs text-gray-600">
                <span>8px</span>
                <span>{{ selectedText.fontSize?.toFixed(2) }}px</span>
                <span>100px</span>
              </div>
            </div>

            <div class="space-y-2">
              <label class="text-sm text-gray-600">Font Family</label>
              <select
                v-model="selectedText.fontFamily"
                class="w-full px-3 py-2 border-none outline-none rounded-full bg-[#4A71D4] text-white"
              >
                <option v-for="font in fontFamilies" :key="font" :value="font">
                  {{ font }}
                </option>
              </select>
            </div>

            <!-- <div class="space-y-2">
              <label class="text-sm text-gray-600">Rotation</label>
              <BaseRangeSlider
                v-model="selectedText.rotation"
                :min="0"
                :max="360"
                :thumbWidth="8"
                :thumbHeight="20"
                :trackHeight="5"
                thumbColor="#4A71D4"
                thumbBorderRadius="12px"
                trackActiveColor="#4A71D4"
                trackInactiveColor="#D6E7FF"
                class="w-full"
              />
              <div class="flex justify-between text-xs text-gray-600">
                <span>0°</span>
                <span>{{ selectedText.rotation }}°</span>
                <span>360°</span>
              </div>
            </div> -->

            <button
              @click="deleteSelectedText"
              class="bg-red-100 text-red-600 px-3 py-2 rounded-lg text-sm w-full mt-4"
            >
              Delete Text
            </button>
          </div>

          <div
            v-else
            class="flex flex-col items-center justify-center py-10 text-center text-gray-500"
          >
            <span class="material-icons text-4xl mb-2">text_fields</span>
            <p>Click the "Add Text" button to add text to your image</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
