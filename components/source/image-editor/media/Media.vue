<script setup lang="ts">
import { useStore } from 'vuex'
import type { AiImage } from '~/types/savePostSettings'
import RightPanelTab from '../RightPanelTab.vue'
import MediaCard from './MediaCard.vue'

const tabs = [
  {
    name: 'Uploads',
  },
  {
    name: 'Saved Pictures',
  },
]

const store = useStore()

const { selectedImages } = useGeneratePost()

const editorSavedImages = computed<AiImage[]>(
  () => store.state.createPost.editorSavedImages,
)

const currentTab = ref('Uploads')
</script>

<template>
  <div class="flex flex-col size-full">
    <RightPanelTab :tabs="tabs" v-model="currentTab" />
    <div class="w-full flex-grow p-4">
      <p class="text-base font-semibold text-[#525252] leading-[21px] pb-4">
        Images
      </p>
      <Transition name="page" mode="out-in">
        <div
          v-if="currentTab === 'Uploads' && selectedImages.length > 0"
          class="grid grid-cols-3 gap-2 m-xl:gap-4"
        >
          <MediaCard
            v-for="image in selectedImages"
            :key="image.id"
            :image="image"
          />
        </div>
        <div
          v-else-if="
            currentTab === 'Saved Pictures' && editorSavedImages.length > 0
          "
          class="grid grid-cols-3 gap-4"
        >
          <MediaCard
            v-for="image in editorSavedImages"
            :key="image.id"
            :image="image"
          />
        </div>
        <div
          v-else
          class="w-full h-[calc(100%-22px)] flex justify-center items-center"
        >
          <p class="text-base font-semibold text-[#525252] leading-[21px]">
            No images found
          </p>
        </div>
      </Transition>
    </div>
  </div>
</template>
