<script setup lang="ts">
import { useStore } from 'vuex'
import type { AiImage } from '~/types/savePostSettings'

const props = defineProps<{
  image: AiImage
}>()

const { setImageEditor } = useGeneratePost()
const { image } = toRefs(props)
const store = useStore()

const imageName = computed(() => {
  if (image.value.url.startsWith('data:image')) {
    return 'Base64 Image'
  }

  try {
    const url = new URL(image.value.url)
    const pathname = url.pathname
    const fileName = pathname.substring(pathname.lastIndexOf('/') + 1)

    return fileName.split('?')[0]
  } catch (error) {
    console.error('Invalid URL:', error)
    return 'Unknown Image'
  }
})
const setEditorImage = () => {
  const editorImage = {
    image: image.value,
  }
  setImageEditor(editorImage)
}
</script>
<template>
  <div class="flex flex-col gap-2">
    <img
      :src="image.url"
      :alt="imageName"
      class="w-full h-auto aspect-[104/140] object-cover rounded-lg cursor-pointer"
      @click="setEditorImage"
    />
    <p class="text-xs text-[#707070] leading-4 truncate">
      {{ imageName }}
    </p>
  </div>
</template>

<style scoped></style>
