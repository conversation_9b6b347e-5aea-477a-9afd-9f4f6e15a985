<template>
  <div ref="dropdownRef" class="relative min-w-[160px]">
    <button
      @click="toggleDropdown"
      class="flex items-center justify-between space-x-4 text-[#707070] text-xs font-semibold px-2 w-full"
    >
      <p class="truncate text-left pr-2">
        <span v-if="displayText">{{ displayText }}</span>
        <span v-else>Select a size</span>
      </p>
      <ClientOnly>
        <fa class="text-sm" :icon="['fas', 'chevron-down']" />
      </ClientOnly>
    </button>

    <!-- Dropdown menu -->
    <div
      v-if="isOpen"
      class="absolute right-0 z-10 mt-1 rounded-lg h-auto overflow-hidden shadow-2xl"
    >
      <div
        class="py-2 rounded-lg overflow-y-auto w-[240px] max-h-[260px] bg-white text-[#525252] custom-scroll"
      >
        <div
          v-for="option in sizes"
          :key="getOptionId(option)"
          class="flex items-center px-4 py-2 hover:bg-[#F1F2F6] cursor-pointer"
          :class="[model?.id === option.id ? 'bg-[#F1F2F6]' : '']"
          @click="selectOption(option)"
        >
          <span>{{ getOptionLabel(option) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { SizeOption } from '~/types/imageEditor'

const sizes: SizeOption[] = [
  {
    id: 0,
    name: 'Original Ratio',
    ratio: 'x:y',
  },
  // Instagram
  {
    id: 1,
    name: 'Instagram Post',
    width: 1080,
    height: 1080,
    ratio: '1:1',
  },
  {
    id: 2,
    name: 'Instagram Post',
    width: 1080,
    height: 1350,
    ratio: '4:5',
  },
  {
    id: 3,
    name: 'Instagram Post',
    width: 1080,
    height: 566,
    ratio: '1.91:1',
  },
  {
    id: 4,
    name: 'Instagram Post',
    width: 1080,
    height: 1920,
    ratio: '9:16',
  },
  // Facebook
  {
    id: 5,
    name: 'Facebook Post',
    width: 1200,
    height: 1200,
    ratio: '1:1',
  },
  {
    id: 6,
    name: 'Facebook Post',
    width: 1200,
    height: 630,
    ratio: '1.91:1',
  },
  {
    id: 7,
    name: 'Facebook Cover',
    width: 820,
    height: 312,
    ratio: '2.63:1',
  },
  {
    id: 8,
    name: 'Facebook Story',
    width: 1080,
    height: 1920,
    ratio: '9:16',
  },
  // Twitter (X)
  {
    id: 9,
    name: 'Twitter Post',
    width: 1200,
    height: 675,
    ratio: '16:9',
  },
  {
    id: 10,
    name: 'Twitter Header',
    width: 1500,
    height: 500,
    ratio: '3:1',
  },
  // LinkedIn
  {
    id: 11,
    name: 'LinkedIn Post',
    width: 1200,
    height: 627,
    ratio: '1.91:1',
  },
  {
    id: 12,
    name: 'LinkedIn Cover',
    width: 1584,
    height: 396,
    ratio: '4:1',
  },
  // Pinterest
  {
    id: 13,
    name: 'Pinterest Pin',
    width: 1000,
    height: 1500,
    ratio: '2:3',
  },
  // YouTube
  {
    id: 15,
    name: 'YouTube Thumbnail',
    width: 1280,
    height: 720,
    ratio: '16:9',
  },
  // General
  {
    id: 17,
    name: 'Full HD',
    width: 1920,
    height: 1080,
    ratio: '16:9',
  },
]

const isOpen = ref(false)
const dropdownRef = ref<HTMLDivElement | null>(null)
const model = defineModel<SizeOption | null>({ default: null })

const getOptionId = (option: SizeOption): string => {
  return String(option.id)
}

const getOptionLabel = (option: SizeOption): string => {
  return `${option.name} [${option.ratio}]`
}

const displayText = computed(() => {
  return model.value ? getOptionLabel(model.value) : ''
})

const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

const selectOption = (option: SizeOption): void => {
  model.value = option
  isOpen.value = false
}

const handleClickOutside = (event: MouseEvent): void => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped lang="scss"></style>
