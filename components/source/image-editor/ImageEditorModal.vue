<script setup lang="ts">
import type { ImageEditorMainPanel } from '~/types/imageEditor'
import LeftPanel from './LeftPanel.vue'
import MainPanel from './MainPanel.vue'
import RightPanel from './RightPanel.vue'

const { setImageEditor } = useGeneratePost()
const { imageEditingDone } = useSharedImageEditor()

const selectedMenu = ref('Media')
const mainPanelRef = ref<ImageEditorMainPanel | null>(null)

const closeModal = () => {
  setImageEditor({ isOpen: false })
}

const handleDoneImageEdit = () => {
  imageEditingDone()
  closeModal()
}
</script>

<template>
  <div class="absolute inset-0 rounded-2xl p-4 z-[9999] size-full">
    <div
      class="flex flex-col rounded-2xl size-full bg-white shadow-[4px_4px_16px_#0000002E] z-[9999]"
    >
      <div class="flex items-center justify-end py-3.5 px-6 border-b">
        <button
          @click="closeModal"
          class="size-4 rounded-full flex justify-center items-center"
        >
          <ClientOnly>
            <fa class="text-2xl text-[#525252]" :icon="['fas', 'times']" />
          </ClientOnly>
        </button>
      </div>
      <div
        class="w-full h-full max-h-[calc(100%-113px)] grid-cols-[200px_1fr_260px] grid m-xl:grid-cols-[224px_1fr_360px] overflow-hidden"
      >
        <LeftPanel v-model="selectedMenu" />
        <ClientOnly>
          <MainPanel ref="mainPanelRef" />
        </ClientOnly>
        <RightPanel :currentMenu="selectedMenu" />
      </div>
      <div class="flex justify-end p-4 border-t space-x-4">
        <button
          @click="closeModal"
          class="bg-white text-[#4A71D4] border-[1.5px] border-[#4A71D4] w-26 h-[35px] rounded-full font-semibold"
        >
          Cancel
        </button>
        <button
          @click="handleDoneImageEdit"
          class="bg-[#4A71D4] text-white w-26 h-[35px] rounded-full font-semibold"
        >
          Done
        </button>
      </div>
    </div>
  </div>
</template>
