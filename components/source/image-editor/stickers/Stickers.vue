<script setup lang="ts">
import { Notomoji } from '@svgmoji/noto'
import type { FlatEmoji } from 'svgmoji'
import data from 'svgmoji/emoji.json'
import DeleteIcon from '~/components/shared/icon/editor/Delete.vue'
import type { VImageConfig } from '~/types/imageEditor'

const notomoji = new Notomoji({ data, type: 'group' })

const groupNames: Record<number | 'ungrouped', string> = {
  0: 'Smileys & Emotion',
  1: 'People & Body',
  2: 'Animals & Nature',
  3: 'Food & Drink',
  4: 'Travel & Places',
  5: 'Activities',
  6: 'Objects',
  7: 'Symbols',
  8: 'Flags',
  9: 'Components',
  ungrouped: 'Ungrouped',
}
const emojiByGroup = computed(() => {
  const groups: Record<string, any[]> = {}

  for (const emoji of Object.values(notomoji.data)) {
    const groupKey = typeof emoji.group === 'number' ? emoji.group : 'ungrouped'
    const groupName = groupNames[groupKey] ?? `Group ${groupKey}`

    if (!groups[groupName]) {
      groups[groupName] = []
    }

    groups[groupName].push(emoji)
  }

  return groups
})
console.log('Notomoji group:', emojiByGroup.value)

const {
  stickers,
  group,
  layer,
  history,
  selectedText,
  selectedElement,
  selectedSticker,
  transformerStickerRef,
  handleStickerClick,
} = useSharedImageEditor()

const loadRemoteImage = async (url: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new window.Image()
    img.crossOrigin = 'Anonymous'
    img.onload = () => resolve(img)
    img.onerror = reject
    img.src = url
  })
}

const handleSelectEmoji = async (emoji: FlatEmoji) => {
  if (!group.value || !history.value.present) return
  const groupNode = group.value.getNode()
  const box = groupNode.getClientRect()
  const groupWidth = box.width
  const groupHeight = box.height
  // const image = await loadRemoteImage(notomoji.url(emoji.emoji))

  const newEmoji: VImageConfig = {
    id: `shape_${Date.now()}`,
    x: groupWidth / 2,
    y: groupHeight / 2,
    width: 200,
    height: 200,
    image: undefined,
    src: notomoji.url(emoji.emoji),
    stroke: '#ffffff',
    strokeEnabled: false,
    strokeWidth: 2,
    draggable: true,
    opacity: 1,
  }
  stickers.value.push(newEmoji)
  selectedText.value = null
  selectedElement.value = null
  handleStickerClick(newEmoji)
}

const handleDeleteSelected = () => {
  if (!selectedSticker.value) return
  const index = stickers.value.findIndex(
    (item) => item.id === selectedSticker.value?.id,
  )
  if (index !== -1) {
    stickers.value.splice(index, 1)
    selectedSticker.value = null
    transformerStickerRef.value?.getNode().moveToTop()
    layer.value?.getNode().batchDraw()
  }
}
</script>

<template>
  <div class="flex flex-col size-full p-4">
    <h2 class="text-lg font-semibold mb-4">Stickers</h2>
    <ul v-if="selectedSticker" class="space-y-3 mb-5">
      <li class="flex flex-col">
        <label class="text-base">Opacity</label>
        <BaseRangeSlider
          v-model="selectedSticker.opacity"
          :min="0"
          :max="1"
          :step="0.01"
          :thumbWidth="8"
          :thumbHeight="20"
          :trackHeight="5"
          thumbColor="#4A71D4"
          thumbBorderRadius="12px"
          trackActiveColor="#4A71D4"
          trackInactiveColor="#D6E7FF"
          class="w-full"
        />
        <div class="flex justify-between text-xs text-gray-600">
          <span>0</span>
          <span>{{
            selectedSticker.opacity
              ? (selectedSticker.opacity * 100).toFixed(0)
              : 0
          }}</span>
          <span>100</span>
        </div>
      </li>
      <li class="flex items-center justify-between">
        <label class="text-base">Delete selected</label>
        <button
          class="action-button text-[#E21F3F]"
          @click="handleDeleteSelected"
        >
          <DeleteIcon class="w-4" />
        </button>
      </li>
    </ul>
    <div class="w-full flex-grow">
      <p class="text-lg font-semibold mb-4">Emoji</p>
      <div class="grid grid-cols-3 gap-3">
        <div
          v-for="emoji in notomoji.popularEmoji"
          :key="emoji.hexcode"
          :title="emoji.annotation"
          class="flex justify-center items-center p-3 border rounded hover:bg-gray-100 cursor-pointer"
          @click="handleSelectEmoji(emoji)"
        >
          <img
            :src="notomoji.url(emoji.hexcode)"
            :alt="emoji.annotation"
            class="w-full h-auto aspect-square"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
.action-button {
  @apply w-8 h-8 flex justify-center items-center rounded-full hover:bg-[#F1F2F6];
}
</style>
