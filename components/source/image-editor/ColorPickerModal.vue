<script setup lang="ts">
const emit = defineEmits<{
  (event: 'change', value: string): void
}>()

const isOpen = ref(false)
const colorPickerRef = ref<HTMLDivElement | null>(null)
const model = defineModel<string>({ default: '#FFFFFF' })

const applyColor = (color: string) => {
  model.value = color
  emit('change', color)
}

const toggleColorPicker = () => {
  isOpen.value = !isOpen.value
}

const handleClickOutside = (event: MouseEvent): void => {
  if (
    colorPickerRef.value &&
    !colorPickerRef.value.contains(event.target as Node)
  ) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div ref="colorPickerRef" class="relative z-10">
    <button
      class="size-full border rounded"
      :style="{ backgroundColor: model }"
      @click="toggleColorPicker"
    ></button>
    <div
      v-if="isOpen"
      class="absolute top-10 right-0 w-max bg-white/90 rounded p-1"
    >
      <SourceColorPicker
        title=""
        :selectedColorCode="model"
        :applyColor="applyColor"
      />
      <div class="flex items-center justify-between">
        <span class="text-xs">More colors</span>
        <input type="color" v-model="model" @change="applyColor(model)" />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
