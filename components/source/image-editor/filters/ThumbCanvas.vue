<script setup lang="ts">
import Konva from 'konva'

const props = defineProps<{
  editorImage: any
  filter: any
}>()

const imageNode = ref()

const getFilterConfig = (filterId: string) => {
  const fixedPercentage = 50

  switch (filterId) {
    case 'grayscale':
      return { grayscale: fixedPercentage / 100 }
    case 'sepia':
      return { sepia: fixedPercentage / 100 }
    case 'invert':
      return { invert: fixedPercentage / 100 }
    case 'blur':
      return { blur: fixedPercentage / 10 } // Reduced for thumbnails
    case 'brighten':
      return { brightness: 0.5 }
    case 'contrast':
      return { contrast: 1 + fixedPercentage / 100 }
    case 'hue':
      return { hue: fixedPercentage * 3.6 } // 0-360 degrees
    case 'pixelate':
      return { pixelSize: fixedPercentage / 10 }
    case 'threshold':
      return { threshold: fixedPercentage / 100 }
    case 'noise':
      return { noise: fixedPercentage / 100 }
    case 'solarize':
      return { solarize: true, threshold: fixedPercentage / 100 }
    case 'emboss':
      return { emboss: true, embossStrength: fixedPercentage / 100 }
    case 'kaleidoscope':
      return {
        kaleidoscope: true,
        kaleidoscopePower: Math.max(2, Math.floor(fixedPercentage / 10)),
      }
    case 'vintage':
      return {
        brightness: fixedPercentage / 100,
        saturation: fixedPercentage / 100,
        hue: fixedPercentage / 100,
      }
    case 'gloomy':
      return {
        brightness: 1 - fixedPercentage / 200,
        contrast: 1 - fixedPercentage / 200,
        saturation: 1 - fixedPercentage / 150,
      }
    case 'cool':
      return {
        colorAdjust: {
          blue: 1 + fixedPercentage / 200,
          red: 1 - fixedPercentage / 200,
        },
      }
    case 'warm':
      return {
        colorAdjust: {
          red: 1 + fixedPercentage / 200,
          blue: 1 - fixedPercentage / 200,
        },
      }
    default:
      return {}
  }
}

const filterInstance = computed(() => {
  if (props.filter.id === 'none') return null

  const filterMap: Record<string, keyof typeof Konva.Filters> = {
    grayscale: 'Grayscale',
    sepia: 'Sepia',
    invert: 'Invert',
    blur: 'Blur',
    brighten: 'Brighten',
    contrast: 'Contrast',
    hue: 'HSV',
    pixelate: 'Pixelate',
    threshold: 'Threshold',
    noise: 'Noise',
    solarize: 'Solarize',
    emboss: 'Emboss',
    kaleidoscope: 'Kaleidoscope',
    saturation: 'HSV',
  }

  const filterName = filterMap[props.filter.id]
  return filterName ? Konva.Filters[filterName] : null
})

onMounted(async () => {
  await nextTick()
  if (props.editorImage && imageNode.value) {
    imageNode.value?.getNode().cache()
  }
})
</script>

<template>
  <v-stage
    :config="{
      width: 70,
      height: 70,
    }"
  >
    <v-layer>
      <v-image
        ref="imageNode"
        v-if="editorImage"
        :config="{
          image: editorImage,
          draggable: false,
          width: 70,
          height: 70,
          filters: filterInstance ? [filterInstance] : [],
          ...getFilterConfig(filter.id),
        }"
      />
    </v-layer>
  </v-stage>
</template>
