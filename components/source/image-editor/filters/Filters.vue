<script setup lang="ts">
import { useStore } from 'vuex'
import RightPanelTab from '../RightPanelTab.vue'
import ThumbCanvas from './ThumbCanvas.vue'

const { currentFilter, filterPercentage, applyFilter, editorImage } =
  useSharedImageEditor()

const tabs = [{ name: 'Properties' }, { name: 'Layers' }]

const filters = [
  { id: 'none', name: 'No Filter' },
  { id: 'grayscale', name: 'Gray<PERSON><PERSON>' },
  { id: 'sepia', name: '<PERSON><PERSON>' },
  { id: 'invert', name: 'Invert' },
  { id: 'blur', name: 'Blur' },
  { id: 'brighten', name: 'Bright<PERSON>' },
  { id: 'contrast', name: '<PERSON><PERSON><PERSON>' },
  { id: 'hue', name: '<PERSON>e Shift' },
  { id: 'pixelate', name: 'Pixelate' },
  { id: 'threshold', name: 'Threshold' },
  { id: 'noise', name: 'Noise' },
  { id: 'solarize', name: 'Solarize' },
  { id: 'saturation', name: 'Saturation' },
  { id: 'emboss', name: '<PERSON>bos<PERSON>' },
  { id: 'kaleidoscope', name: 'Kaleido<PERSON>' },
  { id: 'vintage', name: 'Vintage' },
  { id: 'gloomy', name: 'Gloomy' },
  { id: 'cool', name: 'Cool Tone' },
  { id: 'warm', name: 'Warm Tone' },
]

const store = useStore()
const currentTab = ref('Layers')

const filterRows = computed(() => {
  const rows = []
  for (let i = 0; i < filters.length; i += 4) {
    rows.push(filters.slice(i, i + 4))
  }
  return rows
})

const selectedRowIndex = computed(() => {
  const index = filterRows.value.findIndex((row) =>
    row.some((filter) => filter.id === currentFilter.value),
  )
  return index
})

const handleSelectFilter = (filterId: string) => {
  currentFilter.value = filterId
}
</script>

<template>
  <div class="flex flex-col size-full">
    <RightPanelTab :tabs="tabs" v-model="currentTab" />
    <div class="w-full flex-grow p-4">
      <Transition name="page" mode="out-in">
        <div
          v-if="currentTab === 'Properties'"
          class="w-full h-full flex flex-col"
        >
          <p class="text-base font-semibold text-[#525252] leading-[21px] pb-2">
            Properties
          </p>
          <div class="w-full h-full grid grid-cols-3 gap-2 m-xl:gap-4"></div>
        </div>
        <div
          v-else-if="currentTab === 'Layers'"
          class="w-full h-full flex flex-col"
        >
          <p class="text-base font-semibold text-[#525252] leading-[21px] pb-2">
            Layers
          </p>

          <div class="flex flex-col gap-4">
            <template v-for="(row, rowIndex) in filterRows" :key="rowIndex">
              <div class="grid grid-cols-4 gap-2 m-xl:gap-4">
                <div
                  v-for="filter in row"
                  :key="filter.id"
                  class="w-full"
                  @click="handleSelectFilter(filter.id)"
                >
                  <div
                    class="size-[50px] m-xl:size-[70px] rounded-[4px] cursor-pointer transition-all duration-300 ease-in-out overflow-hidden"
                    :class="[
                      currentFilter === filter.id
                        ? 'border-4 border-[#4A71D4]'
                        : 'border-4 border-transparent',
                      filter.id === 'none'
                        ? 'bg-[#E1E9FF] relative'
                        : 'bg-gray-300',
                    ]"
                  >
                    <SharedIconNo
                      v-if="filter.id === 'none'"
                      class="w-5 text-[#9fbfde] absolute inset-0 m-auto"
                    />
                    <ThumbCanvas
                      v-else
                      :editorImage="editorImage"
                      :filter="filter"
                    />
                  </div>
                  <p class="pt-2 text-xs text-[#707070] truncate">
                    {{ filter.name }}
                  </p>
                </div>
              </div>

              <div
                v-if="selectedRowIndex === rowIndex && currentFilter !== 'none'"
                class="w-full flex items-center space-x-4 mb-3"
              >
                <span class="text-sm text-[#525252]">Percentage</span>
                <BaseRangeSlider
                  v-model="filterPercentage"
                  :id="rowIndex"
                  :min="0"
                  :max="100"
                  :thumbWidth="8"
                  :thumbHeight="20"
                  :trackHeight="4"
                  thumbColor="#4A71D4"
                  thumbBorderRadius="12px"
                  trackActiveColor="#4A71D4"
                  trackInactiveColor="#D6E7FF"
                  class="w-full"
                  :disabled="false"
                  @change="applyFilter()"
                />
                <div
                  class="h-[25px] min-w-[58px] bg-[#EBEDF5] rounded-full flex justify-center items-center"
                >
                  <span class="text-sm text-[#525252]"
                    >{{ filterPercentage }}%</span
                  >
                </div>
              </div>
            </template>
          </div>
        </div>
      </Transition>
    </div>
  </div>
</template>
