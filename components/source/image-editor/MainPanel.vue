<script setup lang="ts">
import type Konva from 'konva'
import { useStore } from 'vuex'
import CropIcon from '~/components/shared/icon/editor/Crop.vue'
import DeleteIcon from '~/components/shared/icon/editor/Delete.vue'
import DownloadIcon from '~/components/shared/icon/editor/Download.vue'
import FlipIcon from '~/components/shared/icon/editor/Flip.vue'
import PreviewIcon from '~/components/shared/icon/editor/Preview.vue'
import RedoIcon from '~/components/shared/icon/editor/Redo.vue'
import RotateIcon from '~/components/shared/icon/editor/Rotate.vue'
import UndoIcon from '~/components/shared/icon/editor/Undo.vue'
import UploadIcon from '~/components/shared/icon/editor/Upload.vue'
import ZoonInIcon from '~/components/shared/icon/editor/ZoomIn.vue'
import ZoomOutIcon from '~/components/shared/icon/editor/ZoomOut.vue'
import StickerImage from '~/components/source/image-editor/Canvas/StickerImage.vue'
import type { AiImage } from '~/types/savePostSettings'
import SizeDropdown from './SizeDropdown.vue'

const {
  MIN_ZOOM,
  MAX_ZOOM,
  canvasContainer,
  stage,
  layer,
  group,
  elements,
  stickers,
  konvaImage,
  zoomPercentage,
  isUndoDisabled,
  isRedoDisabled,
  isCroping,
  imageIsLoaded,
  editorImage,
  stageSize,
  textItems,
  selectedText,
  selectedElement,
  selectedSize,
  imageConfig,
  groupConfig,
  transformerTextRef,
  cropRectRef,
  transformerCropRef,
  transformerElementRef,
  cropRectConfig,
  transformerTextConfig,
  transformerCropConfig,
  transformerElementConfig,
  handleZoomIn,
  handleZoomOut,
  applyZoom,
  handleUndo,
  handleRedo,
  handleReset,
  handleRotate,
  handleFlip,
  handleCrop,
  handleCropDone,
  handlePreview,
  generatedImageData,
  base64ToBlob,
  handleTextClick,
  handleTextDragEnd,
  handleTextTransformEnd,
  handleElementDragEnd,
  handleElementTransformEnd,
  handleStageClick,
  updateCropTransformer,
  handleElementClick,
  cancelCrop,
  handleStickerClick,
  handleStickerDragEnd,
  handleStickerTransformEnd,
  selectedSticker,
  transformerStickerRef,
  transformerStickersConfig,
} = useSharedImageEditor()
const { addOrUpdateSelectedImage } = useGeneratePost()

const store = useStore()

const imageEditorimage = computed<AiImage | null>(
  () => store.state.createPost.imageEditor.image,
)

const handleDownload = (): void => {
  const result = generatedImageData()

  if (!result) return

  const { dataURL } = result

  // Create download link
  const link = document.createElement('a')
  link.download = 'edited-image.png'
  link.href = dataURL
  document.body.appendChild(link)
  link.click()

  // Clean up
  document.body.removeChild(link)
}
const saveImageToServer = () => {
  const result = generatedImageData()
  if (!result) return
  const { dataURL } = result
  const blob = base64ToBlob(dataURL)
  if (!blob) return

  const url = URL.createObjectURL(blob)
  store.commit('createPost/SET_OBJECT_URLS', url)

  if (imageEditorimage.value) {
    addOrUpdateSelectedImage({
      url: url,
      id: imageEditorimage.value.id,
    })
    store.commit('createPost/ADD_OR_UPDATE_EDITOR_SAVED_IMAGE', {
      url: url,
      id: imageEditorimage.value.id,
    })
  }
}

const handleDelete = () => {
  if (!imageEditorimage.value) return
  store.commit('createPost/DELETE_EDITOR_IMAGE', imageEditorimage.value.id)
}
</script>

<template>
  <div class="flex flex-col size-full">
    <div class="w-full h-12 flex items-center justify-between px-2">
      <div class="flex items-center space-x-2">
        <button
          :disabled="isUndoDisabled"
          class="action-button text-[#525252]"
          :class="[isUndoDisabled ? 'opacity-50' : '']"
          @click="handleUndo"
        >
          <UndoIcon class="w-4" />
        </button>
        <button
          :disabled="isRedoDisabled"
          class="action-button text-[#525252]"
          :class="[isRedoDisabled ? 'opacity-50' : '']"
          @click="handleRedo"
        >
          <RedoIcon class="w-4" />
        </button>
        <button
          class="action-button text-[#525252]"
          @click="handleReset"
          :disabled="!imageIsLoaded"
          :class="[!imageIsLoaded ? 'opacity-50' : '']"
        >
          <fa class="w-4 text-base" :icon="['fas', 'repeat']" />
        </button>
        <button
          v-if="isCroping"
          class="action-button text-[#525252] text-2xl font-normal"
          @click="handleCropDone"
        >
          ✓
        </button>
        <button
          v-if="isCroping"
          class="action-button text-[#525252] text-xl font-normal"
          @click="cancelCrop"
        >
          ✗
        </button>
      </div>
      <div class="flex items-center space-x-2">
        <PreviewIcon @click="handlePreview" class="w-5 cursor-pointer" />
        <SizeDropdown v-model="selectedSize" />
      </div>
    </div>
    <div
      ref="canvasContainer"
      class="w-full h-[calc(100%-128px)] min-[1101px]:h-[calc(100%-96px)] bg-[#F1F2F6] flex justify-center items-center"
    >
      <!-- Konva stage when image is loaded -->
      <v-stage
        v-if="imageIsLoaded"
        ref="stage"
        :config="
          {
            width: stageSize.width - 4,
            height: stageSize.height - 4,
          } as Konva.StageConfig
        "
        @click="handleStageClick"
      >
        <v-layer ref="layer">
          <v-group ref="group" :config="groupConfig">
            <v-image
              ref="konvaImage"
              v-if="editorImage"
              :config="imageConfig"
            />
            <v-group v-if="elements.length">
              <v-path
                v-for="item in elements"
                :key="item.id"
                :config="item"
                @click="handleElementClick(item)"
                @dragend="handleElementDragEnd(item.id)"
                @transformend="handleElementTransformEnd(item.id)"
              />
            </v-group>
            <v-group v-if="stickers.length">
              <StickerImage
                v-for="item in stickers"
                :key="item.id"
                :config="item"
              />
            </v-group>
            <v-group v-if="textItems.length">
              <v-text
                v-for="item in textItems"
                :key="item.id"
                :config="item"
                @click="handleTextClick(item)"
                @dragend="handleTextDragEnd(item.id)"
                @transformend="handleTextTransformEnd(item.id)"
              />
            </v-group>
            <v-rect
              v-if="isCroping"
              ref="cropRectRef"
              :config="cropRectConfig"
              @dragmove="updateCropTransformer"
              @transform="updateCropTransformer"
              @transformend="updateCropTransformer"
            />
          </v-group>
          <v-transformer
            v-if="isCroping"
            ref="transformerCropRef"
            :config="transformerCropConfig"
          />
          <v-transformer
            v-if="selectedSticker"
            ref="transformerStickerRef"
            :config="transformerStickersConfig"
          />
          <v-transformer
            v-if="selectedText"
            ref="transformerTextRef"
            :config="transformerTextConfig"
          />
          <v-transformer
            v-if="selectedElement"
            ref="transformerElementRef"
            :config="transformerElementConfig"
          />
        </v-layer>
      </v-stage>
    </div>
    <div
      class="w-full h-auto min-[1101px]:h-12 flex items-center justify-center gap-x-9 py-2.5 px-2 min-[1101px]:justify-between flex-wrap"
    >
      <div class="flex items-center space-x-2">
        <button
          class="action-button text-[#525252]"
          @click="handleCrop"
          :disabled="!imageIsLoaded"
          :class="[!imageIsLoaded ? 'opacity-50' : '']"
        >
          <CropIcon class="w-4" />
        </button>
        <div class="w-px h-5 bg-[#707070]"></div>
        <button
          class="action-button text-[#525252]"
          @click="handleRotate"
          :disabled="!imageIsLoaded"
          :class="[!imageIsLoaded ? 'opacity-50' : '']"
        >
          <RotateIcon class="w-4" />
        </button>
        <div class="w-px h-5 bg-[#707070]"></div>
        <button
          class="action-button text-[#525252]"
          @click="handleFlip"
          :disabled="!imageIsLoaded"
          :class="[!imageIsLoaded ? 'opacity-50' : '']"
        >
          <FlipIcon class="w-4" />
        </button>
        <div class="w-px h-5 bg-[#707070]"></div>
        <button
          class="action-button text-[#E21F3F]"
          @click="handleDelete"
          :disabled="!imageIsLoaded"
          :class="[!imageIsLoaded ? 'opacity-50' : '']"
        >
          <DeleteIcon class="w-4" />
        </button>
      </div>
      <div class="flex items-center space-x-2">
        <div class="flex items-center space-x-2">
          <button
            :disabled="zoomPercentage <= MIN_ZOOM || !imageIsLoaded"
            class="action-button text-[#525252]"
            :class="[
              zoomPercentage <= MIN_ZOOM || !imageIsLoaded ? 'opacity-50' : '',
            ]"
            @click="handleZoomOut"
          >
            <ZoomOutIcon class="w-4" />
          </button>
          <BaseRangeSlider
            v-model="zoomPercentage"
            :min="MIN_ZOOM"
            :max="MAX_ZOOM"
            :thumbWidth="8"
            :thumbHeight="16"
            :trackHeight="4"
            thumbColor="#4A71D4"
            thumbBorderRadius="12px"
            trackActiveColor="#4A71D4"
            trackInactiveColor="#D6E7FF"
            class="w-[100px]"
            :disabled="!imageIsLoaded"
            @change="applyZoom"
          />
          <button
            :disabled="zoomPercentage >= MAX_ZOOM || !imageIsLoaded"
            class="action-button text-[#525252]"
            :class="[
              zoomPercentage >= MAX_ZOOM || !imageIsLoaded ? 'opacity-50' : '',
            ]"
            @click="handleZoomIn"
          >
            <ZoonInIcon class="w-4" />
          </button>
        </div>
        <div class="w-px h-5 bg-[#707070]"></div>
        <button
          class="action-button text-[#525252]"
          @click="handleDownload"
          :disabled="!imageIsLoaded"
          :class="[!imageIsLoaded ? 'opacity-50' : '']"
        >
          <DownloadIcon class="w-4" />
        </button>
        <div class="w-px h-5 bg-[#707070]"></div>
        <button
          :disabled="!imageIsLoaded"
          class="action-button text-[#525252]"
          :class="[!imageIsLoaded ? 'opacity-50' : '']"
          @click="saveImageToServer"
        >
          <UploadIcon class="w-4" />
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.action-button {
  @apply w-8 h-8 flex justify-center items-center rounded-full hover:bg-[#F1F2F6];
}
</style>
