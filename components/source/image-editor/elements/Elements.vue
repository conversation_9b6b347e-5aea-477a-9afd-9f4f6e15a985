<script setup lang="ts">
import type Konva from 'konva'
import type { SVGAttributes } from 'vue'
import DeleteIcon from '~/components/shared/icon/editor/Delete.vue'
import ColorPickerModal from '../ColorPickerModal.vue'

const shapes: SVGAttributes[] = [
  {
    id: '1',
    name: 'Circle',
    width: '480',
    height: '480',
    d: 'M240 480C372.548 480 480 372.548 480 240C480 107.452 372.548 0 240 0C107.452 0 0 107.452 0 240C0 372.548 107.452 480 240 480Z',
    fill: '#BFBFBF',
  },
  {
    id: '2',
    name: 'circle 8 in octogon layout',
    width: '480',
    height: '480',
    d: 'M437.3 158.3A99.5 99.5 0 0 0 321.6 42.8a99.5 99.5 0 0 0-163.4 0A99.5 99.5 0 0 0 42.7 158.3a99.5 99.5 0 0 0 0 163.4 99.5 99.5 0 0 0 115.6 115.6 99.5 99.5 0 0 0 163.4 0 99.5 99.5 0 0 0 115.5-115.6 99.5 99.5 0 0 0 0-163.4Z',
    fill: '#BFBFBF',
  },
  {
    id: '3',
    name: 'circle intersection in plus shape',
    width: '480',
    height: '480',
    d: 'M360 289.7c43.4 0 86.9-16.6 120-49.7a169.2 169.2 0 0 0-120-49.7 169.2 169.2 0 0 0 49.7-120c-46.9 0-89.3 19-120 49.7 0-43.4-16.6-86.9-49.7-120a169.2 169.2 0 0 0-49.7 120 169.2 169.2 0 0 0-120-49.7c0 46.8 19 89.3 49.7 120-43.4 0-86.9 16.6-120 49.7a169.2 169.2 0 0 0 120 49.7 169.2 169.2 0 0 0-49.7 120c46.8 0 89.3-19 120-49.7 0 43.4 16.6 86.9 49.7 120a169.2 169.2 0 0 0 49.7-120 169.2 169.2 0 0 0 120 49.7c0-46.9-19-89.3-49.7-120Z',
    fill: '#BFBFBF',
  },
  {
    id: '4',
    name: 'circle intersection in plus and X shape layout',
    width: '480',
    height: '480',
    d: 'M0 240a169.7 169.7 0 0 0 240 0 169.7 169.7 0 0 0-240 0ZM240 0a169.7 169.7 0 0 0 0 240 169.7 169.7 0 0 0 0-240ZM480 240a169.7 169.7 0 0 0-240 0 169.7 169.7 0 0 0 240 0ZM240 480a169.7 169.7 0 0 0 0-240 169.7 169.7 0 0 0 0 240Z',
    fill: '#BFBFBF',
  },
  {
    id: '5',
    name: 'circle intersection in X shape layout',
    width: '480',
    height: '480',
    d: 'M240 240A240 240 0 0 0 0 480a240 240 0 0 0 240-240ZM240 240A240 240 0 0 0 0 0a240 240 0 0 0 240 240ZM480 0a240 240 0 0 0-240 240A240 240 0 0 0 480 0ZM480 480a240 240 0 0 0-240-240 240 240 0 0 0 240 240Z',
    fill: '#BFBFBF',
  },
  {
    id: '6',
    name: 'circle large donut',
    width: '480',
    height: '480',
    d: 'M240 0a240 240 0 1 0 0 480 240 240 0 0 0 0-480Zm0 360a120 120 0 1 1 0-240 120 120 0 0 1 0 240Z',
    fill: '#BFBFBF',
  },
  {
    id: '7',
    name: 'circle with circle cutout',
    width: '480',
    height: '480',
    d: 'M240 0c-43.7 0-84.7 11.7-120 32.1a240 240 0 0 1 0 415.8A240 240 0 1 0 240 0Z',
    fill: '#BFBFBF',
  },
  {
    id: '8',
    name: 'cross 8 bulb shape',
    width: '480',
    height: '480',
    d: 'M211.5 154.6a30 30 0 0 0 57 0l15.5-46.7a46.4 46.4 0 1 0-88 0l15.5 46.7ZM268.5 325.4a30 30 0 0 0-57 0L196 372.1a46.4 46.4 0 1 0 88 0l-15.5-46.7ZM372.1 196l-46.7 15.5a30 30 0 0 0 0 57l46.7 15.5c29.8 10 60.9-12 61.1-43.3v-1.3a46.4 46.4 0 0 0-61-43.4ZM107.9 284l46.7-15.5a30 30 0 0 0 0-57L107.9 196a46.4 46.4 0 1 0 0 88.1ZM320.5 199.7l44-22a46.4 46.4 0 0 0 12.6-73.9l-.9-.9a46.4 46.4 0 0 0-74 12.5l-22 44a30 30 0 0 0 40.3 40.3ZM159.5 280.3l-44 22a46.4 46.4 0 0 0-12.5 74l.8.8a46.4 46.4 0 0 0 74-12.6l22-44a30 30 0 0 0-40.3-40.2ZM364.6 302.3l-44-22a30 30 0 0 0-40.3 40.2l22 44a46.4 46.4 0 0 0 73.9 12.6l.8-.9a46.4 46.4 0 0 0-12.4-74ZM115.4 177.7l44 22a30 30 0 0 0 40.3-40.2l-22-44a46.4 46.4 0 1 0-62.2 62.2Z',
    fill: '#BFBFBF',
  },
  {
    id: '9',
    name: 'cross round cone',
    width: '480',
    height: '480',
    d: 'm240 240 71.7-107.5A85.2 85.2 0 0 0 240.8 0h-1.6a85.2 85.2 0 0 0-70.9 132.5L240 240l-107.5-71.7A85.2 85.2 0 0 0 0 239.2v1.6c0 68 75.8 108.6 132.5 70.9L240 240l-71.7 107.5A85.2 85.2 0 0 0 239.2 480h1.6c68 0 108.6-75.8 70.9-132.5L240 240l107.5 71.7A85.2 85.2 0 0 0 480 240.8a85.2 85.2 0 0 0-132.5-72.5L240 240Z',
    fill: '#BFBFBF',
  },
  {
    id: '10',
    name: 'diamond large',
    width: '480',
    height: '480',
    d: 'M0 240 240 0l240 240-240 240z',
    fill: '#BFBFBF',
  },
  {
    id: '11',
    name: 'diamond large round',
    width: '480',
    height: '480',
    d: 'M197.6 42.4 42.4 197.6a60 60 0 0 0 0 84.8l155.2 155.2a60 60 0 0 0 84.8 0l155.2-155.2a60 60 0 0 0 0-84.8L282.4 42.4a60 60 0 0 0-84.8 0Z',
    fill: '#BFBFBF',
  },
  {
    id: '12',
    name: 'diamond medium X shape',
    width: '480',
    height: '480',
    d: 'M480 120 360 0 240 120 120 0 0 120l120 120L0 360l120 120 120-120 120 120 120-120-120-120 120-120z',
    fill: '#BFBFBF',
  },
  {
    id: '13',
    name: 'diamond square rounded',
    width: '480',
    height: '480',
    d: 'm60 240 132 176c24 32 72 32 96 0l132-176L288 64a60 60 0 0 0-96 0L60 240Z',
    fill: '#BFBFBF',
  },
  {
    id: '14',
    name: 'diamond thin',
    width: '480',
    height: '480',
    d: 'M360 240 240 0 120 240l120 240 120-240z',
    fill: '#BFBFBF',
  },
  {
    id: '15',
    name: 'diamond thin plus and X shape',
    width: '480',
    height: '480',
    d: 'M371.3 294.4 480 240l-108.7-54.4 38.4-115.3-115.3 38.4L240 0l-54.4 108.7L70.3 70.3l38.4 115.3L0 240l108.7 54.4-38.4 115.3 115.3-38.4L240 480l54.4-108.7 115.3 38.4-38.4-115.3z',
    fill: '#BFBFBF',
  },
  {
    id: '16',
    name: 'diamond thin plus shape',
    width: '480',
    height: '480',
    d: 'm320 320 160-80-160-80L240 0l-80 160L0 240l160 80 80 160 80-160z',
    fill: '#BFBFBF',
  },
  {
    id: '17',
    name: 'diamond thin plus shape round',
    width: '480',
    height: '480',
    d: 'M372.7 186.3 320 160l-26.3-52.7a60 60 0 0 0-107.4 0L160 160l-52.7 26.3a60 60 0 0 0 0 107.4L160 320l26.3 52.7a60 60 0 0 0 107.4 0L320 320l52.7-26.3a60 60 0 0 0 0-107.4Z',
    fill: '#BFBFBF',
  },
  {
    id: '18',
    name: 'flower bulb petals',
    width: '480',
    height: '480',
    d: 'm349.4 276.5 54.7 18.2a57.7 57.7 0 1 0 0-109.4l-54.7 18.2a57.4 57.4 0 0 1-27.1 2.4c5.1-7 12-13 20.8-17.5l51.6-25.8a57.7 57.7 0 1 0-77.3-77.3l-25.8 51.6a57.4 57.4 0 0 1-17.5 20.8 57.4 57.4 0 0 1 2.4-27.1l18.2-54.7a57.6 57.6 0 1 0-109.4 0l18.2 54.7a57.4 57.4 0 0 1 2.4 27.1 57.3 57.3 0 0 1-17.5-20.8l-25.8-51.6a57.7 57.7 0 1 0-77.3 77.3l51.5 25.8a57.4 57.4 0 0 1 21 17.5c-8.7 1.4-18 .7-27.2-2.4l-54.7-18.2a57.7 57.7 0 1 0 0 109.4l54.7-18.2a57.4 57.4 0 0 1 27.1-2.4 57.3 57.3 0 0 1-20.8 17.5l-51.6 25.8a57.7 57.7 0 1 0 77.3 77.3l25.8-51.5a57.4 57.4 0 0 1 17.5-21c1.4 8.7.7 18-2.4 27.2l-18.2 54.7a57.7 57.7 0 1 0 109.4 0l-18.2-54.7a57.4 57.4 0 0 1-2.4-27.1c7 5.1 13 12 17.5 20.9l25.8 51.5a57.7 57.7 0 1 0 77.4-77.4l-51.6-25.7a57.4 57.4 0 0 1-21-17.5c8.7-1.4 18-.7 27.2 2.4ZM240 300a60 60 0 1 1 0-120 60 60 0 0 1 0 120Z',
    fill: '#BFBFBF',
  },
  {
    id: '19',
    name: 'flower circle 8 in octogon layout',
    width: '480',
    height: '480',
    d: 'M450.9 169.7a100 100 0 0 0-13.6-11.4A99.4 99.4 0 0 0 321.6 42.7a99.3 99.3 0 0 0-163.4 0A99.4 99.4 0 0 0 42.7 158.3a99.3 99.3 0 0 0 0 163.4 99.4 99.4 0 0 0 115.6 115.6 99.3 99.3 0 0 0 163.4 0 99.4 99.4 0 0 0 115.5-115.5 99.3 99.3 0 0 0 13.7-152ZM240 300a60 60 0 1 1 0-120 60 60 0 0 1 0 120Z',
    fill: '#BFBFBF',
  },
  {
    id: '20',
    name: 'flower oval 4 plus and X shape',
    width: '480',
    height: '480',
    d: 'M480 240c0-29.1-20.7-55.8-55.2-76.5 9.7-39.1 5.5-72.6-15-93.2-20.7-20.6-54.2-24.8-93.3-15.1C295.8 20.7 269.1 0 240 0s-55.8 20.7-76.5 55.2c-39.1-9.7-72.6-5.5-93.2 15s-24.8 54.2-15.1 93.2C20.7 184.3 0 211 0 240s20.7 55.8 55.2 76.5c-9.7 39.1-5.5 72.6 15 93.2 20.7 20.6 54.2 24.8 93.2 15.1C184.2 459.3 211 480 240 480s55.8-20.7 76.5-55.2c39.1 9.7 72.6 5.5 93.2-15s24.8-54.2 15-93.2C459.4 295.8 480 269 480 240Zm-240 60a60 60 0 1 1 0-120 60 60 0 0 1 0 120Z',
    fill: '#BFBFBF',
  },
  {
    id: '21',
    name: 'mix square circle quarter corners',
    width: '480',
    height: '480',
    d: 'M0 0h230c138 0 250 112 250 250v230H250C112 480 0 368 0 230V0Z',
    fill: '#BFBFBF',
  },
  {
    id: '22',
    name: 'mix square circle quarter corners circle cutout',
    width: '480',
    height: '480',
    d: 'M240 0A240 240 0 0 0 0 240v240h240a240 240 0 0 0 240-240V0H240Zm0 360a120 120 0 1 1 0-240 120 120 0 0 1 0 240Z',
    fill: '#BFBFBF',
  },
  {
    id: '23',
    name: 'mix square circle quarter corners donut',
    width: '480',
    height: '480',
    d: 'M480 0H240A240 240 0 0 0 0 240v240h240a240 240 0 0 0 240-240V0ZM360 240a120 120 0 0 1-120 120H120V240a120 120 0 0 1 120-120h120v120ZM480 0H240A240 240 0 0 0 0 240v240h240a240 240 0 0 0 240-240V0ZM360 240a120 120 0 0 1-120 120H120V240a120 120 0 0 1 120-120h120v120Z',
    fill: '#BFBFBF',
  },
  {
    id: '24',
    name: 'mix square diamond',
    width: '480',
    height: '480',
    d: 'M409.7 310.3 480 240l-70.3-70.3V70.3h-99.4L240 0l-70.3 70.3H70.3v99.4L0 240l70.3 70.3v99.4h99.4L240 480l70.3-70.3h99.4v-99.4z',
    fill: '#BFBFBF',
  },
  {
    id: '25',
    name: 'oval',
    width: '480',
    height: '480',
    d: 'M240 360C372.548 360 480 306.274 480 240C480 173.726 372.548 120 240 120C107.452 120 0 173.726 0 240C0 306.274 107.452 360 240 360Z',
    fill: '#BFBFBF',
  },
  {
    id: '26',
    name: 'oval 2 overlap filled',
    width: '480',
    height: '480',
    d: 'M480 180c0-66.3-107.5-120-240-120S0 113.7 0 180v120c0 66.3 107.5 120 240 120s240-53.7 240-120V180Z',
    fill: '#BFBFBF',
  },
  {
    id: '27',
    name: 'oval 2 plus shape',
    width: '480',
    height: '480',
    d: 'M480 240c0-47-54-87.7-132.7-107.3C327.7 54 287 0 240 0s-87.7 54-107.3 132.7C54 152.3 0 193 0 240s54 87.7 132.7 107.3C152.3 426 193 480 240 480s87.7-54 107.3-132.7C426 327.8 480 287 480 240Z',
    fill: '#BFBFBF',
  },
  {
    id: '28',
    name: 'oval 2 X shape',
    width: '480',
    height: '480',
    d: 'M409.7 409.7c33.2-33.2 23.8-100.2-17.9-169.7 41.7-69.6 51.1-136.5 18-169.7C376.4 37 309.4 46.5 240 88.2 170.4 46.5 103.5 37 70.3 70.2 37 103.6 46.5 170.5 88.2 240c-41.7 69.5-51.1 136.5-18 169.7 33.3 33.2 100.2 23.8 169.8-17.9 69.5 41.7 136.5 51.1 169.7 18Z',
    fill: '#BFBFBF',
  },
  {
    id: '29',
    name: 'oval 3 overlap filled',
    width: '480',
    height: '480',
    d: 'M480 120C480 53.7 372.6 0 240 0S0 53.7 0 120v240c0 66.3 107.5 120 240 120s240-53.7 240-120V120Z',
    fill: '#BFBFBF',
  },
  {
    id: '30',
    name: 'oval 4 plus and X shape',
    width: '480',
    height: '480',
    d: 'M480 240c0-29.1-20.7-55.8-55.2-76.5 9.7-39.1 5.5-72.6-15-93.2-20.7-20.6-54.2-24.8-93.3-15.1C295.8 20.7 269.1 0 240 0s-55.8 20.7-76.5 55.2c-39.1-9.7-72.6-5.5-93.2 15s-24.8 54.2-15.1 93.2C20.7 184.3 0 211 0 240s20.7 55.8 55.2 76.5c-9.7 39.1-5.5 72.6 15 93.2 20.7 20.6 54.2 24.8 93.2 15.1C184.3 459.3 211 480 240 480s55.8-20.7 76.5-55.2c39.1 9.7 72.6 5.5 93.2-15s24.8-54.2 15.1-93.2C459.3 295.8 480 269 480 240Z',
    fill: '#BFBFBF',
  },
  {
    id: '31',
    name: 'rectangle',
    width: '480',
    height: '480',
    d: 'M80 0h320v480H80z',
    fill: '#BFBFBF',
  },
  {
    id: '32',
    name: 'rectangle third plus shape',
    width: '480',
    height: '480',
    d: 'M480 160H320V0H160v160H0v160h160v160h160V320h160V160z',
    fill: '#BFBFBF',
  },
  {
    id: '33',
    name: 'rectangle third round plus shape',
    width: '480',
    height: '480',
    d: 'M400 160h-80V80a80 80 0 0 0-160 0v80H80a80 80 0 0 0 0 160h80v80a80 80 0 0 0 160 0v-80h80a80 80 0 0 0 0-160Z',
    fill: '#BFBFBF',
  },
  {
    id: '34',
    name: 'rectangle third round X shape',
    width: '480',
    height: '480',
    d: 'M409.7 296.6 353.1 240l56.6-56.6A80 80 0 0 0 296.6 70.3L240 126.9l-56.6-56.6A80 80 0 0 0 70.3 183.4l56.6 56.6-56.6 56.6a80 80 0 0 0 113.1 113.1l56.6-56.6 56.6 56.6a80 80 0 0 0 113.1-113.1Z',
    fill: '#BFBFBF',
  },
  {
    id: '35',
    name: 'rectangle third round X shape bloat',
    width: '480',
    height: '480',
    d: 'M409 295.9a79 79 0 0 1 .7-112.5A80 80 0 0 0 296.6 70.3 79 79 0 0 1 184 71a80.7 80.7 0 0 0-113.4-1.1C39 101 39 152 70.3 183.4s30.5 82.7.7 112.5a80.7 80.7 0 0 0-1.1 113.4 80 80 0 0 0 113.5.4 79 79 0 0 1 113.2 0 80 80 0 0 0 113.5-.4c31-31.4 30-82.2-1.1-113.4Z',
    fill: '#BFBFBF',
  },
  {
    id: '36',
    name: 'shape 3 sided triangle',
    width: '480',
    height: '480',
    d: 'M240 60 32.2 420h415.7L240 60z',
    fill: '#BFBFBF',
  },
  {
    id: '37',
    name: 'shape 4 sided square',
    width: '480',
    height: '480',
    d: 'M70.3 70.3h339.4v339.4H70.3z',
    fill: '#BFBFBF',
  },
  {
    id: '38',
    name: 'shape 5 sided pentagon',
    width: '480',
    height: '480',
    d: 'M240 11.8 0 186.1l91.7 282.2h296.6L480 186.1 240 11.8z',
    fill: '#BFBFBF',
  },
  {
    id: '39',
    name: 'shape 6 sided hexagon',
    width: '480',
    height: '480',
    d: 'M360 32.2H120L0 240l120 207.9h240L480 240 360 32.2z',
    fill: '#BFBFBF',
  },
  {
    id: '40',
    name: 'shape 7 sided heptagon',
    width: '480',
    height: '480',
    d: 'M240 6 47.5 98.7 0 307l133.2 167h213.6L480 307 432.5 98.7 240 6z',
    fill: '#BFBFBF',
  },
  {
    id: '41',
    name: 'shape 8 sided octagon',
    width: '480',
    height: '480',
    d: 'M339.4 0H140.6L0 140.6v198.8L140.6 480h198.8L480 339.4V140.6L339.4 0z',
    fill: '#BFBFBF',
  },
  {
    id: '42',
    name: 'shape 9 sided nonagon',
    width: '480',
    height: '480',
    d: 'm240 3.7-156.6 57L0 205l29 164.2 127.7 107.2h166.7l127.7-107.2L480 205 396.7 60.7 240 3.7z',
    fill: '#BFBFBF',
  },
  {
    id: '43',
    name: 'shape 10 sided decagon',
    width: '480',
    height: '480',
    d: 'M314.2 11.8H165.8l-120 87.1L0 240l45.8 141.1 120 87.2h148.4l120-87.2L480 240 434.2 98.9l-120-87.1z',
    fill: '#BFBFBF',
  },
  {
    id: '44',
    name: 'shape circle cutout plus shape',
    width: '480',
    height: '480',
    d: 'M480 240A240 240 0 0 1 240 0 240 240 0 0 1 0 240a240 240 0 0 1 240 240 240 240 0 0 1 240-240Z',
    fill: '#BFBFBF',
  },
  {
    id: '45',
    name: 'shape circle edge mirror',
    width: '480',
    height: '480',
    d: 'M240 360c88.8 0 166.4-48.3 207.9-120A240 240 0 0 0 32 240 240 240 0 0 0 240 360Z',
    fill: '#BFBFBF',
  },
  {
    id: '46',
    name: 'shape circle edge mirror plus shape',
    width: '480',
    height: '480',
    d: 'M447.9 240c-14-24.3-32.3-46-53.7-63.9 2.4-27.8 0-56-7.2-83a241.2 241.2 0 0 0-83.1-7.3 241.3 241.3 0 0 0-64-53.7c-24.2 14-45.8 32.3-63.8 53.7-27.8-2.4-56 0-83.1 7.2a241.2 241.2 0 0 0-7.2 83.1A241.3 241.3 0 0 0 32 240c14 24.3 32.3 46 53.7 63.9-2.5 27.8 0 56 7.2 83 27.1 7.3 55.3 9.7 83.1 7.3 18 21.4 39.6 39.6 63.9 53.7 24.3-14 46-32.3 63.9-53.7 27.8 2.4 56 0 83-7.2 7.3-27.1 9.7-55.3 7.3-83.1 21.4-18 39.6-39.6 53.7-63.9Z',
    fill: '#BFBFBF',
  },
  {
    id: '47',
    name: 'shape heart',
    width: '480',
    height: '480',
    d: 'M438.82 41.18c-54.9-54.9-143.92-54.9-198.82 0-54.9-54.9-143.92-54.9-198.82 0-54.9 54.9-54.9 143.92 0 198.82L240 438.82 438.82 240c54.9-54.9 54.9-143.92 0-198.82Z',
    fill: '#BFBFBF',
  },
  {
    id: '48',
    name: 'shape heart round',
    width: '480',
    height: '480',
    d: 'M438.82 74.19c-54.9-54.9-143.92-54.9-198.82 0-54.9-54.9-143.92-54.9-198.82 0-54.9 54.9-54.9 143.92 0 198.82l156.4 156.4a60 60 0 0 0 84.85 0l156.4-156.4c54.9-54.9 54.9-143.92 0-198.82Z',
    fill: '#BFBFBF',
  },
  {
    id: '49',
    name: 'square diamond round',
    width: '360',
    height: '360',
    d: 'M300 0H60C26.8629 0 0 26.8629 0 60V300C0 333.137 26.8629 360 60 360H300C333.137 360 360 333.137 360 300V60C360 26.8629 333.137 0 300 0Z',
    fill: '#BFBFBF',
  },
  {
    id: '50',
    name: 'square large',
    width: '480',
    height: '480',
    d: 'M0 0h480v480H0z',
    fill: '#BFBFBF',
  },
  {
    id: '51',
    name: 'square large round',
    width: '480',
    height: '480',
    d: 'M360 0H120C53.7258 0 0 53.7258 0 120V360C0 426.274 53.7258 480 120 480H360C426.274 480 480 426.274 480 360V120C480 53.7258 426.274 0 360 0Z',
    fill: '#BFBFBF',
  },
  {
    id: '52',
    name: 'star rectangle 4',
    width: '480',
    height: '480',
    d: 'M480 210H270V0h-60v210H0v60h210v210h60V270h210v-60z',
    fill: '#BFBFBF',
  },
  {
    id: '53',
    name: 'star rectangle 4 round',
    width: '480',
    height: '480',
    d: 'M450 210H270V30a30 30 0 1 0-60 0v180H30a30 30 0 1 0 0 60h180v180a30 30 0 1 0 60 0V270h180a30 30 0 1 0 0-60Z',
    fill: '#BFBFBF',
  },
  {
    id: '54',
    name: 'star rectangle 8 round',
    width: '480',
    height: '480',
    d: 'M450 210H312.4l97.3-97.3a30 30 0 1 0-42.4-42.4L270 167.6V30a30 30 0 1 0-60 0v137.6l-97.3-97.3a30 30 0 1 0-42.4 42.4l97.3 97.3H30a30 30 0 1 0 0 60h137.6l-97.3 97.3a30 30 0 1 0 42.4 42.4l97.3-97.3V450a30 30 0 1 0 60 0V312.4l97.3 97.3a30 30 0 1 0 42.4-42.4L312.4 270H450a30 30 0 1 0 0-60Z',
    fill: '#BFBFBF',
  },
  {
    id: '55',
    name: 'triangle half',
    width: '480',
    height: '480',
    d: 'M480 480H0V0l480 480z',
    fill: '#BFBFBF',
  },
  {
    id: '56',
    name: 'triangle large',
    width: '480',
    height: '480',
    d: 'M480 0H0l240 480L480 0z',
    fill: '#BFBFBF',
  },
  {
    id: '57',
    name: 'triangle quarter mirror',
    width: '480',
    height: '480',
    d: 'M480 0H0l240 240L480 0zM0 480h480L240 240 0 480z',
    fill: '#BFBFBF',
  },
]

const {
  group,
  layer,
  history,
  elements,
  selectedSticker,
  selectedElement,
  transformerElementRef,
  selectedText,
  handleElementClick,
} = useSharedImageEditor()

const handleSelectShape = (shape: SVGAttributes) => {
  if (!group.value || !history.value.present) return
  const groupNode = group.value.getNode()
  const box = groupNode.getClientRect()
  const groupWidth = box.width
  const groupHeight = box.height
  const newShape: Konva.PathConfig = {
    id: `shape_${Date.now()}`,
    x: groupWidth / 2,
    y: groupHeight / 2,
    width: 100,
    height: 100,
    data: shape.d,
    fill: shape.fill,
    stroke: shape.fill,
    strokeEnabled: false,
    strokeWidth: 2,
    draggable: true,
    opacity: 1,
    scale: {
      x: 0.3,
      y: 0.3,
    },
  }
  elements.value.push(newShape)
  selectedText.value = null
  selectedSticker.value = null
  handleElementClick(newShape)
}

const handleDeleteSelected = () => {
  if (!selectedElement.value) return
  const index = elements.value.findIndex(
    (item) => item.id === selectedElement.value?.id,
  )
  if (index !== -1) {
    elements.value.splice(index, 1)
    selectedElement.value = null
    transformerElementRef.value?.getNode().moveToTop()
    layer.value?.getNode().batchDraw()
  }
}
</script>

<template>
  <div class="flex flex-col size-full p-4">
    <h2 class="text-lg font-semibold mb-4">Elements</h2>
    <ul v-if="selectedElement" class="space-y-3 mb-5">
      <li
        v-if="typeof selectedElement.fill === 'string'"
        class="flex items-center justify-between"
      >
        <label class="text-base">Fill color</label>
        <ColorPickerModal v-model="selectedElement.fill" class="size-6" />
      </li>
      <li class="flex flex-col">
        <label class="text-base">Opacity</label>
        <BaseRangeSlider
          v-model="selectedElement.opacity"
          :min="0"
          :max="1"
          :step="0.01"
          :thumbWidth="8"
          :thumbHeight="20"
          :trackHeight="5"
          thumbColor="#4A71D4"
          thumbBorderRadius="12px"
          trackActiveColor="#4A71D4"
          trackInactiveColor="#D6E7FF"
          class="w-full"
        />
        <div class="flex justify-between text-xs text-gray-600">
          <span>0</span>
          <span>{{
            selectedElement.opacity
              ? (selectedElement.opacity * 100).toFixed(0)
              : 0
          }}</span>
          <span>100</span>
        </div>
      </li>
      <li class="flex items-center justify-between">
        <label class="text-base">Delete selected</label>
        <button
          class="action-button text-[#E21F3F]"
          @click="handleDeleteSelected"
        >
          <DeleteIcon class="w-4" />
        </button>
      </li>
    </ul>
    <div class="w-full flex-grow">
      <p class="text-lg font-semibold mb-4">Shapes</p>
      <div class="grid grid-cols-3 gap-3">
        <div
          v-for="shape in shapes"
          :key="shape.id"
          :title="shape.name"
          class="flex justify-center items-center p-3 border rounded hover:bg-gray-100 cursor-pointer"
          @click="handleSelectShape(shape)"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            :width="shape.width"
            :height="shape.height"
            :viewBox="`0 0 ${shape.width} ${shape.height}`"
            :name="shape.name"
            class="w-full h-auto"
          >
            <path :d="shape.d" :fill="shape.fill"></path>
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
.action-button {
  @apply w-8 h-8 flex justify-center items-center rounded-full hover:bg-[#F1F2F6];
}
</style>
