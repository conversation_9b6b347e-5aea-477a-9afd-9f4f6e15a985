<script setup lang="ts">
const { adjustmentSliders, applyAdjustment } = useSharedImageEditor()
</script>

<template>
  <div class="flex flex-col size-full p-4">
    <div class="w-full h-full flex flex-col">
      <p class="text-base font-semibold text-[#525252] leading-[21px] pb-2">
        Adjustment
      </p>

      <div class="flex flex-col gap-4 mt-2">
        <div
          v-for="slide in adjustmentSliders"
          :key="slide.id"
          class="w-full flex flex-col gap-2 items-center"
        >
          <div class="w-full flex justify-between items-center">
            <span class="text-base text-[#525252]">{{ slide.label }} </span>
            <div
              class="h-6 min-w-12 bg-[#EBEDF5] rounded-full flex justify-center items-center"
            >
              <span class="text-sm text-[#525252]">{{ slide.lavel }}%</span>
            </div>
          </div>
          <BaseRangeSlider
            v-model="slide.lavel"
            :key="slide.id"
            :min="0"
            :max="100"
            :thumbWidth="8"
            :thumbHeight="20"
            :trackHeight="5"
            thumbColor="#4A71D4"
            thumbBorderRadius="12px"
            trackActiveColor="#4A71D4"
            trackInactiveColor="#D6E7FF"
            class="w-full"
            :disabled="false"
            @change="applyAdjustment(slide)"
          />
        </div>
      </div>
    </div>
  </div>
</template>
