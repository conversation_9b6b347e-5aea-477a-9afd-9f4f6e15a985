<script setup lang="ts">
import { useStore } from 'vuex'
import RangeSlider from './RangeSlider.vue'

interface CumulativeNewTab {
  label: string
  route: string
}

const cumulativeNewTab: CumulativeNewTab[] = [
  { label: 'Cumulative', route: '' },
  { label: 'New', route: '' },
]

const store = useStore()

const currentTab = ref('Cumulative')
const sliderValue = ref(50)

const handleTabChange = () => {
  store.commit('insights/SET_CURRENT_HEADER_TAB', currentTab.value)
}

const changeValue = () => {
  console.log(sliderValue.value, 'value')
}
</script>

<template>
  <div class="flex items-center space-x-6">
    <BaseTabs
      v-model="currentTab"
      :tabs="cumulativeNewTab"
      :is-route-enable="false"
      tabsClass="w-full max-w-[260px] h-[35px] bg-[#EBEDF5] shadow-sm"
      circleClass="bg-[#4A71D4]"
      @change="handleTabChange"
    />
    <Transition name="page" mode="out-in">
      <div
        v-if="currentTab === 'New'"
        class="w-full h-[35px] flex items-center"
      >
        <RangeSlider
          v-model="sliderValue"
          :min="0"
          :max="100"
          @change="changeValue"
        />
      </div>
    </Transition>
  </div>
</template>

<style scoped></style>
