<script setup lang="ts">
const data = ref({
  postingActivity: {
    draft: 5,
    awaitingApproval: 3,
    approvedAndSchedule: 15,
    published: 421,
  },
  followers: 4457,
  growthRate: {
    yearToDate: 7.51,
    oneMonth: -0.25,
    threeMonth: 3.2,
    oneYear: 10.14,
    threeYear: 15.6,
    fiveYear: 7.33,
    tenYear: 4.48,
    maximum: 9.01,
  },
  engagement: {
    likes: 491043,
    comments: 21080,
    shares: 5741,
    mentions: 0,
    news: 0,
    total: 517864,
  },
})

const postingActivityLabels = computed(() => {
  return {
    draft: { label: 'Draft', value: data.value.postingActivity.draft },
    awaitingApproval: {
      label: 'Awaiting Approval',
      value: data.value.postingActivity.awaitingApproval,
    },
    approvedAndSchedule: {
      label: 'Approved and Schedule',
      value: data.value.postingActivity.approvedAndSchedule,
    },
    published: {
      label: 'Published',
      value: data.value.postingActivity.published,
    },
  }
})

const growthRateLabels = computed(() => {
  return {
    yearToDate: {
      label: 'Year-to-Date',
      value: data.value.growthRate.yearToDate,
    },
    oneMonth: { label: '1-Month', value: data.value.growthRate.oneMonth },
    threeMonth: { label: '3-Month', value: data.value.growthRate.threeMonth },
    oneYear: { label: '1-Year', value: data.value.growthRate.oneYear },
    threeYear: { label: '3-Year', value: data.value.growthRate.threeYear },
    fiveYear: { label: '5-Year', value: data.value.growthRate.fiveYear },
    tenYear: { label: '10-Year', value: data.value.growthRate.tenYear },
    maximum: { label: 'Maximum', value: data.value.growthRate.maximum },
  }
})

const engagementLabels = computed(() => {
  return {
    likes: {
      color: '#0E9F52',
      label: 'Likes',
      value: data.value.engagement.likes,
    },
    comments: {
      color: '#D66C00',
      label: 'Comments',
      value: data.value.engagement.comments,
    },
    shares: {
      color: '#525252',
      label: 'Shares',
      value: data.value.engagement.shares,
    },
    mentions: {
      color: '#0072D6',
      label: 'Mentions',
      value: data.value.engagement.mentions,
    },
    news: {
      color: '#8A2BE2',
      label: 'News',
      value: data.value.engagement.news,
    },
  }
})

const getBoxStyle = (color: string, value: number) => {
  return value === 0 ? { backgroundColor: '#fff' } : { backgroundColor: color }
}

const getBoxClass = (value: number) => {
  return value === 0 ? 'border border-[#707070]' : ''
}
</script>

<template>
  <div class="px-6 py-[22px]">
    <section class="">
      <h3 class="section-title">Posting Activity</h3>
      <div class="pl-4 pt-3.5 space-y-1.5">
        <div
          v-for="item in postingActivityLabels"
          :key="item.label"
          class="w-full flex items-center justify-between"
        >
          <span class="label">{{ item.label }}</span>
          <span class="value">{{ item.value.toLocaleString() }}</span>
        </div>
      </div>
    </section>

    <section class="pt-[22px]">
      <h3 class="section-title flex items-center justify-between">
        <span>Followers</span>
        <span>{{ data.followers.toLocaleString() }}</span>
      </h3>
      <div class="pl-4 pt-3.5 space-y-1.5">
        <h3
          class="text-[#333333] text-lg font-semibold flex items-center justify-between"
        >
          <span>Period</span>
          <span>Growth Rate</span>
        </h3>
        <div
          v-for="item in growthRateLabels"
          :key="item.label"
          class="w-full flex items-center justify-between"
        >
          <span class="label">{{ item.label }}</span>
          <span class="value">{{ item.value }}%</span>
        </div>
      </div>
    </section>

    <section class="pt-[22px]">
      <h3 class="section-title">Engagement</h3>
      <div class="pl-4 pt-3.5 pb-4 space-y-1.5">
        <div
          v-for="item in engagementLabels"
          :key="item.label"
          class="w-full flex items-center justify-between"
        >
          <div class="flex items-center space-x-2">
            <div
              class="size-4 rounded-[4px]"
              :style="getBoxStyle(item.color, item.value)"
              :class="getBoxClass(item.value)"
            ></div>
            <span class="label">{{ item.label }}</span>
          </div>
          <span class="value">{{ item.value.toLocaleString() }}</span>
        </div>
      </div>
    </section>
    <div
      class="w-full flex items-center justify-between section-title pt-1.5 border-t border-[#C2C2C2]"
    >
      <span class="">Total Engagement</span>
      <span class="">{{ data.engagement.total.toLocaleString() }}</span>
    </div>
  </div>
</template>

<style scoped>
.section-title {
  @apply text-[#525252] text-lg font-semibold;
}
.label,
.value {
  @apply text-[#333333] text-lg font-normal;
}
</style>
