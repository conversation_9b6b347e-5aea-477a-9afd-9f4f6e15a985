<template>
  <div ref="sliderElement" class="slider-wrapper">
    <div class="track"></div>
    <div class="active-track" :style="{ width: `${thumbPosition}%` }"></div>
    <div
      class="thumb"
      :style="{ left: `${clampedThumbPosition}%` }"
      @mousedown="startDrag"
      @touchstart="startDrag"
    >
      <div class="thumb-label flex justify-center items-center">
        <span>Weekly</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface SliderProps {
  min: number
  max: number
}

const props = withDefaults(defineProps<SliderProps>(), {
  min: 0,
  max: 100,
})

const model = defineModel<number>({
  default: 50,
})
const emit = defineEmits<{
  change: []
}>()

const thumbPosition = ref<number>(calculatePositionFromValue(model.value))
const isDragging = ref<boolean>(false)
const sliderElement = ref<HTMLDivElement | null>(null)
const thumbWidthPercent = ref<number>(0)

// Compute clamped position to keep thumb fully visible within track
const clampedThumbPosition = computed<number>(() => {
  const halfThumbPercent = thumbWidthPercent.value / 2
  return Math.max(
    halfThumbPercent,
    Math.min(100 - halfThumbPercent, thumbPosition.value),
  )
})

function calculatePositionFromValue(value: number): number {
  return ((value - props.min) / (props.max - props.min)) * 100
}

function calculateValueFromPosition(position: number): number {
  return props.min + (position / 100) * (props.max - props.min)
}

// Handle start dragging
const startDrag = (event: MouseEvent | TouchEvent): void => {
  event.preventDefault()
  isDragging.value = true
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('touchmove', onDrag, { passive: false })
  document.addEventListener('mouseup', stopDrag)
  document.addEventListener('touchend', stopDrag)
}

// Handle drag movement
const onDrag = (event: MouseEvent | TouchEvent): void => {
  if (!isDragging.value || !sliderElement.value) return

  if (event.cancelable) {
    event.preventDefault()
  }

  const slider = sliderElement.value
  const rect = slider.getBoundingClientRect()

  let clientX: number
  if ('touches' in event) {
    clientX = event.touches[0].clientX
  } else {
    clientX = event.clientX
  }

  const offsetX = clientX - rect.left
  let newPosition = (offsetX / rect.width) * 100
  newPosition = Math.max(0, Math.min(100, newPosition))

  thumbPosition.value = newPosition
  model.value = Math.round(calculateValueFromPosition(newPosition))
  emit('change')
}

// Stop dragging
const stopDrag = (): void => {
  isDragging.value = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('touchmove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('touchend', stopDrag)
}

// Calculate thumb width as percentage of slider width
const calculateThumbWidthPercent = (): void => {
  if (!sliderElement.value) return

  const sliderWidth = sliderElement.value.getBoundingClientRect().width
  const thumbWidth = 70 // The width of thumb in pixels from CSS

  thumbWidthPercent.value = (thumbWidth / sliderWidth) * 100
}

onMounted(() => {
  calculateThumbWidthPercent()
  window.addEventListener('resize', calculateThumbWidthPercent)
})

// Clean up event listeners
onUnmounted(() => {
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('touchmove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('touchend', stopDrag)
  window.removeEventListener('resize', calculateThumbWidthPercent)
})
</script>

<style scoped>
.slider-wrapper {
  @apply w-full relative h-2 bg-[#d6e7ff] rounded cursor-pointer my-6;
}

.active-track {
  @apply absolute h-full bg-[#4a71d4] rounded;
}

.thumb {
  @apply absolute w-[70px] h-[30px] bg-[#4a71d4] rounded-2xl cursor-grab;
  top: 50%;
  transform: translate(-50%, -50%);
  transition: box-shadow 0.2s;
}

.thumb:hover {
  box-shadow: 0 0 0 8px rgba(66, 133, 244, 0.2);
}

.thumb:active {
  @apply cursor-grabbing;
}

.thumb-label {
  @apply size-full text-white px-2 py-0.5 rounded-2xl text-xs font-semibold whitespace-nowrap;
}
</style>
