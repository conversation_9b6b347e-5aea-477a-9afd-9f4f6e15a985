<script setup lang="ts">
import type { ApexOptions } from 'apexcharts'
import VueApexCharts from 'vue3-apexcharts'
import { useStore } from 'vuex'

interface ChartData {
  name: string
  type: string
  data: [number, number][]
}

interface Props {
  series: ChartData[]
}

const props = withDefaults(defineProps<Props>(), {
  series: () => [],
})

const store = useStore()
const { $toast } = useNuxtApp()

// Chart reference and date management
const chartRef = ref<ApexCharts | null>(null)
const initialDate = ref('')
const endingDate = ref('')

const currentHeaderTab = computed(() => store.state.insights.currentHeaderTab)
const currentDateRange = computed(() => store.state.insights.currentDateRange)
const startDate = computed(() => store.state.insights.startDate)
const endDate = computed(() => store.state.insights.endDate)
const formatDate = computed(() => store.state.system.formatDate)
const formatTime = computed(() => store.state.system.formatTime)
const dayDiff = computed(() => {
  const a = new Date(initialDate.value)
  const b = new Date(endingDate.value)
  return Math.abs(b.getTime() - a.getTime()) / (1000 * 60 * 60 * 24)
})
const apexChartTimeFormat = computed(() => {
  if (formatTime.value === 'HH:mm') return 'HH:mm'
  else if (formatTime.value === 'hh:mm aaa') return 'hh:mm tt'
  else if (formatTime.value === 'hh:mm aa') return 'hh:mm TT'
  else return 'hh:mm tt'
})

const chartOptions = ref<ApexOptions>({
  chart: {
    height: 450,
    type: 'line',
    stacked: false,
    toolbar: {
      show: false,
    },
    zoom: {
      enabled: true,
      autoScaleYaxis: true,
    },
  },
  colors: ['#8EBDFF', '#0E9F52', '#D66C00', '#525252', '#0072D6', '#8A2BE2'],
  stroke: {
    width: [3, 3, 3, 3, 3, 3],
    curve: [
      'smooth',
      'stepline',
      'stepline',
      'stepline',
      'stepline',
      'stepline',
    ],
  },
  fill: {
    type: 'solid',
    opacity: 1,
  },
  grid: {
    show: false,
    padding: {
      left: 0,
      right: -8,
    },
    row: {
      colors: ['#F1F2F6'],
      opacity: 1,
    },
  },
  markers: {
    size: 0,
  },
  xaxis: {
    type: 'datetime',
    min: new Date('01 Jan 2011').getTime(),
    max: new Date('01 Jan 2012').getTime(),
    tickPlacement: 'on',
    labels: {
      show: true,
      // format: 'yyyy',
      datetimeFormatter: {
        year: 'MMM, yyyy',
        month: 'MMM',
        day: 'ddd',
        hour: 'HH:mm',
      },
    },
    axisTicks: {
      show: true,
      borderType: 'solid',
      color: '#525252',
      height: 10,
      offsetX: 1,
      offsetY: 0,
    },
    axisBorder: {
      show: true,
      color: '#525252',
      offsetX: -2,
      offsetY: 0,
    },
  },
  yaxis: [
    {
      title: {
        text: undefined,
      },
      labels: {
        formatter: (value: number) => `${value}`,
        offsetX: -16,
      },
      axisTicks: {
        show: true,
        color: '#525252',
        width: 10,
        offsetX: 0,
        offsetY: 0,
      },
      axisBorder: {
        show: true,
        color: '#525252',
        offsetX: 0,
        offsetY: 0,
      },
    },
    {
      opposite: true,
      title: {
        text: undefined,
      },
      labels: {
        formatter: (value: number) => `${value}`,
        offsetX: -20,
      },
      axisTicks: {
        show: true,
        color: '#525252',
        width: 10,
        offsetX: 0,
        offsetY: 0,
      },
      axisBorder: {
        show: true,
        color: '#525252',
        offsetX: 0,
        offsetY: 0,
      },
    },
  ],
  tooltip: {
    shared: true,
    intersect: false,
    y: {
      formatter: (value: number) => `${value.toLocaleString()} `,
    },
  },
  legend: {
    show: false,
    horizontalAlign: 'center',
    clusterGroupedSeries: false,
  },
})

const updateCurveStyles = () => {
  if (!chartRef.value) return

  const newCurveStyles =
    currentHeaderTab.value === 'New'
      ? 'smooth'
      : ['smooth', 'stepline', 'stepline', 'stepline', 'stepline', 'stepline']

  chartRef.value?.updateOptions({
    stroke: {
      curve: newCurveStyles,
    },
  })
}

const toggleSeries = (index: number) => {
  if (chartRef.value) {
    chartRef.value?.toggleSeries(props.series[index].name)
  }
}

const zoomChart = (value1: string, value2: string) => {
  if (chartRef.value) {
    chartRef.value?.zoomX(
      new Date(value1).getTime(),
      new Date(value2).getTime(),
    )
  }
}

const checkDateString = (data: string) => {
  if (data === 'Current') {
    initialDate.value = '01 August 2012'
    endingDate.value = '02 August 2012'
  } else if (data === '1 Month') {
    initialDate.value = '01 July 2012'
    endingDate.value = '31 July 2012'
  } else if (data === '6 Months') {
    initialDate.value = '01 February 2012'
    endingDate.value = '31 July 2012'
  } else if (data === 'YTD') {
    initialDate.value = '01 January 2012'
    endingDate.value = '31 June 2012'
  } else if (data === '1 Year') {
    initialDate.value = '01 January 2012'
    endingDate.value = '31 December 2012'
  } else if (data === '3 Years') {
    initialDate.value = '01 January 2011'
    endingDate.value = '31 December 2013'
  } else if (data === '5 Years') {
    initialDate.value = '01 January 2012'
    endingDate.value = '31 December 2016'
  } else if (data === 'Max') {
    initialDate.value = '01 January 2012'
    endingDate.value = new Date().toDateString()
  } else if (data === 'Date Range' && startDate.value && endDate.value) {
    initialDate.value = startDate.value
    endingDate.value = endDate.value
  }

  zoomChart(initialDate.value, endingDate.value)
}

const updateDateTimeFormat = () => {
  if (!chartRef.value) return
  chartRef.value?.updateOptions({
    xaxis: {
      labels: {
        datetimeFormatter: {
          year: `${dayDiff.value >= 1096 ? 'yyyy' : 'yyyy MMM'}`,
          month: `${dayDiff.value >= 1096 ? '' : 'MMM'}`,
          day: `${dayDiff.value <= 2 ? 'dd MMM yyyy' : 'dd'}`,
          hour: `${apexChartTimeFormat.value}`,
        },
        rotate: -45,
        minHeight: dayDiff.value <= 2 ? 78 : 40,
        rotateAlways: dayDiff.value <= 2,
      },
    },
  })
}

const updateDateFormat = () => {
  if (!chartRef.value) return
  chartRef.value?.updateOptions({
    tooltip: {
      enabled: true,
      x: {
        show: true,
        format: `${formatDate.value}`,
      },
    },
  })
}

const zoomIn = (initial: string, ending: string) => {
  const a = new Date(initial)
  const b = new Date(ending)
  const timeDiff = Math.abs(b.getTime() - a.getTime()) / (1000 * 60 * 60 * 24)

  if (timeDiff <= 1) {
    $toast('clear')
    $toast('success', {
      message: 'Max Zoomed',
      className: 'toasted-bg-archive',
    })
  } else if (timeDiff > 1 && timeDiff < 10) {
    a.setDate(a.getDate() + 1)
  } else if (timeDiff >= 10 && timeDiff < 20) {
    a.setDate(a.getDate() + 3)
  } else if (timeDiff >= 20 && timeDiff < 100) {
    a.setDate(a.getDate() + Math.floor(timeDiff / 10))
  } else if (timeDiff >= 100) {
    a.setDate(a.getDate() + Math.floor(timeDiff / 30))
  }

  initialDate.value = a.toDateString()
  endingDate.value = b.toDateString()
  zoomChart(a.toDateString(), b.toDateString())
}

const zoomOut = (initial: string, ending: string) => {
  const a = new Date(initial)
  const b = new Date(ending)
  const timeDiff = Math.abs(b.getTime() - a.getTime()) / (1000 * 60 * 60 * 24)

  if (timeDiff >= 1 && timeDiff < 10) {
    a.setDate(a.getDate() - 1)
  } else if (timeDiff >= 10 && timeDiff < 20) {
    a.setDate(a.getDate() - 3)
  } else if (timeDiff >= 20 && timeDiff < 100) {
    a.setDate(a.getDate() - Math.floor(timeDiff / 10))
  } else if (timeDiff >= 100) {
    a.setDate(a.getDate() - Math.floor(timeDiff / 30))
  }

  initialDate.value = a.toDateString()
  endingDate.value = b.toDateString()
  zoomChart(a.toDateString(), b.toDateString())
}

const chartZoomByScroll = (event: WheelEvent) => {
  if (event.deltaY < 0) {
    zoomIn(initialDate.value, endingDate.value)
  } else {
    zoomOut(initialDate.value, endingDate.value)
  }
  event.preventDefault()
}

const addScroll = () => {
  const container = document.getElementById('chartRef')
  if (container) {
    container.addEventListener('wheel', chartZoomByScroll)
  }
}

// Watchers
watch(currentHeaderTab, () => {
  updateCurveStyles()
  console.log(currentHeaderTab.value, 'currentHeaderTab')
})

watch([startDate, endDate], () => {
  checkDateString(currentDateRange.value)
})

watch(currentDateRange, (data) => {
  checkDateString(data)
})

watch(formatDate, () => {
  updateDateFormat()
})

watch([apexChartTimeFormat, dayDiff], () => {
  updateDateTimeFormat()
})

onMounted(async () => {
  await nextTick()
  setTimeout(() => {
    checkDateString(currentDateRange.value)
    updateDateTimeFormat()
  })
  updateDateFormat()
  addScroll()
})

onUnmounted(() => {
  const container = document.getElementById('chartRef')
  if (container) {
    container.removeEventListener('wheel', chartZoomByScroll)
  }
})
</script>

<template>
  <div class="w-full h-[calc(100%-92px)] pt-[22px]">
    <div
      class="flex items-center justify-between text-xl text-[#525252] font-semibold"
    >
      <span>Followers</span>
      <span>Engagement</span>
    </div>
    <!-- Chart -->
    <div id="chart" class="w-full h-[calc(100%-68px)]">
      <ClientOnly>
        <VueApexCharts
          ref="chartRef"
          id="chartRef"
          type="line"
          width="100%"
          height="100%"
          :options="chartOptions"
          :series="series"
        />
      </ClientOnly>
    </div>
    <div class="flex items-center justify-center gap-4 py-2">
      <label
        v-for="(item, index) in series"
        :key="item.name"
        class="flex items-center gap-2 cursor-pointer"
      >
        <InputsCheckBoxInput
          :id="item.name"
          :modelValue="true"
          :checkColor="chartOptions.colors?.[index]"
          borderColor="#707070"
          @update:modelValue="() => toggleSeries(index)"
        />
        <span class="text-[#525252] text-lg">{{ item.name }}</span>
      </label>
    </div>
  </div>
</template>

<style scoped></style>
