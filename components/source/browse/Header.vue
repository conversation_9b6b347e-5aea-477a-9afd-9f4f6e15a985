<script setup lang="ts">
interface Props {
  platform: string
  username: string
  socialIcon: string
  subHeaderClass: string
  showExitButton: boolean
}

const props = withDefaults(defineProps<Props>(), {
  platform: '',
  username: '',
  socialIcon: '',
  subHeaderClass: '',
  showExitButton: true,
})
</script>

<template>
  <div class="w-full">
    <div
      class="w-full h-[35px] bg-blue-200 flex justify-center items-center text-base text-white font-semibold relative"
    >
      {{ platform }}
      <button
        v-if="showExitButton"
        class="absolute top-1/2 -translate-y-1/2 right-4 flex justify-center items-center"
        @click="$emit('close')"
      >
        <ClientOnly>
          <fa class="text-xl text-white" :icon="['fas', 'times']" />
        </ClientOnly>
      </button>
    </div>
    <div
      class="w-full h-[33px] flex justify-start items-center text-base text-[#323744] font-semibold px-6 space-x-2"
      :class="[subHeaderClass]"
    >
      <img
        class="w-4 rounded-full h-auto object-cover aspect-square"
        :src="socialIcon"
        :alt="platform"
      />
      <span class="">{{ username }}</span>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
