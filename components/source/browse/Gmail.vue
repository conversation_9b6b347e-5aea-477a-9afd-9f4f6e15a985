<script setup lang="ts">
import tiltokIcon from '~/assets/img/svg/gmail.svg'

interface SubAccount {
  id: number
  text: string
  value: number
  select: boolean
}

const props = defineProps<{
  subAccount: SubAccount
  showExitButton: boolean
}>()

const { accounts, checkUncheckSubAccounts } = useSourceApi()

const handleClose = () => {
  checkUncheckSubAccounts(
    `${props.subAccount.id}_${props.subAccount.text}`,
    accounts.value,
  )
}
</script>

<template>
  <div class="w-full h-full rounded-2xl flex flex-col overflow-hidden">
    <SourceBrowseHeader
      platform="Gmail"
      username="Sharparchive"
      :socialIcon="tiltokIcon"
      subHeaderClass="bg-[#FFDFEC]"
      :showExitButton="showExitButton"
      @close="handleClose"
    />
    <div
      class="w-full h-[calc(100%-68px)] flex-grow bg-[#F1F2F6] p-4 overflow-y-auto custom-scroll space-y-0.5"
    ></div>
  </div>
</template>

<style lang="scss" scoped></style>
