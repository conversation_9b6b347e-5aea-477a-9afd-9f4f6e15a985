<script setup lang="ts">
import { useStore } from 'vuex'
import twitterIcon from '~/assets/img/icon/TwitterIcon/twitter.svg'

interface SubAccount {
  id: number
  text: string
  value: number
  select: boolean
}

const props = defineProps<{
  subAccount: SubAccount
  showExitButton: boolean
}>()

const store = useStore()
const twitterPosts = computed(() => store.state.browse.twitterPosts)

const { accounts, checkUncheckSubAccounts } = useSourceApi()

const handleClose = () => {
  checkUncheckSubAccounts(
    `${props.subAccount.id}_${props.subAccount.text}`,
    accounts.value,
  )
}
</script>

<template>
  <div class="w-full h-full rounded-2xl flex flex-col overflow-hidden">
    <SourceBrowseHeader
      platform="Twitter"
      username="Sharparchive"
      :socialIcon="twitterIcon"
      subHeaderClass="bg-[#D1EBFF]"
      :showExitButton="showExitButton"
      @close="handleClose"
    />
    <div
      class="w-full h-[calc(100%-68px)] flex-grow bg-[#F1F2F6] p-4 overflow-y-auto custom-scroll space-y-0.5"
    >
      <SourceHubSocialsTwitterSinglePost
        v-for="post in twitterPosts"
        :key="post.id"
        :singlePost="post"
        class="max-w-[680px] w-full mx-auto"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
