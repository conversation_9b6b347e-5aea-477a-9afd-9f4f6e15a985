<script setup lang="ts">
import { useStore } from 'vuex'
import linkedinIcon from '~/assets/img/icon/LinkedInIcon/<EMAIL>'

interface SubAccount {
  id: number
  text: string
  value: number
  select: boolean
}

const props = defineProps<{
  subAccount: SubAccount
  showExitButton: boolean
}>()

const store = useStore()
const linkedinPosts = computed(() => store.state.browse.linkedinPosts)

const { accounts, checkUncheckSubAccounts } = useSourceApi()

const handleClose = () => {
  checkUncheckSubAccounts(
    `${props.subAccount.id}_${props.subAccount.text}`,
    accounts.value,
  )
}
</script>

<template>
  <div class="w-full h-full rounded-2xl flex flex-col overflow-hidden">
    <SourceBrowseHeader
      platform="Linkedin"
      username="Sharparchive"
      :socialIcon="linkedinIcon"
      subHeaderClass="bg-[#C2EBFF]"
      :showExitButton="showExitButton"
      @close="handleClose"
    />
    <div
      class="w-full h-[calc(100%-68px)] flex-grow bg-[#F1F2F6] p-4 overflow-y-auto custom-scroll space-y-2"
    >
      <SourceHubSocialsLinkedinSinglePost
        v-for="post in linkedinPosts"
        :key="post.id"
        :singlePost="post"
        class="max-w-[680px] w-full mx-auto"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
