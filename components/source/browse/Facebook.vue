<script setup lang="ts">
import { useStore } from 'vuex'
import facebookIcon from '~/assets/img/icon/FacebookIcon/<EMAIL>'

interface SubAccount {
  id: number
  text: string
  value: number
  select: boolean
}

const props = defineProps<{
  subAccount: SubAccount
  showExitButton: boolean
}>()

const { accounts, checkUncheckSubAccounts } = useSourceApi()
const store = useStore()

const facebookPosts = computed(() => store.state.browse.facebookPosts)

const handleClose = () => {
  checkUncheckSubAccounts(
    `${props.subAccount.id}_${props.subAccount.text}`,
    accounts.value,
  )
}
</script>

<template>
  <div class="w-full h-full rounded-2xl flex flex-col overflow-hidden">
    <SourceBrowseHeader
      platform="Facebook"
      username="Sharparchive"
      :socialIcon="facebookIcon"
      subHeaderClass="bg-[#D6E7FF]"
      :showExitButton="showExitButton"
      @close="handleClose"
    />
    <div
      class="w-full h-[calc(100%-68px)] flex-grow bg-[#F1F2F6] p-4 overflow-y-auto custom-scroll space-y-4"
    >
      <SourceHubSocialsSinglePost
        v-for="post in facebookPosts"
        :key="post.id"
        :singlePost="post"
        class="max-w-[680px] w-full mx-auto"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
