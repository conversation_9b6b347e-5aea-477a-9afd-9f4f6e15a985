<script setup lang="ts">
import type { EventContentArg } from '@fullcalendar/core';
import { format, parseISO } from 'date-fns';

const props = defineProps<{
  eventContent: EventContentArg
}>()

const { id, socialPlatform, profileImage, image, name, status, content, date } =
  props.eventContent.event.extendedProps

const isPostMenuOpen = ref(false)
const isOpenScheduleMenu = ref(false)

const localTime = computed(() => {
  if (!date) return ''
  return format(parseISO(date), 'hh:mm aaa')
})

const statusClass = (status: string) => {
  const statusClasses: { [key: string]: string } = {
    Pending: 'border-[#F96D00]',
    Approved: 'border-[#0E9F52]',
    Posted: 'border-[#3964D0]',
  }
  return statusClasses[status] || 'border-[#3964D0]'
}

const handleMouseLeave = () => {
  isPostMenuOpen.value = false
  isOpenScheduleMenu.value = false
}
</script>

<template>
  <div class="group" @mouseleave="handleMouseLeave">
    <div
      class="flex flex-col w-full bg-[#EBEDF5] rounded-lg group border-l-[5px] p-2"
      :class="[statusClass(status)]"
    >
      <div class="flex items-center justify-between text-[#505050]">
        <span class="text-base font-medium">{{ localTime }}</span>
        <SourcePostMenu v-model:isMenuOpen="isPostMenuOpen" />
      </div>
      <div
        class="flex items-center space-x-1 mt-3.5 font-semibold text-[#333333]"
      >
        <img
          class="w-5 min-w-5 bg-gray-400 rounded-full h-auto object-cover aspect-square"
          src="~/assets/img/icon/FacebookIcon/<EMAIL>"
          :alt="name"
        />
        <span class="whitespace-normal line-clamp-1">{{ name }}</span>
      </div>
      <img
        class="w-full h-auto object-cover aspect-[197/80] rounded-lg mt-2"
        :src="image"
        :alt="name"
      />
      <div class="line-clamp-1 mt-2 text-[#525252] whitespace-normal">
        {{ content }}
      </div>
    </div>
    <SourcePostScheduleMenu
      menuFor="week"
      class="hidden group-hover:block mt-4"
      triggerBtnClass="w-full flex items-center justify-between bg-[#F1F2F6] rounded-full p-[7px] px-4"
      v-model:isMenuOpen="isOpenScheduleMenu"
    />
  </div>
</template>

<style scoped></style>
