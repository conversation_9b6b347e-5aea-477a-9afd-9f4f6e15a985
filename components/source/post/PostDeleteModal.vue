<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const isPostDeleteModal = computed(() => store.state.post.isPostDeleteModal)

const closeModal = () => {
  store.commit('post/SET_IS_POST_DELETE_MODAL', false)
}

const deletePost = () => {
  console.log('Delete Post')
  closeModal()
}
</script>

<template>
  <BaseModal
    :isOpen="isPostDeleteModal"
    @closeModal="closeModal"
    exitButtonClass="size-5 text-[#505050] mr-6 mt-4"
  >
    <div
      class="flex items-center flex-col max-w-[640px] rounded-2xl bg-[#FFFFFF]"
    >
      <div class="pt-4 px-6 text-lg w-full rounded-t-2xl">
        <h3 class="text-[#505050] font-semibold">Delete Post?</h3>
        <p class="text-[#000000] pt-8">
          Post will be permanently deleted. Are you sure to delete this post
          from Facebook?
        </p>
      </div>
      <div
        class="pt-6 pb-4 px-6 text-lg w-full rounded-b-2xl flex justify-end items-center space-x-2"
      >
        <button
          @click="deletePost"
          class="h-[35px] px-6 rounded-full flex justify-center items-center text-base bg-[#4A71D4] text-white font-semibold"
        >
          Delete
        </button>
      </div>
    </div>
  </BaseModal>
</template>
