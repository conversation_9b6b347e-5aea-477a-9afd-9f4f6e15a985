<script setup lang="ts">
import { format } from 'date-fns'

interface WeekRange {
  start: Date
  end: Date
}

interface MonthYear {
  month: number
  year: number
}

const props = withDefaults(
  defineProps<{
    currentTab: 'List' | 'Week' | 'Month'
    selectedDate: Date
    weekRange: WeekRange
    monthYear: MonthYear
    currentTitle: string
    calenderHeight?: string
    selectedFormat?: string
  }>(),
  {
    selectedFormat: 'MMMM d, yyyy', // ⬅️ your default value
  },
)

const emit = defineEmits<{
  'update:date': [date: Date]
  'update:range': [range: WeekRange]
  'update:monthYear': [monthAndYear: MonthYear]
}>()

const calendarContainerRef = ref<HTMLElement | null>(null)
const showDatePicker = ref(false)
const calendarYear = ref(new Date().getFullYear())
const localWeekRange = ref<WeekRange>({ ...props.weekRange })
const selectedMonthYear = ref<MonthYear>({
  month: new Date().getMonth(),
  year: new Date().getFullYear(),
})

// Computed properties
const displayDate = computed(() =>
  props.selectedDate
    ? format(props.selectedDate, props.selectedFormat)
    : 'Select Date',
)

// Methods
const selectMonth = (month: number) => {
  selectedMonthYear.value = { month, year: calendarYear.value }
  emit('update:monthYear', selectedMonthYear.value)
}

const decrementYear = () => {
  calendarYear.value--
}

const incrementYear = () => {
  calendarYear.value++
}

const toggleCalendar = () => {
  showDatePicker.value = !showDatePicker.value
  if (props.currentTab === 'Month') {
    calendarYear.value = new Date().getFullYear()
  }
}

const handleClickOutsideOfCalendar = (event: Event) => {
  if (
    calendarContainerRef.value &&
    !calendarContainerRef.value.contains(event.target as Node) &&
    showDatePicker.value
  ) {
    showDatePicker.value = false
  }
}

watch(
  () => props.weekRange,
  (newRange) => {
    localWeekRange.value = { ...newRange }
  },
  { immediate: true, deep: true },
)
watch(
  () => props.monthYear,
  (newValue) => {
    selectedMonthYear.value = { ...newValue }
    calendarYear.value = newValue.year
  },
  { immediate: true, deep: true },
)

onMounted(() => {
  document.addEventListener('click', handleClickOutsideOfCalendar)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutsideOfCalendar)
})
</script>

<template>
  <div ref="calendarContainerRef" class="relative px-2">
    <div
      tabindex="0"
      @click="toggleCalendar"
      class="date-display-button h-[35px] px-7 rounded-full flex justify-center items-center text-base bg-[#4A71D4] text-white font-semibold min-w-40 cursor-default"
      :class="calenderHeight"
    >
      <p>
        {{ props.currentTab === 'List' ? displayDate : props.currentTitle }}
      </p>
      <slot name="icon"></slot>
    </div>
    <div
      v-if="showDatePicker"
      class="picker-modal block absolute top-full left-0 mt-2 bg-white shadow-[2px_2px_4px_#22283114] p-2 rounded-lg z-10"
    >
      <VDatePicker
        v-if="props.currentTab === 'List'"
        :model-value="props.selectedDate"
        borderless
        @update:model-value="(date: Date) => emit('update:date', date)"
      />
      <slot name="button"></slot>
      <VDatePicker
        v-if="props.currentTab === 'Week'"
        v-model.range="localWeekRange"
        borderless
        @update:model-value="
          (dateRange: WeekRange) => emit('update:range', dateRange)
        "
      />
      <div v-if="props.currentTab === 'Month'" class="month-selector w-[320px]">
        <div class="flex justify-between items-center mb-3 px-2">
          <button
            @click="decrementYear"
            class="text-[#525252] cursor-pointer p-1 rounded-md size-7 flex justify-center items-center hover:bg-[#F1F2F6]"
          >
            <ClientOnly>
              <fa :icon="['fas', 'chevron-left']" />
            </ClientOnly>
          </button>
          <span class="font-semibold text-[#525252]">{{ calendarYear }}</span>
          <button
            @click="incrementYear"
            class="text-[#525252] cursor-pointer p-1 rounded-md size-7 flex justify-center items-center hover:bg-[#F1F2F6]"
          >
            <ClientOnly>
              <fa :icon="['fas', 'chevron-right']" />
            </ClientOnly>
          </button>
        </div>
        <div class="grid grid-cols-3 gap-2 p-2">
          <button
            v-for="month in 12"
            :key="month"
            @click="selectMonth(month - 1)"
            class="month-btn py-2 px-4 text-sm rounded transition-colors"
            :class="{
              'bg-[#4A71D4] text-white':
                selectedMonthYear.year === calendarYear &&
                selectedMonthYear.month === month - 1,
              'hover:bg-[#4A71D4] hover:text-white': true,
            }"
          >
            {{ format(new Date(calendarYear, month - 1, 1), 'MMMM') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.vc-monthly .is-not-in-month *) {
  opacity: 0.5;
}
.date-select-box > .picker-modal {
  box-shadow: 0px 0px 8px #2228313d;
  margin-top: 0px;
}
.date-select-box > .date-display-button {
  height: 27px;
}
</style>
