<script setup lang="ts">
import type { EventContentArg } from '@fullcalendar/core';
import { format, parseISO } from 'date-fns';

const props = defineProps<{
  eventContent: EventContentArg
}>()

const { id, socialPlatform, profileImage, image, name, status, content, date } =
  props.eventContent.event.extendedProps

const isPostMenuOpen = ref(false)
const isOpenScheduleMenu = ref(false)

const localTime = computed(() => {
  if (!date) return ''
  return format(parseISO(date), 'hh:mm aaa')
})

const statusClass = (status: string) => {
  const statusClasses: { [key: string]: string } = {
    Pending: 'border-[#F96D00]',
    Approved: 'border-[#0E9F52]',
    Posted: 'border-[#3964D0]',
  }
  return statusClasses[status] || 'border-[#3964D0]'
}

const handleMouseLeave = () => {
  isPostMenuOpen.value = false
  isOpenScheduleMenu.value = false
}
</script>

<template>
  <div class="space-y-1 group w-full" @mouseleave="handleMouseLeave">
    <div
      class="flex items-center justify-between bg-[#EBEDF5] rounded-lg group border-l-[5px] px-2 py-[7px] w-full"
      :class="[statusClass(status)]"
    >
      <div class="flex items-center space-x-1 font-semibold text-[#333333]">
        <img
          class="w-5 min-w-5 bg-gray-400 rounded-full h-auto object-cover aspect-square"
          src="~/assets/img/icon/FacebookIcon/<EMAIL>"
          :alt="name"
        />
        <span class="whitespace-normal line-clamp-1 break-all">{{ name }}</span>
      </div>
      <div>{{ localTime }}</div>
    </div>
    <SourcePostScheduleMenu
      menuFor="month"
      class="hidden group-hover:block z-2"
      triggerBtnClass="flex text-[#525252] ml-auto text-lg cursor-pointer p-1 rounded-full size-[35px] justify-center items-center bg-[#F1F2F6] hover:bg-[#F1F2F6]"
      v-model:isMenuOpen="isOpenScheduleMenu"
    />
  </div>
</template>

<style scoped></style>
