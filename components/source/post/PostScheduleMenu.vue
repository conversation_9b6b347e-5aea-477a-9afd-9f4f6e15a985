<script setup lang="ts">
const props = defineProps<{
  isMenuOpen?: boolean
  triggerBtnClass?: string
  menuFor: 'week' | 'month'
}>()

const isOpen = ref(false)
const menuRef = ref<HTMLElement | null>(null)

const emit = defineEmits<{
  (e: 'update:isMenuOpen', value: boolean): void
}>()

const toggleMenu = () => {
  isOpen.value = !isOpen.value
}

const closeMenu = () => {
  isOpen.value = false
}

const handleClickOutside = (event: Event) => {
  if (menuRef.value && !menuRef.value.contains(event.target as Node)) {
    isOpen.value = false
  }
}

watch(
  () => props.isMenuOpen,
  (newVal) => {
    if (newVal !== undefined) {
      isOpen.value = newVal
    }
  },
  { immediate: true },
)

watch(isOpen, (newVal) => {
  if (props.isMenuOpen !== undefined) {
    emit('update:isMenuOpen', newVal)
  }
})

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div ref="menuRef" class="text-[#525252] z-2">
    <button @click.stop="toggleMenu" :class="[triggerBtnClass]">
      <template v-if="menuFor === 'week'">
        <span>Schedule</span>
        <ClientOnly>
          <fa
            class="text-xl text-[#4A71D4]"
            :icon="['fas', !isOpen ? 'caret-down' : 'caret-up']"
          />
        </ClientOnly>
      </template>
      <template v-else>
        <ClientOnly>
          <fa :icon="['fas', 'plus']" />
        </ClientOnly>
      </template>
    </button>
    <div v-if="isOpen" class="bg-[#F1F2F6] rounded-lg w-full absolute top-[calc(100%-2px)]">
      <div @click="closeMenu" class="px-4 py-[7px] hover:bg-[#ebedf5cb] rounded-t-full">
        Schedule Post
      </div>
      <div @click="closeMenu" class="px-4 py-[7px] hover:bg-[#ebedf5cb]">
        Schedule Story
      </div>
      <div @click="closeMenu" class="px-4 py-[7px] hover:bg-[#ebedf5cb] rounded-b-full">
        Create Reels
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
