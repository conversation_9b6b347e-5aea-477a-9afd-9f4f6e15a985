<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const isPostPreviewModal = computed(() => store.state.post.isPostPreviewModal)
const facebookPost = computed(() => store.state.social.singlePost)

const closePostPreviewModal = () => {
  store.commit('post/SET_IS_POST_PREVIEW_MODAL', false)
}
</script>

<template>
  <BaseModal
    :isOpen="isPostPreviewModal"
    @closeModal="closePostPreviewModal"
    containerClass="max-w-[1024px] m-xl:max-w-[1280px] w-full"
  >
    <div class="flex items-center flex-col w-full rounded-2xl">
      <div class="py-4 px-6 bg-[#FFFFFF] text-lg w-full rounded-t-2xl">
        <h3 class="text-[#505050] font-semibold">Post overview</h3>
        <p class="text-[#707070] pt-1.5">
          This view of your post may not represent exactly how it appears on
          Facebook's News Feed.
        </p>
        <div class="flex justify-between items-center">
          <p class="#333333 pt-3.5">Post Details: ID: 589045257315046</p>
          <SourcePostMenu />
        </div>
      </div>
      <div
        class="bg-[#F1F2F6] w-full pt-4 max-h-[50vh] overflow-y-auto custom-scroll"
      >
        <SourceHubSocialsSinglePost
          :singlePost="facebookPost"
          class="w-full max-w-[730px] mx-auto"
        />
      </div>
      <div
        class="py-4 px-6 bg-[#FFFFFF] text-lg w-full rounded-b-2xl flex justify-end items-center space-x-2"
      >
        <button
          @click="closePostPreviewModal"
          class="h-[35px] px-6 rounded-full flex justify-center items-center text-base border border-[#4A71D4] text-[#4A71D4] bg-white font-semibold"
        >
          Cancel
        </button>
        <button
          @click="closePostPreviewModal"
          class="h-[35px] px-6 rounded-full flex justify-center items-center text-base bg-[#4A71D4] text-white font-semibold"
        >
          Publish Now
        </button>
      </div>
    </div>
  </BaseModal>
</template>
