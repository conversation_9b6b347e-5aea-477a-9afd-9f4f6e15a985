<template>
  <div ref="nested shadow" class="balance__menu relative font-serif">
    <div
      ref="dropdown"
      class="dropdown-btn bg-yellow-primary transition-border-radius duration-500 rounded-t-3xl outline-none"
      :class="[`${source}`, round ? 'rounded-3xl' : '']"
    >
      <button
        class="w-full h-full text-left border-none outline-none flex items-center justify-between"
        @click="enableClick ? expand($event) : ''"
      >
        <span class="text-lg font-bold font-sans truncate">{{
          singlePostSettings ? singlePostSettings.name : 'Saved Settings'
        }}</span>
        <SharedIconDownTriangle
          class="text-gray-default transition-all duration-300 ease-in-out cursor-pointer transform min-w-5"
          :class="!menuOpen ? 'rotate-0' : 'rotate-180'"
          @click="enableClick ? expand($event) : ''"
        />
      </button>
    </div>
    <!-- Desktop -->
    <div
      class="hidden md:flex dropdown fixed flex-col rounded-b-3xl bg-yellow-primary"
      :style="{ '--height': getHeight() }"
      :class="[increaseHeight ? 'expand' : '', `${source}`]"
    >
      <div
        class="menu-wrapper flex-grow relative scroll overall_scroll"
        :class="[`scroll__${source}`]"
      >
        <div class="menu-content">
          <div class="menu-item list-wrapper">
            <div>
              <div
                class="list-item cursor-pointer flex border-b border-b-0 group-archive"
                :class="[`group-archive__${source}`]"
              >
                <template v-if="socialTop">
                  <!-- :visible="false"
                  :type="'Total'" -->
                  <SavedSettings
                    :selected-type="socialType"
                    :feeds-list="savedPostSettingsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @selected-item="selectedItem($event)"
                  ></SavedSettings>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="
          (menuOpen && !socialType && !emailType) ||
          (socialType && socialTotalPages === 1) ||
          (emailType && socialTotalPages === 1)
        "
        class="equity cursor-pointer sticky bottom-0 left-0 !bg-[#3E5EAF]"
        @click="showEditFeed(), hideMobileHeader()"
      >
        <span class="text-lg text-bold font-sans">{{
          singlePostSettings ? 'Open Saved Setting' : 'Save this Setting'
        }}</span>
        <fa :icon="[singlePostSettings ? 'fas' : 'far', 'floppy-disk']" />
      </div>
      <div
        v-if="
          menuOpen &&
          ((socialType && socialTotalPages > 1) ||
            (emailType && socialTotalPages > 1))
        "
        class="equity sticky bottom-0 left-0 flex items-center justify-between rounded-b-3xl"
        :class="[`${source}`]"
      >
        <div class="flex items-center space-x-5">
          <button
            v-if="socialType || emailType"
            :disabled="isSocialFirstPage"
            class="flex justify-center items-center w-8 h-8 rounded-full"
            :class="
              isSocialFirstPage
                ? 'bg-white-opasity-50 cursor-default'
                : 'bg-white cursor-pointer'
            "
            @click="clickPreviousPage(false)"
          >
            <fa
              :icon="['fas', 'chevron-left']"
              class="feeds-button"
              :class="[`feeds-button__${source}`]"
            />
          </button>
          <span
            v-if="socialType || emailType"
            class="text-lg font-sans text-normal"
            >{{ socialCurrentPage }}/{{ socialTotalPages }}</span
          >
          <button
            v-if="socialType || emailType"
            :disabled="isSocialLastPage"
            class="flex justify-center items-center w-8 h-8 rounded-full"
            :class="
              isSocialLastPage
                ? 'bg-white-opasity-50  cursor-default'
                : 'bg-white cursor-pointer'
            "
            @click="clickNextPage(false)"
          >
            <fa
              :icon="['fas', 'chevron-right']"
              class="feeds-button"
              :class="[`feeds-button__${source}`]"
            />
          </button>
        </div>
        <span
          class="cursor-pointer"
          @click="showEditFeed(), hideMobileHeader()"
        >
          <fa :icon="[singlePostSettings ? 'fas' : 'far', 'floppy-disk']" />
        </span>
      </div>
    </div>
    <!-- Mobile -->
    <div
      class="md:hidden dropdown flex flex-col rounded-b-3xl bg-yellow-primary"
      :style="[
        { '--maxHeight': `${windowHeight - 130}px` },
        { '--height': getHeight() },
      ]"
      :class="[increaseHeight ? 'expand' : '', `${source}`]"
    >
      <div
        class="menu-wrapper flex-grow relative scroll overall_scroll"
        :class="[`scroll__${source}`]"
      >
        <div class="menu-content">
          <div class="menu-item list-wrapper">
            <div>
              <div
                class="list-item cursor-pointer flex border-b border-b-0 group-archive"
                :class="[`group-archive__${source}`]"
              >
                <template v-if="socialTop">
                  <!-- :visible="isSocialVisible"
                  :type="'Total'" -->
                  <SavedSettings
                    :selected-type="socialType"
                    :feeds-list="savedPostSettingsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @selected-item="selectedItem($event)"
                  ></SavedSettings>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="
          (menuOpen && !socialType && !emailType) ||
          (socialType && socialTotalPages === 1) ||
          (emailType && socialTotalPages === 1)
        "
        class="equity cursor-pointer sticky bottom-0 left-0"
        :class="[`${source}`]"
        @click="showEditFeed(), hideMobileHeader()"
      >
        <span class="text-lg text-bold font-sans">{{
          singlePostSettings ? 'Open Saved Setting' : 'Save this Setting'
        }}</span>
        <fa :icon="[singlePostSettings ? 'fas' : 'far', 'floppy-disk']" />
      </div>
      <div
        v-if="
          menuOpen &&
          ((socialType && socialTotalPages > 1) ||
            (emailType && socialTotalPages > 1))
        "
        class="equity sticky bottom-0 left-0 flex items-center justify-between rounded-b-3xl"
        :class="[`${source}`]"
      >
        <div class="flex items-center space-x-5">
          <button
            v-if="socialType || emailType"
            :disabled="isSocialFirstPage"
            class="flex justify-center items-center w-8 h-8 rounded-full"
            :class="
              isSocialFirstPage
                ? 'bg-white-opasity-50 cursor-default'
                : 'bg-white cursor-pointer'
            "
            @click="clickPreviousPage(false)"
          >
            <fa
              :icon="['fas', 'chevron-left']"
              class="feeds-button"
              :class="[`feeds-button__${source}`]"
            />
          </button>
          <span
            v-if="socialType || emailType"
            class="text-lg font-sans text-normal"
            >{{ socialCurrentPage }}/{{ socialTotalPages }}</span
          >
          <button
            v-if="socialType || emailType"
            :disabled="isSocialLastPage"
            class="flex justify-center items-center w-8 h-8 rounded-full"
            :class="
              isSocialLastPage
                ? 'bg-white-opasity-50  cursor-default'
                : 'bg-white cursor-pointer'
            "
            @click="clickNextPage(false)"
          >
            <fa
              :icon="['fas', 'chevron-right']"
              class="feeds-button"
              :class="[`feeds-button__${source}`]"
            />
          </button>
        </div>
        <span
          class="cursor-pointer"
          @click="showEditFeed(), hideMobileHeader()"
        >
          <fa :icon="[singlePostSettings ? 'fas' : 'far', 'floppy-disk']" />
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core'
import { mapState } from 'vuex'
import SavedSettings from '~/components/source/create-post/SavedSettings.vue'

export default defineComponent({
  name: 'SavedSettingsDropdown',
  filters: {
    currency(value) {
      const formattedValue = new Intl.NumberFormat().format(Math.abs(value))
      return value >= 0 ? `$${formattedValue}` : `$(${formattedValue})`
    },
  },
  components: {
    SavedSettings,
  },
  props: {
    active: {
      type: Boolean,
      required: true,
      default: false,
    },
    height: {
      type: Number,
      required: true,
      default: 0,
    },
    source: {
      type: String,
      required: false,
      default: '',
    },
    windowHeight: {
      type: Number,
      default: 0,
    },
  },
  setup() {
    const feedBodyHeight = ref(0)
    const breakpoints = useBreakpoints(breakpointsTailwind)
    const nuxtApp = useNuxtApp()
    const collapse = ref(false)
    const menuOpen = ref(false)
    const increaseHeight = ref(false)
    const toggle = ref(false)
    const scroll = ref(false)
    const round = ref(true)
    const progress = ref(false)
    const socialType = ref(true)
    const emailType = ref(true)
    const webType = ref(true)
    const savedPostSettingsList = ref([])
    const emailFeedsList = ref([])
    const webFeedsList = ref([])
    const menuHeight = ref(false)
    const showFeeds = ref(true)
    const social = ref([])
    const email = ref([])
    const socialTop = ref(false)
    const emailTop = ref(false)
    const webTop = ref(false)
    const socialStatus = ref(false)
    const emailStatus = ref(false)
    const webStatus = ref(false)
    const oldScrollY = ref(0)
    const perPage = ref(0)
    const windowWidth = ref('')

    return {
      feedBodyHeight,
      isDesktop: breakpoints.greaterOrEqual('md'),
      nuxtApp,
      collapse,
      menuOpen,
      increaseHeight,
      toggle,
      scroll,
      round,
      progress,
      socialType,
      emailType,
      webType,
      savedPostSettingsList,
      emailFeedsList,
      webFeedsList,
      menuHeight,
      showFeeds,
      social,
      email,
      socialTop,
      emailTop,
      webTop,
      socialStatus,
      emailStatus,
      webStatus,
      oldScrollY,
      perPage,
      windowWidth,
    }
  },
  computed: {
    loggedIn() {
      return this.$auth.loggedIn
    },
    ...mapState('createPost', ['singlePostSettings', 'savedPostSettings']),
    ...mapState('socialFeed', [
      'socialFeeds',
      'showSinglePost',
      'showSingleImagePost',
    ]),
    ...mapState('feedsDropdown', [
      'socialInitialItem',
      'emailInitialItem',
      'socialLoadCount',
      'emailLoadCount',
      'socialPerPage',
      'emailPerPage',
      'socialCurrentPage',
      'emailCurrentPage',
      'socialTotalPages',
      'emailTotalPages',
      'socialSelectItemPage',
      'emailSelectItemPage',
      'socialCurrentInitialItem',
      'emailCurrentInitialItem',
      'socialCurrentLoadCount',
      'emailCurrentLoadCount',
      'oldScrollTop',
      'currentOldScrollTop',
      'currentScrollPosition',
    ]),
    socialPage: {
      get() {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.social = []
        for (const item of this.savedPostSettingsList.slice(
          this.socialInitialItem,
          this.socialLoadCount,
        )) {
          // eslint-disable-next-line vue/no-side-effects-in-computed-properties
          this.social.push(item)
        }
        return this.social
      },
      set(newValue) {
        this.social = []
        this.social = newValue
      },
    },
    emailPage: {
      get() {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.email = []
        for (const item of this.emailFeedsList.slice(
          this.socialInitialItem,
          this.socialLoadCount,
        )) {
          // eslint-disable-next-line vue/no-side-effects-in-computed-properties
          this.email.push(item)
        }
        return this.email
      },
      set(newValue) {
        this.email = []
        this.email = newValue
      },
    },
    isSocialFirstPage() {
      return this.socialCurrentPage === 1
    },
    isEmailFirstPage() {
      return this.emailCurrentPage === 1
    },
    isSocialLastPage() {
      return this.socialCurrentPage === this.socialTotalPages
    },
    isEmailLastPage() {
      return this.emailCurrentPage === this.emailTotalPages
    },
    enableClick() {
      return this.savedPostSettings && this.savedPostSettings.length > 0
    },
    isSocialVisible() {
      return this.savedPostSettingsList && this.savedPostSettingsList.length > 0
    },
    isEmailVisible() {
      return this.emailFeedsList.length > 0
    },
    isWebVisible() {
      return this.webFeedsList.length > 0
    },
  },
  watch: {
    savedPostSettings: {
      handler(data) {
        if (data) {
          this.resizeWindow()
          this.setTotalPages()
        }
      },
      deep: true,
    },
    windowWidth(data) {
      this.setTotalPages()
    },
  },
  created() {
    this.nuxtApp.$bus.$on('expand', () => {
      this.finalCollapsed()
    })
  },
  mounted() {
    this.windowWidth = window.innerWidth
    window.addEventListener('resize', this.resizeWindow)
    window.addEventListener('resize', this.getWindowWindth)
    if (this.savedPostSettings && this.savedPostSettings.length > 0) {
      this.resizeWindow()
    }
  },
  destroyed() {
    setTimeout(() => {
      window.removeEventListener('resize', this.resizeWindow)
      window.removeEventListener('resize', this.getWindowWindth)
    }, 2000)
  },
  methods: {
    getWindowWindth() {
      this.windowWidth = window.innerWidth
    },
    getHeight() {
      const totalType = 1
      if (this.socialTotalPages === 1) {
        if (
          this.menuOpen &&
          this.socialType &&
          this.emailType &&
          this.webType
        ) {
          return `${
            this.savedPostSettingsList.length * 42 +
            this.emailFeedsList.length * 42 +
            this.webFeedsList.length * 42 +
            totalType * 42
          }px`
        } else if (
          this.menuOpen &&
          this.socialType &&
          this.emailType &&
          !this.webType
        ) {
          return `${
            this.savedPostSettingsList.length * 42 +
            this.emailFeedsList.length * 42 +
            totalType * 42
          }px`
        } else if (
          this.menuOpen &&
          this.socialType &&
          !this.emailType &&
          this.webType
        ) {
          return `${
            this.savedPostSettingsList.length * 42 +
            this.webFeedsList.length * 42 +
            totalType * 42
          }px`
        } else if (
          this.menuOpen &&
          !this.socialType &&
          this.emailType &&
          this.webType
        ) {
          return `${
            this.emailFeedsList.length * 42 +
            this.webFeedsList.length * 42 +
            totalType * 42
          }px`
        } else if (
          this.menuOpen &&
          this.socialType &&
          !this.emailType &&
          !this.webType
        ) {
          return `${this.savedPostSettingsList.length * 42 + totalType * 42}px`
        } else if (
          this.menuOpen &&
          !this.socialType &&
          this.emailType &&
          !this.webType
        ) {
          return `${this.emailFeedsList.length * 42 + totalType * 42}px`
        } else if (
          this.menuOpen &&
          !this.socialType &&
          !this.emailType &&
          this.webType
        ) {
          return `${this.webFeedsList.length * 42 + totalType * 42}px`
        } else if (
          this.menuOpen &&
          !this.socialType &&
          !this.emailType &&
          !this.webType
        ) {
          return `${totalType * 42}px`
        }
      } else if (this.socialTotalPages > 1) {
        if (
          this.menuOpen &&
          this.socialType &&
          this.emailType &&
          this.webType
        ) {
          return `${this.socialPerPage * 42 + totalType * 42}px`
        } else if (
          this.menuOpen &&
          this.socialType &&
          this.emailType &&
          !this.webType
        ) {
          return `${this.socialPerPage * 42 + totalType * 42}px`
        } else if (
          this.menuOpen &&
          this.socialType &&
          !this.emailType &&
          this.webType
        ) {
          return `${this.socialPerPage * 42 + totalType * 42}px`
        } else if (
          this.menuOpen &&
          !this.socialType &&
          this.emailType &&
          this.webType
        ) {
          return `${this.socialPerPage * 42 + totalType * 42}px`
        } else if (
          this.menuOpen &&
          this.socialType &&
          !this.emailType &&
          !this.webType
        ) {
          return `${this.socialPerPage * 42 + totalType * 42}px`
        } else if (
          this.menuOpen &&
          !this.socialType &&
          this.emailType &&
          !this.webType
        ) {
          return `${this.socialPerPage * 42 + totalType * 42}px`
        } else if (
          this.menuOpen &&
          !this.socialType &&
          !this.emailType &&
          this.webType
        ) {
          return `${this.socialPerPage * 42 + totalType * 42}px`
        } else if (
          this.menuOpen &&
          !this.socialType &&
          !this.emailType &&
          !this.webType
        ) {
          return `${totalType * 42}px`
        }
      }
      if (this.menuOpen && this.menuHeight) {
        return `${totalType * 42}px`
      } else {
        return ''
      }
    },
    handleScroll() {
      const allItem = document.querySelectorAll('.overall_scroll')
      allItem.forEach((item) => {
        if (item.offsetHeight !== 0) {
          if (this.oldScrollY < item.scrollTop) {
            if (
              item.scrollTop >= this.oldScrollTop + this.socialPerPage * 42 &&
              this.socialTotalPages - this.socialCurrentPage !== 1
            ) {
              this.$store.commit(
                'feedsDropdown/SET_OLD_SCROLL_TOP',
                this.oldScrollTop + this.socialPerPage * 42,
              )
              this.clickNextPage(true)
            } else if (
              item.scrollTop + item.clientHeight === item.scrollHeight &&
              this.socialTotalPages !== this.socialCurrentPage
            ) {
              const lastPageTotalItem =
                (this.savedPostSettingsList.length +
                  this.emailFeedsList.length +
                  this.webFeedsList.length +
                  3 -
                  (this.socialTotalPages - 1) * this.socialPerPage) *
                42
              this.$store.commit(
                'feedsDropdown/SET_OLD_SCROLL_TOP',
                this.oldScrollTop + lastPageTotalItem,
              )
              this.clickNextPage(true)
            }
          } else if (!(this.oldScrollY < item.scrollTop)) {
            if (this.socialTotalPages === this.socialCurrentPage) {
              const lastPageTotalItem =
                (this.savedPostSettingsList.length +
                  this.emailFeedsList.length +
                  this.webFeedsList.length +
                  3 -
                  (this.socialTotalPages - 1) * this.socialPerPage) *
                42
              if (item.scrollTop <= this.oldScrollTop - lastPageTotalItem) {
                this.$store.commit(
                  'feedsDropdown/SET_OLD_SCROLL_TOP',
                  this.oldScrollTop - lastPageTotalItem,
                )
                this.clickPreviousPage(true)
              }
            } else if (
              this.socialTotalPages !== this.socialCurrentPage &&
              this.socialCurrentPage !== 1
            ) {
              if (item.scrollTop === 0) {
                this.$store.commit(
                  'feedsDropdown/SET_OLD_SCROLL_TOP',
                  this.oldScrollTop - this.socialPerPage * 42,
                )
                this.clickPreviousPage(true)
              } else if (
                item.scrollTop <=
                this.oldScrollTop - this.socialPerPage * 42
              ) {
                this.$store.commit(
                  'feedsDropdown/SET_OLD_SCROLL_TOP',
                  this.oldScrollTop - this.socialPerPage * 42,
                )
                this.clickPreviousPage(true)
              }
            }
          }
          this.oldScrollY = item.scrollTop <= 0 ? 0 : item.scrollTop
        }
      })
    },
    activeFeed() {
      this.socialTop = false
      this.emailTop = false
      this.webTop = false
      this.socialStatus = false
      this.emailStatus = false
      this.webStatus = false
      if (
        (this.savedPostSettingsList && this.savedPostSettingsList.length > 0) ||
        (this.emailFeedsList && this.emailFeedsList.length > 0) ||
        (this.webFeedsList && this.webFeedsList.length > 0)
      ) {
        if (
          this.savedPostSettings[0].provider !== 'Microsoft' &&
          this.savedPostSettings[0].provider !== 'Google' &&
          this.savedPostSettings[0].provider !== 'Web'
        ) {
          this.socialType = true
          this.emailType = true
          this.webType = true
          this.socialTop = true
        } else if (
          this.savedPostSettings[0].provider === 'Microsoft' ||
          this.savedPostSettings[0].provider === 'Google'
        ) {
          this.emailType = true
          this.socialType = true
          this.webType = true
          this.emailTop = true
        } else if (this.savedPostSettings[0].provider === 'Web') {
          this.emailType = true
          this.socialType = true
          this.webType = true
          this.webTop = true
        }
      }
    },
    selectedItem(id) {
      this.savedPostSettingsList.forEach((element) => {
        element.backgroundColor = false
      })
      if (this.socialType || this.emailType || this.webType) {
        this.savedPostSettingsList.forEach((element, i) => {
          if (element.id === id) {
            if (this.singlePostSettings?.id === id) {
              element.backgroundColor = false
              this.$store.commit('createPost/SET_SINGLE_POST_SETTINGS', null)
            } else {
              element.backgroundColor = true
              this.$store.dispatch('createPost/getSinglePostSetting', id)
            }
          } else {
            element.backgroundColor = false
          }
          this.savedPostSettingsList[i] = element
        })

        this.socialPage = this.savedPostSettingsList
      }
    },
    resizeWindow() {
      if (this.isDesktop) {
        this.feedBodyHeight = window.innerHeight - (60 + 16 + 16)
      } else if (!this.isDesktop) {
        if (this.windowHeight > 0) {
          this.feedBodyHeight = this.windowHeight - 84
          console.log(this.feedBodyHeight, 'feedBodyHeight')
        } else {
          this.feedBodyHeight = window.innerHeight - 149 - 84
          console.log(this.feedBodyHeight, 'feedBodyHeight')
        }
      }
      if (this.feedBodyHeight >= 202) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_PER_PAGE',
          Math.floor((this.feedBodyHeight - 40 * 2) / 42),
        )
      } else {
        this.$store.commit('feedsDropdown/SET_SOCIAL_PER_PAGE', 1)
        this.$store.commit('feedsDropdown/SET_EMAIL_PER_PAGE', 1)
      }
      if (this.savedPostSettings && this.savedPostSettings.length > 0) {
        this.groupFeedByType()
      }
    },
    setTotalPages() {
      if (this.increaseHeight) {
        setTimeout(() => {
          // this.collapsed()
        }, 100)
      }
      this.$store.commit(
        'feedsDropdown/SET_SOCIAL_LOAD_COUNT',
        this.socialPerPage,
      )
      this.$store.commit('feedsDropdown/SET_SOCIAL_CURRENT_PAGE', 1)
      this.$store.commit(
        'feedsDropdown/SET_SOCIAL_SELECT_ITEM_PAGE',
        this.socialCurrentPage,
      )
      this.$store.commit('feedsDropdown/SET_SOCIAL_INITIAL_ITEM', 0)
      this.$store.commit(
        'feedsDropdown/SET_SOCIAL_CURRENT_INITIAL_ITEM',
        this.socialInitialItem,
      )
      this.$store.commit(
        'feedsDropdown/SET_SOCIAL_CURRENT_LOAD_COUNT',
        this.socialLoadCount,
      )
      this.$store.commit(
        'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
        Math.ceil(
          (this.savedPostSettingsList.length +
            this.emailFeedsList.length +
            this.webFeedsList.length +
            1) /
            this.socialPerPage,
        ),
      )
    },
    setSocialEmailTotalPages() {
      document.querySelectorAll('.overall_scroll').forEach((item) => {
        item.removeEventListener('scroll', this.handleScroll)
        item.scrollTop = 0
      })
      this.$store.commit('feedsDropdown/SET_SOCIAL_CURRENT_PAGE', 1)
      this.$store.commit('feedsDropdown/SET_OLD_SCROLL_TOP', 0)
      console.log(this.socialType, this.emailType, this.webType, 'hello')
      if (!this.emailType && this.socialType && this.webType) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil(
            (this.savedPostSettingsList.length + this.webFeedsList.length + 1) /
              this.socialPerPage,
          ),
        )
      } else if (!this.socialType && this.emailType && this.webType) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil(
            (this.emailFeedsList.length + this.webFeedsList.length + 1) /
              this.socialPerPage,
          ),
        )
      } else if (!this.webType && this.socialType && this.emailType) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil(
            (this.savedPostSettingsList.length +
              this.emailFeedsList.length +
              1) /
              this.socialPerPage,
          ),
        )
      } else if (!this.emailType && !this.socialType && this.webType) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil((this.webFeedsList.length + 1) / this.socialPerPage),
        )
      } else if (!this.webType && !this.socialType && this.emailType) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil((this.emailFeedsList.length + 1) / this.socialPerPage),
        )
      } else if (!this.emailType && !this.webType && this.socialType) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil(
            (this.savedPostSettingsList.length + 1) / this.socialPerPage,
          ),
        )
      } else if (!this.socialType && !this.emailType && !this.webType) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil(2 / this.socialPerPage),
        )
      } else if (this.emailType && this.socialType && this.webType) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil(
            (this.savedPostSettingsList.length +
              this.emailFeedsList.length +
              this.webFeedsList.length +
              1) /
              this.socialPerPage,
          ),
        )
      }
      setTimeout(() => {
        document.querySelectorAll('.overall_scroll').forEach((item) => {
          item.addEventListener('scroll', this.handleScroll)
        })
      }, 500)
    },
    clickPreviousPage(value) {
      if (this.socialCurrentPage > 0) {
        if (this.socialType || this.emailType) {
          if (!value) {
            const allItem = document.querySelectorAll('.overall_scroll')
            allItem.forEach((item) => {
              item.removeEventListener('scroll', this.handleScroll)
              if (
                this.socialTotalPages !== this.socialCurrentPage &&
                item.offsetHeight !== 0
              ) {
                item.scrollTop = this.oldScrollTop - this.socialPerPage * 42
                if (item.offsetHeight !== 0) {
                  this.$store.commit(
                    'feedsDropdown/SET_OLD_SCROLL_TOP',
                    this.oldScrollTop - this.socialPerPage * 42,
                  )
                }
              } else if (
                this.socialTotalPages === this.socialCurrentPage &&
                item.offsetHeight !== 0
              ) {
                const lastPageTotalItem =
                  (this.savedPostSettingsList.length +
                    this.emailFeedsList.length +
                    this.webFeedsList.length +
                    3 -
                    (this.socialTotalPages - 1) * this.socialPerPage) *
                  42
                item.scrollTop = this.oldScrollTop - lastPageTotalItem
                if (item.offsetHeight !== 0) {
                  this.$store.commit(
                    'feedsDropdown/SET_OLD_SCROLL_TOP',
                    this.oldScrollTop - lastPageTotalItem,
                  )
                }
              }
            })
          }
          this.$store.commit('feedsDropdown/SET_SOCIAL_PREVIOUS_PAGE', 1)
          this.$store.commit(
            'feedsDropdown/SET_SOCIAL_LOAD_COUNT',
            this.socialInitialItem,
          )
          this.$store.commit(
            'feedsDropdown/SET_SET_SOCIAL_INITIAL_ITEM_PREVIOUS',
            this.socialPerPage,
          )
        }
      }
      setTimeout(() => {
        const allItem = document.querySelectorAll('.overall_scroll')
        allItem.forEach((item) => {
          item.addEventListener('scroll', this.handleScroll)
        })
      }, 500)
    },
    clickNextPage(value) {
      if (this.socialCurrentPage < this.socialTotalPages) {
        if (this.socialType || this.emailType || this.webType) {
          if (!value) {
            const allItem = document.querySelectorAll('.overall_scroll')
            allItem.forEach((item) => {
              item.removeEventListener('scroll', this.handleScroll)
              if (
                this.socialTotalPages - this.socialCurrentPage !== 1 &&
                item.offsetHeight !== 0
              ) {
                item.scrollTop = this.oldScrollTop + this.socialPerPage * 42
                if (item.offsetHeight !== 0) {
                  this.$store.commit(
                    'feedsDropdown/SET_OLD_SCROLL_TOP',
                    this.oldScrollTop + this.socialPerPage * 42,
                  )
                }
              } else if (
                this.socialTotalPages - this.socialCurrentPage === 1 &&
                item.offsetHeight !== 0
              ) {
                const lastPageTotalItem =
                  (this.savedPostSettingsList.length +
                    this.emailFeedsList.length +
                    this.webFeedsList.length +
                    3 -
                    (this.socialTotalPages - 1) * this.socialPerPage) *
                  42
                item.scrollTop = this.oldScrollTop + lastPageTotalItem
                if (item.offsetHeight !== 0) {
                  this.$store.commit(
                    'feedsDropdown/SET_OLD_SCROLL_TOP',
                    this.oldScrollTop + lastPageTotalItem,
                  )
                }
              }
            })
          }
          this.$store.commit('feedsDropdown/SET_SOCIAL_NEXT_PAGE', 1)
          this.$store.commit(
            'feedsDropdown/SET_SOCIAL_INITIAL_ITEM',
            this.socialLoadCount,
          )
          this.$store.commit(
            'feedsDropdown/SET_SOCIAL_LOAD_COUNT_NEXT',
            this.socialPerPage,
          )
        }
      }
      setTimeout(() => {
        const allItem = document.querySelectorAll('.overall_scroll')
        allItem.forEach((item) => {
          item.addEventListener('scroll', this.handleScroll)
        })
      }, 500)
    },
    groupFeedByType() {
      this.savedPostSettingsList = []
      this.emailFeedsList = []
      this.webFeedsList = []
      for (const item of this.savedPostSettings) {
        if (item.provider === 'Microsoft' || item.provider === 'Google') {
          this.emailFeedsList.push(item)
        } else if (item.provider === 'Web') {
          this.webFeedsList.push(item)
        } else {
          this.savedPostSettingsList.push(item)
        }
      }
      this.activeFeed()
    },
    expandSocial() {
      let totalType = 0
      if (
        this.savedPostSettingsList.length > 0 &&
        this.emailFeedsList.length > 0 &&
        this.webFeedsList.length > 0
      ) {
        totalType = 3
      } else if (
        (this.savedPostSettingsList.length > 0 &&
          this.emailFeedsList.length > 0) ||
        (this.savedPostSettingsList.length > 0 &&
          this.webFeedsList.length > 0) ||
        (this.webFeedsList.length > 0 && this.emailFeedsList.length > 0)
      ) {
        totalType = 2
      } else if (
        this.savedPostSettingsList.length > 0 ||
        this.emailFeedsList.length > 0 ||
        this.webFeedsList.length > 0
      ) {
        totalType = 1
      } else {
        totalType = 0
      }
      if (!this.socialType) {
        if (this.emailType && this.webType) {
          this.$emit(
            'define-height',
            this.savedPostSettingsList.length * 42 +
              this.emailFeedsList.length * 42 +
              this.webFeedsList.length * 42 +
              totalType * 42 +
              40,
          )
        } else if (this.emailType && !this.webType) {
          this.$emit(
            'define-height',
            this.savedPostSettingsList.length * 42 +
              this.emailFeedsList.length * 42 +
              totalType * 42 +
              40,
          )
        } else if (!this.emailType && this.webType) {
          this.$emit(
            'define-height',
            this.savedPostSettingsList.length * 42 +
              this.webFeedsList.length * 42 +
              totalType * 42 +
              40,
          )
        } else {
          this.$emit(
            'define-height',
            this.savedPostSettingsList.length * 42 + totalType * 42 + 40,
          )
        }
        setTimeout(() => {
          this.socialType = true
          this.setSocialEmailTotalPages()
          this.menuHeight = false
        }, 300)
      } else if (this.socialType) {
        this.socialType = false
        this.setSocialEmailTotalPages()
        setTimeout(() => {
          if (this.emailType && this.webType) {
            this.$emit(
              'define-height',
              this.emailFeedsList.length * 42 +
                this.webFeedsList.length * 42 +
                totalType * 42 +
                40,
            )
          } else if (this.emailType && !this.webType) {
            this.$emit(
              'define-height',
              this.emailFeedsList.length * 42 + totalType * 42 + 40,
            )
          } else if (!this.emailType && this.webType) {
            this.$emit(
              'define-height',
              this.webFeedsList.length * 42 + totalType * 42 + 40,
            )
          } else {
            this.$emit('define-height', totalType * 42 + 40)
          }
        }, 300)
        if (!this.socialType && !this.emailType && !this.webType) {
          this.menuHeight = true
        }
      }
    },
    expandEmail() {
      let totalType = 0
      if (
        this.savedPostSettingsList.length > 0 &&
        this.emailFeedsList.length > 0 &&
        this.webFeedsList.length > 0
      ) {
        totalType = 3
      } else if (
        (this.savedPostSettingsList.length > 0 &&
          this.emailFeedsList.length > 0) ||
        (this.savedPostSettingsList.length > 0 &&
          this.webFeedsList.length > 0) ||
        (this.webFeedsList.length > 0 && this.emailFeedsList.length > 0)
      ) {
        totalType = 2
      } else if (
        this.savedPostSettingsList.length > 0 ||
        this.emailFeedsList.length > 0 ||
        this.webFeedsList.length > 0
      ) {
        totalType = 1
      } else {
        totalType = 0
      }
      if (!this.emailType) {
        if (this.socialType && this.webType) {
          this.$emit(
            'define-height',
            this.emailFeedsList.length * 42 +
              this.savedPostSettingsList.length * 42 +
              this.webFeedsList.length * 42 +
              totalType * 42 +
              40,
          )
        } else if (this.socialType && !this.webType) {
          this.$emit(
            'define-height',
            this.savedPostSettingsList.length * 42 +
              this.emailFeedsList.length * 42 +
              totalType * 42 +
              40,
          )
        } else if (!this.socialType && this.webType) {
          this.$emit(
            'define-height',
            this.emailFeedsList.length * 42 +
              this.webFeedsList.length * 42 +
              totalType * 42 +
              40,
          )
        } else {
          this.$emit(
            'define-height',
            this.emailFeedsList.length * 42 + totalType * 42 + 40,
          )
        }
        setTimeout(() => {
          this.emailType = true
          this.setSocialEmailTotalPages()
          this.menuHeight = false
        }, 300)
      } else if (this.emailType) {
        this.emailType = false
        this.setSocialEmailTotalPages()
        setTimeout(() => {
          if (this.socialType && this.webType) {
            this.$emit(
              'define-height',
              this.savedPostSettingsList.length * 42 +
                this.webFeedsList.length * 42 +
                totalType * 42 +
                40,
            )
          } else if (this.socialType && !this.webType) {
            this.$emit(
              'define-height',
              this.savedPostSettingsList.length * 42 + totalType * 42 + 40,
            )
          } else if (!this.socialType && this.webType) {
            this.$emit(
              'define-height',
              this.webFeedsList.length * 42 + totalType * 42 + 40,
            )
          } else {
            this.$emit('define-height', totalType * 42 + 40)
          }
        }, 300)
        if (!this.socialType && !this.emailType && !this.webType) {
          this.menuHeight = true
        }
      }
    },
    expandWeb() {
      let totalType = 0
      if (
        this.savedPostSettingsList.length > 0 &&
        this.emailFeedsList.length > 0 &&
        this.webFeedsList.length > 0
      ) {
        totalType = 3
      } else if (
        (this.savedPostSettingsList.length > 0 &&
          this.emailFeedsList.length > 0) ||
        (this.savedPostSettingsList.length > 0 &&
          this.webFeedsList.length > 0) ||
        (this.webFeedsList.length > 0 && this.emailFeedsList.length > 0)
      ) {
        totalType = 2
      } else if (
        this.savedPostSettingsList.length > 0 ||
        this.emailFeedsList.length > 0 ||
        this.webFeedsList.length > 0
      ) {
        totalType = 1
      } else {
        totalType = 0
      }
      if (!this.webType) {
        if (this.socialType && this.emailType) {
          this.$emit(
            'define-height',
            this.webFeedsList.length * 42 +
              this.savedPostSettingsList.length * 42 +
              this.emailFeedsList.length * 42 +
              totalType * 42 +
              40,
          )
        } else if (this.socialType && !this.emailType) {
          this.$emit(
            'define-height',
            this.webFeedsList.length * 42 +
              this.savedPostSettingsList.length * 42 +
              totalType * 42 +
              40,
          )
        } else if (!this.socialType && this.emailType) {
          this.$emit(
            'define-height',
            this.emailFeedsList.length * 42 +
              this.webFeedsList.length * 42 +
              totalType * 42 +
              40,
          )
        } else {
          this.$emit(
            'define-height',
            this.webFeedsList.length * 42 + totalType * 42 + 40,
          )
        }
        setTimeout(() => {
          this.webType = true
          this.setSocialEmailTotalPages()
          this.menuHeight = false
        }, 300)
      } else if (this.webType) {
        this.webType = false
        this.setSocialEmailTotalPages()
        setTimeout(() => {
          if (this.socialType && this.emailType) {
            this.$emit(
              'define-height',
              this.savedPostSettingsList.length * 42 +
                this.emailFeedsList.length * 42 +
                totalType * 42 +
                40,
            )
          } else if (this.socialType && !this.emailType) {
            this.$emit(
              'define-height',
              this.savedPostSettingsList.length * 42 + totalType * 42 + 40,
            )
          } else if (!this.socialType && this.emailType) {
            this.$emit(
              'define-height',
              this.emailFeedsList.length * 42 + totalType * 42 + 40,
            )
          } else {
            this.$emit('define-height', totalType * 42 + 40)
          }
        }, 300)
        if (!this.socialType && !this.emailType && !this.webType) {
          this.menuHeight = true
        }
      }
    },
    selectedFeed(listItem) {
      let username = ''
      if (listItem.provider === 'Facebook') {
        username = listItem.name ? listItem.name : listItem.username
      } else {
        username = listItem.username ? listItem.username : listItem.name
      }
      this.$router.push('/home')
      this.nuxtApp.$bus.$emit('clear-all-date-range')
      this.$store.commit('home/RESET_START_END_DATE')
      if (this.showSinglePost || this.showSingleImagePost) {
        setTimeout(() => {
          this.$store.commit('home/SET_CURRENT_SOCIAL_COMPONENT', {
            provider: listItem.provider,
            username,
            id: listItem.id,
            selectedFeed: listItem,
          })
          this.$store.commit('home/SET_TEMP_ARRAY', [])
          this.$store.commit('socialFeed/SET_SHOW_TWITTER', true)
        }, 305)
      } else if (
        listItem.provider === 'Google' ||
        listItem.provider === 'Microsoft'
      ) {
        this.$store.commit('home/SET_EMAIL_DYNAMIC_COMP', {
          comp: 'EmailContent',
        })
        this.$store.commit('home/EXPAND_FULL_IMAGE', false)
        this.$store.commit('home/SET_TEMP_ARRAY', [])
        setTimeout(() => {
          this.$store.commit('home/SET_CURRENT_SOCIAL_COMPONENT', {
            provider: listItem.provider,
            username,
            id: listItem.id,
            selectedFeed: listItem,
          })
        }, 500)
      } else {
        this.$store.commit('home/SET_CURRENT_SOCIAL_COMPONENT', {
          provider: listItem.provider,
          username,
          id: listItem.id,
          selectedFeed: listItem,
        })

        this.$store.commit('home/SET_TEMP_ARRAY', [])
      }
      this.$store.commit('home/RESET_WEB_SEARCH')
      this.$store.commit('home/SET_SHOW_COMP', false)
      this.$store.commit('home/SET_CURRENT_TAB', 'All')
      this.$store.commit('home/SET_CURRENT_HEADER', 'RealTimeFeed')
      this.$store.dispatch('socialFeed/singlePostClose', false)
      this.$store.dispatch('socialFeed/singleImagePostClose', false)
      this.$store.commit('home/RESET_TWITTER_COMMENTS')
      this.$store.commit('home/RESET_PREVIOUS_TWITTER_COMMENTS')
      this.$store.commit('socialFeed/RESET_PREVIOUS_SINGLE_POST')
    },
    showEditFeed() {
      this.$store.commit('createPost/SET_SHOW_SAVE_POST_SETTINGS_MODAL', true)
      this.collapsed()
    },
    showAddFeedsComp() {
      this.$store.commit('socialFeed/SHOW_ADD_FEED_MODAL', true)
    },
    checkSelectedItem() {
      if (this.socialType && this.emailType && this.webType) {
        let tempArray = []
        if (this.socialTop) {
          tempArray = [
            ...this.savedPostSettingsList,
            ...this.emailFeedsList,
            ...this.webFeedsList,
          ]
        } else if (this.emailTop) {
          tempArray = [
            ...this.emailFeedsList,
            ...this.savedPostSettingsList,
            ...this.webFeedsList,
          ]
        } else if (this.webTop) {
          tempArray = [
            ...this.webFeedsList,
            ...this.savedPostSettingsList,
            ...this.emailFeedsList,
          ]
        }

        let enter = true
        if (enter) {
          for (const item of tempArray.slice(
            this.socialInitialItem,
            this.socialLoadCount,
          )) {
            if (item.backgroundColor === true) {
              enter = false
              this.socialStatus = true
              this.emailStatus = true
              this.webStatus = true
            }
          }
        }
        if (
          !this.socialStatus &&
          !this.emailStatus &&
          !this.webStatus &&
          enter
        ) {
          this.clickNextPage(false)
          // this.checkSelectedItem()
        }
      }
    },
    finalCollapsed() {
      this.toggle = false
      // if (!this.toggle && this.progress) {
      document.querySelectorAll('.overall_scroll').forEach((item) => {
        item.removeEventListener('scroll', this.handleScroll)
        item.scrollTop = 0
      })
      this.scroll = false
      this.increaseHeight = false
      this.$store.commit('feedsDropdown/SET_SHOW_DROPDOWN', false)
      setTimeout(() => {
        this.$emit('collapse-header', false)
      }, 140)
      setTimeout(() => {
        this.round = true
        this.menuOpen = false
        this.menuHeight = false
        this.$emit('expand', false)
        this.$store.commit('home/SET_FEEDS_DROPDOWN', false)
      }, 400)
      setTimeout(() => {
        this.progress = false
      }, 900)
      // }
    },
    collapsed($event) {
      if (this.progress) {
        this.toggle = false
      }
      if (!this.toggle && this.progress) {
        document.querySelectorAll('.overall_scroll').forEach((item) => {
          item.removeEventListener('scroll', this.handleScroll)
          item.scrollTop = 0
        })
        this.scroll = false
        this.increaseHeight = false
        this.$store.commit('feedsDropdown/SET_SHOW_DROPDOWN', false)
        setTimeout(() => {
          this.$emit('collapse-header', false)
        }, 140)
        setTimeout(() => {
          this.round = true
          this.menuOpen = false
          this.menuHeight = false
          this.$emit('expand', false)
          this.$store.commit('home/SET_FEEDS_DROPDOWN', false)
        }, 400)
        setTimeout(() => {
          this.progress = false
        }, 900)
      }
    },
    expand($event) {
      if (!this.progress) {
        this.$store.commit('feedsDropdown/SET_SOCIAL_INITIAL_ITEM', 0)
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_LOAD_COUNT',
          this.socialPerPage,
        )
        this.$store.commit('feedsDropdown/SET_OLD_SCROLL_TOP', 0)
        this.$store.commit('feedsDropdown/SET_CURRENT_SCROLL_POSITION', 0)
        this.$store.commit('feedsDropdown/SET_CURRENT_OLD_SCROLL_TOP', 0)
        this.$store.commit('feedsDropdown/SET_SOCIAL_CURRENT_PAGE', 1)
        this.activeFeed()
        if (this.isDesktop && this.showSingleImagePost) {
          setTimeout(() => {
            window.dispatchEvent(new Event('resize'))
          }, 100)
        }
        this.toggle = true
      } else if (this.progress) {
        if (this.isDesktop && this.showSingleImagePost) {
          setTimeout(() => {
            window.dispatchEvent(new Event('resize'))
          }, 800)
        }
        this.toggle = false
      }
      if (
        this.toggle &&
        !this.progress &&
        (this.emailType || this.socialType || this.webType) &&
        (this.emailFeedsList.length > 0 ||
          this.savedPostSettingsList.length > 0 ||
          this.webFeedsList.length > 0)
      ) {
        this.$store.commit(
          'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
          Math.ceil(
            (this.savedPostSettingsList.length +
              this.emailFeedsList.length +
              this.webFeedsList.length +
              1) /
              this.socialPerPage,
          ),
        )
        document.querySelectorAll('.overall_scroll').forEach((item) => {
          if (item.offsetHeight === 0) {
            setTimeout(() => {
              item.addEventListener('scroll', this.handleScroll)
            }, 500)
          }
        })
        this.menuOpen = true
        this.menuHeight = false
        this.round = false
        this.$emit('collapse-header', true)
        if (
          this.savedPostSettingsList.length > 0 &&
          this.emailFeedsList.length > 0 &&
          this.webFeedsList.length > 0
        ) {
          if (
            this.savedPostSettingsList.length +
              this.emailFeedsList.length +
              this.webFeedsList.length +
              3 >=
            this.socialPerPage
          ) {
            this.$emit('define-height', this.socialPerPage * 42 + 3 * 42 + 40)
          } else if (
            this.savedPostSettingsList.length +
              this.emailFeedsList.length +
              this.webFeedsList.length +
              3 <
            this.socialPerPage
          ) {
            this.$emit(
              'define-height',
              this.savedPostSettingsList.length * 42 +
                this.emailFeedsList.length * 42 +
                this.webFeedsList.length * 42 +
                3 * 42 +
                40,
            )
          }
        } else if (
          this.savedPostSettingsList.length > 0 &&
          this.emailFeedsList.length > 0
        ) {
          if (
            this.savedPostSettingsList.length +
              this.emailFeedsList.length +
              2 >=
            this.socialPerPage
          ) {
            this.$emit('define-height', this.socialPerPage * 42 + 2 * 42 + 40)
          } else if (
            this.savedPostSettingsList.length + this.emailFeedsList.length + 2 <
            this.socialPerPage
          ) {
            this.$emit(
              'define-height',
              this.savedPostSettingsList.length * 42 +
                this.emailFeedsList.length * 42 +
                2 * 42 +
                40,
            )
          }
        } else if (
          this.savedPostSettingsList.length > 0 &&
          this.webFeedsList.length > 0
        ) {
          if (
            this.savedPostSettingsList.length + this.webFeedsList.length + 2 >=
            this.socialPerPage
          ) {
            this.$emit('define-height', this.socialPerPage * 42 + 2 * 42 + 40)
          } else if (
            this.savedPostSettingsList.length + this.webFeedsList.length + 2 <
            this.socialPerPage
          ) {
            this.$emit(
              'define-height',
              this.savedPostSettingsList.length * 42 +
                this.webFeedsList.length * 42 +
                2 * 42 +
                40,
            )
          }
        } else if (
          this.emailFeedsList.length > 0 &&
          this.webFeedsList.length > 0
        ) {
          if (
            this.emailFeedsList.length + this.webFeedsList.length + 2 >=
            this.socialPerPage
          ) {
            this.$emit('define-height', this.socialPerPage * 42 + 2 * 42 + 40)
          } else if (
            this.emailFeedsList.length + this.webFeedsList.length + 2 <
            this.socialPerPage
          ) {
            this.$emit(
              'define-height',
              this.emailFeedsList.length * 42 +
                this.webFeedsList.length * 42 +
                2 * 42 +
                40,
            )
          }
        } else if (this.savedPostSettingsList.length > 0) {
          if (this.savedPostSettingsList.length + 1 >= this.socialPerPage) {
            this.$emit('define-height', this.socialPerPage * 42 + 42 + 40)
          } else {
            this.$emit(
              'define-height',
              this.savedPostSettingsList.length * 42 + 42 + 40,
            )
          }
        } else if (this.emailFeedsList.length > 0) {
          if (this.emailFeedsList.length + 1 >= this.socialPerPage) {
            this.$emit('define-height', this.socialPerPage * 42 + 42 + 40)
          } else {
            this.$emit(
              'define-height',
              this.emailFeedsList.length * 42 + 42 + 40,
            )
          }
        } else if (this.webFeedsList.length > 0) {
          if (this.webFeedsList.length + 1 >= this.socialPerPage) {
            this.$emit('define-height', this.socialPerPage * 42 + 42 + 40)
          } else {
            this.$emit('define-height', this.webFeedsList.length * 42 + 42 + 40)
          }
        }
        this.$store.commit('home/SET_FEEDS_DROPDOWN', true)
        this.$emit('expand', true)
        setTimeout(() => {
          this.increaseHeight = true
          this.$store.commit('feedsDropdown/SET_SHOW_DROPDOWN', true)
        }, 300)
        setTimeout(() => {
          this.scroll = true
          this.progress = true
        }, 900)
      } else if (!this.toggle && this.progress) {
        document.querySelectorAll('.overall_scroll').forEach((item) => {
          item.removeEventListener('scroll', this.handleScroll)
          item.scrollTop = 0
        })
        this.scroll = false
        this.increaseHeight = false
        this.$store.commit('feedsDropdown/SET_SHOW_DROPDOWN', false)
        setTimeout(() => {
          this.$emit('collapse-header', false)
        }, 140)
        setTimeout(() => {
          this.round = true
          this.menuOpen = false
          this.menuHeight = false
          this.$emit('expand', false)
          this.$store.commit('home/SET_FEEDS_DROPDOWN', false)
        }, 400)
        setTimeout(() => {
          this.progress = false
          this.activeFeed()
        }, 900)
      }
    },
    hideMobileHeader() {
      this.$store.dispatch('header/closeMobileHeader')
    },
    setNameOrUsername(feed) {
      if (feed.provider === 'Twitter') {
        const addAtSign = !feed.name.length > 0
        const name = feed.name.length > 0 ? feed.name : feed.username
        return addAtSign ? '@' + name : name
      } else {
        return feed.name.length > 0 ? feed.name : feed.username
      }
    },
  },
})
</script>

<style lang="scss" scoped>
@import url('https://fonts.googleapis.com/css2?family=Neuton');

name-font {
  font-family: 'Neuton', serif;
}

.feedsShadow {
  box-shadow: 0 25px 50px 20px rgb(0 0 0 / 25%);
}

.bg-white-opasity-50 {
  background-color: #ffffff80;
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s;
}
.fade-enter,
.fade-leave-to {
  height: 100%;
  opacity: 100;
}
.tooltip {
  @apply invisible;
}
.has-tooltip:hover .tooltip {
  @apply visible;
  left: -30px;
  padding: 3px 10px;
}
.text-xxs {
  font-size: 11px;
  line-height: 16px;
}
.min-w-7-2 {
  min-width: 1.875rem !important;
}
.min-h-7-2 {
  min-height: 1.875rem !important;
}

.menu-wrapper {
  .background {
    opacity: 0;
    position: absolute;
    z-index: 10;
    transition:
      margin-top 0.5s ease-in-out,
      opacity 0.3s ease 0.5s,
      background 0.3s ease 0.5s;
    height: 2.75rem;
    @apply w-full;

    .corner-top {
      position: absolute;
      top: -20px;
      right: 0px;
      display: inline-block;
      transition: color 0.3s ease 0.5s;
    }

    .corner-bottom {
      position: absolute;
      bottom: -20px;
      right: 0px;
      transform: rotate(270deg);
      display: inline-block;
      transition: color 0.3s ease 0.5s;
    }

    &.active {
      opacity: 1;
      .corner-top,
      .corner-bottom {
        // opacity: 1;
      }
    }
  }
}

.balance__menu {
  z-index: 80;
  transition:
    border-radius 0.5s ease-in-out,
    border-top-left-radius 0.5s ease-in-out,
    border-top-right-radius 0.5s ease-in-out;
  max-width: 16rem;
  min-width: 16rem;
  @apply md:w-full w-full;
  .btn-wrapper {
    background: #4c5764;
    @apply w-full px-4 flex justify-between items-center shadow-sm;

    .dropdown-btn {
      direction: ltr;
      line-height: 2.15rem !important;
      @apply text-white w-full h-10 text-lg focus:outline-none;
    }
  }
  .dropdown-btn {
    direction: ltr;
    @apply text-white w-full h-10 px-3.5 text-lg flex justify-between items-center shadow-sm focus:outline-none;
  }
}

/* these classname shuld be provided through the `source` props. */
.archive {
  background: #8db230 !important;
}

.search {
  background: #7d80bd !important;
}

.pricing {
  background: #a22a2a !important;
}
.savedSettings {
  background: #4a71d4 !important;
}

.alert {
  background: #e05252 !important;
}

.settings {
  background: #e0ad1f !important;
}

.help {
  background: #e05252 !important;
}

.feeds-button {
  color: #e4801d;
}
.username__archive,
.feeds-button__archive {
  color: #8db230 !important;
}
.username__search,
.feeds-button__search {
  color: #7d80bd !important;
}
.username__pricing,
.feeds-button__pricing {
  color: #a22a2a !important;
}
.username__alert,
.feeds-button__alert {
  color: #e05252 !important;
}
.username__settings,
.feeds-button__settings {
  color: #e0ad1f !important;
}

.dropdown {
  max-width: 16rem;
  overflow: hidden;
  @apply w-full;
  z-index: 100;
  overflow-x: hidden;
  height: 0;
  max-height: calc(100% - 132px);
  transform-origin: top;
  transition:
    transform 0.3s linear,
    height 0.5s linear;
  .dropdown-btn,
  .menu-title,
  .list-title,
  .equity {
    background: #e4801d;
    line-height: 2.15rem !important;
    @apply text-white w-full h-10 px-3 text-lg flex justify-between items-center shadow-sm focus:outline-none;

    * {
      // pointer-events: none;
    }

    &__archive {
      background: #5f822d !important;
    }

    &__search {
      background: #5a57a2 !important;
    }

    &__pricing {
      background: #a22a2a !important;
    }

    &__savedSettings {
      background: #4a71d4 !important;
    }

    &__alert {
      background: #9d1616 !important;
    }

    &__settings {
      background: #695316 !important;
    }

    &__help {
      background: #e05252 !important;
    }
  }

  .equity {
    @apply py-2;
    padding-left: 13px;
    padding-right: 13px;
  }

  .scroll {
    scroll-behavior: smooth;
    overflow-y: auto;
    overflow-x: hidden;
    -ms-overflow-style: none; /* IE 11 */
    scrollbar-width: thin;
    scrollbar-color: #a1cdff #ececec; /* Firefox 64 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    /* Track */
    &::-webkit-scrollbar-track {
      border-radius: 3px;
      background: #ececec;
    }

    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #a1cdff;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #a1cdff;
    }
  }
  .scroll__archive {
    scroll-behavior: smooth;
    scrollbar-color: #5f822d #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #5f822d;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #5f822d;
    }
  }
  .scroll__search {
    scroll-behavior: smooth;
    scrollbar-color: #5a57a2 #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #5a57a2;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #5a57a2;
    }
  }
  .scroll__pricing {
    scroll-behavior: smooth;
    scrollbar-color: #a22a2a #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #a22a2a;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #a22a2a;
    }
  }
  .scroll__alert {
    scroll-behavior: smooth;
    scrollbar-color: #9d1616 #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #9d1616;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #9d1616;
    }
  }
  .scroll__settings {
    scroll-behavior: smooth;
    scrollbar-color: #695316 #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #695316;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #695316;
    }
  }

  .list-wrapper {
    .group-archive {
      background: #b76d1d;
      font-style: normal;
      font-variant: normal;
      font-weight: normal;
      border-bottom: 0px;
      letter-spacing: 0;
      font-size: 1.125rem;
      color: #f2f2f2;

      &__name {
        height: 42px;
      }
      &__archive {
        background: #5f822d !important;
      }
      &__search {
        background: #5a57a2 !important;
      }
      &__pricing {
        background: #a22a2a !important;
      }
      &__savedSettings {
        background: #4a71d4 !important;
      }
      &__alert {
        background: #9d1616 !important;
      }
      &__settings {
        background: #695316 !important;
      }
      &__help {
        background: #e05252 !important;
      }

      .list {
        .list-item {
          height: 42px;
          background: #e4801d;
          border-color: #b76d1d;
        }
        .__archive {
          background: #8db230;
          border-color: #5f822d !important;
        }
        .__search {
          background: #7d80bd;
          border-color: #5a57a2 !important;
        }
        .__pricing {
          background: #d94848;
          border-color: #a22a2a !important;
        }
        .__savedSettings {
          background: #4a71d4 !important;
          border-color: #4a71d4 !important;
        }
        .__alert {
          background: #e05252;
          border-color: #9d1616 !important;
        }
        .__settings {
          background: #e0ad1f;
          border-color: #695316 !important;
        }
        .__help {
          background: #8db230;
          border-color: #5f822d !important;
        }

        .__active_home {
          background: #cc6f15;
        }
        .__active_archive {
          background: #7b9a29;
          border-color: #5f822d !important;
        }
        .__active_search {
          background: #696db4;
          border-color: #5a57a2 !important;
        }
        .__active_pricing {
          background: #c44040;
          border-color: #a22a2a !important;
        }
        .__active_savedSettings {
          background: #4a71d4;
          border-color: #4a71d4 !important;
        }
        .__active_alert {
          background: #9d1616;
          border-color: #9d1616 !important;
        }
        .__active_settings {
          background: #b18714;
          border-color: #695316 !important;
        }
      }
    }
  }

  .list-item {
    // direction: ltr;
    text-align: left;
  }

  &.expand {
    height: var(--height);
    // height: calc(100vh - 172px);
  }
  &.expand1 {
    height: auto;
  }
}

.dropdown-icon {
  pointer-events: none;

  &.rotate {
    transform: rotate(180deg) !important;
  }
}
.line-height {
  line-height: 16px;
}
.list-inner-width {
  width: calc(100% - 40px);
}
.username-width {
  width: calc(100% - 90px);
}
.username-clamp {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.mail-icon-wrapper {
  @apply flex justify-center items-center absolute w-3.5 h-3.5 rounded-full bg-white;
  bottom: -2px;
  right: -1px;
}
.social-icon {
  @apply absolute w-3.5 h-3.5;
  bottom: -2px;
  right: -1px;
}
.letter-spacing-1px {
  letter-spacing: 1px;
}
@media (max-width: 767px) {
  .dropdown {
    // transition: height 1s linear;
    max-width: 100%;
    max-height: var(--maxHeight);
  }
  .balance__menu {
    max-width: 100% !important;
  }
  .mobile-inner-width {
    width: calc(100% - 30px);
  }
  .mobile-username-width {
    width: calc(100% - 100px);
  }
  .mobile-clamp {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .expand {
    max-height: var(--maxHeight);
  }
}
/* for page */
.fadeIn-enter-active,
.fadeIn-leave-active {
  transition: opacity 0.5s;
}
.fadeIn-enter,
.fadeIn-leave-to {
  opacity: 0;
}
</style>
