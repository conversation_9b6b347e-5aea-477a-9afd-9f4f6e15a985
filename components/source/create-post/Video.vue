<script setup lang="ts">
import { useStore } from 'vuex'
import type { CapturedVideo } from '~/types/savePostSettings'
import CaptureVideo from './CaptureVideo.vue'
import Hashtags from './Hashtags.vue'
import PostTextInput from './PostTextInput.vue'
import PostToAccount from './PostToAccount.vue'
import Tags from './Tags.vue'

const store = useStore()
const { revokeVideoAndThumbnailURL } = useGeneratePost()

const capturedVideo = computed<CapturedVideo | null>(
  () => store.state.createPost.capturedVideo,
)
const isCaptureVideoComp = computed<boolean>(
  () => store.state.createPost.isCaptureVideoComp,
)

const setIsCaptureVideoComp = (isCaptureVideoComp: boolean) => {
  store.commit('createPost/SET_IS_CAPTURE_VIDEO_COMP', isCaptureVideoComp)
}
const setCapturedVideo = (video: CapturedVideo | null) => {
  store.commit('createPost/SET_CAPTURED_VIDEO', video)
}
const handleCaptureVideoDone = () => {
  setIsCaptureVideoComp(false)
}

const removeCapturedVideo = () => {
  revokeVideoAndThumbnailURL()
  setCapturedVideo(null)
  store.commit('createPost/UPDATE_PREVIEW_CONTENT', {
    video: null,
  })
  setIsCaptureVideoComp(true)
}

onUnmounted(() => {
  setIsCaptureVideoComp(true)
  setCapturedVideo(null)
  store.commit('createPost/SET_GENERATED_CONTENT', null)
  store.commit('createPost/SET_PREVIEW_CONTENT', null)
})
</script>

<template>
  <div class="size-full flex flex-col p-4 overflow-y-auto custom-scroll">
    <CaptureVideo
      v-if="isCaptureVideoComp"
      @videoDone="handleCaptureVideoDone"
    />
    <template v-else>
      <h3 class="font-semibold">Post to</h3>
      <PostToAccount />
      <div v-if="capturedVideo?.thumbnailUrl" class="mt-4 w-full">
        <div class="w-full h-auto max-w-[212px] relative group">
          <img
            :src="capturedVideo.thumbnailUrl"
            alt="Video"
            class="w-full h-auto rounded-lg aspect-[212/120] object-cover bg-gray-300"
          />
          <div
            class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 space-x-2 hidden group-hover:flex"
          >
            <button
              class="size-[35px] rounded-full bg-white flex justify-center items-center"
            >
              <SharedIconPencil class="text-[#525252] size-[18px]" />
            </button>
            <button
              @click="removeCapturedVideo"
              class="size-[35px] rounded-full bg-[#E21F3F] flex justify-center items-center"
            >
              <ClientOnly>
                <fa class="text-2xl text-white" :icon="['fas', 'times']" />
              </ClientOnly>
            </button>
          </div>
        </div>
      </div>
      <PostTextInput placeholder="Caption video" />
      <Hashtags class="mt-4" />
      <Tags />
    </template>
  </div>
</template>

<style lang="scss" scoped></style>
