<script setup lang="ts">
import { useStore } from 'vuex'
import facebookIconWhite from '~/assets/img/icon/FacebookIcon/fbBgWhite.svg'
import facebookIcon from '~/assets/img/icon/FacebookIcon/<EMAIL>'
import InstagramIconWhite from '~/assets/img/icon/InstagramIcon/instagraBgWhite.svg'
import InstagramIcon from '~/assets/img/icon/InstagramIcon/instagram-red.svg'
import linkedinIconWhite from '~/assets/img/icon/LinkedInIcon/linkedInBgWhite.svg'
import linkedinIcon from '~/assets/img/icon/LinkedInIcon/<EMAIL>'
import twitterIcon from '~/assets/img/icon/TwitterIcon/twitter.svg'
import twitterIconWhite from '~/assets/img/icon/TwitterIcon/twitterBgWhite.svg'
import InstagramPreviewComp from '~/components/source/hub/socials/InstagramSinglePost.vue'
import LinkedinPreviewComp from '~/components/source/hub/socials/LinkedinSinglePost.vue'
import FacebookPreviewComp from '~/components/source/hub/socials/SinglePost.vue'
import TwitterPreviewComp from '~/components/source/hub/socials/TwitterSinglePost.vue'
import type {
  AccountOption,
  CapturedVideo,
  PreviewContent,
} from '~/types/savePostSettings'

interface DataMap {
  [key: string]: any
}
interface Variant {
  normal: string
  white: string
}
interface IconsMap {
  [key: string]: Variant
}

const componentMap: DataMap = {
  Facebook: FacebookPreviewComp,
  Instagram: InstagramPreviewComp,
  LinkedIn: LinkedinPreviewComp,
  Twitter: TwitterPreviewComp,
}

const store = useStore()

const previewContent = computed<PreviewContent | null>(
  () => store.state.createPost.previewContent,
)
const socialPreviewComp = computed<string>(
  () => store.state.createPost.socialPreviewComp,
)
const socialMedias = computed<string[]>(() => {
  const postToAccounts: AccountOption[] =
    store.state.createPost.runningPostSettings?.postToAccounts || []

  const providers = postToAccounts.map((account) => account.provider)
  const uniqueProviders = [...new Set(providers)]

  if (uniqueProviders.length === 0) {
    setSocialPreviewComp('')
  } else {
    if (!socialPreviewComp.value) {
      setSocialPreviewComp(uniqueProviders[0])
    }
  }

  return uniqueProviders
})
const capturedVideo = computed<CapturedVideo | null>(
  () => store.state.createPost.capturedVideo,
)
const currentTab = computed<string>(() => store.state.createPost.currentTab)

const facebookPost = () => {
  const hashtags = previewContent.value?.hashtags?.length
    ? '<br/>' + previewContent.value.hashtags.map((tag) => tag.name).join(' ')
    : ''
  const images = previewContent.value?.images?.map((image) => image.url) || []
  const post = {
    tags: previewContent.value?.tags || [],
    archivedAttachmentImages: images,
    archivedFullPicture: '',
    archivedSourceUrl: '',
    attachmentImages: [],
    attachmentUrl: '',
    caption: '',
    commentsCount: '',
    createdAt: new Date().toISOString(),
    description: '',
    event: '',
    facebookUrl: '',
    fullPicture: '',
    id: 'fb-preview-post',
    isExpired: false,
    isHidden: false,
    isPublished: false,
    link: '',
    linkName: '',
    name: '',
    parentId: '',
    placeCity: '',
    placeCountry: '',
    placeName: '',
    privacy: 'Public',
    profileImageUrl: '/social/profile-picture.png',
    profilename: 'Sharparchive',
    provider: 'Facebook',
    reactionsCount: 0,
    sharesCount: 0,
    socialId: '',
    socialUid: '',
    sourceUrl: previewContent.value?.video?.videoUrl || '',
    statusType: 'added_photos',
    text: (previewContent.value?.text || '') + hashtags,
    type: previewContent.value?.video ? 'video' : '',
    updatedAt: new Date().toISOString(),
  }
  return post
}
const instagramPost = () => {
  const postType = previewContent.value?.video
    ? 'VIDEO'
    : previewContent.value?.images?.length &&
        previewContent.value.images?.length > 1
      ? 'CAROUSEL_ALBUM'
      : 'IMAGE'

  const hashtags = previewContent.value?.hashtags?.length
    ? '<br/>' + previewContent.value.hashtags.map((tag) => tag.name).join(' ')
    : ''
  const images = previewContent.value?.images?.map((image) => image.url) || []
  const post = {
    userName: 'Sharparchive',
    isVerified: true,
    profileImageUrl: '/social/profile-picture.png',
    type: postType, // Options: "CAROUSEL_ALBUM", "IMAGE", "VIDEO"
    text: (previewContent.value?.text || '') + hashtags,
    provider: 'Instagram',
    createdAt: new Date().toISOString(),
    likeCount: 0,
    commentsCount: 0,
    sourceUrl: previewContent.value?.video?.videoUrl || '',
    archivedSourceUrl: images?.length ? images[0] : '',
    archivedThumbnailUrl: previewContent.value?.video?.thumbnailUrl || '', // video thumb
    archivedChildrenMedias: images || [],
    tags: previewContent.value?.tags || [],
  }
  return post
}
const twitterPost = () => {
  const hashtags = previewContent.value?.hashtags?.length
    ? '<br/>' + previewContent.value.hashtags.map((tag) => tag.name).join(' ')
    : ''
  const tags = previewContent.value?.tags?.length
    ? '<br/>' +
      previewContent.value.tags
        .map(
          (tag) =>
            `<span style="color: rgb(29 155 240)">${tag.username}</span>`,
        )
        .join(' ')
    : ''
  const images = previewContent.value?.images?.map((image) => image.url) || []
  const post = {
    id: 'twitter-preview-post',
    socialId: '',
    name: 'Sharparchive',
    username: 'sharparchive',
    profileImageUrl: '/social/profile-picture.png',
    text: (previewContent.value?.text || '') + hashtags + tags,
    createdAt: new Date().toISOString(),
    replyCount: 0,
    retweetCount: 0,
    likeCount: 0,
    mediaUrl: images || [],
    mediaType: previewContent.value?.video ? 'video' : 'photo',
    videoUrl: previewContent.value?.video?.videoUrl || null,
    polls: [],
    provider: 'Twitter',
    tags: previewContent.value?.tags || [],
  }
  return post
}
const linkedinPost = () => {
  const hashtags = previewContent.value?.hashtags?.length
    ? '<br/>' +
      previewContent.value.hashtags
        .map((tag) => `<span class="text-[#0B66C3]">${tag.name}</span>`)
        .join(' ')
    : ''
  const category = previewContent.value?.video
    ? 'VIDEO'
    : previewContent.value?.images?.length &&
        previewContent.value?.images.length > 0
      ? 'IMAGE'
      : 'TEXT'
  const images = previewContent.value?.images?.map((image) => image.url) || []
  const post = {
    id: 'linkedin-preview-post',
    socialId: '',
    name: 'Sharparchive',
    userName: '',
    profilePic: '/social/profile-picture.png',
    profileImageUrl: '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    title: '',
    text: (previewContent.value?.text || '') + hashtags,
    description: '',
    poll: '',
    visibility: 'PUBLIC',
    lifecycleState: '',
    likedByCurrentUser: false,
    category: category,
    thumbnailUrl: previewContent.value?.video?.thumbnailUrl || '',
    mediaUrls: previewContent.value?.video
      ? [previewContent.value?.video?.videoUrl]
      : images || [],
    likesCount: 0,
    aggregatedCommentsCount: 0,
    firstLevelCommentsCount: 0,
    sourceUrl: null,
    provider: 'LinkedIn',
    tags: previewContent.value?.tags || [],
  }
  return post
}
const setSocialPreviewComp = (comp: string) => {
  store.commit('createPost/SET_SOCIAL_PREVIEW_COMP', comp)
}
const getSocialPost = (provider: string) => {
  switch (provider) {
    case 'Facebook':
      return facebookPost()
    case 'Instagram':
      return instagramPost()
    case 'Twitter':
      return twitterPost()
    case 'LinkedIn':
      return linkedinPost()
    default:
      return {}
  }
}
const getCurrentIcon = (provider: string) => {
  const iconsMap: IconsMap = {
    Facebook: {
      normal: facebookIcon,
      white: facebookIconWhite,
    },
    Instagram: {
      normal: InstagramIcon,
      white: InstagramIconWhite,
    },
    Twitter: {
      normal: twitterIcon,
      white: twitterIconWhite,
    },
    LinkedIn: {
      normal: linkedinIcon,
      white: linkedinIconWhite,
    },
  }
  return iconsMap[provider]
}

// dummy Report
const report = [
  {
    id: 1,
    text: 'Promissory statement',
    type: 'error',
  },
  {
    id: 2,
    text: `Instagram doesn't support links`,
    type: 'error',
  },
  {
    id: 3,
    text: 'Picture is mandatory for Instagram',
    type: 'error',
  },
  {
    id: 4,
    text: 'Your content is potentially offensive.',
    type: 'warning',
  },
  {
    id: 5,
    text: 'Make your link short',
    type: 'warning',
  },
]
</script>

<template>
  <div
    class="size-full py-4 overflow-y-auto custom-scroll flex flex-col flex-grow"
  >
    <BaseVideoPlayer
      v-if="capturedVideo && socialMedias.length === 0"
      :videoSrc="capturedVideo?.videoUrl"
      :thumbnail="capturedVideo?.thumbnailUrl"
      wrapperClass="w-full bg-gray-300"
    />
    <div v-else class="px-4 flex flex-col flex-grow">
      <div class="flex justify-center items-center space-x-4">
        <button
          v-for="media in socialMedias"
          :key="media"
          class="size-9 rounded-full aspect-square space-x-4"
          @click="setSocialPreviewComp(media)"
        >
          <img
            v-if="socialPreviewComp === media"
            class="size-9 rounded-full h-auto object-cover aspect-square"
            :src="getCurrentIcon(media).normal"
            alt="platform"
          />
          <img
            v-else
            class="size-9 rounded-full h-auto object-cover aspect-square"
            :src="getCurrentIcon(media).white"
            alt="platform"
          />
        </button>
      </div>
      <template v-if="socialPreviewComp">
        <Transition name="page" mode="out-in">
          <component
            :is="componentMap[socialPreviewComp]"
            :isLive="false"
            :singlePost="getSocialPost(socialPreviewComp)"
            class="max-w-[680px] w-full mx-auto mt-4"
            :class="[
              socialPreviewComp === 'Facebook' ? '!pt-2.5 pb-3' : '',
              socialPreviewComp === 'Twitter' ? '!py-4' : '',
            ]"
          />
        </Transition>
        <div class="bg-white rounded-lg px-6 pt-3.5 pb-4 mt-6">
          <p class="text-[#525252] font-semibold text-lg">Report</p>
          <div class="flex flex-col space-y-2 pt-4">
            <div
              v-for="item in report"
              :key="item.id"
              class="flex items-center justify-between rounded-lg px-4 py-3"
              :class="[item.type === 'error' ? 'bg-[#FFECEF]' : 'bg-[#FFF8E3]']"
            >
              <div class="flex items-center space-x-4 flex-grow">
                <div
                  v-if="item.type === 'error'"
                  class="flex justify-center items-center size-5 min-w-5 bg-[#E21F3F] rounded-sm"
                >
                  <SharedIconNo class="w-3.5 text-white" />
                </div>
                <div
                  v-else
                  class="flex justify-center items-center size-5 min-w-5 bg-[#DEA300] rounded-sm"
                >
                  <SharedIconWarning class="w-3.5 text-white" />
                </div>
                <span class="text-lg text-[#333333]">{{ item.text }}</span>
              </div>
              <button
                class="rounded-full text-white bg-[#4A71D4] text-sm px-4 py-[7px] min-w-[77px]"
              >
                Fix this
              </button>
            </div>
          </div>
        </div>
      </template>
      <div v-else class="flex justify-center items-center h-full">
        <p class="text-xl hidden">
          {{
            currentTab === 'Video'
              ? 'Capture video to preview'
              : 'Select an account to preview'
          }}
        </p>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
