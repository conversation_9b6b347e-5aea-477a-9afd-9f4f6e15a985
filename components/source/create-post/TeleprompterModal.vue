<script setup lang="ts">
import { useStore } from 'vuex'
import SpeedDropdown from './SpeedDropdown.vue'

const store = useStore()
const rawText = computed<string>(() => store.state.createPost.teleprompter.text)

// Process text into lines and wrap with spans
const processedText = computed<string>(() => {
  if (!rawText.value) return ''

  // Split by sentences or reasonable chunks
  const lines = rawText.value.split(/(?<=[.!?])\s+/)

  const mapLines = lines
    .map(
      (line, index) =>
        `<span class="teleprompter-line" id="line-${index}" data-line-index="${index}">${line}</span>`,
    )
    .join('<br>')

  return `<div class="w-full h-[120px]"></div>${mapLines}<div class="w-full h-[80px]"></div>`
})

const isPlaying = ref<boolean>(false)
const selectedSpeed = ref<number>(1)
const currentLineIndex = ref<number>(0)
const scrollContainer = ref<HTMLDivElement | null>(null)
const totalLines = ref<number>(0)
let scrollInterval: number | null = null

// Font size control
const MAX_FONT_SIZE = 48
const MIN_FONT_SIZE = 14
const fontSize = ref<number>(24)
const lineHeight = computed(() => `${fontSize.value * 1.9}px`)

const increaseFontSize = () => {
  if (fontSize.value < MAX_FONT_SIZE) fontSize.value += 2
}

const decreaseFontSize = () => {
  if (fontSize.value > MIN_FONT_SIZE) fontSize.value -= 2
}

const closeModal = () => {
  stopScrolling()
  store.commit('createPost/SET_TELEPROMPTER_IS_OPEN', false)
}

// Get scroll speed based on selected speed
const getScrollSpeed = () => {
  // Base speed in pixels per second
  const baseSpeed = 20
  // Return adjusted speed (0 means paused)
  return selectedSpeed.value === 0 ? 0 : baseSpeed * selectedSpeed.value
}

// Function to only stop scrolling without resetting position
const pauseAtEnd = () => {
  isPlaying.value = false
  if (scrollInterval) {
    clearInterval(scrollInterval)
    scrollInterval = null
  }
}

const startScrolling = () => {
  isPlaying.value = true

  if (scrollInterval) {
    clearInterval(scrollInterval)
  }

  const speed = getScrollSpeed()
  if (speed === 0) return

  // Dynamic interval time: Faster speeds use smaller intervals
  const intervalTime = Math.max(10, 50 / selectedSpeed.value) // Min 10ms, scales with speed
  const pixelsPerInterval = speed * (intervalTime / 1000)

  scrollInterval = window.setInterval(() => {
    if (!scrollContainer.value) return

    scrollContainer.value.scrollTop += pixelsPerInterval
    updateCurrentLine()

    // Use pauseAtEnd instead of stopScrolling when reaching the end
    if (
      scrollContainer.value.scrollTop >=
      scrollContainer.value.scrollHeight - scrollContainer.value.clientHeight
    ) {
      pauseAtEnd()
    }
  }, intervalTime)
}

const pauseScrolling = () => {
  isPlaying.value = false
  if (scrollInterval) {
    clearInterval(scrollInterval)
    scrollInterval = null
  }
}

const stopScrolling = () => {
  pauseScrolling()
  if (scrollContainer.value) {
    scrollContainer.value.scrollTop = 0
    currentLineIndex.value = 0
    updateLineHighlighting()
  }
}

const goToNextLine = () => {
  if (!scrollContainer.value) return

  if (currentLineIndex.value < totalLines.value - 1) {
    currentLineIndex.value++
    scrollToLine(currentLineIndex.value)
  }
}

const goToPreviousLine = () => {
  if (!scrollContainer.value) return

  if (currentLineIndex.value > 0) {
    currentLineIndex.value--
    scrollToLine(currentLineIndex.value)
  }
}

const scrollToLine = (lineIndex: number) => {
  if (!scrollContainer.value) return

  const lineElement = scrollContainer.value.querySelector(`#line-${lineIndex}`)
  if (lineElement) {
    // Scroll the line to the top of the visible area
    lineElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
    updateLineHighlighting()
  }
}

// For manual scrolling
const updateCurrentLineManual = () => {
  if (!scrollContainer.value) return

  // Get all line elements
  const lineElements =
    scrollContainer.value.querySelectorAll('.teleprompter-line')
  totalLines.value = lineElements.length

  if (lineElements.length === 0) return

  // Find the line that's closest to the top of the viewport
  let closestLine = 0
  let closestDistance = Infinity

  lineElements.forEach((element) => {
    const rect = element.getBoundingClientRect()
    const containerRect = scrollContainer.value!.getBoundingClientRect()
    const distance = Math.abs(rect.top - containerRect.top)

    if (distance < closestDistance) {
      closestDistance = distance
      closestLine = parseInt(element.getAttribute('data-line-index') || '0', 10)
    }
  })

  if (currentLineIndex.value !== closestLine) {
    currentLineIndex.value = closestLine
    updateLineHighlighting()
  }
}

// For automatic scrolling that waits for lines to fully pass
const updateCurrentLineAutoScroll = () => {
  if (!scrollContainer.value) return

  // Get all line elements
  const lineElements =
    scrollContainer.value.querySelectorAll('.teleprompter-line')
  totalLines.value = lineElements.length

  if (lineElements.length === 0) return

  // Get container coordinates
  const containerRect = scrollContainer.value.getBoundingClientRect()
  const containerTop = containerRect.top

  // Find the first line that's fully visible at the top
  let nextLineIndex = currentLineIndex.value

  for (let i = 0; i < lineElements.length; i++) {
    const rect = lineElements[i].getBoundingClientRect()

    // Check if the line is fully visible at the top of the container
    if (rect.top >= containerTop && rect.bottom > containerTop) {
      nextLineIndex = parseInt(
        lineElements[i].getAttribute('data-line-index') || '0',
        10,
      )
      break
    }
  }

  if (currentLineIndex.value !== nextLineIndex) {
    currentLineIndex.value = nextLineIndex
    updateLineHighlighting()
  }
}

const updateCurrentLine = () => {
  if (isPlaying.value) {
    updateCurrentLineAutoScroll()
  } else {
    updateCurrentLineManual()
  }
}

const updateLineHighlighting = () => {
  if (!scrollContainer.value) return

  // Reset all lines
  const allLines = scrollContainer.value.querySelectorAll('.teleprompter-line')
  allLines.forEach((line) => {
    line.classList.remove('highlighted')
    line.classList.remove('before-current')
  })

  // Highlight current line
  const currentLine = scrollContainer.value.querySelector(
    `#line-${currentLineIndex.value}`,
  )
  if (currentLine) {
    currentLine.classList.add('highlighted')
  }

  // Dim lines before current
  for (let i = 0; i < currentLineIndex.value; i++) {
    const line = scrollContainer.value.querySelector(`#line-${i}`)
    if (line) {
      line.classList.add('before-current')
    }
  }
}

const handleSpeedChange = () => {
  // If currently playing, restart with new speed
  if (isPlaying.value) {
    pauseScrolling()
    startScrolling()
  }
}

// Initialize after component is mounted
onMounted(async () => {
  await nextTick()

  if (scrollContainer.value) {
    const lineElements =
      scrollContainer.value.querySelectorAll('.teleprompter-line')
    totalLines.value = lineElements.length
    updateLineHighlighting()
  }
})

// Watch for changes in playing state
watch(isPlaying, (newValue) => {
  if (newValue) {
    startScrolling()
  } else {
    pauseScrolling()
  }
})

// Watch for changes in the text
watch(processedText, () => {
  stopScrolling()

  // Re-initialize with new text
  nextTick(() => {
    if (scrollContainer.value) {
      const lineElements =
        scrollContainer.value.querySelectorAll('.teleprompter-line')
      totalLines.value = lineElements.length
      updateLineHighlighting()
    }
  })
})

// Clean up intervals when component is destroyed
onBeforeUnmount(() => {
  if (scrollInterval) {
    clearInterval(scrollInterval)
  }
})
</script>

<template>
  <div
    class="w-[640px] h-[315px] rounded-2xl bg-white shadow-[4px_4px_16px_#0000002E] z-[9999]"
  >
    <div class="flex justify-between items-center p-6 pt-[22px]">
      <p class="text-lg font-semibold text-[#505050]">Teleprompter</p>
      <button @click="closeModal">
        <ClientOnly>
          <fa class="text-2xl text-[#505050]" :icon="['fas', 'times']" />
        </ClientOnly>
      </button>
    </div>

    <div class="relative pt-4 min-h-[172px] bottom-inner-shadow">
      <div
        ref="scrollContainer"
        v-html="processedText"
        class="font-semibold text-[#333333] text-left tracking-[0.38px] max-h-[172px] h-[172px] overflow-y-auto custom-scroll px-6"
        :style="{ fontSize: fontSize + 'px', lineHeight: lineHeight }"
        @scroll="updateCurrentLine"
      ></div>
    </div>

    <div class="px-6 flex justify-between items-center mt-3">
      <div class="flex items-center space-x-2 w-24">
        <button
          :disabled="fontSize >= MAX_FONT_SIZE"
          title="Increase font size"
          class="action-button"
          :class="[fontSize >= MAX_FONT_SIZE ? 'opacity-50' : '']"
          @click="increaseFontSize"
        >
          <SharedIconZoomIn class="w-4 text-[#525252]" />
        </button>
        <div class="w-[1px] h-4 bg-[#707070] bg-opacity-25"></div>
        <button
          :disabled="fontSize <= MIN_FONT_SIZE"
          title="Decrease font size"
          class="action-button"
          :class="[fontSize <= MIN_FONT_SIZE ? 'opacity-50' : '']"
          @click="decreaseFontSize"
        >
          <SharedIconZoomOut class="w-4 text-[#525252]" />
        </button>
      </div>
      <div class="flex items-center space-x-2">
        <button
          :disabled="currentLineIndex === 0"
          title="Previous"
          class="action-button"
          :class="[currentLineIndex === 0 ? 'opacity-50' : '']"
          @click="goToPreviousLine"
        >
          <SharedIconPrevious class="h-4 text-[#707070]" />
        </button>
        <button
          :disabled="
            !isPlaying &&
            currentLineIndex === totalLines - 1 &&
            totalLines !== 1
          "
          :title="isPlaying ? 'Pause' : 'Play'"
          class="action-button"
          :class="[
            !isPlaying &&
            currentLineIndex === totalLines - 1 &&
            totalLines !== 1
              ? 'opacity-50'
              : '',
          ]"
          @click="isPlaying = !isPlaying"
        >
          <SharedIconPause v-if="isPlaying" class="w-4 text-[#707070]" />
          <SharedIconPlay v-else class="h-4 text-[#707070]" />
        </button>
        <button title="Stop" class="action-button" @click="stopScrolling">
          <div class="size-4 rounded-sm bg-[#E21F3F]"></div>
        </button>
        <button
          :disabled="currentLineIndex === totalLines - 1"
          title="Next"
          class="action-button"
          :class="[currentLineIndex === totalLines - 1 ? 'opacity-50' : '']"
          @click="goToNextLine"
        >
          <SharedIconNext class="h-4 text-[#707070]" />
        </button>
      </div>
      <div class="flex items-center w-24 justify-end">
        <SpeedDropdown v-model="selectedSpeed" @change="handleSpeedChange" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.bottom-inner-shadow::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50px;
  background: linear-gradient(180deg, #ffffff00 0%, #ffffff 100%) 0% 0%;
}

.action-button {
  @apply size-[32px] rounded-full bg-white flex justify-center items-center hover:bg-gray-100/60;
}

/* These styles need to be global to affect the v-html content */
:deep(.teleprompter-line) {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 6px;
  transition: all 0.2s ease;
  line-height: 1.5;
}

:deep(.highlighted) {
  background-color: #d6e7ff;
}

:deep(.before-current) {
  /* color: #a0a0a0; */
}
</style>
