<script setup lang="ts">
import { useStore } from 'vuex'
import type { AiImage, GeneratedContent } from '~/types/savePostSettings'
import Hashtags from './Hashtags.vue'
import PostTextInput from './PostTextInput.vue'
import PostToAccount from './PostToAccount.vue'
import Tags from './Tags.vue'

const store = useStore()
const {
  selectedImages,
  updatePreviewContent,
  addOrUpdateSelectedImage,
  removeSelectedImage,
  getNumericId,
  setImageEditor,
} = useGeneratePost()

const content = computed<GeneratedContent | null>(
  () => store.state.createPost.generatedContent,
)

const handleSelectImage = (image: AiImage) => {
  if (selectedImages.value.some((img) => img.id === image.id)) return
  addOrUpdateSelectedImage(image)
  updatePreviewContent({ images: selectedImages.value })
}
const handleRemoveImage = (index: number): void => {
  removeSelectedImage(index)
  updatePreviewContent({ images: selectedImages.value })
}
const seeMoreImage = () => {
  store.dispatch('createPost/seeMoreImage')
}
const showEditImageModal = (isOpen: boolean, image: AiImage) => {
  const editorImage = {
    isOpen,
    image,
  }
  setImageEditor(editorImage)
}
const handleUploadImage = () => {
  try {
    const fileInput = document.createElement('input')
    fileInput.type = 'file'
    fileInput.accept = 'image/*'
    fileInput.multiple = true
    fileInput.click()

    fileInput.onchange = () => {
      if (fileInput.files && fileInput.files.length > 0) {
        const files = Array.from(fileInput.files)

        files.forEach((file) => {
          const generatedImageUrl = URL.createObjectURL(file)
          const image = {
            id: getNumericId(),
            url: generatedImageUrl,
          }
          addOrUpdateSelectedImage(image)

          // For Cleanup
          store.commit('createPost/SET_OBJECT_URLS', generatedImageUrl)
        })

        updatePreviewContent({ images: selectedImages.value })
      }
    }
  } catch (error) {
    console.error('Error uploading images:', error)
  }
}

onUnmounted(() => {
  store.commit('createPost/SET_SELECTED_IMAGES', [])
  store.commit('createPost/SET_GENERATED_CONTENT', null)
  store.commit('createPost/SET_PREVIEW_CONTENT', null)
})
</script>

<template>
  <div class="size-full flex flex-col p-4 overflow-y-auto custom-scroll">
    <h3 class="font-semibold">Post to</h3>
    <PostToAccount />
    <PostTextInput />

    <!-- Select Images Section -->
    <div
      v-if="
        (content && content?.images?.length > 0) || selectedImages.length > 0
      "
      class="flex flex-col"
    >
      <div
        v-if="selectedImages.length > 0"
        class="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2 overflow-y-auto custom-scroll"
      >
        <div
          v-for="(image, index) in selectedImages"
          :key="index"
          class="w-full relative group"
        >
          <img
            :src="image.url"
            :alt="image.url"
            class="w-full h-auto rounded-lg aspect-[214/120] border-4 border-[#4A71D4] object-cover"
          />
          <div
            class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 space-x-2 hidden group-hover:flex"
          >
            <button
              @click="showEditImageModal(true, image)"
              class="size-[35px] rounded-full bg-white flex justify-center items-center"
            >
              <SharedIconPencil class="text-[#525252] size-[18px]" />
            </button>
            <button
              class="size-[35px] rounded-full bg-[#E21F3F] flex justify-center items-center"
              @click="handleRemoveImage(image.id)"
            >
              <ClientOnly>
                <fa class="text-2xl text-white" :icon="['fas', 'times']" />
              </ClientOnly>
            </button>
          </div>
        </div>
      </div>
      <template v-if="content && content?.images?.length > 0">
        <div
          class="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 m-xl:grid-cols-4 gap-2 overflow-y-auto custom-scroll"
          :class="[
            selectedImages.length > 0 ? 'max-h-[186px]' : 'max-h-[320px]',
          ]"
        >
          <img
            v-for="image in content.images"
            :key="image.id"
            :src="image.url"
            :alt="image.url"
            class="w-full h-auto rounded-lg aspect-[214/120] cursor-pointer object-cover"
            @click="handleSelectImage(image)"
          />
        </div>
        <button
          class="text-[#4A71D4] text-base font-medium mt-3.5 ml-auto"
          @click="seeMoreImage"
        >
          More
        </button>
      </template>
    </div>

    <!-- Search Image, Upload Image and Video Clip Section -->
    <div class="flex items-center py-4 space-x-4">
      <input
        type="text"
        placeholder="Describe image you want to generate"
        class="flex-grow text-[#525252] h-[35px] px-6 py-1.5 bg-[#E3EFFF] rounded-[20px] outline-none placeholder:text-[#525252]"
      />
      <div class="flex items-center space-x-2">
        <button
          class="rounded-full size-[35px] bg-[#E3EFFF] flex justify-center items-center hover:bg-[#ddebff]"
        >
          <ClientOnly>
            <fa
              class="text-base rounded-full"
              :class="'text-[#525252]'"
              :icon="['fas', 'magnifying-glass']"
            />
          </ClientOnly>
        </button>
        <button
          class="rounded-full size-[35px] bg-[#E3EFFF] flex justify-center items-center hover:bg-[#ddebff]"
        >
          <SharedIconVideo class="fill-[#525252]" />
        </button>
        <button
          @click="handleUploadImage"
          class="rounded-full size-[35px] bg-[#E3EFFF] flex justify-center items-center hover:bg-[#ddebff]"
        >
          <SharedIconVerticalClip class="fill-[#525252]" />
        </button>
      </div>
    </div>

    <Hashtags />
    <Tags />
  </div>
</template>

<style lang="scss" scoped></style>
