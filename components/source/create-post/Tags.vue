<script setup lang="ts">
import { useStore } from 'vuex'
import type { GeneratedContent, Tag } from '~/types/savePostSettings'

const store = useStore()

const { selectedTags, updatePreviewContent } = useGeneratePost()

const content = computed<GeneratedContent | null>(
  () => store.state.createPost.generatedContent,
)

const isTagExist = (tag: Tag) => {
  return selectedTags.value.some((item) => item.id === tag.id)
}

const handleSelectTags = (tag: Tag) => {
  if (isTagExist(tag)) {
    selectedTags.value = selectedTags.value.filter((item) => item.id !== tag.id)
  } else {
    selectedTags.value.push(tag)
  }
  updatePreviewContent({ tags: selectedTags.value })
}
</script>

<template>
  <div
    v-if="content && content?.tags?.length > 0"
    class="flex flex-col pt-3.5 pb-4 border-t border-[#2E2B2B]/20"
  >
    <p class="text-base text-[#707070] font-medium">Tags</p>
    <div class="pt-2 flex items-start justify-between space-x-2">
      <div
        class="text-sm text-[#525252] font-medium flex items-center flex-wrap gap-2"
      >
        <button
          v-for="tag in content.tags"
          :key="tag.id"
          class="rounded-full px-4 py-[6.5px]"
          :class="[
            isTagExist(tag) ? 'bg-[#4A71D4] text-white' : 'bg-[#EBEDF5]',
          ]"
          @click="handleSelectTags(tag)"
        >
          {{ tag.username }}
        </button>
      </div>
      <button class="text-[#4A71D4] text-base font-medium mt-[7px]">
        More
      </button>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
