<script setup lang="ts">
import { useStore } from 'vuex'
import type { GeneratedContent, Hashtag } from '~/types/savePostSettings'

const store = useStore()

const { selectedHashtags, updatePreviewContent } = useGeneratePost()

const content = computed<GeneratedContent | null>(
  () => store.state.createPost.generatedContent,
)

const isHashtagExist = (hashtag: Hashtag) => {
  return selectedHashtags.value.some((item) => item.id === hashtag.id)
}

const handleSelectHashtags = (hashtag: Hashtag) => {
  if (isHashtagExist(hashtag)) {
    selectedHashtags.value = selectedHashtags.value.filter(
      (item) => item.id !== hashtag.id,
    )
  } else {
    selectedHashtags.value.push(hashtag)
  }
  updatePreviewContent({ hashtags: selectedHashtags.value })
}
</script>

<template>
  <div
    v-if="content && content?.hashtags?.length > 0"
    class="flex flex-col pt-3.5 pb-4 border-t border-[#2E2B2B]/20"
  >
    <p class="text-base text-[#707070] font-medium">Hashtags</p>
    <div class="pt-2 flex items-start justify-between space-x-2">
      <div
        class="text-sm text-[#525252] font-medium flex items-center flex-wrap gap-2"
      >
        <button
          v-for="tag in content.hashtags"
          :key="tag.id"
          class="rounded-full px-4 py-[6.5px]"
          :class="[
            isHashtagExist(tag) ? 'bg-[#4A71D4] text-white' : 'bg-[#EBEDF5]',
          ]"
          @click="handleSelectHashtags(tag)"
        >
          {{ tag.name }}
        </button>
      </div>
      <button class="text-[#4A71D4] text-base font-medium mt-[7px]">
        More
      </button>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
