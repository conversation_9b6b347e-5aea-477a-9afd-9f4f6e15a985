<script setup lang="ts">
interface OptionType {
  label: string
  value: number
}

const options: OptionType[] = [
  { label: '0x', value: 0 },
  { label: '1x', value: 1 },
  { label: '2x', value: 2 },
  { label: '3x', value: 3 },
  { label: '4x', value: 4 },
  { label: '5x', value: 5 },
]

const emit = defineEmits<{
  change: []
}>()

const isOpen = ref(false)
const model = defineModel<number>({ default: 0 })
const dropdownRef = ref<HTMLDivElement | null>(null)

const selectedOption = computed(() => {
  return options.find((opt) => opt.value === model.value)
})

const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

const selectOption = (option: OptionType) => {
  model.value = option.value
  isOpen.value = false
  emit('change')
}

const handleClickOutside = (event: Event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div ref="dropdownRef" class="relative">
    <button
      title="Speed"
      class="text-[#525252] text-base font-bold size-[32px] rounded-full bg-white flex justify-center items-center hover:bg-gray-100/60"
      @click="toggleDropdown"
    >
      {{ selectedOption?.label || 'Select' }}
    </button>

    <ul
      v-if="isOpen"
      class="absolute bottom-full right-0 bg-white shadow-[4px_4px_16px_#0000002E] rounded-md z-10 py-1"
    >
      <li
        v-for="option in options.slice().reverse()"
        :key="option.value"
        class="text-[#525252] text-base font-bold px-4 py-0.5 hover:bg-gray-100/40 cursor-pointer text-right"
        :class="{ 'bg-gray-100/60': model === option.value }"
        @click="selectOption(option)"
      >
        {{ option.label }}
      </li>
    </ul>
  </div>
</template>

<style scoped></style>
