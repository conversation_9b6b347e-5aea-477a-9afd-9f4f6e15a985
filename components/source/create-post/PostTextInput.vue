<script setup lang="ts">
import type { EmojiExt } from 'vue3-emoji-picker'

interface Props {
  placeholder?: string
}

withDefaults(defineProps<Props>(), {
  placeholder: 'Type your post',
})

const { text } = useGeneratePost()
const showEmojiPicker = ref(false)
const textareaRef = ref<HTMLTextAreaElement | null>(null)
const emojiPickerRef = ref<HTMLDivElement | null>(null)
const emojiButtonRef = ref<HTMLButtonElement | null>(null)

const toggleEmojiPicker = () => {
  showEmojiPicker.value = !showEmojiPicker.value
  const el = textareaRef.value
  if (!el) return
  nextTick(() => {
    el.focus()
  })
}

const addEmoji = (emoji: EmojiExt) => {
  const el = textareaRef.value
  if (!el) return

  const start = el.selectionStart
  const end = el.selectionEnd
  const emojiChar = emoji.i

  text.value = text.value.slice(0, start) + emojiChar + text.value.slice(end)

  nextTick(() => {
    el.focus()
    el.setSelectionRange(start + emojiChar.length, start + emojiChar.length)
  })
}

const handleClickOutside = (event: MouseEvent): void => {
  if (!emojiPickerRef.value || !event.target) return

  const target = event.target as Node
  const clickedOnEmojiPicker = emojiPickerRef.value.contains(target)
  const clickedOnEmojiButton = emojiButtonRef.value?.contains(target) || false

  if (!clickedOnEmojiPicker && !clickedOnEmojiButton) {
    showEmojiPicker.value = false
  }
}

onMounted(() => {
  window.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  window.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div class="relative">
    <div class="w-full relative rounded-lg min-h-20 h-20 mt-4 overflow-hidden">
      <textarea
        ref="textareaRef"
        name="post"
        id="post"
        v-model="text"
        rows="3"
        :placeholder="placeholder"
        class="resize-none w-full h-full bg-[#F1F2F6] rounded-lg text-[#525252] text-base pl-4 pr-10 py-3.5 focus:outline-none placeholder:text-[#707070] custom-scroll"
      ></textarea>
      <button
        ref="emojiButtonRef"
        @click.stop="toggleEmojiPicker"
        class="absolute right-4 bottom-4 size-5 rounded-full flex justify-center items-center"
      >
        <SharedIconEmojiSmileFace />
      </button>
    </div>

    <div
      ref="emojiPickerRef"
      v-if="showEmojiPicker"
      class="absolute right-4 top-[calc(100%-10px)] z-20"
    >
      <NuxtEmojiPicker
        :native="false"
        :display-recent="true"
        :static-texts="{ placeholder: 'Search' }"
        :disable-skin-tones="true"
        theme="light"
        @select="addEmoji"
        class="emoji-picker"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.v3-body-inner) {
  scrollbar-color: #a1cdff #a1cdff50;
}
</style>
