<script setup lang="ts">
import { useStore } from 'vuex'
import type { PostSettings } from '~/types/savePostSettings'

const {
  tabs,
  targetWordCountOptions,
  targetLengthOptions,
  autoContentOptions,
  contentStyleOptions,
  currentTab,
  autoImage,
  teleprompter,
  autoHashtags,
  autoUsernames,
  autoShortenLinks,
  wordCountRange,
  targetLengthRange,
  contentStyle,
  autoContent,
  prompt,
  name,
  selectedAccounts,
  autoGenerateContent,
  updateRunningSettings,
  handleToggleAutoImage,
  handleToggleTeleprompter,
  handleToggleAutoHashtags,
  handleToggleAutoUsernames,
  handleToggleAutoShortenLinks,
} = useSavePostSettings()

const store = useStore()

const runningPostSettings = computed<PostSettings | null>(
  () => store.state.createPost.runningPostSettings,
)
const globalCurrentTab = computed(() => store.state.createPost.currentTab)
const currentSettings = computed<PostSettings>(() => {
  const settings: PostSettings = {
    postType: currentTab.value,
    name: name.value,
    postToAccounts: selectedAccounts.value,
    autoImage: autoImage.value,
    teleprompter: teleprompter.value,
    autoHashtags: autoHashtags.value,
    autoUsernames: autoUsernames.value,
    autoShortenLinks: autoShortenLinks.value,
    autoGenerateContent: autoGenerateContent.value,
    prompt: prompt.value,
    wordCountRange: wordCountRange.value?.range || null,
    targetLengthRange: targetLengthRange.value?.range || null,
    contentStyle: contentStyle.value?.label || null,
    autoContent: autoContent.value?.label || null,
  }
  return settings
})
const isCaptureVideoComp = computed<boolean>(
  () => store.state.createPost.isCaptureVideoComp,
)

const handleTabChange = () => {
  store.commit('createPost/SET_CURRENT_TAB', currentTab.value)
  updateRunningSettings({ postType: currentTab.value })
}
const toggleAutoImage = () => {
  handleToggleAutoImage()
  updateRunningSettings({ autoImage: autoImage.value })
}
const toggleTeleprompter = () => {
  handleToggleTeleprompter()
  updateRunningSettings({ teleprompter: teleprompter.value })
}
const toggleAutoHashtags = () => {
  handleToggleAutoHashtags()
  updateRunningSettings({ autoHashtags: autoHashtags.value })
}
const toggleAutoUsernames = () => {
  handleToggleAutoUsernames()
  updateRunningSettings({ autoUsernames: autoUsernames.value })
}
const toggleAutoShortenLinks = () => {
  handleToggleAutoShortenLinks()
  updateRunningSettings({ autoShortenLinks: autoShortenLinks.value })
}
const changeTargetWordCount = () => {
  updateRunningSettings({ wordCountRange: wordCountRange.value?.range })
}
const changeTargetLength = () => {
  updateRunningSettings({ targetLengthRange: targetLengthRange.value?.range })
}
const changeContentStyle = () => {
  updateRunningSettings({ contentStyle: contentStyle.value?.label })
}
const changeAutoContent = () => {
  updateRunningSettings({ autoContent: autoContent.value?.label })
}
const changePromtInput = () => {
  updateRunningSettings({ prompt: prompt.value })
}
const handleGeneratePost = () => {
  store.dispatch('createPost/getGeneratedContent', runningPostSettings.value)
}

watch(globalCurrentTab, () => {
  currentTab.value = globalCurrentTab.value
})

onMounted(() => {
  store.commit('createPost/SET_RUNNING_POST_SETTINGS', currentSettings.value)
})
onUnmounted(() => {
  store.commit('createPost/SET_GENERATED_CONTENT', null)
  store.commit('createPost/SET_PREVIEW_CONTENT', null)
})
</script>

<template>
  <div class="min-w-[272px] h-full pt-4 border-r-2 border-[#2e2b2b13]">
    <div class="w-full px-4">
      <BaseTabs
        v-model="currentTab"
        :tabs="tabs"
        :is-route-enable="false"
        tabsClass="w-[240px] h-[35px] bg-[#EBEDF5] shadow-md"
        circleClass="bg-[#4A71D4]"
        @change="handleTabChange"
      />
    </div>
    <div
      class="w-full h-[calc(100%-40px)] px-4 pb-4 overflow-y-auto custom-scroll"
    >
      <div class="w-full mt-6 space-y-[15px]">
        <div v-if="currentTab === 'Text'" class="setting-item">
          <span>Auto Image</span>
          <InputsToggleInput
            :id="0"
            :select="autoImage"
            uncheckedLabelBgColor="#EBEDF5"
            @toggle-select="toggleAutoImage"
          />
        </div>
        <div
          v-if="currentTab === 'Video' && isCaptureVideoComp"
          class="setting-item"
        >
          <span>Teleprompter</span>
          <InputsToggleInput
            :id="1"
            :select="teleprompter"
            uncheckedLabelBgColor="#EBEDF5"
            @toggle-select="toggleTeleprompter"
          />
        </div>
        <div class="setting-item">
          <span>Auto #Hashtags</span>
          <InputsToggleInput
            :id="2"
            :select="autoHashtags"
            uncheckedLabelBgColor="#EBEDF5"
            @toggle-select="toggleAutoHashtags"
          />
        </div>
        <div class="setting-item">
          <span>Auto @Usernames</span>
          <InputsToggleInput
            :id="3"
            :select="autoUsernames"
            uncheckedLabelBgColor="#EBEDF5"
            @toggle-select="toggleAutoUsernames"
          />
        </div>
        <div class="setting-item">
          <span>Auto Shorten Links</span>
          <InputsToggleInput
            :id="4"
            :select="autoShortenLinks"
            uncheckedLabelBgColor="#EBEDF5"
            @toggle-select="toggleAutoShortenLinks"
          />
        </div>
      </div>
      <div class="w-full mt-[22px] space-y-3.5 text-base text-[#525252]">
        <h4 class="font-semibold">Genetate</h4>
        <div v-if="currentTab === 'Text'" class="space-y-2">
          <h6>Target Word Count</h6>
          <BaseDropsDown
            :options="targetWordCountOptions"
            v-model="wordCountRange"
            labelKey="range"
            placeholder="Select Word Range"
            :dorpdownPlaceholder="true"
            :menuWidth="240"
            :menuHeight="35"
            menuBgColor="#4A71D4"
            menuTextColor="#fff"
            arrowColor="#fff"
            :dropdownWidth="240"
            :dropdownMaxHeight="290"
            dropsdownTextColor="#525252"
            scrollbarTrackColor="#a1cdff50"
            scrollbarThumbColor="#a1cdff"
            @change="changeTargetWordCount"
          />
        </div>
        <div v-else class="space-y-2">
          <h6>Target Length</h6>
          <BaseDropsDown
            :options="targetLengthOptions"
            v-model="targetLengthRange"
            labelKey="range"
            placeholder="Select Target Length"
            :dorpdownPlaceholder="true"
            :menuWidth="240"
            :menuHeight="35"
            menuBgColor="#4A71D4"
            menuTextColor="#fff"
            arrowColor="#fff"
            :dropdownWidth="240"
            :dropdownMaxHeight="290"
            dropsdownTextColor="#525252"
            scrollbarTrackColor="#a1cdff50"
            scrollbarThumbColor="#a1cdff"
            @change="changeTargetLength"
          />
        </div>
        <div class="space-y-2">
          <h6>Content Style</h6>
          <BaseDropsDown
            :options="contentStyleOptions"
            v-model="contentStyle"
            labelKey="label"
            placeholder="Select Content Style"
            :dorpdownPlaceholder="true"
            :menuWidth="240"
            :menuHeight="35"
            menuBgColor="#4A71D4"
            menuTextColor="#fff"
            arrowColor="#fff"
            :dropdownWidth="240"
            :dropdownMaxHeight="290"
            dropsdownTextColor="#525252"
            scrollbarTrackColor="#a1cdff50"
            scrollbarThumbColor="#a1cdff"
            @change="changeContentStyle"
          />
        </div>
        <div class="space-y-2">
          <h6>Auto Content</h6>
          <BaseDropsDown
            :options="autoContentOptions"
            v-model="autoContent"
            labelKey="label"
            placeholder="Select Content Type"
            :dorpdownPlaceholder="true"
            :menuWidth="240"
            :menuHeight="35"
            menuBgColor="#4A71D4"
            menuTextColor="#fff"
            arrowColor="#fff"
            :dropdownWidth="240"
            :dropdownMaxHeight="290"
            dropsdownTextColor="#525252"
            scrollbarTrackColor="#a1cdff50"
            scrollbarThumbColor="#a1cdff"
            @change="changeAutoContent"
          />
        </div>
      </div>
      <textarea
        name="optional"
        id="optional"
        v-model="prompt"
        rows="10"
        placeholder="Describe what you want (Optional)"
        class="resize-none w-full mt-6 bg-[#E3EFFF] rounded-lg text-[#333333] text-base p-4 focus:outline-none placeholder:text-[#525252] h-[300px] custom-scroll"
        @input="changePromtInput"
      ></textarea>
      <button
        class="ml-auto mt-[22px] h-[35px] px-6 rounded-full flex justify-center items-center text-base bg-[#4A71D4] text-white font-semibold"
        @click="handleGeneratePost"
      >
        Generate
      </button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.setting-item {
  @apply flex items-center justify-between;
  span {
    @apply text-base text-[#525252];
  }
}
</style>
