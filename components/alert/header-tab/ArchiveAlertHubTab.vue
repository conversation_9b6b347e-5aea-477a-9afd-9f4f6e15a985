<template>
  <div
    ref="contentWrapper"
    class="content__tabs flex flex-wrap bg-[#fff] items-center rounded-full h-[39px] md:shadow w-[450px]"
  >
    <div class="hidden 2xl:block">
      <div
        class="background__circle bg-[#D63C3C]"
        :style="{
          left: `${offsetLeftPx}px`,
          width: `${backgroundWidth + 1}px`,
        }"
      ></div>
    </div>

    <div class="2xl:hidden">
      <div
        class="background__circle bg-[#D63C3C]"
        :style="{
          left: `${offsetLeftPx}px`,
          width: `${backgroundWidth}px`,
        }"
      ></div>
    </div>

    <NuxtLink
      ref="feeds"
      to="/setting/archive"
      :class="route.fullPath.includes('archive') ? 'active' : ''"
      class="tab cursor-pointer rounded-full text-center h-auto md:px-4 px-3 py-1 w-1/3"
      data-index="0"
      @click.native="
        (showTabMenu('SettingArchive', '.content__tabs', '.tab.active', $event),
        animate('.tab', $event))
      "
    >
      <span
        class="pointer-events-none text-[#525252] md:text-base text-sm font-semibold"
      >
        Archive
      </span>
    </NuxtLink>

    <NuxtLink
      ref="groups"
      to="/setting/alert"
      :class="route.fullPath.includes('alert') ? 'active' : ''"
      class="tab cursor-pointer rounded-full text-center h-auto md:px-4 px-3 py-1 w-1/3"
      data-index="1"
      @click.native="
        (showTabMenu('SettingAlert', '.content__tabs', '.tab.active', $event),
        animate('.tab', $event))
      "
    >
      <span
        class="pointer-events-none text-[#525252] md:text-base text-sm font-semibold"
      >
        Alert
      </span>
    </NuxtLink>

    <NuxtLink
      ref="response"
      to="/setting/hub"
      :class="route.fullPath.includes('hub') ? 'active' : ''"
      class="tab cursor-pointer rounded-full text-center h-auto md:px-4 px-3 py-1 w-1/3"
      data-index="2"
      @click.native="
        (showTabMenu('SettingHub', '.content__tabs', '.tab.active', $event),
        animate('.tab', $event))
      "
    >
      <span
        class="pointer-events-none text-[#525252] md:text-base text-sm font-semibold"
      >
        Hub
      </span>
    </NuxtLink>
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia'
// import { defineComponent, ref, onMounted } from '@nuxtjs/composition-api'
import { useTab } from '~/composables/feeds/useTab.js'
import { useAlert } from '~/stores/alert'

const contentWrapper = ref(null)
const store = useAlert()
// const { feedsGroupsResponseTab, showSummaryTableData } = storeToRefs(store)
const {
  tab,
  animate,
  showTabMenu,
  offsetLeftPx,
  activeComponent,
  backgroundWidth,
} = useTab('SettingAlert')

onMounted(() => {
  const activeLink = contentWrapper.value.querySelector('.tab.active')
  tab(activeLink)
})
const route = useRoute()
</script>

<style lang="scss" scoped>
.content__tabs {
  position: relative;
  .background__circle {
    top: 0px;
    left: 0px;
    z-index: -1;
    transition:
      width 0.2s ease-in-out 0.2s,
      left 0.5s ease-in-out;
    z-index: 1;
    @apply absolute h-full rounded-full inline-block;
  }
  .tab {
    @apply relative overflow-hidden;
    > span {
      position: relative;
      transition: color 0.2s ease-in-out;
      z-index: 10;
      @apply text-[#525252];
    }
    &.active {
      > span {
        @apply text-white;
      }
    }
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .tab-width {
    width: 20.5rem;
  }
}
@media (max-width: 767px) {
  .tab-width {
    width: 100%;
  }
}
</style>
