<script setup lang="ts">
const isOpen = ref(false)
const dropdownRef = ref<HTMLDivElement | null>(null)

const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

const layoutOptions = defineModel<
  Record<string, { name: string; checked: boolean }>
>({ default: {} })

const toggleOption = (key: string) => {
  layoutOptions.value[key].checked = !layoutOptions.value[key].checked
}

const handleClickOutside = (event: MouseEvent): void => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div ref="dropdownRef" class="relative">
    <div
      v-if="isOpen"
      class="fixed inset-0 size-full z-10"
      @click="toggleDropdown"
    ></div>
    <button
      class="h-[35px] w-[160px] px-6 rounded-full text-[#525252] bg-[#F1F2F6] text-base font-semibold flex items-center justify-between z-11 relative"
      @click="toggleDropdown"
    >
      <span>Layout</span>
      <ClientOnly>
        <fa
          class="text-xl text-[#D63C3C] transition-all duration-300 ease-in-out"
          :class="[isOpen ? 'rotate-180' : '']"
          :icon="['fas', 'caret-down']"
        />
      </ClientOnly>
    </button>
    <ul
      v-if="isOpen"
      class="absolute top-full right-0 w-[160px] bg-white shadow-[0px_0px_8px_#2228313D] rounded-md z-20 py-1"
    >
      <li class="px-4 py-[7px] text-base text-[#707070] leading-[21px]">
        Select Layout
      </li>

      <li
        v-for="(layout, key) in layoutOptions"
        :key="key"
        class="px-4 py-[7px] hover:bg-gray-100/40 cursor-pointer text-base leading-[21px] text-[#333333] items-center flex gap-2"
        @click="toggleOption(key)"
      >
        <InputsCheckBoxInput
          :id="layout.name"
          v-model="layout.checked"
          :checkColor="'#D63C3C'"
        />
        <span>{{ layout.name }}</span>
      </li>
    </ul>
  </div>
</template>
