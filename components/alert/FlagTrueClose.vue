<template>
  <div class="text-[#333333] flex flex-col flex-grow">
    <div class="flex flex-col flex-grow">
      <div class="flex flex-col space-y-4">
        <div>Reason</div>
        <div ref="dropdown" class="dropdown-width text-left">
          <InputsSelectInput
            v-model="concern"
            class="w-[265px] h-9"
            class-style-name="searchPageScrollStyle searchPageScrollWidth target-select-input-alert text-base"
            :options="concerns"
            place-holder="Select a reason"
            :place-holder-disabled="true"
            :color="concern === 'Select a reason' ? '#707070' : '#FFFFFF'"
            :background="concern === 'Select a reason' ? '#F1F2F6' : '#D63C3C'"
            caret-bg="#F1F2F6"
            :caret-color="concern === 'Select a reason' ? '#D63C3C' : '#FFFFFF'"
            scroll-color="#9e7912"
          ></InputsSelectInput>
        </div>
      </div>
      <p class="!mt-3.5 text-[#525252]">Add Note</p>
      <textarea
        id="addNote"
        v-model="trueCloseNote"
        name="addNote"
        cols="30"
        rows="3"
        class="w-full mt-4 mb-6 p-4 resize-none rounded-lg placeholder-[#707070] text-[#333333] bg-[#F1F2F6] outline-none border-none"
        placeholder="Type here..."
      >
      </textarea>
    </div>
    <div class="mb-3.5">
      <div class="flex flex-row justify-end md:space-x-5 space-x-2">
        <button
          class="focus:outline-none w-[120px] h-[39px] text-[#FFFFFF] rounded-full outline-none font-semibold md:text-base text-sm"
          :class="
            concern === 'Select a reason' && !trueCloseNote
              ? 'bg-[#C2C2C2] cursor-not-allowed'
              : 'bg-[#D63C3C]'
          "
          @click="
            concern === 'Select a reason' && !trueCloseNote
              ? ''
              : setDisposition('')
          "
        >
          <span>Close</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAlert } from '~/stores/alert'

const store = useAlert()
const { setDisposition } = store
const concerns = [
  { id: 0, text: 'Minor Concern', value: 0 },
  { id: 1, text: 'Major Concern', value: 1 },
  { id: 2, text: 'Concern 3', value: 2 },
  { id: 3, text: 'Concern 4', value: 3 },
]
const concern = ref('Select a reason')
const trueCloseNote = ref('')
</script>

<style lang="scss" scoped>
.dropdown-btn {
  //direction: ltr;
  line-height: 2.15rem !important;
  width: 360px;
  @apply text-ash-default bg-white h-9 px-3 text-lg flex justify-between items-center focus:outline-none;
}
.dropdown-width {
  width: 360px;
}
</style>
