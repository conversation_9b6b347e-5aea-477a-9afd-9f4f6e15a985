<template>
  <div class="w-full h-full flex flex-col pb-4">
    <div
      class="flex lg:flex-row flex-col lg:space-x-7 lg:space-y-0 lg:items-center lg:justify-between px-6 py-4 border-b-2 border-[#F1F2F6]"
    >
      <!-- :class="summaryComp === 'SummaryGroupsTable' ? 'justify-between' : ''" -->
      <div v-if="openClose === 'Open'" class="flex items-center space-x-7">
        <feeds-groups-response></feeds-groups-response>
        <area-line-tab
          class="mt-0"
          :class="summaryComp === 'SummaryGroupsTable' ? 'lg:w-60' : 'lg:w-60'"
        ></area-line-tab>
      </div>
      <scatter-line-tab v-else class="mt-0">
        <!-- v-if="
          summaryComp === 'SummaryAllFlagsCRTable' ||
          summaryComp === 'SummaryTeamCRTable' ||
          summaryComp === 'SummaryPersonCRTable'
        " -->
      </scatter-line-tab>
      <div class="flex items-center gap-6">
        <div
          v-if="openClose === 'Close' && currentGraph === 'LineGraph'"
          class="flex items-center space-x-6"
        >
          <div class="flex items-center space-x-2">
            <label
              for="Components"
              class="text-[#525252] text-base font-semibold"
            >
              Trendline
            </label>
            <InputsToggleInput
              :id="1"
              :select="trendLine"
              bgColor="#ffffff"
              checkedBgColor="#ffffff"
              uncheckedLabelBgColor="#C2C2C2"
              labelBgColor="#D63C3C"
              @toggle-select="
                () => {
                  trendLine = !trendLine
                }
              "
            />
          </div>
          <div class="flex items-center space-x-2">
            <label
              for="Components"
              class="text-[#525252] text-base font-semibold"
              >Compare</label
            >
            <InputsToggleInput
              :id="2"
              :select="compareSeries"
              bgColor="#ffffff"
              checkedBgColor="#ffffff"
              uncheckedLabelBgColor="#C2C2C2"
              labelBgColor="#D63C3C"
              @toggle-select="
                () => {
                  compareSeries = !compareSeries
                }
              "
            />
          </div>
        </div>
        <LayoutDropdown v-model="layoutOptions" />
      </div>
      <!-- <div
        class="flex-grow flex flex-col flex-nowrap lg:flex-row lg:space-x-7 lg:items-center"
        :class="
          summaryComp === 'SummaryGroupsTable'
            ? 'largeDesktop:flex hidden'
            : 'block'
        "
      >
        <p
          class="md:text-xl text-md md:font-bold font-bold text-gray-1200 whitespace-nowrap lg:mt-0 mt-4"
        >
          Severity Index
        </p>
        <div class="flex-grow rounded-full pl-px lg:mt-0 mt-2 mb-3 lg:mb-0">
          <SeveritySlidingBar :bar-id="'id0'" />
        </div>
      </div> -->
      <!-- <transition name="fadeIn">
        <div
          v-if="summaryComp === 'SummaryGroupsTable'"
          class="w-full lg:w-60 h-9 rounded-full bg-red-moreLightness flex items-center justify-start relative mt-0 mb-4 lg:mb-0 order-first lg:order-none"
        >
          <div class="w-1/2 h-full">
            <input
              id="open"
              v-model="openCheckbox"
              class="toggle-checkbox w-1/2 h-full absolute left-0 top-0 block rounded-full appearance-none cursor-pointer"
              type="checkbox"
            />
            <label
              for="open"
              class="toggle-label overflow-hidden w-full h-full transition-all duration-800 ease-in-out bg-red-moreLightness cursor-pointer text-red-deep flex items-center justify-center font-bold"
              :class="closeCheckbox ? 'rounded-l-full' : 'rounded-full'"
              >Open</label
            >
          </div>
          <div class="w-1/2 h-full">
            <input
              id="close"
              v-model="closeCheckbox"
              class="toggle-checkbox w-1/2 h-full absolute right-0 top-0 block rounded-full appearance-none cursor-pointer"
              type="checkbox"
            />
            <label
              for="close"
              class="toggle-label overflow-hidden w-full h-full transition-all duration-800 ease-in-out bg-red-moreLightness cursor-pointer text-red-deep font-bold flex items-center justify-center"
              :class="openCheckbox ? 'rounded-r-full' : 'rounded-full'"
              >Closed</label
            >
          </div>
        </div>
      </transition> -->
    </div>
    <!-- <div
      class="flex flex-col flex-nowrap lg:flex-row lg:space-x-7 lg:items-center lg:mt-4 lg:mb-4"
      :class="
        summaryComp === 'SummaryGroupsTable'
          ? 'flex largeDesktop:hidden'
          : 'hidden'
      "
    >
      <p
        class="md:text-xl text-md md:font-bold font-bold text-gray-1200 whitespace-nowrap lg:mt-0 mt-4"
      >
        Severity Index
      </p>
      <div class="flex-grow rounded-full pl-px md:mt-0 mt-3 mb-4 md:mb-0">
        <SeveritySlidingBar :bar-id="'id100'" />
      </div>
    </div> -->
    <div class="pt-4 px-6 w-full flex items-center gap-6">
      <div
        class="flex"
        :class="
          openClose === 'Close' && currentGraph === 'LineGraph'
            ? 'flex-col gap-2.5 items-start pl-6 w-1/2'
            : 'items-center gap-4 w-full'
        "
      >
        <span class="text-base text-[#333333] font-semibold">Score</span>
        <div class="max-w-[558px] w-full">
          <GradientRangeSlider
            v-model="scoreSliderValues"
            :min="0"
            :max="100"
            :step="1"
          />
        </div>
      </div>
      <div
        v-if="openClose === 'Close' && currentGraph === 'LineGraph'"
        class="flex flex-col items-start gap-2.5 w-1/2 pl-6 border-l border-[#E0E0E0]"
      >
        <span class="text-base text-[#333333] font-semibold"
          >Rolling Average</span
        >
        <div class="max-w-[558px] w-full">
          <RangeSlider
            v-model="rollingSliderValues"
            :min="0"
            :max="100"
            :step="1"
          />
        </div>
      </div>
    </div>
    <div class="flex-grow w-full">
      <transition name="dynamicComp" mode="out-in">
        <component :is="componentMap[currentGraph]"></component>
      </transition>
    </div>
    <date-range-tab></date-range-tab>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import AreaGraph from '~/components/alert/area-line-graph/AreaGraph.vue'
import AreaLineTab from '~/components/alert/area-line-graph/graph-tab/AreaLineTab.vue'
import DateRangeTab from '~/components/alert/area-line-graph/graph-tab/DateRangeTab.vue'
import ScatterLineTab from '~/components/alert/area-line-graph/graph-tab/ScatterLineTab.vue'
import LineGraph from '~/components/alert/area-line-graph/LineGraph.vue'
import ScatterGraph from '~/components/alert/area-line-graph/ScatterGraph.vue'
import FeedsGroupsResponse from '~/components/alert/header-tab/FeedsGroupsResponse.vue'
// import SeveritySlidingBar from '~/components/alert/sliding-bar/SeveritySlidingBar.vue'
import { useAlert } from '~/stores/alert'
import GradientRangeSlider from '../GradientRangeSlider.vue'
import LayoutDropdown from '../LayoutDropdown.vue'
import RangeSlider from '../RangeSlider.vue'

interface ComponentMap {
  [key: string]: any
}

const componentMap: ComponentMap = {
  AreaGraph,
  LineGraph,
  ScatterGraph,
}

const store = useAlert()
const {
  currentGraph,
  summaryComp,
  openClose,
  compareSeries,
  compareSelectInput,
  trendLine,
  isGraphExpanded,
} = storeToRefs(store)

const openCheckbox = ref(true)
const closeCheckbox = ref(true)
const selectedAccounts = ref([])
const hideTrendLine = ref(false)
const layoutOptions = ref({
  piChart: { name: 'Pie Chart', checked: false },
  score: { name: 'Score', checked: false },
})
const middleCompareSeries = ref(false)
const scoreSliderValues = ref([40, 100])
const rollingSliderValues = ref([0])
</script>

<style lang="scss" scoped>
.content__tabs {
  position: relative;
  .background__circle {
    top: 0px;
    left: 0px;
    z-index: -1;
    transition:
      width 0.3s ease-in-out 0.2s,
      left 0.5s ease-in-out;
    z-index: 1;
    @apply absolute h-full rounded-full inline-block;
  }
  .tab {
    @apply relative overflow-hidden;
    > span {
      position: relative;
      transition: color 0.2s ease-in-out;
      z-index: 10;
      @apply text-gray-1200;
    }
    &.active {
      > span {
        @apply text-white;
      }
    }
  }
}

/* for dynamic Components */
.dynamicComp-enter-active,
.dynamicComp-leave-active {
  transition: opacity 0.5s;
}
.dynamicComp-enter,
.dynamicComp-leave-to {
  opacity: 0;
}

.fadeIn-enter-active {
  transition: opacity 0.5s;
}
.fadeIn-leave-active {
  transition: opacity 0s;
}
.fadeIn-enter,
.fadeIn-leave-to {
  opacity: 0;
}

.toggle-checkbox {
  transition: all 0.5s ease-in-out;
  &:checked + .toggle-label {
    @apply bg-red-deep text-white;
    transition: all 0.5s ease-in-out;
  }
}
</style>
