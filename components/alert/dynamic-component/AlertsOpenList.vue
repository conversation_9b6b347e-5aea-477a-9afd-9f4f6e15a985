<template>
  <div
    class="h-full overflow-hidden rounded-2xl"
    :style="{ '--lastRowHeight': lastRowHeight + 'px' }"
  >
    <div class="flex flex-col flex-grow h-full rounded-2xl">
      <div class="w-full py-5 bg-red-deep rounded-t-2xl"></div>
      <div class="w-full inner-body flex-grow bg-white rounded-b-2xl">
        <div
          ref="scrollContainer"
          class="overflow-auto scroll pl-3.5 md:pl-5 h-full"
        >
          <table class="text-md md:text-xl min-w-full">
            <thead>
              <tr
                class="text-red-deep font-bold text-left sticky z-10 bg-white top-0"
              >
                <th>Active</th>
                <th>Edit</th>
                <th>Alert Name</th>
                <th class="text-right">Unreviewed</th>
                <th class="min-w-502">Concern Threshold</th>
                <th class="min-w-502 right-62">Confidence Threshold</th>
                <th
                  class="text-right md:sticky md:top-0 md:right-0 md:bg-white emptyThWidth md:z-3"
                  :class="{
                    'sticky-border': scrollPosition > 24,
                  }"
                >
                  Severity Index
                </th>
              </tr>
            </thead>

            <tbody
              v-for="(item, itemIndex) in alertOpenListSeverity"
              :key="item.id"
            >
              <tr>
                <td>
                  <div
                    class="relative inline-block w-9 align-middle select-none transition-all duration-800 ease-in-out"
                  >
                    <input
                      :id="`toggle-${item.id}`"
                      :checked="item.active"
                      type="checkbox"
                      name="toggle"
                      class="toggle-checkbox absolute block rounded-full bg-red-deep appearance-none cursor-pointer"
                      @change="updateActive(item.id)"
                    />
                    <label
                      :for="`toggle-${item.id}`"
                      class="toggle-label block overflow-hidden h-5 rounded-full transition-all duration-800 ease-in-out bg-ash-default cursor-pointer"
                    ></label>
                  </div>
                </td>
                <td class="table-td flex items-center action-icon top-margin">
                  <div class="flex items-center space-x-3">
                    <button
                      type="button"
                      data-title="Edit"
                      class="w-5 h-5 md:w-7 md:h-7 cursor-pointer flex items-center justify-center"
                      @click="handleEdit(item.id)"
                    >
                      <div
                        class="w-[18px] h-[18px] md:w-[22px] md:h-[22px] bg-red-deep relative rounded-full flex items-end"
                      >
                        <EditIcon />
                      </div>
                    </button>
                  </div>
                </td>
                <td
                  :class="{
                    'cursor-pointer': mouseY <= 48,
                  }"
                  @click="handleRowClick(itemIndex)"
                  @mousemove="handleMousemove"
                >
                  {{ item.alertName }}
                </td>
                <td
                  class="text-right"
                  :class="{
                    'cursor-pointer': mouseY <= 48,
                  }"
                  @click="handleRowClick(itemIndex)"
                  @mousemove="handleMousemove"
                >
                  {{ item.unReviewed }}
                </td>
                <td class="min-w-502">
                  <SlidingBar
                    :bar-id="`concern-${item.id}`"
                    :input-value="item.concernThreshold.toString()"
                    :show-input-fields="item.showConcernThresholdInput"
                    @update-show-input="updateShowInput"
                    @update-hide-input="updateHideInput"
                  />
                </td>
                <td class="min-w-502">
                  <SlidingBar
                    :bar-id="`confidence-${item.id}`"
                    :input-value="item.confidenceThreshold.toString()"
                    :show-input-fields="item.showConfidenceThreshold"
                    @update-show-input="updateShowInput"
                    @update-hide-input="updateHideInput"
                  />
                </td>
                <td
                  class="text-right md:sticky md:top-0 md:right-0 md:bg-white emptyThWidth md:z-3"
                  :class="{
                    'sticky-border': scrollPosition > 24,
                    'cursor-pointer': mouseY <= 48,
                  }"
                  @click="handleRowClick(itemIndex)"
                  @mousemove="handleMousemove"
                >
                  {{ item.severityIndex }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia'
import { useStore } from 'vuex'
import SlidingBar from '~/components/alert/sliding-bar/SlidingBar.vue'
import EditIcon from '~/components/shared/icon/EditIcon.vue'
import { useAlert } from '~/stores/alert'

// Store setup
const store = useAlert()
const vueStore = useStore()
const { currentComp } = storeToRefs(store)

// Reactive data

const alertOpenListSeverity = ref([
  {
    id: '1',
    active: false,
    edit: false,
    alertName: 'Promissory Statements',
    unReviewed: 13,
    concernThreshold: 50,
    showConcernThresholdInput: false,
    showConfidenceThreshold: false,
    confidenceThreshold: 10,
    severityIndex: 0.92,
  },
  {
    id: '2',
    active: true,
    edit: false,
    alertName: 'Unsubstantiated Claims',
    unReviewed: 13,
    concernThreshold: 82.7,
    showConcernThresholdInput: false,
    showConfidenceThreshold: false,
    confidenceThreshold: 60,
    severityIndex: 0.48,
  },
  {
    id: '3',
    active: false,
    edit: false,
    alertName: 'Profanity',
    unReviewed: 13,
    concernThreshold: 48.3,
    showConcernThresholdInput: false,
    showConfidenceThreshold: false,
    confidenceThreshold: 50,
    severityIndex: 0.73,
  },
  {
    id: '4',
    active: false,
    edit: false,
    alertName: 'Insider Trading',
    unReviewed: 13,
    concernThreshold: 10,
    showConcernThresholdInput: false,
    showConfidenceThreshold: false,
    confidenceThreshold: 40,
    severityIndex: 0.92,
  },
  {
    id: '5',
    active: true,
    edit: false,
    alertName: 'Flame Wars',
    unReviewed: 13,
    concernThreshold: 50,
    showConcernThresholdInput: false,
    showConfidenceThreshold: false,
    confidenceThreshold: 100,
    severityIndex: 0.92,
  },
  {
    id: '6',
    active: false,
    edit: false,
    alertName: 'Fraud',
    unReviewed: 13,
    concernThreshold: 100,
    showConcernThresholdInput: false,
    showConfidenceThreshold: false,
    confidenceThreshold: 50,
    severityIndex: 0.92,
  },
  {
    id: '7',
    active: true,
    edit: false,
    alertName: 'Misogyny',
    unReviewed: 13,
    concernThreshold: 70,
    showConcernThresholdInput: false,
    showConfidenceThreshold: false,
    confidenceThreshold: 60,
    severityIndex: 0.92,
  },
  {
    id: '8',
    active: false,
    edit: false,
    alertName: 'Corporate Security',
    unReviewed: 13,
    concernThreshold: 20,
    showConcernThresholdInput: false,
    showConfidenceThreshold: false,
    confidenceThreshold: 50,
    severityIndex: 0.92,
  },
  {
    id: '9',
    active: false,
    edit: false,
    alertName: 'Bullying',
    unReviewed: 13,
    concernThreshold: 40,
    showConcernThresholdInput: false,
    showConfidenceThreshold: false,
    confidenceThreshold: 30,
    severityIndex: 0.92,
  },
  {
    id: '10',
    active: true,
    edit: false,
    alertName: 'Threats/Violence',
    unReviewed: 13,
    concernThreshold: 60,
    showConcernThresholdInput: false,
    showConfidenceThreshold: false,
    confidenceThreshold: 50,
    severityIndex: 0.92,
  },
])

const scrollContainer = ref(null)
const scrollPosition = ref(0)
const lastRowHeight = ref(0)
const mouseY = ref(0)

// Methods

const updateShowInput = (barId) => {
  if (barId.includes('concern-')) {
    const itemId = barId.split('concern-')[1]
    alertOpenListSeverity.value = alertOpenListSeverity.value.map((item) => {
      if (item.id === itemId) {
        item.showConcernThresholdInput = true
      } else {
        item.showConcernThresholdInput = false
      }
      item.showConfidenceThreshold = false
      return item
    })
  } else if (barId.includes('confidence-')) {
    const itemId = barId.split('confidence-')[1]
    alertOpenListSeverity.value = alertOpenListSeverity.value.map((item) => {
      if (item.id === itemId) {
        item.showConfidenceThreshold = true
      } else {
        item.showConfidenceThreshold = false
      }
      item.showConcernThresholdInput = false
      return item
    })
  }
}

const updateHideInput = () => {
  alertOpenListSeverity.value = alertOpenListSeverity.value.map((item) => {
    item.showConcernThresholdInput = false
    item.showConfidenceThreshold = false
    return item
  })
}

const updateActive = (itemId) => {
  const item = alertOpenListSeverity.value.find((item) => item.id === itemId)
  if (item) {
    item.active = !item.active
  }
}

const handleEdit = (itemId) => {
  store.setCurrentComp({
    currentComp: 'GeneralAlertSettings',
    previousCurrentComp: currentComp.value,
  })
}

const handleResize = () => {
  if (!scrollContainer.value) return

  const totalRowsHeight = alertOpenListSeverity.value.length * 48 + 60 + 10
  if (scrollContainer.value.offsetHeight > totalRowsHeight) {
    lastRowHeight.value =
      scrollContainer.value.offsetHeight - (totalRowsHeight - 48)
  } else {
    lastRowHeight.value = 48
  }
}

const handleScroll = () => {
  if (!scrollContainer.value) return

  scrollPosition.value =
    scrollContainer.value.scrollWidth -
    scrollContainer.value.clientWidth -
    scrollContainer.value.scrollLeft
}

const handleMousemove = (event) => {
  mouseY.value = event.pageY - event.target.getBoundingClientRect().top
}

const handleRowClick = (index) => {
  if (mouseY.value <= 48) {
    store.setCurrentComp({
      currentComp: 'AlertOpenEdit',
      previousCurrentComp: currentComp.value,
    })
  }
}

const addEvents = () => {
  window.addEventListener('resize', handleResize)
  if (scrollContainer.value) {
    scrollContainer.value.addEventListener('scroll', handleScroll)
  }
}

const removeEvents = () => {
  window.removeEventListener('resize', handleResize)
  if (scrollContainer.value) {
    scrollContainer.value.removeEventListener('scroll', handleScroll)
  }
}

const squeeze = computed(() => vueStore.state.header.squeeze)

onMounted(async () => {
  await nextTick()
  addEvents()
  handleResize()
  handleScroll()
})

onUnmounted(() => {
  removeEvents()
})

watch(squeeze, (newSqueeze, oldSqueeze) => {
  if (newSqueeze !== oldSqueeze) {
    setTimeout(() => {
      handleScroll()
      handleResize()
    }, 800)
  }
})
</script>

<style lang="scss" scoped>
.inner-body {
  height: calc(100% - 40px);
}
.top-margin {
  margin-top: 1px;
}
.min-w-502 {
  min-width: 350px;
}
.emptyThWidth {
  min-width: 168px;
}
.max-w-118 {
  max-width: 550px;
}
table tr th {
  @apply px-2.5 md:px-6 py-4 whitespace-nowrap;
}
table tr th:first-child {
  @apply px-0 pr-2.5 md:pr-6 whitespace-nowrap;
}
table tr th:last-child {
  @apply pl-2.5 pr-4 md:pl-6 md:pr-5 whitespace-nowrap;
}
table tr td {
  @apply px-2.5 md:px-6 py-1.5 whitespace-nowrap text-gray-1200;
}
table tr td:first-child {
  @apply px-0 pr-2.5 md:pr-6 pb-2.5;
}
table tr td:nth-child(2) {
  @apply pb-0 pt-2;
}
table tr td:last-child {
  @apply pl-2.5 pr-4 md:pl-6 md:pr-5 py-2.5;
}
table tbody:last-child tr {
  vertical-align: top;
  height: var(--lastRowHeight);
}
table tbody:last-child tr td {
  @apply pb-3;
}
.toggle-checkbox {
  @apply w-4 h-4;
  border: 0px;
  top: 2px;
  left: 2px;
  transition: all 0.5s ease-in-out;
  background-color: #ffffff;
  &:checked {
    @apply right-0;
    left: 18px;
    transition: all 0.5s ease-in-out;
    background-color: #ffffff;
  }
  &:checked + .toggle-label {
    @apply bg-white;
    transition: all 0.5s ease-in-out;
    background-color: #a22a2a;
  }
}
.action-icon {
  @apply pl-3 md:pl-5;
}
.scroll {
  scrollbar-color: #a22a2a #ececec; /* Firefox 64 */

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: #a22a2a;
    border-radius: 3px;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #a22a2a;
  }
}
@media (max-width: 767px) {
  .emptyThWidth {
    min-width: 0px;
  }
  .min-w-502 {
    min-width: 270px;
  }
  .top-margin {
    margin-top: 4px;
  }
}

@media (min-width: 768px) {
  .sticky-border:before {
    content: '';
    width: 100%;
    height: 100%;
    position: absolute;
    display: block;
    left: -1px;
    top: 0px;
    border-left: 1px solid gray;
  }
}
</style>
