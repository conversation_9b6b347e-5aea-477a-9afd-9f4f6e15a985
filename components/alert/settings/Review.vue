<script setup lang="ts">
import { useVuelidate } from '@vuelidate/core'
import { required } from '@vuelidate/validators'

const reviewTableHeader = ['Cluster', 'Alert name', 'Assign to']
const reviewTableBodies = ref([
  {
    id: 1,
    cluster: 'Compliance',
    alertName: [
      'Promissory Statements',
      'Unsubstantiated Claims',
      'Code of Ethics',
      'Complaints',
    ],
    assignTo: '<PERSON><PERSON>',
  },
  {
    id: 2,
    cluster: 'Human Resources',
    alertName: ['Profanity', 'Toxic Management', 'Misogyny', 'Racism'],
    assignTo: '<PERSON><PERSON>',
  },
  {
    id: 3,
    cluster: 'Corporate Security',
    alertName: ['Bullying', 'Workplace Violence'],
    assignTo: '<PERSON><PERSON>',
  },
])
const showClusterInputField = ref(false)
const clusterName = ref('')
const rules = {
  clusterName: { required },
}
const v$ = useVuelidate(rules, { clusterName })
const addNewCluster = () => {
  v$.value.$touch()
  if (!v$.value.$invalid) {
    reviewTableBodies.value.push({
      id: reviewTableBodies.value.length + 1,
      cluster: clusterName.value,
      alertName: [
        'Promissory Statements',
        'Unsubstantiated Claims',
        'Code of Ethics',
        'Complaints',
      ],
      assignTo: 'Suzie Sullivan',
    })
    clusterName.value = ''
    v$.value.$reset()
  }
}
</script>

<template>
  <div class="border-r-2 birder-[#F1F2F4] scroll">
    <div class="py-3.5 px-6">
      <h2 class="text-[#525252] font-semibold text-lg">Review</h2>
      <div class="w-full flex space-x-4 justify-end items-center mt-4">
        <HomeSearchBar
          class="!bg-[#F1F2F6] !w-[280px] !h-[36px]"
          bgColor="!bg-[#F1F2F6] h-[35px]"
          textColor="!text-[#707070] placeholder:!text-[#707070] !text-base"
        />
        <button
          class="flex justify-center items-center bg-red-deep text-white font-semibold w-[118px] h-9 rounded-full"
        >
          New Alert
        </button>
      </div>
      <div class="mt-4 min-w-full scroll overflow-auto">
        <div>
          <div
            class="flex flex-row"
            :style="{
              borderTop: '1px solid #F1F2F4 !important',
              borderBottom: '1px solid #F1F2F4 !important',
            }"
          >
            <div
              class="text-[#333333] font-semibold px-4 py-[7px] text-left [&:nth-child(1)]:min-w-[200px] [&:nth-child(2)]:min-w-[440px] [&:nth-child(3)]:min-w-[200px]"
              v-for="value in reviewTableHeader"
              :key="value"
            >
              {{ value }}
            </div>
          </div>
        </div>
        <div>
          <div
            v-for="reviewTableBody in reviewTableBodies"
            :key="reviewTableBody.id"
            class="text-[#333333] !py-3.5 flex min-h-[132px]"
            :style="{
              borderBottom: '1px solid #F1F2F4 !important',
            }"
          >
            <div
              class="font-semibold items-baseline px-4 min-w-[200px]"
              :style="{
                borderRight: '1px solid #F1F2F4 !important',
              }"
            >
              <div class="h-full w-full flex justify-start items-start">
                {{ reviewTableBody.cluster }}
              </div>
            </div>
            <div
              class="px-4 min-w-[440px] flex flex-col space-y-1.5"
              :style="{
                borderRight: '1px solid #F1F2F4 !important',
              }"
            >
              <div v-for="value in reviewTableBody.alertName" :key="value">
                <p>{{ value }}</p>
              </div>
            </div>
            <div class="px-4 min-w-[200px]">{{ reviewTableBody.assignTo }}</div>
          </div>
        </div>
      </div>
      <div class="w-full flex flex-col justify-start items-start mt-4">
        <Transition name="fadeIn" mode="out-in">
          <button
            v-if="!showClusterInputField"
            class="w-[144px] h-9 rounded-full bg-[#F1F2F6] flex text-[#525252] font-semibold space-x-3 items-center justify-center"
            @click="showClusterInputField = true"
          >
            <ClientOnly>
              <fa class="text-base" :icon="['fas', 'plus']" />
            </ClientOnly>
            <p>Add Cluster</p>
          </button>
          <div v-else class="w-full flex justify-between items-center">
            <div class="flex space-x-4 items-start">
              <InputsTextInput
                v-model="clusterName"
                placeHolder="Type cluster name"
                placeHolderClass="placeholder-[#A1A1A1] placeholder-opacity-100"
                color="#707070"
                background="#F1F2F6"
                extendClass="!h-[35px] !w-[280px] !shadow-none !border-none"
                :error="v$.clusterName.$error"
                :error-message="v$.clusterName.$errors"
                @blur="v$.clusterName.$touch()"
              />
              <button
                class="w-[104px] h-[35px] rounded-full bg-red-deep flex text-white font-semibold space-x-3 items-center justify-center"
                @click="addNewCluster"
              >
                Add
              </button>
            </div>
            <ClientOnly>
              <fa
                class="text-xl cursor-pointer"
                :icon="['fas', 'times']"
                @click="showClusterInputField = false"
              />
            </ClientOnly>
          </div>
        </Transition>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scroll {
  overflow-x: auto;
  overflow-y: auto;
  scrollbar-color: #ffe0e0 #ffe0e080; /* Firefox 64 */
  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: #ffe0e0;
    border-radius: 3px;
  }
  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #ffe0e0;
  }
}
</style>
