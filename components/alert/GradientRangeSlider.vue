<template>
  <div
    ref="sliderElement"
    class="slider-wrapper"
    role="group"
    aria-label="Multi-thumb slider"
  >
    <!-- Base inactive gray track -->
    <div class="track base-track"></div>

    <!-- Multi-thumb: gradient BETWEEN thumbs -->
    <div
      v-if="isMultiThumb"
      class="track gradient-track"
      :style="{
        maskImage: `linear-gradient(to right, transparent ${clampedMinPosition}%, black ${clampedMinPosition}%, black ${clampedMaxPosition}%, transparent ${clampedMaxPosition}%)`,
        WebkitMaskImage: `linear-gradient(to right, transparent ${clampedMinPosition}%, black ${clampedMinPosition}%, black ${clampedMaxPosition}%, transparent ${clampedMaxPosition}%)`,
      }"
    ></div>

    <!-- Single-thumb: gradient FROM LEFT to the thumb -->
    <div
      v-else
      class="track gradient-track"
      :style="{
        maskImage: `linear-gradient(to right, black 0%, black ${clampedThumbPositions[0]}%, transparent ${clampedThumbPositions[0]}%, transparent 100%)`,
        WebkitMaskImage: `linear-gradient(to right, black 0%, black ${clampedThumbPositions[0]}%, transparent ${clampedThumbPositions[0]}%, transparent 100%)`,
      }"
    ></div>

    <!-- Thumbs -->
    <div
      v-for="(position, index) in thumbPositions"
      :key="`thumb-${index}`"
      class="thumb"
      :style="getThumbStyle(position, index)"
      role="slider"
      tabindex="0"
      :aria-valuemin="props.min"
      :aria-valuemax="props.max"
      :aria-valuenow="modelValue[index]"
      :aria-label="`Thumb ${index + 1}`"
      @mousedown="handleMouseDown($event, index)"
      @touchstart="handleTouchStart($event, index)"
      @keydown="handleKeyDown($event, index)"
    >
      <div class="thumb-label">
        <span>{{ modelValue[index] }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

interface MultiThumbSliderProps {
  min?: number
  max?: number
  step?: number
}

const props = withDefaults(defineProps<MultiThumbSliderProps>(), {
  min: 0,
  max: 100,
  step: 1,
})

const modelValue = defineModel<number[]>({ required: true })

// Reactive state
const sliderElement = ref<HTMLDivElement | null>(null)
const thumbPositions = ref<number[]>([])
const isDragging = ref<boolean>(false)
const activeThumbIndex = ref<number>(-1)

const thumbWidth = 48

// detect single vs multi thumb
const isMultiThumb = computed(
  () => Array.isArray(modelValue.value) && modelValue.value.length > 1,
)

// --- Utils for position/value mapping --- //
function calculatePositionFromValue(value: number): number {
  if (!sliderElement.value) return 0
  const sliderWidth = sliderElement.value.getBoundingClientRect().width
  const usableWidth = sliderWidth - thumbWidth
  const ratio = (value - props.min) / (props.max - props.min)
  const centerX = ratio * usableWidth + thumbWidth / 2
  return (centerX / sliderWidth) * 100
}

function calculateValueFromPosition(position: number): number {
  if (!sliderElement.value) return props.min
  const sliderWidth = sliderElement.value.getBoundingClientRect().width
  const usableWidth = sliderWidth - thumbWidth
  const centerX = (position / 100) * sliderWidth
  const offset = Math.max(0, Math.min(centerX - thumbWidth / 2, usableWidth))
  const ratio = offset / usableWidth
  const rawValue = props.min + ratio * (props.max - props.min)
  return Math.round(rawValue / props.step) * props.step
}

// --- Initialize thumbs --- //
const initializeThumbs = (): void => {
  if (!Array.isArray(modelValue.value) || modelValue.value.length === 0) {
    modelValue.value = [props.min]
  }
  thumbPositions.value = modelValue.value.map((val) =>
    calculatePositionFromValue(val),
  )
}

// clamp only for rendering
const clampedThumbPositions = computed<number[]>(() => {
  return thumbPositions.value.map((position) =>
    Math.max(0, Math.min(100, position)),
  )
})

const clampedMinPosition = computed<number>(() => {
  if (clampedThumbPositions.value.length === 0) return 0
  return Math.min(...clampedThumbPositions.value)
})

const clampedMaxPosition = computed<number>(() => {
  if (clampedThumbPositions.value.length === 0) return 100
  return Math.max(...clampedThumbPositions.value)
})

// Gradient color helper
function getThumbColor(position: number): string {
  const gradientStops = [
    { position: 0, color: [2, 133, 67] }, // #028543
    { position: 25, color: [104, 122, 13] }, // #687A0D
    { position: 50, color: [120, 104, 1] }, // #786801
    { position: 75, color: [168, 98, 5] }, // #A86205
    { position: 100, color: [228, 46, 0] }, // #E42E00
  ]
  for (let i = 0; i < gradientStops.length - 1; i++) {
    const current = gradientStops[i]
    const next = gradientStops[i + 1]

    if (position >= current.position && position <= next.position) {
      const ratio =
        (position - current.position) / (next.position - current.position)
      const r = Math.round(
        current.color[0] + (next.color[0] - current.color[0]) * ratio,
      )
      const g = Math.round(
        current.color[1] + (next.color[1] - current.color[1]) * ratio,
      )
      const b = Math.round(
        current.color[2] + (next.color[2] - current.color[2]) * ratio,
      )
      return `rgb(${r}, ${g}, ${b})`
    }
  }

  return position <= 0 ? 'rgb(2, 133, 67)' : 'rgb(227, 45, 0)'
}

// Style for each thumb
function getThumbStyle(position: number, index: number) {
  const left = clampedThumbPositions.value[index] ?? position
  return {
    left: `${left}%`,
    backgroundColor: getThumbColor(left),
    zIndex: activeThumbIndex.value === index ? 20 : 10,
  }
}

// Drag handlers
function startDrag(event: MouseEvent | TouchEvent, thumbIndex: number): void {
  if (event.cancelable) event.preventDefault()

  isDragging.value = true
  activeThumbIndex.value = thumbIndex

  const handleMove = (moveEvent: MouseEvent | TouchEvent): void => {
    if (!isDragging.value || !sliderElement.value) return
    if (moveEvent.cancelable) moveEvent.preventDefault()

    const rect = sliderElement.value.getBoundingClientRect()

    let clientX: number
    if ('touches' in moveEvent) {
      clientX = moveEvent.touches[0].clientX
    } else {
      clientX = moveEvent.clientX
    }

    const offsetX = clientX - rect.left
    let newPosition = (offsetX / rect.width) * 100
    newPosition = Math.max(0, Math.min(100, newPosition))

    const newValue = calculateValueFromPosition(newPosition)
    modelValue.value[thumbIndex] = newValue
    thumbPositions.value[thumbIndex] = calculatePositionFromValue(newValue)
  }

  const handleEnd = (): void => {
    isDragging.value = false
    activeThumbIndex.value = -1

    document.removeEventListener('mousemove', handleMove)
    document.removeEventListener('touchmove', handleMove)
    document.removeEventListener('mouseup', handleEnd)
    document.removeEventListener('touchend', handleEnd)
  }

  document.addEventListener('mousemove', handleMove)
  document.addEventListener('touchmove', handleMove, { passive: false })
  document.addEventListener('mouseup', handleEnd)
  document.addEventListener('touchend', handleEnd)
}

function handleMouseDown(event: MouseEvent, index: number): void {
  startDrag(event, index)
}

function handleTouchStart(event: TouchEvent, index: number): void {
  startDrag(event, index)
}

// ✅ Keyboard support
function handleKeyDown(event: KeyboardEvent, index: number): void {
  const step = props.step
  let newValue = modelValue.value[index]

  switch (event.key) {
    case 'ArrowLeft':
    case 'ArrowDown':
      newValue = Math.max(props.min, newValue - step)
      break
    case 'ArrowRight':
    case 'ArrowUp':
      newValue = Math.min(props.max, newValue + step)
      break
    case 'PageDown':
      newValue = Math.max(props.min, newValue - step * 10)
      break
    case 'PageUp':
      newValue = Math.min(props.max, newValue + step * 10)
      break
    case 'Home':
      newValue = props.min
      break
    case 'End':
      newValue = props.max
      break
    default:
      return
  }

  event.preventDefault()
  modelValue.value[index] = newValue
  thumbPositions.value[index] = calculatePositionFromValue(newValue)
}

// Watch v-model changes
watch(
  () => modelValue.value,
  () => {
    initializeThumbs()
  },
  { deep: true },
)

// Lifecycle
onMounted(() => {
  initializeThumbs()
  window.addEventListener('resize', initializeThumbs)
})

onUnmounted(() => {
  window.removeEventListener('resize', initializeThumbs)
})
</script>

<style scoped>
.slider-wrapper {
  @apply w-full relative cursor-pointer my-2;
  height: 7px;
}
.track {
  @apply absolute w-full rounded-full;
  height: 7px;
}

/* Base inactive gray track */
.base-track {
  background-color: #e3e3e3;
}

/* Gradient track, masked */
.gradient-track {
  background: linear-gradient(
    90deg,
    #028543 0%,
    #8dc820 25%,
    #f7cd00 52%,
    #fc8d08 76%,
    #e32d00 100%
  );
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

/* Thumbs */
.thumb {
  @apply absolute rounded-full cursor-grab flex items-center justify-center;
  width: 48px;
  height: 25px;
  top: 50%;
  transform: translate(-50%, -50%);
  transition: box-shadow 0.2s;
  z-index: 10;
}

.thumb:hover {
  box-shadow: 0 0 0 6px rgba(0, 0, 0, 0.1);
}

.thumb:active {
  @apply cursor-grabbing;
  box-shadow: 0 0 0 8px rgba(0, 0, 0, 0.1);
}

.thumb-label {
  @apply text-white text-xs font-semibold whitespace-nowrap select-none;
}
</style>
