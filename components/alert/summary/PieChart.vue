<template>
  <ClientOnly>
    <VueApexCharts
      v-if="pieSeries.length > 0"
      id="chart"
      ref="chart"
      type="pie"
      width="380"
      :options="options"
      :series="pieSeries"
    />
  </ClientOnly>
</template>

<script setup>
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core'
import { storeToRefs } from 'pinia'
import VueApexCharts from 'vue3-apexcharts'
import { useAlert } from '~/stores/alert'

const store = useAlert()
const isDesktop = useBreakpoints(breakpointsTailwind).greater('md')

const { pieSeries, pieLabels, summaryComp } = storeToRefs(store)

const chart = ref(null)
const areaClick = ref(0)

const updateSummaryPieArea = (oldSummaryCompFeed) => {
  areaClick.value = areaClick.value + 1
  setTimeout(() => {
    areaClick.value = 0
  }, 300)

  if (isDesktop.value) {
    if (areaClick.value === 2) {
      if (summaryComp.value === 'SummaryAllFeedsTable') {
        store.setSummaryComponent({
          summaryComp: 'SummaryIndividualFeedsTable',
          previousSummaryComp: store.summaryComp,
        })
      } else if (summaryComp.value === 'SummaryIndividualFeedsTable') {
        store.setSummaryComponent({
          summaryComp: 'SummaryIndividualSocialFeedsTable',
          previousSummaryComp: store.summaryComp,
        })
      } else if (summaryComp.value === 'SummaryIndividualSocialFeedsTable') {
        store.setSummaryComponent({
          summaryComp: 'SummaryIndividualSocialFeedDetails',
          previousSummaryComp: store.summaryComp,
        })
      } else if (summaryComp.value === 'SummaryAllFlagsORTable') {
        store.setSummaryComponent({
          summaryComp: 'SummaryFlagPersonsORTable',
          previousSummaryComp: store.summaryComp,
        })
      }
    }
  } else if (!isDesktop.value) {
    if (areaClick.value === 4) {
      if (summaryComp.value === 'SummaryAllFeedsTable') {
        store.setSummaryComponent({
          summaryComp: 'SummaryIndividualFeedsTable',
          previousSummaryComp: store.summaryComp,
        })
      } else if (summaryComp.value === 'SummaryIndividualFeedsTable') {
        store.setSummaryComponent({
          summaryComp: 'SummaryIndividualSocialFeedsTable',
          previousSummaryComp: store.summaryComp,
        })
      } else if (summaryComp.value === 'SummaryIndividualSocialFeedsTable') {
        store.setSummaryComponent({
          summaryComp: 'SummaryIndividualSocialFeedDetails',
          previousSummaryComp: store.summaryComp,
        })
      } else if (summaryComp.value === 'SummaryAllFlagsORTable') {
        store.setSummaryComponent({
          summaryComp: 'SummaryFlagPersonsORTable',
          previousSummaryComp: store.summaryComp,
        })
      }
    }
  }
}

const updateOptions = (labels) => {
  chart.value?.updateOptions({ labels })
}

const options = ref({
  chart: {
    width: 380,
    type: 'pie',
    redrawOnParentResize: true,
    events: {
      click: (event, chartContext, config) => {
        updateSummaryPieArea(config.globals.seriesNames[config.seriesIndex])
      },
    },
  },
  colors: [
    '#5A84CC',
    '#9D1616',
    '#6E9C2E',
    '#5A57A2',
    '#0091AA',
    '#E89922',
    '#265198',
    '#E05252',
  ],
  dataLabels: {
    enabled: false,
  },
  labels: pieLabels.value,
  legend: {
    show: false,
  },
  stroke: {
    show: false,
    width: 0,
  },
  plotOptions: {
    pie: {
      startAngle: 0,
      endAngle: 360,
      expandOnClick: false,
      offsetX: 0,
      offsetY: 0,
      customScale: 1,
      // dataLabels: {
      //   offset: 0,
      //   minAngleToShowLabel: 10,
      // },
    },
  },
})

// Watch for summaryComp changes
watch(summaryComp, (data) => {
  updateOptions(pieLabels.value)
})
</script>
