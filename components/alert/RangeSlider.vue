<template>
  <div
    ref="sliderElement"
    class="slider-wrapper"
    role="group"
    aria-label="Range slider"
    @mousedown="handleTrackClick"
    @touchstart="handleTrackClick"
  >
    <!-- Inactive track (always full width) -->
    <div class="track base-track"></div>

    <!-- Active track (overlays only active part) -->
    <div
      v-if="isMultiThumb"
      class="track active-track"
      :class="[isDragging ? '' : 'animate']"
      :style="{
        left: `${clampedMinPosition}%`,
        width: `${clampedMaxPosition - clampedMinPosition}%`,
      }"
    ></div>
    <div
      v-else
      class="track active-track"
      :class="[isDragging ? '' : 'animate']"
      :style="{
        left: `0%`,
        width: `${clampedThumbPositions[0]}%`,
      }"
    ></div>

    <!-- Thumbs -->
    <div
      v-for="(position, index) in thumbPositions"
      :key="`thumb-${index}`"
      class="thumb"
      :style="getThumbStyle(position, index)"
      role="slider"
      tabindex="0"
      :aria-valuemin="props.min"
      :aria-valuemax="props.max"
      :aria-valuenow="modelValue[index]"
      :aria-label="`Thumb ${index + 1}`"
      @mousedown="handleMouseDown($event, index)"
      @touchstart="handleTouchStart($event, index)"
      @keydown="handleKeyDown($event, index)"
    >
      <div class="thumb-label">
        <span>{{ modelValue[index] }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

interface RangeSliderProps {
  min?: number
  max?: number
  step?: number
}

const props = withDefaults(defineProps<RangeSliderProps>(), {
  min: 0,
  max: 100,
  step: 1,
})

const modelValue = defineModel<number[]>({ required: true })

const sliderElement = ref<HTMLDivElement | null>(null)
const thumbPositions = ref<number[]>([])
const isDragging = ref(false)
const activeThumbIndex = ref(-1)

const thumbWidth = 48

const isMultiThumb = computed(
  () => Array.isArray(modelValue.value) && modelValue.value.length > 1,
)

// --- Utils --- //
function calculatePositionFromValue(value: number): number {
  if (!sliderElement.value) return 0
  const sliderWidth = sliderElement.value.getBoundingClientRect().width
  const usableWidth = sliderWidth - thumbWidth
  const ratio = (value - props.min) / (props.max - props.min)
  const centerX = ratio * usableWidth + thumbWidth / 2
  return (centerX / sliderWidth) * 100
}

function calculateValueFromPosition(position: number): number {
  if (!sliderElement.value) return props.min
  const sliderWidth = sliderElement.value.getBoundingClientRect().width
  const usableWidth = sliderWidth - thumbWidth
  const centerX = (position / 100) * sliderWidth
  const offset = Math.max(0, Math.min(centerX - thumbWidth / 2, usableWidth))
  const ratio = offset / usableWidth
  const rawValue = props.min + ratio * (props.max - props.min)
  return Math.round(rawValue / props.step) * props.step
}

const initializeThumbs = (): void => {
  if (!Array.isArray(modelValue.value) || modelValue.value.length === 0) {
    modelValue.value = [props.min]
  }
  thumbPositions.value = modelValue.value.map((val) =>
    calculatePositionFromValue(val),
  )
}

const clampedThumbPositions = computed(() =>
  thumbPositions.value.map((pos) => Math.max(0, Math.min(100, pos))),
)

const clampedMinPosition = computed(() =>
  clampedThumbPositions.value.length === 0
    ? 0
    : Math.min(...clampedThumbPositions.value),
)

const clampedMaxPosition = computed(() =>
  clampedThumbPositions.value.length === 0
    ? 100
    : Math.max(...clampedThumbPositions.value),
)

function getThumbStyle(position: number, index: number) {
  const left = clampedThumbPositions.value[index] ?? position
  return {
    left: `${left}%`,
    backgroundColor: '#707070',
    zIndex: activeThumbIndex.value === index ? 20 : 10,
    transition: isDragging.value ? 'none' : 'left 0.25s ease, box-shadow 0.2s',
  }
}

function handleTrackClick(event: MouseEvent | TouchEvent) {
  if (!sliderElement.value) return

  let clientX: number
  if ('touches' in event) {
    clientX = event.touches[0].clientX
  } else {
    clientX = event.clientX
  }

  const rect = sliderElement.value.getBoundingClientRect()
  const offsetX = clientX - rect.left
  let newPosition = (offsetX / rect.width) * 100
  newPosition = Math.max(0, Math.min(100, newPosition))

  const newValue = calculateValueFromPosition(newPosition)

  // Find nearest thumb
  let nearestIndex = 0
  let minDistance = Infinity
  modelValue.value.forEach((val, i) => {
    const dist = Math.abs(val - newValue)
    if (dist < minDistance) {
      minDistance = dist
      nearestIndex = i
    }
  })

  // Move nearest thumb (transition handles animation)
  modelValue.value[nearestIndex] = newValue
  thumbPositions.value[nearestIndex] = calculatePositionFromValue(newValue)
}

// --- Drag Handlers --- //
function startDrag(event: MouseEvent | TouchEvent, thumbIndex: number): void {
  if (event.cancelable) event.preventDefault()

  isDragging.value = true
  activeThumbIndex.value = thumbIndex

  const handleMove = (moveEvent: MouseEvent | TouchEvent): void => {
    if (!isDragging.value || !sliderElement.value) return
    if (moveEvent.cancelable) moveEvent.preventDefault()

    const rect = sliderElement.value.getBoundingClientRect()
    let clientX =
      'touches' in moveEvent ? moveEvent.touches[0].clientX : moveEvent.clientX

    const offsetX = clientX - rect.left
    let newPosition = (offsetX / rect.width) * 100
    newPosition = Math.max(0, Math.min(100, newPosition))

    const newValue = calculateValueFromPosition(newPosition)
    modelValue.value[thumbIndex] = newValue
    thumbPositions.value[thumbIndex] = calculatePositionFromValue(newValue)
  }

  const handleEnd = (): void => {
    isDragging.value = false
    activeThumbIndex.value = -1

    document.removeEventListener('mousemove', handleMove)
    document.removeEventListener('touchmove', handleMove)
    document.removeEventListener('mouseup', handleEnd)
    document.removeEventListener('touchend', handleEnd)
  }

  document.addEventListener('mousemove', handleMove)
  document.addEventListener('touchmove', handleMove, { passive: false })
  document.addEventListener('mouseup', handleEnd)
  document.addEventListener('touchend', handleEnd)
}

function handleMouseDown(event: MouseEvent, index: number) {
  startDrag(event, index)
}

function handleTouchStart(event: TouchEvent, index: number) {
  startDrag(event, index)
}

function handleKeyDown(event: KeyboardEvent, index: number) {
  const step = props.step
  let newValue = modelValue.value[index]

  switch (event.key) {
    case 'ArrowLeft':
    case 'ArrowDown':
      newValue = Math.max(props.min, newValue - step)
      break
    case 'ArrowRight':
    case 'ArrowUp':
      newValue = Math.min(props.max, newValue + step)
      break
    case 'PageDown':
      newValue = Math.max(props.min, newValue - step * 10)
      break
    case 'PageUp':
      newValue = Math.min(props.max, newValue + step * 10)
      break
    case 'Home':
      newValue = props.min
      break
    case 'End':
      newValue = props.max
      break
    default:
      return
  }

  event.preventDefault()
  modelValue.value[index] = newValue
  thumbPositions.value[index] = calculatePositionFromValue(newValue)
}

watch(
  () => modelValue.value,
  () => {
    initializeThumbs()
  },
  { deep: true },
)

onMounted(() => {
  initializeThumbs()
  window.addEventListener('resize', initializeThumbs)
})

onUnmounted(() => {
  window.removeEventListener('resize', initializeThumbs)
})
</script>

<style scoped>
.slider-wrapper {
  @apply w-full relative cursor-pointer my-2;
  height: 7px;
}
.track {
  @apply absolute top-0 h-[7px] rounded-full;
}
.base-track {
  background-color: #e3e3e3; /* inactive */
  width: 100%; /* always span full slider */
  z-index: 1;
}
.active-track {
  background-color: #707070;
  height: 100%;
  position: absolute;
  z-index: 2;
}

.active-track.animate {
  transition:
    left 0.25s ease,
    width 0.25s ease;
}

.thumb {
  @apply absolute rounded-full cursor-grab flex items-center justify-center;
  width: 48px;
  height: 25px;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}
.thumb:hover {
  box-shadow: 0 0 0 6px rgba(0, 0, 0, 0.1);
}
.thumb:active {
  @apply cursor-grabbing;
  box-shadow: 0 0 0 8px rgba(0, 0, 0, 0.1);
}
.thumb-label {
  @apply text-white text-xs font-semibold whitespace-nowrap select-none;
}
</style>
