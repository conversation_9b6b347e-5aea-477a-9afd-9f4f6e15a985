<template>
  <div
    class="w-full h-full rounded-2xl overflow-hidden border-0"
    @click.stop="toggleOffAllDropdowns"
  >
    <div
      class="flex flex-col flex-grow h-full scroll rounded-2xl full-wrapper"
      :class="desktop ? '' : 'w-full'"
    >
      <!-- justify-between bigScreen:justify-start -->
      <div
        class="py-1.5 min-h-[35px] bg-red-deep rounded-t-2xl flex justify-center space-x-6 text-sm md:text-base text-white font-semibold"
        :class="[
          activityLog && contentSelected ? 'md:activeLogSelected' : '',
          activityLog && !contentSelected ? 'md:onlyActivity' : '',
          !activityLog && contentSelected ? 'md:onlySelected' : '',
          desktop ? 'header-wrapper' : 'fixed mobile-full-width',
        ]"
      >
        Alert List
      </div>
      <div
        class="inner-body flex flex-col flex-grow bg-[#F1F2F6] rounded-b-2xl mt-47 md:mt-0"
        :class="[
          activityLog && contentSelected ? 'md:activeLogSelected' : '',
          activityLog && !contentSelected ? 'md:onlyActivity' : '',
          !activityLog && contentSelected ? 'md:onlySelected' : '',
          !desktop ? 'scroll w-full' : 'body-wrapper',
        ]"
      >
        <div
          class="px-6 py-4 flex justify-between items-center bg-[#fff] border-b-2 border-[#f1f2f6]"
        >
          <div
            v-if="openClose === 'Open'"
            class="px-4 py-1.5 bg-[#F1F2F6] rounded-lg text-[#525252]"
          >
            <p>Unreviewed: <span class="font-semibold">8</span></p>
          </div>
          <div v-else-if="openClose === 'Close'" class="flex space-x-2">
            <div class="px-4 py-1.5 bg-[#F1F2F6] rounded-lg text-[#525252]">
              <p>Total Alert: <span class="font-semibold">60</span></p>
            </div>
            <div class="px-4 py-1.5 bg-[#FFE3E3] rounded-lg text-[#AF2D2D]">
              <p>True: <span class="font-semibold">26</span></p>
            </div>
            <div class="px-4 py-1.5 bg-[#E3FFE5] rounded-lg text-[#0D7A40]">
              <p>False: <span class="font-semibold">34</span></p>
            </div>
          </div>
          <div class="flex space-x-4 items-center">
            <BaseMultiSelectDropsDown
              v-model="selectedDataOptions"
              class="z-11"
              placeholder="Data Column"
              labelKey="text"
              :options="dataColumnOptions"
              :dorpdownPlaceholder="false"
              :showDisplayText="false"
              checkColor="#D63C3C"
              :button-class="
                isOpen
                  ? 'w-[171px] h-[35px] !text-white !bg-[#D63C3C] rounded-full'
                  : 'w-[171px] h-[35px] text-[#525252] !bg-[#F1F2F6] rounded-full'
              "
              :arrowColor="isOpen ? '#fff' : '#D63C3C'"
              @toggle="
                ($event) => {
                  isOpen = $event
                }
              "
              :style="{ '--tableHeight': tableHeight + 8 + 'px' }"
              dropdownMainClass="!w-[238px] !mt-0 rounded-lg !right-0 !box-shadow: 0px 0px 8px #2228313D;"
              dropdownClass="!max-h-fit !h-[var(--tableHeight)] !bg-white"
              @change="handleSelectionChange"
            >
              <template #option="{ option }">
                <div class="flex items-center space-x-2">
                  <span class="text-[#333333] whitespace-nowrap">{{
                    option.text
                  }}</span>
                </div>
              </template>
            </BaseMultiSelectDropsDown>
            <HomeSearchBar
              class="!bg-[#F1F2F6] !w-[280px] !h-[35px]"
              bgColor="!bg-[#F1F2F6] h-[35px]"
              textColor="!text-[#707070] placeholder:!text-[#707070] !text-base"
              @real-time-search="
                ($event) => {
                  searchQuery = $event
                  console.log('Search text from parent:', $event)
                }
              "
            />
          </div>
        </div>
        <div
          class="md:px-0 pb-3 flex-grow bg-[#F1F2F6] scroll h-full table-wrapper"
          :class="contentSelected ? 'px-2' : 'px-0'"
        >
          <table class="min-w-full bg-[#fff]">
            <thead>
              <tr
                class="text-[#707070] font-semibold text-sm text-left sticky z-10 bg-[#fff] top-0"
              >
                <template v-for="dataColumnOption in dataColumnOptions">
                  <th
                    v-if="dataColumnOption.checked"
                    :key="dataColumnOption.id"
                    class="min-w-[140px]"
                    :class="[
                      dataColumnOption.toggle ? 'bg-[#F1F2F6]' : '',
                      dataColumnOption.filterOptions &&
                      dataColumnOption.filterOptions.length > 0
                        ? 'cursor-pointer'
                        : '',
                    ]"
                    @click.stop="filterToggle(dataColumnOption)"
                  >
                    <div
                      class="flex justify-between items-center space-x-1 w-full relative"
                    >
                      <span>{{ dataColumnOption.text }}</span>
                      <component
                        v-if="dataColumnOption.toggle"
                        class="cursor-pointer size-5"
                        :is="dataColumnOption.icon"
                      ></component>
                      <div
                        v-if="dataColumnOption.toggle"
                        class="!ml-0 absolute bg-white rounded top-[28px] right-[-22px] min-w-[104px] w-fit h-[78px] shadow-[0_0_8px_#2228313D] py-1 z-20"
                      >
                        <ul>
                          <li
                            v-for="filterOption in dataColumnOption.filterOptions"
                            :key="filterOption.id"
                            class="px-4 py-[7px] font-normal text-[#333333] hover:bg-gray-100 cursor-pointer text-left"
                            :class="filterOption.checked ? 'bg-gray-100' : ''"
                            @click.stop="
                              dataColumnOption.runAction(
                                dataColumnOption,
                                filterOption,
                              )
                            "
                          >
                            <p>
                              {{ dataColumnOption.id === 1 ? 'Sort by ' : ''
                              }}<span
                                :class="
                                  filterOption.checked ? '!font-semibold' : ''
                                "
                                >{{ filterOption.text }}</span
                              >
                            </p>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </th>
                </template>
              </tr>
            </thead>

            <template
              v-for="(
                ContentDetail, contentDetailsIndex
              ) in seachContentDetails"
              :key="contentDetailsIndex + 'table'"
            >
              <tbody
                class="rounded-2xl transition-all duration-500 ease-in-out"
                :class="
                  ContentDetail.Selected ? 'text-[#333333]' : 'text-[#333333]'
                "
              >
                <tr
                  :key="contentDetailsIndex + 'row'"
                  class="cursor-pointer vertical-alignment py-4 !border-b !border-[#F1F2F6]"
                  :class="
                    ContentDetail.Selected
                      ? 'bg-selected-content font-semibold'
                      : ''
                  "
                  @click.stop="
                    (selectedFlagged(ContentDetail.id),
                    setDisposition(''),
                    (ContentDetail.alertContextChecked = false))
                  "
                >
                  <td class="min-w-[280px]">{{ ContentDetail.alertName }}</td>
                  <td
                    v-if="
                      dataColumnOptions.find((c) => c.text === 'Alert ID')
                        .checked
                    "
                    class="min-w-[160px]"
                  >
                    {{ ContentDetail.alertId }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find((c) => c.text === 'Created On') &&
                      dataColumnOptions.find((c) => c.text === 'Created On')
                        .checked
                    "
                    class="min-w-[240px]"
                  >
                    {{ ContentDetail.createdOn }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find((c) => c.text === 'Closed on') &&
                      dataColumnOptions.find((c) => c.text === 'Closed on')
                        .checked
                    "
                    class="min-w-[240px]"
                  >
                    {{ ContentDetail.createdOn }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find(
                        (c) => c.text === 'Alert Open Time',
                      ) &&
                      dataColumnOptions.find(
                        (c) => c.text === 'Alert Open Time',
                      ).checked
                    "
                    class="min-w-[200px]"
                  >
                    {{ ContentDetail.alertOpenTime }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find(
                        (c) => c.text === 'Response Time',
                      ) &&
                      dataColumnOptions.find((c) => c.text === 'Response Time')
                        .checked
                    "
                    class="min-w-[200px]"
                  >
                    {{ ContentDetail.alertOpenTime }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find(
                        (c) => c.text === 'Account Name',
                      ) &&
                      dataColumnOptions.find((c) => c.text === 'Account Name')
                        .checked
                    "
                    class="min-w-[240px]"
                  >
                    {{ ContentDetail.accountName }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find((c) => c.text === 'Reviewed by') &&
                      dataColumnOptions.find((c) => c.text === 'Reviewed by')
                        .checked
                    "
                    class="min-w-[240px]"
                  >
                    {{ ContentDetail.accountName }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find((c) => c.text === 'Score').checked
                    "
                    class="min-w-[160px]"
                  >
                    {{ ContentDetail.score }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find((c) => c.text === 'Type').checked
                    "
                    class="min-w-[240px]"
                  >
                    {{ ContentDetail.type }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find((c) => c.text === 'Source').checked
                    "
                    class="min-w-[240px]"
                  >
                    {{ ContentDetail.Source }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find((c) => c.text === 'Cluster')
                        .checked
                    "
                    class="min-w-[240px]"
                  >
                    {{ ContentDetail.cluster ? ContentDetail.cluster : '' }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find(
                        (c) => c.text === 'Score Threshold Setting',
                      ).checked
                    "
                    class="min-w-[240px]"
                  >
                    {{
                      ContentDetail.scoreThresholdSetting
                        ? ContentDetail.scoreThresholdSetting
                        : ''
                    }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find((c) => c.text === 'Content')
                        .checked
                    "
                    class="min-w-[240px]"
                  >
                    {{ ContentDetail.content ? ContentDetail.content : '' }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find((c) => c.text === 'Explanation')
                        .checked
                    "
                    class="min-w-[240px]"
                  >
                    {{
                      ContentDetail.explanation ? ContentDetail.explanation : ''
                    }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find((c) => c.text === 'Send Time')
                        .checked
                    "
                    class="min-w-[240px]"
                  >
                    {{ ContentDetail.sendTime ? ContentDetail.sendTime : '' }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find((c) => c.text === 'Alert Internal')
                        .checked
                    "
                    class="min-w-[240px]"
                  >
                    {{
                      ContentDetail.alertInternal
                        ? ContentDetail.alertInternal
                        : ''
                    }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find((c) => c.text === 'Assigned To')
                        .checked
                    "
                    class="min-w-[240px]"
                  >
                    {{
                      ContentDetail.assignedTo ? ContentDetail.assignedTo : ''
                    }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find((c) => c.text === 'Role').checked
                    "
                    class="min-w-[240px]"
                  >
                    {{ ContentDetail.role ? ContentDetail.role : '' }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find(
                        (c) => c.text === 'User Permission',
                      ).checked
                    "
                    class="min-w-[240px]"
                  >
                    {{
                      ContentDetail.userPermission
                        ? ContentDetail.userPermission
                        : ''
                    }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find(
                        (c) => c.text === 'Alerts Permission',
                      ).checked
                    "
                    class="min-w-[240px]"
                  >
                    {{
                      ContentDetail.alertsPermission
                        ? ContentDetail.alertsPermission
                        : ''
                    }}
                  </td>
                  <td
                    v-if="
                      dataColumnOptions.find((c) => c.text === 'Status').checked
                    "
                    class="min-w-[240px]"
                  >
                    {{ ContentDetail.status ? ContentDetail.status : '' }}
                  </td>
                  <transition name="table-fadeIn" mode="out-in">
                    <td v-if="activityLog" class="rounded-tr-2xl">
                      <transition-group name="table-group-fadeIn">
                        <td
                          v-if="activityLog && !ContentDetail.Selected"
                          key="td1"
                          class="activity-td"
                        >
                          <div class="rounded-tr-2xl relative has-tooltip">
                            <span>
                              {{ strLimit(ContentDetail.ActivityLog, 47) }}
                            </span>
                            <span
                              v-if="ContentDetail.ActivityLog.length > 30"
                              class="tooltip activity-log-tooltip-width scroll"
                              :class="
                                contentDetailsIndex > contentDetails.length - 3
                                  ? 'tooltip1'
                                  : ''
                              "
                              >{{ ContentDetail.ActivityLog }}</span
                            >
                          </div>
                        </td>
                        <div
                          v-if="activityLog && ContentDetail.Selected"
                          key="td2"
                          class="commentWrapper rounded-tr-2xl activity-td"
                        >
                          <div class="flex space-x-6">
                            <div class="fullTime flex flex-col">
                              <div class="date">{{ ContentDetail.date }}</div>
                              <div class="time">{{ ContentDetail.time }}</div>
                            </div>

                            <div class="comment relative has-tooltip">
                              <p class="whitespace-normal">
                                {{ ContentDetail.comment }}
                              </p>
                              <p
                                v-if="ContentDetail.comment.length > 10"
                                class="tooltip comment-tooltip-width scroll"
                                :class="
                                  contentDetailsIndex >
                                  contentDetails.length - 3
                                    ? 'tooltip1'
                                    : ''
                                "
                              >
                                {{ ContentDetail.comment }}
                              </p>
                            </div>
                          </div>
                        </div>
                      </transition-group>
                    </td>
                  </transition>
                </tr>
                <transition name="table-FadeIn" mode="out-in">
                  <tr v-if="ContentDetail.Selected" class="w-full">
                    <td
                      colspan="19"
                      class="detailsWrapper"
                      :class="
                        !activityLog && !setActivityLog ? 'w-full' : 'w-2/3'
                      "
                    >
                      <table class="w-full">
                        <tbody>
                          <tr class="w-full flex max-h-[663px]">
                            <td
                              class="leftWrapper !px-0 transition-all duration-500 ease-in-out"
                              :class="
                                ContentDetail.activityLog
                                  ? 'w-[25%]'
                                  : 'w-[33.33%]'
                              "
                              :style="{
                                borderRight: '1px solid #F1F2F4 !important',
                              }"
                            >
                              <div class="h-full w-full flex flex-col">
                                <div class="!px-6 flex-grow">
                                  <div
                                    class="pb-4 border-b border-[#F1F2F4] flex flex-col space-y-2.5"
                                  >
                                    <h2
                                      class="text-[#525252] font-semibold !mb-1"
                                    >
                                      Alert Summary
                                    </h2>
                                    <div
                                      v-for="(
                                        alertSummary, alertSummaryIndex
                                      ) in alertSummaries"
                                      :key="alertSummaryIndex + 'row1'"
                                      class="userDetails"
                                    >
                                      <div
                                        class="grid gap-x-2 items-center transition-all ease-in-out duration-500"
                                        :class="
                                          ContentDetail.activityLog
                                            ? 'grid-cols-[145px_4px_1fr]'
                                            : 'grid-cols-[200px_4px_1fr]'
                                        "
                                      >
                                        <div class="text-[#525252]">
                                          {{ alertSummary.heading }}
                                        </div>
                                        <p class="text-[#333333]">:</p>
                                        <div
                                          class="text-[#333333] px-2 py-1 rounded grid grid-cols-[1fr_20px] justify-between items-center"
                                          @mouseenter="alertSummary.copy = true"
                                          @mouseleave="
                                            alertSummary.copy = false
                                          "
                                          :class="
                                            alertSummary.heading === 'Alert ID'
                                              ? 'hover:bg-[#F1F2F6]'
                                              : ''
                                          "
                                        >
                                          <p class="text-clamp">
                                            {{ alertSummary.value }}
                                          </p>
                                          <SharedIconAlertCopyIcon
                                            v-if="
                                              alertSummary.copy &&
                                              alertSummary.heading ===
                                                'Alert ID'
                                            "
                                            class="cursor-pointer"
                                            @click="
                                              handleCopy(alertSummary.value)
                                            "
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="py-4">
                                    <h2 class="text-[#525252] font-semibold">
                                      Flagged Account
                                    </h2>
                                    <div
                                      class="py-4 flex space-x-4 items-center"
                                    >
                                      <img
                                        class="size-10"
                                        :src="userDetail.profileImageUrl"
                                        :alt="userDetail.profilename"
                                      />
                                      <div class="flex flex-col space-y-0.5">
                                        <h2
                                          class="text-[#333333] font-semibold"
                                        >
                                          {{ userDetail.profilename }}
                                        </h2>
                                        <p class="text-sm text-[#707070]">
                                          {{ userDetail.profileUserName }}
                                        </p>
                                      </div>
                                    </div>
                                    <div class="flex flex-col space-y-2.5">
                                      <div
                                        class="grid gap-x-2 items-center transition-all ease-in-out duration-500"
                                        :class="
                                          ContentDetail.activityLog
                                            ? 'grid-cols-[145px_4px_1fr]'
                                            : 'grid-cols-[200px_4px_1fr]'
                                        "
                                      >
                                        <p class="text-[#525252]">
                                          {{ userDetail.emailLabel }}
                                        </p>
                                        <p class="text-[#333333]">:</p>
                                        <div
                                          class="text-[#333333] rounded-[4px] px-2 py-1 hover:bg-[#F1F2F6] grid grid-cols-[1fr_20px] justify-between items-center"
                                          @mouseenter="userDetail.copy = true"
                                          @mouseleave="userDetail.copy = false"
                                        >
                                          <p class="text-[#333333] text-clamp">
                                            {{ userDetail.emailAddress }}
                                          </p>
                                          <SharedIconAlertCopyIcon
                                            v-if="userDetail.copy"
                                            class="cursor-pointer"
                                            @click="
                                              handleCopy(
                                                userDetail.emailAddress,
                                              )
                                            "
                                          />
                                        </div>
                                      </div>
                                      <div
                                        class="grid gap-x-2 items-center transition-all ease-in-out duration-500"
                                        :class="
                                          ContentDetail.activityLog
                                            ? 'grid-cols-[145px_4px_1fr]'
                                            : 'grid-cols-[200px_4px_1fr]'
                                        "
                                      >
                                        <p class="text-[#525252]">
                                          {{ userDetail.phoneLabel }}
                                        </p>
                                        <p class="text-[#333333]">:</p>
                                        <div
                                          class="text-[#333333] px-2 py-1 rounded-[4px] hover:bg-[#F1F2F6] grid grid-cols-[1fr_20px] justify-between items-center"
                                          @mouseenter="userDetail.copy = true"
                                          @mouseleave="userDetail.copy = false"
                                        >
                                          <p class="text-[#333333] text-clamp">
                                            {{ userDetail.number }}
                                          </p>
                                          <SharedIconAlertCopyIcon
                                            v-if="userDetail.copy"
                                            class="cursor-pointer"
                                            @click="
                                              handleCopy(userDetail.number)
                                            "
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="border-t border-[#F1F2F4] px-6 pt-4"
                                >
                                  <div class="flex space-x-4 items-center">
                                    <p class="text-[#525252] font-semibold">
                                      Activity Log
                                    </p>
                                    <InputsToggleInput
                                      :id="ContentDetail.id"
                                      :select="ContentDetail.activityLog"
                                      bgColor="#ffffff"
                                      checkedBgColor="#ffffff"
                                      uncheckedLabelBgColor="#C2C2C2"
                                      labelBgColor="#D63C3C"
                                      @toggle-select="
                                        () =>
                                          (ContentDetail.activityLog =
                                            !ContentDetail.activityLog)
                                      "
                                    />
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td
                              class="!px-0 scroll !overflow-hidden hover:!overflow-y-auto transition-all duration-500 ease-in-out"
                              :class="
                                ContentDetail.activityLog
                                  ? 'w-[25%]'
                                  : 'w-[33.33%]'
                              "
                              :style="{
                                borderRight: '1px solid #F1F2F4 !important',
                              }"
                            >
                              <div class="">
                                <p class="!px-6 text-[#525252] font-semibold">
                                  Source Viewer
                                </p>
                                <SourceHubSocialsSinglePost
                                  class="mt-4 !pt-0 !px-2"
                                  :single-post="ContentDetail.singlePost"
                                  scroll-color="#D63C3C"
                                  :hide-features="true"
                                />
                              </div>
                            </td>
                            <td
                              class="middleWrapper !px-0 scroll !overflow-hidden hover:!overflow-y-auto transition-all duration-500 ease-in-out"
                              :class="
                                ContentDetail.activityLog
                                  ? 'w-[25%]'
                                  : 'w-[33.33%]'
                              "
                              :style="{
                                borderRight: ContentDetail.activityLog
                                  ? '1px solid #F1F2F4 !important'
                                  : '0px',
                              }"
                            >
                              <div class="w-full h-full !px-6">
                                <div
                                  class="explanation space-y-3.5 w-full h-full"
                                >
                                  <div class="h-full flex flex-col space-y-3.5">
                                    <h2 class="text-[#525252] font-semibold">
                                      Alert Context
                                    </h2>
                                    <div
                                      class="flex flex-col space-y-2.5 pb-4"
                                      :class="
                                        openClose === 'Open'
                                          ? 'border-b border-[#F1F1F4]'
                                          : 'flex-grow'
                                      "
                                    >
                                      <div
                                        v-for="(
                                          value, key
                                        ) in ContentDetail.alertContextList"
                                        :key="key"
                                        class="grid gap-x-4 transition-all ease-in-out duration-500"
                                        :class="
                                          ContentDetail.activityLog
                                            ? 'grid-cols-[145px_4px_1fr]'
                                            : 'grid-cols-[200px_4px_1fr]'
                                        "
                                      >
                                        <p class="text-[#525252]">
                                          {{ key }}
                                        </p>
                                        <p class="text-[#333333]">:</p>
                                        <p class="text-[#333333] line-clamp-1">
                                          {{ value }}
                                        </p>
                                      </div>
                                    </div>
                                    <div
                                      v-if="openClose === 'Open'"
                                      class="flex flex-col space-y-[14px] flex-grow"
                                    >
                                      <h2 class="text-[#525252] font-semibold">
                                        Action
                                      </h2>
                                      <div
                                        class="flex space-x-[18px] items-center"
                                      >
                                        <p class="text-[#333333]">
                                          Send Directly
                                        </p>
                                        <InputsCheckBoxInput
                                          id="send_directly"
                                          v-model="
                                            ContentDetail.alertContextChecked
                                          "
                                          checkColor="#EF8914"
                                          @update:modelValue="() => ''"
                                        />
                                      </div>
                                      <Transition
                                        name="dynamicComp"
                                        mode="out-in"
                                      >
                                        <div
                                          v-if="
                                            !ContentDetail.alertContextChecked
                                          "
                                          class="flex flex-col flex-grow space-y-4"
                                        >
                                          <p class="text-[#333333]">
                                            Alert Confirmation
                                          </p>
                                          <div class="flex space-x-4">
                                            <button
                                              class="w-1/2 h-[39px] font-semibold flex justify-center items-center rounded-full"
                                              :class="
                                                disposition ===
                                                'HoldSendCloseTab'
                                                  ? 'bg-[#D63C3C] text-[#FFFFFF]'
                                                  : 'text-[#333333] bg-[#F1F2F6]'
                                              "
                                              @click.stop="
                                                setDisposition(
                                                  'HoldSendCloseTab',
                                                )
                                              "
                                            >
                                              True
                                            </button>
                                            <button
                                              class="w-1/2 h-[39px] font-semibold flex justify-center items-center rounded-full"
                                              :class="
                                                disposition === 'FlagFalse'
                                                  ? 'bg-[#0E9F52] text-[#FFFFFF]'
                                                  : 'text-[#333333] bg-[#F1F2F6]'
                                              "
                                              @click.stop="
                                                setDisposition('FlagFalse')
                                              "
                                            >
                                              False
                                            </button>
                                          </div>
                                          <Transition
                                            name="dynamicComp"
                                            mode="out-in"
                                          >
                                            <div
                                              v-if="disposition"
                                              class="flex flex-col flex-grow space-y-2"
                                            >
                                              <h2
                                                v-if="
                                                  disposition ===
                                                  'HoldSendCloseTab'
                                                "
                                                class="text-[#333333]"
                                              >
                                                Action
                                              </h2>
                                              <Transition
                                                name="dynamicComp"
                                                mode="out-in"
                                              >
                                                <component
                                                  :is="
                                                    componentMap[disposition]
                                                  "
                                                ></component>
                                              </Transition>
                                            </div>
                                          </Transition>
                                        </div>
                                        <AlertFlagTrueSend
                                          v-else-if="
                                            ContentDetail.alertContextChecked
                                          "
                                          :alertContextChecked="
                                            ContentDetail.alertContextChecked
                                          "
                                          @unselect-send-directly="
                                            ($event) =>
                                              (ContentDetail.alertContextChecked =
                                                $event)
                                          "
                                        />
                                      </Transition>
                                    </div>
                                    <div class="w-full flex justify-end">
                                      <button
                                        class="w-[165px] h-[39px] rounded-full flex justify-center items-center bg-[#D63C3C] text-white font-semibold"
                                      >
                                        Generate Report
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td
                              class="!px-0 scroll !overflow-hidden hover:!overflow-y-auto transition-all duration-500 ease-in-out"
                              :class="
                                ContentDetail.activityLog
                                  ? 'w-[25%] '
                                  : 'w-[0%]'
                              "
                            >
                              <transition name="table-fadeIn" mode="out-in">
                                <table v-if="ContentDetail.activityLog">
                                  <tbody>
                                    <tr class="activity_log">
                                      <td class="!py-0">
                                        <h2
                                          class="!px-6 text-[#525252] font-semibold"
                                        >
                                          Activity Log
                                        </h2>
                                        <div
                                          class="!px-6 rightWrapperInner mt-3.5 overflow-hidden flex flex-col space-y-[18px]"
                                        >
                                          <div
                                            v-for="(
                                              CommentDetail, commentDetailsIndex
                                            ) in commentDetails"
                                            :key="commentDetailsIndex + 'row3'"
                                            class="commentWrapper"
                                          >
                                            <div
                                              class="grid grid-cols-[20px_1fr] gap-x-6"
                                            >
                                              <div
                                                class="activitylogicon flex flex-col h-full relative"
                                                :class="
                                                  !CommentDetail.waitingText
                                                    ? 'mt-0.5'
                                                    : 'mt-[3px]'
                                                "
                                              >
                                                <SharedIconAlertActivityLogIcon
                                                  v-if="
                                                    !CommentDetail.waitingText
                                                  "
                                                  class="min-w-[20px] min-h-[20px]"
                                                />
                                                <div
                                                  v-else
                                                  class="w-[20px] h-[20px] min-w-[20px] min-h-[20px] rounded-full border-2 border-[#333333] bg-transparent"
                                                ></div>
                                                <div
                                                  class="w-[1px] min-h-[34px] h-full rounded-full bg-[#C2C2C2] relative top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
                                                  :class="
                                                    commentDetailsIndex ===
                                                    commentDetails.length - 1
                                                      ? 'hidden'
                                                      : ''
                                                  "
                                                ></div>
                                              </div>
                                              <div
                                                class="comment flex flex-col text-[#333333]"
                                              >
                                                <div
                                                  v-if="
                                                    CommentDetail.date &&
                                                    CommentDetail.time
                                                  "
                                                >
                                                  <span>{{
                                                    CommentDetail.date
                                                  }}</span>
                                                  <span> - </span>
                                                  <span>{{
                                                    CommentDetail.time
                                                  }}</span>
                                                </div>
                                                <div>
                                                  <p
                                                    v-if="CommentDetail.comment"
                                                    class="whitespace-normal"
                                                  >
                                                    {{ CommentDetail.comment }}
                                                  </p>
                                                  <p
                                                    v-if="
                                                      CommentDetail.reviewed
                                                    "
                                                    class="whitespace-normal font-semibold"
                                                  >
                                                    {{ CommentDetail.reviewed }}
                                                  </p>
                                                  <p
                                                    v-if="
                                                      CommentDetail.disposition
                                                    "
                                                    class="whitespace-normal"
                                                  >
                                                    {{
                                                      CommentDetail.disposition
                                                    }}
                                                  </p>
                                                  <p
                                                    v-if="
                                                      CommentDetail.reviewTime
                                                    "
                                                    class="whitespace-normal"
                                                  >
                                                    {{
                                                      CommentDetail.reviewTime
                                                    }}
                                                  </p>
                                                  <p
                                                    v-if="CommentDetail.actions"
                                                    class="whitespace-normal"
                                                  >
                                                    {{ CommentDetail.actions }}
                                                  </p>
                                                  <p
                                                    v-if="
                                                      CommentDetail.waitingText
                                                    "
                                                    class="text-[#707070] italic"
                                                  >
                                                    {{
                                                      CommentDetail.waitingText
                                                    }}
                                                  </p>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                        <button
                                          class="!ml-1 w-[120px] h-[39px] font-semibold flex justify-center items-center rounded-full bg-[#707070] text-white mt-[54px]"
                                        >
                                          Send
                                        </button>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </transition>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                </transition>
              </tbody>
            </template>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core'
import profilePic from 'assets/img/bp/dp-3.svg'
import { storeToRefs } from 'pinia'
import FlagFalse from '~/components/alert/FlagFalse.vue'
import HoldSendCloseTab from '~/components/alert/HoldSendCloseTab.vue'
import SlidingBar from '~/components/alert/sliding-bar/SlidingBar.vue'
import UncertainTrueFalseTab from '~/components/alert/UncertainTrueFalseTab.vue'
import Source from '~/pages/source.vue'
import { useAlert } from '~/stores/alert'
import SharedIconAlertSortIcon from '~/components/shared/icon/alert/SortIcon.vue'
import SharedIconAlertUpDownArrowIcon from '~/components/shared/icon/alert/UpDownArrowIcon.vue'

const componentMap = {
  FlagFalse,
  HoldSendCloseTab,
}
const store = useAlert()
const { setDisposition } = store
const { disposition, activityLog, currentComp, openClose } = storeToRefs(store)

// Reactive variables
const desktop = useBreakpoints(breakpointsTailwind).isGreater('md')
const setActivityLog = ref(false)
const selectedFlag = ref(0)
const contentSelected = ref(false)
const previousId = ref('')

// interface DataColumnOptions {
//   id: number
//   text: string
// }

const openDataColumnOptions = ref([
  {
    id: 1,
    text: 'Alert Name',
    filterOptions: [
      { id: 1, text: 'A-Z', checked: false },
      { id: 2, text: 'Z-A', checked: false },
    ],
    toggle: false,
    disabled: true,
    checked: true,
    runAction: (dataColumnOption, filterOption) => {
      selectFilterOption(dataColumnOption, filterOption)
    },
    icon: markRaw(SharedIconAlertSortIcon),
  },
  {
    id: 2,
    text: 'Alert ID',
    disabled: false,
    checked: true,
  },
  {
    id: 3,
    text: 'Created On',
    disabled: false,
    checked: true,
    filterOptions: [
      { id: 1, text: 'Recent', checked: false },
      { id: 2, text: 'Past', checked: false },
    ],
    toggle: false,
    runAction: (dataColumnOption, filterOption) => {
      selectFilterCreatedOnOption(dataColumnOption, filterOption)
    },
    icon: markRaw(SharedIconAlertUpDownArrowIcon),
  },
  {
    id: 4,
    text: 'Alert Open Time',
    disabled: false,
    checked: true,
    filterOptions: [
      { id: 1, text: 'Shortest', checked: false },
      { id: 2, text: 'Longest', checked: false },
    ],
    toggle: false,
    runAction: (dataColumnOption, filterOption) => {
      selectFilterAlertOpenTimeOption(dataColumnOption, filterOption)
    },
    icon: markRaw(SharedIconAlertUpDownArrowIcon),
  },
  {
    id: 5,
    text: 'Account Name',
    disabled: false,
    checked: true,
  },
  {
    id: 6,
    text: 'Score',
    disabled: false,
    checked: true,
    filterOptions: [
      { id: 1, text: 'Min', checked: false },
      { id: 2, text: 'Max', checked: false },
    ],
    toggle: false,
    runAction: (dataColumnOption, filterOption) => {
      selectFilterScoreOption(dataColumnOption, filterOption)
    },
    icon: markRaw(SharedIconAlertUpDownArrowIcon),
  },
  {
    id: 7,
    text: 'Type',
    disabled: false,
    checked: true,
  },
  {
    id: 8,
    text: 'Source',
    disabled: false,
    checked: true,
  },
  {
    id: 9,
    text: 'Cluster',
    disabled: false,
    checked: false,
  },
  {
    id: 10,
    text: 'Score Threshold Setting',
    disabled: false,
    checked: false,
  },
  {
    id: 11,
    text: 'Content',
    disabled: false,
    checked: false,
  },
  {
    id: 12,
    text: 'Explanation',
    disabled: false,
    checked: false,
  },
  {
    id: 13,
    text: 'Send Time',
    disabled: false,
    checked: false,
  },
  {
    id: 14,
    text: 'Alert Internal',
    disabled: false,
    checked: false,
  },
  {
    id: 15,
    text: 'Assigned To',
    disabled: false,
    checked: false,
  },
  {
    id: 16,
    text: 'Role',
    disabled: false,
    checked: false,
  },
  {
    id: 17,
    text: 'User Permission',
    disabled: false,
    checked: false,
  },
  {
    id: 18,
    text: 'Alerts Permission',
    disabled: false,
    checked: false,
  },
  {
    id: 19,
    text: 'Status',
    disabled: false,
    checked: false,
  },
])
const closeDataColumnOptions = ref([
  {
    id: 1,
    text: 'Alert Name',
    filterOptions: [
      { id: 1, text: 'A-Z', checked: false },
      { id: 2, text: 'Z-A', checked: false },
    ],
    toggle: false,
    disabled: true,
    checked: true,
    runAction: (dataColumnOption, filterOption) => {
      selectFilterOption(dataColumnOption, filterOption)
    },
    icon: markRaw(SharedIconAlertSortIcon),
  },
  {
    id: 2,
    text: 'Alert ID',
    disabled: false,
    checked: true,
  },
  {
    id: 3,
    text: 'Closed on',
    disabled: false,
    checked: true,
    filterOptions: [
      { id: 1, text: 'Recent', checked: false },
      { id: 2, text: 'Past', checked: false },
    ],
    toggle: false,
    runAction: (dataColumnOption, filterOption) => {
      selectFilterCreatedOnOption(dataColumnOption, filterOption)
    },
    icon: markRaw(SharedIconAlertUpDownArrowIcon),
  },
  {
    id: 4,
    text: 'Response Time',
    disabled: false,
    checked: true,
    filterOptions: [
      { id: 1, text: 'Shortest', checked: false },
      { id: 2, text: 'Longest', checked: false },
    ],
    toggle: false,
    runAction: (dataColumnOption, filterOption) => {
      selectFilterAlertOpenTimeOption(dataColumnOption, filterOption)
    },
    icon: markRaw(SharedIconAlertUpDownArrowIcon),
  },
  {
    id: 5,
    text: 'Reviewed by',
    disabled: false,
    checked: true,
  },
  {
    id: 6,
    text: 'Score',
    disabled: false,
    checked: true,
    filterOptions: [
      { id: 1, text: 'Min', checked: false },
      { id: 2, text: 'Max', checked: false },
    ],
    toggle: false,
    runAction: (dataColumnOption, filterOption) => {
      selectFilterScoreOption(dataColumnOption, filterOption)
    },
    icon: markRaw(SharedIconAlertUpDownArrowIcon),
  },
  {
    id: 7,
    text: 'Type',
    disabled: false,
    checked: true,
  },
  {
    id: 8,
    text: 'Source',
    disabled: false,
    checked: true,
  },
  {
    id: 9,
    text: 'Cluster',
    disabled: false,
    checked: false,
  },
  {
    id: 10,
    text: 'Score Threshold Setting',
    disabled: false,
    checked: false,
  },
  {
    id: 11,
    text: 'Content',
    disabled: false,
    checked: false,
  },
  {
    id: 12,
    text: 'Explanation',
    disabled: false,
    checked: false,
  },
  {
    id: 13,
    text: 'Send Time',
    disabled: false,
    checked: false,
  },
  {
    id: 14,
    text: 'Alert Internal',
    disabled: false,
    checked: false,
  },
  {
    id: 15,
    text: 'Assigned To',
    disabled: false,
    checked: false,
  },
  {
    id: 16,
    text: 'Role',
    disabled: false,
    checked: false,
  },
  {
    id: 17,
    text: 'User Permission',
    disabled: false,
    checked: false,
  },
  {
    id: 18,
    text: 'Alerts Permission',
    disabled: false,
    checked: false,
  },
  {
    id: 19,
    text: 'Status',
    disabled: false,
    checked: false,
  },
])
const dataColumnOptions = ref([...openDataColumnOptions.value])
watch(
  () => openClose.value,
  (value) => {
    console.log(value, 'value')
    if (value === 'Open') {
      dataColumnOptions.value = [...openDataColumnOptions.value]
    } else if (value === 'Close') {
      dataColumnOptions.value = [...closeDataColumnOptions.value]
    }
  },
)
const selectedDataOptions = ref(
  JSON.parse(JSON.stringify(dataColumnOptions.value)),
)
const handleSelectionChange = (selectedOptions) => {
  console.log('Selected options:', selectedOptions)
  dataColumnOptions.value.forEach((option, index) => {
    if (option.id === selectedOptions.id) {
      option.checked = !option.checked
    }
  })
  // selectedDataOptions.value = selectedOptions
  // You can perform additional actions here if needed
}

// Utility function for string limiting (replacing the filter)
const strLimit = (str, limit) => {
  if (str.length <= limit) return str
  return str.substring(0, limit) + '...'
}

// Data arrays
const viewers = ref([
  {
    Name: 'Elon Musk',
    UserName: '@WholMarsBlog',
    Time: '8h',
    Status: 'Tesla FSD price rising to $12k on Jan 17',
    CommentCount: '55',
    RetweetCount: '102',
    ReactCount: '1.2K',
  },
  {
    Name: 'Iqbal Sunny',
    UserName: '@IkbalSunny',
    Time: '6h',
    Status:
      'Monthly subscription price will rise when FSD goes to wide release',
    CommentCount: '55',
    RetweetCount: '102',
    ReactCount: '1.2K',
  },
  {
    Name: 'Sandy Marry',
    UserName: '@SandyMarriana',
    Time: '10h',
    Status: 'Monthly sub price staying the same?',
    CommentCount: '55',
    RetweetCount: '102',
    ReactCount: '1.2K',
  },
  {
    Name: 'Alexa Jolly',
    UserName: '@Jolly Alexana',
    Time: '16h',
    Status: 'Tesla FSD price rising to $12k on Jan 17',
    CommentCount: '55',
    RetweetCount: '102',
    ReactCount: '1.2K',
  },
  {
    Name: 'Ariana Granda',
    UserName: '@ArianaGrand',
    Time: '3h',
    Status: 'Monthly sub price staying the same?',
    CommentCount: '5500',
    RetweetCount: '1088',
    ReactCount: '10.2K',
  },
])
const contentDetails = ref([
  {
    id: 1,
    Selected: false,
    checked: true,
    alertName: 'Complaints',
    alertId: 'AF4F68',
    createdOn: '14 August 2025 - 17:48',
    alertOpenTime: '19 Min',
    accountName: 'John Johnson',
    score: 21,
    type: 'Email',
    Source: 'Gmail',
    singlePost: {
      // Video post
      id: 'post345678',
      type: 'video',
      statusType: 'added_video',
      provider: 'Facebook',
      profilename: 'Mike Johnson',
      profileImageUrl: '/social/profile-picture.png',
      text: 'Check out this cool video I made!',
      createdAt: '2025-03-14T18:45:00Z',
      privacy: 'Public',
      sourceUrl:
        'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',
      fullPicture: 'https://picsum.photos/800/600',
      reactionsCount: 108,
      commentsCount: 32,
      sharesCount: 15,
      archivedAttachmentImages: [],
      attachmentImages: [],
    },
    alertContextList: {
      Content: `"nobody will find out"`,
      Explanation: 'Conspiracy tonality',
      Status: openClose.value === 'Open' ? 'Unreviewed' : 'True',
    },
    alertContextChecked: false,
    FlaggedText:
      '...cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod ...',
    ActivityLog:
      '12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor..."',
    activityLog: false,
  },
  {
    id: 2,
    Selected: false,
    checked: true,
    alertName: 'Complaints',
    alertId: 'AS4F9F',
    createdOn: '13 August 2025 - 4:21',
    alertOpenTime: '1 Day',
    accountName: 'Tony Johnson',
    score: 97,
    type: 'Social Media',
    Source: 'Facebook',
    singlePost: {
      // Core post properties
      id: 'post123456',
      type: 'photo', // Possible values: "photo", "video", "reel", "event"
      statusType: 'added_photos', // Possible values: "added_photos", "added_video", "shared_story", "mobile_status_update", "created_event"
      provider: 'Facebook',

      // Profile information
      profilename: 'John Doe',
      profileImageUrl: '/social/profile-picture.png',

      // Post content
      text: 'This is the main text content of the post.',
      description: '',
      link: 'https://example.com/linked-content',
      linkName: '',
      caption: '',

      // Dates
      createdAt: '2025-03-15T12:00:00Z',
      updatedAt: '2025-03-15T14:30:00Z',

      // Location information
      placeName: '',
      placeCity: '',

      // Privacy settings
      privacy: 'Public', // Possible values: "Public", "Friends", "Friends of friends", "Only me", "Your friends", "Custom"

      // Mentions
      mentionText: '',
      // "With <a href='#'>@Jane Smith</a> and <a href='#'>@Mike Johnson</a>",

      // Engagement metrics
      reactionsCount: 42,
      commentsCount: 15,
      sharesCount: 7,

      // Media content
      fullPicture: 'https://picsum.photos/800/600',
      archivedFullPicture: 'https://picsum.photos/800/600',
      sourceUrl:
        'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',
      archivedSourceUrl:
        'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',

      // Multiple images
      attachmentImages: ['https://picsum.photos/800/600'],
      archivedAttachmentImages: ['https://picsum.photos/800/600'],

      // Event information
      event: null,
    },
    alertContextList: {
      Content: `"nobody will find out"`,
      Explanation: 'Conspiracy tonality',
      Status: openClose.value === 'Open' ? 'Unreviewed' : 'True',
    },
    FlaggedText:
      '...cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod ...',
    ActivityLog:
      '12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor..."',
    activityLog: false,
  },
  {
    id: 3,
    Selected: false,
    checked: true,
    alertName: 'Insider Information',
    alertId: 'AS65D8',
    createdOn: '8 August 2025 - 12:14',
    alertOpenTime: '1 Week',
    accountName: 'Anna Deo',
    score: 51,
    type: 'Text',
    Source: 'iMessage',
    singlePost: {
      // Photo post with multiple images
      id: 'post234567',
      type: 'photo',
      statusType: 'added_photos',
      provider: 'Facebook',
      profilename: 'Emily Davis',
      profileImageUrl: '/social/profile-picture.png',
      text: 'Vacation memories from last week!',
      createdAt: '2025-03-12T20:15:00Z',
      placeName: 'Paradise Beach Resort',
      placeCity: 'Cancun',
      privacy: 'Friends',
      attachmentImages: [
        'https://picsum.photos/800/600',
        'https://picsum.photos/800/600',
        'https://picsum.photos/800/600',
        'https://picsum.photos/800/600',
        'https://picsum.photos/800/600',
        'https://picsum.photos/800/600',
      ],
      reactionsCount: 154,
      commentsCount: 37,
      sharesCount: 3,
      archivedAttachmentImages: [
        'https://picsum.photos/800/600',
        'https://picsum.photos/800/600',
        'https://picsum.photos/800/600',
        'https://picsum.photos/800/600',
        'https://picsum.photos/800/600',
        'https://picsum.photos/800/600',
      ],
    },
    alertContextList: {
      Content: `"nobody will find out"`,
      Explanation: 'Conspiracy tonality',
      Status: openClose.value === 'Open' ? 'Unreviewed' : 'True',
    },
    FlaggedText:
      '...cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod ...',
    ActivityLog:
      '12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor..."',
    activityLog: false,
  },
  {
    id: 4,
    Selected: false,
    checked: true,
    alertName: 'Misconduct',
    alertId: 'AF3594',
    createdOn: '4 August 2025 - 10:17',
    alertOpenTime: '1 Week',
    accountName: 'Maria Hanz',
    score: 67,
    type: 'Social Media',
    Source: 'LinkedIn',
    singlePost: {
      // Core post properties
      id: 'post123456',
      type: 'photo', // Possible values: "photo", "video", "reel", "event"
      statusType: 'added_photos', // Possible values: "added_photos", "added_video", "shared_story", "mobile_status_update", "created_event"
      provider: 'Facebook',

      // Profile information
      profilename: 'John Doe',
      profileImageUrl: '/social/profile-picture.png',

      // Post content
      text: 'This is the main text content of the post.',
      description: '',
      link: 'https://example.com/linked-content',
      linkName: '',
      caption: '',

      // Dates
      createdAt: '2025-03-15T12:00:00Z',
      updatedAt: '2025-03-15T14:30:00Z',

      // Location information
      placeName: '',
      placeCity: '',

      // Privacy settings
      privacy: 'Public', // Possible values: "Public", "Friends", "Friends of friends", "Only me", "Your friends", "Custom"

      // Mentions
      mentionText: '',
      // "With <a href='#'>@Jane Smith</a> and <a href='#'>@Mike Johnson</a>",

      // Engagement metrics
      reactionsCount: 42,
      commentsCount: 15,
      sharesCount: 7,

      // Media content
      fullPicture: 'https://picsum.photos/800/600',
      archivedFullPicture: 'https://picsum.photos/800/600',
      sourceUrl:
        'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',
      archivedSourceUrl:
        'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',

      // Multiple images
      attachmentImages: ['https://picsum.photos/800/600'],
      archivedAttachmentImages: ['https://picsum.photos/800/600'],

      // Event information
      event: null,
    },
    alertContextList: {
      Content: `"nobody will find out"`,
      Explanation: 'Conspiracy tonality',
      Status: openClose.value === 'Open' ? 'Unreviewed' : 'True',
    },
    FlaggedText:
      '...cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod ...',
    ActivityLog:
      '12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor..."',
    activityLog: false,
  },
  {
    id: 5,
    Selected: false,
    checked: true,
    alertName: 'Misconduct',
    alertId: 'AR14T5',
    createdOn: '1 August 2025 - 10:18',
    alertOpenTime: '2 Week',
    accountName: 'Will Smith',
    score: 41,
    type: 'Text',
    Source: 'WhatsApp',
    singlePost: {
      // Core post properties
      id: 'post123456',
      type: 'photo', // Possible values: "photo", "video", "reel", "event"
      statusType: 'added_photos', // Possible values: "added_photos", "added_video", "shared_story", "mobile_status_update", "created_event"
      provider: 'Facebook',

      // Profile information
      profilename: 'John Doe',
      profileImageUrl: '/social/profile-picture.png',

      // Post content
      text: 'This is the main text content of the post.',
      description: '',
      link: 'https://example.com/linked-content',
      linkName: '',
      caption: '',

      // Dates
      createdAt: '2025-03-15T12:00:00Z',
      updatedAt: '2025-03-15T14:30:00Z',

      // Location information
      placeName: '',
      placeCity: '',

      // Privacy settings
      privacy: 'Public', // Possible values: "Public", "Friends", "Friends of friends", "Only me", "Your friends", "Custom"

      // Mentions
      mentionText: '',
      // "With <a href='#'>@Jane Smith</a> and <a href='#'>@Mike Johnson</a>",

      // Engagement metrics
      reactionsCount: 42,
      commentsCount: 15,
      sharesCount: 7,

      // Media content
      fullPicture: 'https://picsum.photos/800/600',
      archivedFullPicture: 'https://picsum.photos/800/600',
      sourceUrl:
        'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',
      archivedSourceUrl:
        'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',

      // Multiple images
      attachmentImages: ['https://picsum.photos/800/600'],
      archivedAttachmentImages: ['https://picsum.photos/800/600'],

      // Event information
      event: null,
    },
    alertContextList: {
      Content: `"nobody will find out"`,
      Explanation: 'Conspiracy tonality',
      Status: openClose.value === 'Open' ? 'Unreviewed' : 'True',
    },
    FlaggedText:
      '...cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod ...',
    ActivityLog:
      '12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor..."',
    activityLog: false,
  },
  {
    id: 6,
    Selected: false,
    checked: true,
    alertName: 'Promissory Statements',
    alertId: 'AS6Y82',
    createdOn: '29 July 2025 - 20:35',
    alertOpenTime: '2 Week',
    accountName: 'Maria Hanz',
    score: 87,
    type: 'Social Media',
    Source: 'Facebook',
    singlePost: {
      // Core post properties
      id: 'post123456',
      type: 'photo', // Possible values: "photo", "video", "reel", "event"
      statusType: 'added_photos', // Possible values: "added_photos", "added_video", "shared_story", "mobile_status_update", "created_event"
      provider: 'Facebook',

      // Profile information
      profilename: 'John Doe',
      profileImageUrl: '/social/profile-picture.png',

      // Post content
      text: 'This is the main text content of the post.',
      description: '',
      link: 'https://example.com/linked-content',
      linkName: '',
      caption: '',

      // Dates
      createdAt: '2025-03-15T12:00:00Z',
      updatedAt: '2025-03-15T14:30:00Z',

      // Location information
      placeName: '',
      placeCity: '',

      // Privacy settings
      privacy: 'Public', // Possible values: "Public", "Friends", "Friends of friends", "Only me", "Your friends", "Custom"

      // Mentions
      mentionText: '',
      // "With <a href='#'>@Jane Smith</a> and <a href='#'>@Mike Johnson</a>",

      // Engagement metrics
      reactionsCount: 42,
      commentsCount: 15,
      sharesCount: 7,

      // Media content
      fullPicture: 'https://picsum.photos/800/600',
      archivedFullPicture: 'https://picsum.photos/800/600',
      sourceUrl:
        'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',
      archivedSourceUrl:
        'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',

      // Multiple images
      attachmentImages: ['https://picsum.photos/800/600'],
      archivedAttachmentImages: ['https://picsum.photos/800/600'],

      // Event information
      event: null,
    },
    alertContextList: {
      Content: `"nobody will find out"`,
      Explanation: 'Conspiracy tonality',
      Status: openClose.value === 'Open' ? 'Unreviewed' : 'True',
    },
    FlaggedText:
      '...cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod ...',
    ActivityLog:
      '12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor..."',
    activityLog: false,
  },
  {
    id: 7,
    Selected: false,
    checked: true,
    alertName: 'Promissory Statements',
    alertId: 'AF9I6Y',
    createdOn: '24 July 2025 - 11:45',
    alertOpenTime: '3 Week',
    accountName: 'Anna Deo',
    score: 71,
    type: 'Social Media',
    Source: 'Facebook',
    singlePost: {
      // Core post properties
      id: 'post123456',
      type: 'photo', // Possible values: "photo", "video", "reel", "event"
      statusType: 'added_photos', // Possible values: "added_photos", "added_video", "shared_story", "mobile_status_update", "created_event"
      provider: 'Facebook',

      // Profile information
      profilename: 'John Doe',
      profileImageUrl: '/social/profile-picture.png',

      // Post content
      text: 'This is the main text content of the post.',
      description: '',
      link: 'https://example.com/linked-content',
      linkName: '',
      caption: '',

      // Dates
      createdAt: '2025-03-15T12:00:00Z',
      updatedAt: '2025-03-15T14:30:00Z',

      // Location information
      placeName: '',
      placeCity: '',

      // Privacy settings
      privacy: 'Public', // Possible values: "Public", "Friends", "Friends of friends", "Only me", "Your friends", "Custom"

      // Mentions
      mentionText: '',
      // "With <a href='#'>@Jane Smith</a> and <a href='#'>@Mike Johnson</a>",

      // Engagement metrics
      reactionsCount: 42,
      commentsCount: 15,
      sharesCount: 7,

      // Media content
      fullPicture: 'https://picsum.photos/800/600',
      archivedFullPicture: 'https://picsum.photos/800/600',
      sourceUrl:
        'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',
      archivedSourceUrl:
        'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',

      // Multiple images
      attachmentImages: ['https://picsum.photos/800/600'],
      archivedAttachmentImages: ['https://picsum.photos/800/600'],

      // Event information
      event: null,
    },
    alertContextList: {
      Content: `"nobody will find out"`,
      Explanation: 'Conspiracy tonality',
      Status: openClose.value === 'Open' ? 'Unreviewed' : 'True',
    },
    FlaggedText:
      '...cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod ...',
    ActivityLog:
      '12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor..."',
    activityLog: false,
  },
  {
    id: 8,
    Selected: false,
    checked: true,
    alertName: 'Promissory Statements',
    alertId: 'AS7M3R',
    createdOn: '16 July 2025 - 15:10',
    alertOpenTime: '1 Month',
    accountName: 'John Johnson',
    score: 92,
    type: 'Email',
    Source: 'Gmail',
    singlePost: {
      // Core post properties
      id: 'post123456',
      type: 'photo', // Possible values: "photo", "video", "reel", "event"
      statusType: 'added_photos', // Possible values: "added_photos", "added_video", "shared_story", "mobile_status_update", "created_event"
      provider: 'Facebook',

      // Profile information
      profilename: 'John Doe',
      profileImageUrl: '/social/profile-picture.png',

      // Post content
      text: 'This is the main text content of the post.',
      description: '',
      link: 'https://example.com/linked-content',
      linkName: '',
      caption: '',

      // Dates
      createdAt: '2025-03-15T12:00:00Z',
      updatedAt: '2025-03-15T14:30:00Z',

      // Location information
      placeName: '',
      placeCity: '',

      // Privacy settings
      privacy: 'Public', // Possible values: "Public", "Friends", "Friends of friends", "Only me", "Your friends", "Custom"

      // Mentions
      mentionText: '',
      // "With <a href='#'>@Jane Smith</a> and <a href='#'>@Mike Johnson</a>",

      // Engagement metrics
      reactionsCount: 42,
      commentsCount: 15,
      sharesCount: 7,

      // Media content
      fullPicture: 'https://picsum.photos/800/600',
      archivedFullPicture: 'https://picsum.photos/800/600',
      sourceUrl:
        'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',
      archivedSourceUrl:
        'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',

      // Multiple images
      attachmentImages: ['https://picsum.photos/800/600'],
      archivedAttachmentImages: ['https://picsum.photos/800/600'],

      // Event information
      event: null,
    },
    alertContextList: {
      Content: `"nobody will find out"`,
      Explanation: 'Conspiracy tonality',
      Status: openClose.value === 'Open' ? 'Unreviewed' : 'True',
    },
    FlaggedText:
      '...cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod cupidatat non prodient, sunt in cupa qui, non prodient. Lorem consectetuer adipiscing elit, qudjd ismod ...',
    ActivityLog:
      '12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor 12-3-2021 8:33 am - Flag Created for "Promissor..."',
    activityLog: false,
  },
])
const filterToggle = (option) => {
  dataColumnOptions.value.forEach((dataOption) => {
    if (
      dataOption.id === option.id &&
      dataOption.filterOptions &&
      dataOption.filterOptions.length > 0
    ) {
      dataOption.toggle = !dataOption.toggle
    } else {
      dataOption.toggle = false
    }
  })
}
const selectFilterOption = (dataColumnOption, filterOption) => {
  checkSelectedFilterOption(dataColumnOption, filterOption)
  if (filterOption.text === 'A-Z') {
    contentDetails.value = contentDetails.value.sort((a, b) =>
      a.alertName.localeCompare(b.alertName),
    )
  } else if (filterOption.text === 'Z-A') {
    contentDetails.value = contentDetails.value.sort((a, b) =>
      b.alertName.localeCompare(a.alertName),
    )
  }
}
const parseCreatedOn = (dateStr) => {
  return new Date(dateStr.replace(' -', ' '))
}

const selectFilterCreatedOnOption = (dataColumnOption, filterOption) => {
  checkSelectedFilterOption(dataColumnOption, filterOption)
  if (filterOption.text === 'Recent') {
    // Newest first
    contentDetails.value = [...contentDetails.value].sort(
      (a, b) =>
        parseCreatedOn(b.createdOn).getTime() -
        parseCreatedOn(a.createdOn).getTime(),
    )
  } else if (filterOption.text === 'Past') {
    // Oldest first
    contentDetails.value = [...contentDetails.value].sort(
      (a, b) =>
        parseCreatedOn(a.createdOn).getTime() -
        parseCreatedOn(b.createdOn).getTime(),
    )
  }
}
const parseAlertOpenTime = (timeStr) => {
  const [value, unit] = timeStr.split(' ')
  const num = parseInt(value)

  switch (unit) {
    case 'Min':
    case 'Mins':
      return num
    case 'Hour':
    case 'Hours':
      return num * 60
    case 'Day':
    case 'Days':
      return num * 60 * 24
    case 'Week':
    case 'Weeks':
      return num * 60 * 24 * 7
    case 'Month':
    case 'Months':
      return num * 60 * 24 * 30
    case 'Year':
    case 'Years':
      return num * 60 * 24 * 365
    default:
      return 0
  }
}
const selectFilterAlertOpenTimeOption = (dataColumnOption, filterOption) => {
  checkSelectedFilterOption(dataColumnOption, filterOption)
  if (filterOption.text === 'Shortest') {
    // কম থেকে বেশি → Shortest first
    contentDetails.value = [...contentDetails.value].sort(
      (a, b) =>
        parseAlertOpenTime(a.alertOpenTime) -
        parseAlertOpenTime(b.alertOpenTime),
    )
  } else if (filterOption.text === 'Longest') {
    // বেশি থেকে কম → Longest first
    contentDetails.value = [...contentDetails.value].sort(
      (a, b) =>
        parseAlertOpenTime(b.alertOpenTime) -
        parseAlertOpenTime(a.alertOpenTime),
    )
  }
}
const selectFilterScoreOption = (dataColumnOption, filterOption) => {
  checkSelectedFilterOption(dataColumnOption, filterOption)
  if (filterOption.text === 'Min') {
    contentDetails.value = contentDetails.value.sort(
      (a, b) => a.score - b.score,
    )
  } else if (filterOption.text === 'Max') {
    contentDetails.value = contentDetails.value.sort(
      (a, b) => b.score - a.score,
    )
  }
}

const checkSelectedFilterOption = (dataColumnOption, filterOption) => {
  dataColumnOptions.value.forEach((column) => {
    if (!column.filterOptions) return
    column.filterOptions.forEach((option) => {
      option.checked =
        column.id === dataColumnOption.id && option.id === filterOption.id
    })
  })
}
const searchQuery = ref('')
const seachContentDetails = computed(() => {
  return contentDetails.value.filter(
    (detail) =>
      detail.alertName
        .toLowerCase()
        .includes(searchQuery.value.toLowerCase()) ||
      detail.alertId.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      detail.accountName
        .toLowerCase()
        .includes(searchQuery.value.toLowerCase()) ||
      detail.type.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      detail.Source.toLowerCase().includes(searchQuery.value.toLowerCase()),
  )
})
const commentDetails = ref([
  {
    date: '12-3-2021',
    time: '08:33 am',
    comment: 'Comment posted on Twitter by @bobsmith123',
  },
  {
    date: '12-3-2021',
    time: '08:33 am',
    comment: 'Archived on Sharp Archive',
  },
  {
    date: '12-3-2021',
    time: '08:33 am',
    comment: 'Flagged for "Promissory Statement',
    disposition: 'Disposition: Assigned to Jane Smith',
  },
  {
    date: '12-3-2021',
    time: '08:33 am',
    comment: '',
    reviewed: 'Reviewed by Jane Smith (response time: 00:04:09)',
    reviewTime: 'Review Time: 00:04:09',
    actions:
      'Actions: Marked as "True Flag", notes recorded, flag sent to George Jones',
  },
  {
    date: '12-3-2021',
    time: '08:33 am',
    comment: '',
    reviewed: 'Reviewed by George Jones (response time: 2 days, 06:026:43)',
    reviewTime: 'Review Time: 00:07:23',
    actions:
      'Actions: Marked as "True Flag", notes recorded, flag sent to George Jones',
  },
  {
    date: '12-3-2021',
    time: '08:33 am',
    comment: 'Flagged for "Promissory Statement',
    disposition: 'Disposition: Assigned to Jane Smith',
  },
  {
    date: '12-3-2021',
    time: '08:33 am',
    comment: '',
    reviewed: 'Reviewed by Jane Smith (response time: 00:04:09)',
    reviewTime: 'Review Time: 00:04:09',
    actions:
      'Actions: Marked as "True Flag", notes recorded, flag sent to George Jones',
  },
  {
    date: '12-3-2021',
    time: '08:33 am',
    comment: '',
    reviewed: 'Reviewed by George Jones (response time: 2 days, 06:026:43)',
    reviewTime: 'Review Time: 00:07:23',
    actions:
      'Actions: Marked as "True Flag", notes recorded, flag sent to George Jones',
  },
  {
    waitingText: 'Waiting for next approach',
  },
])
const alertSummaries = ref([
  {
    heading: 'Cluster',
    value: 'Compliance',
  },
  {
    heading: 'Alert Name',
    value: 'Misconduct',
  },
  {
    heading: 'Alert ID',
    value: 'F54D32',
    copy: false,
  },
  {
    heading: 'Alert Open Time',
    value: 'August 14, 2025 8:28 AM',
  },
  {
    heading: 'Type',
    value: 'Social Media',
  },
  {
    heading: 'Source',
    value: '41',
  },
  {
    heading: 'Score Threshol...',
    value: '35',
  },
  {
    heading: 'Alert Interval',
    value: '0.05',
  },
])
const userDetail = ref({
  profilename: 'Tony Johnson',
  profileUserName: '@tony_johnson',
  profileImageUrl: '/social/default-profile.png',
  emailLabel: 'Email',
  emailAddress: '<EMAIL>',
  phoneLabel: 'Phone',
  number: '****** 358 3548',
  copy: false,
})
const { $toast } = useNuxtApp()
const { copy } = useClipboard()
const handleCopy = async (text) => {
  const ok = await copy(text)
  if (ok) {
    $toast('clear')
    $toast('success', {
      message: 'Copied!',
      className: 'toasted-bg-archive',
    })
  }
}
// Methods
const selectedFlagged = (selected) => {
  if (previousId.value === selected) {
    contentDetails.value.map((item) => {
      if (item.id === selected) {
        const dateTime = item.ActivityLog.split(' ', 3)
        item.date = dateTime[0]
        item.time = dateTime[1] + ' ' + dateTime[2]
        item.comment = item.ActivityLog.split('-')[3]
        item.Selected = !item.Selected
        const itemSelectUpdate = item.Selected
        contentSelected.value = itemSelectUpdate
        return itemSelectUpdate
      } else {
        return (item.Selected = false)
      }
    })
  } else {
    contentDetails.value.map((item) => {
      return (item.Selected = false)
    })
    setTimeout(() => {
      contentDetails.value.forEach((item) => {
        if (item.id === selected) {
          previousId.value = item.id
          const dateTime = item.ActivityLog.split(' ', 3)
          item.date = dateTime[0]
          item.time = dateTime[1] + ' ' + dateTime[2]
          item.comment = item.ActivityLog.split('-')[3]
          item.Selected = true
          const itemSelectUpdate = item.Selected
          contentSelected.value = itemSelectUpdate
          return itemSelectUpdate
        }
      })
    }, 500)
  }
}

// Watchers
watch(activityLog, (data) => {
  if (data) {
    setActivityLog.value = true
  } else {
    setTimeout(() => {
      setActivityLog.value = false
    }, 500)
  }
})
const isOpen = ref(false)
const tableHeight = ref(0)
const toggleOffAllDropdowns = () => {
  console.log('toggleOffAllDropdowns')
  dataColumnOptions.value.forEach((dataOption) => {
    dataOption.toggle = false
  })
}
onMounted(() => {
  getTableHeight()
  window.addEventListener('resize', getTableHeight)
  window.addEventListener('click', toggleOffAllDropdowns)
})
onUnmounted(() => {
  window.removeEventListener('resize', getTableHeight)
  window.removeEventListener('click', toggleOffAllDropdowns)
})

const getTableHeight = () => {
  const tableWrapper = document.querySelector('.table-wrapper')
  console.log('tableWrapper', tableWrapper)
  if (tableWrapper) {
    tableHeight.value = tableWrapper.clientHeight
    console.log('tableHeight', tableHeight.value)
  }
}
</script>

<style lang="scss" scoped>
.tooltip {
  @apply invisible h-28 overflow-auto
  whitespace-normal
  absolute
  bg-red-deep
  text-white
  text-left
  z-100
  rounded-2xl
  left-0
  md:px-2 py-0.5
  px-4
  shadow-lg;
}
.has-tooltip:hover .tooltip {
  top: -30px;
  @apply visible;
  transition: all 0.3s linear;
}
.has-tooltip:hover .tooltip1 {
  @apply visible;
  transition: all 0.3s linear;
}
.tooltip-width {
  width: 500px;
  top: -100px;
}
.activity-log-tooltip-width {
  width: 350px;
}
.comment-tooltip-width {
  width: 250px;
}
@media (max-width: 768px) {
  .tooltip-width {
    width: 300px;
  }
  .activity-log-tooltip-width {
    width: 300px;
  }
}
.vertical-alignment {
  vertical-align: top;
}
.header-wrapper,
.body-wrapper {
  height: fit-content;
}
// .onlySelected {
//   min-width: 1780px;
//   max-width: 2550px;
// }
// .onlyActivity {
//   min-width: 1785px;
//   max-width: 2550px;
// }
// .activeLogSelected {
//   min-width: 1830px;
//   max-width: 2550px;
// }
.mobile-full-width {
  width: calc(100% - 16px);
}
/* for dynamic Components */
.dynamicComp-enter-active,
.dynamicComp-leave-active {
  transition: opacity 0.5s;
}
.dynamicComp-enter-from,
.dynamicComp-leave-to {
  opacity: 0;
}
.whole-card {
  box-shadow: 0px 1px 2px #22283126;
  border-radius: 10px;
}
.card {
  @apply bg-white p-3 my-0 md:w-1/2 w-full z-1;
  box-shadow: 0px 1px 2px #22283126;
  border-radius: 10px;
}
.imagePopup-enter-active,
.imagePopup-leave-active {
  transition: opacity 0.5s;
}
.imagePopup-enter,
.imagePopup-leave-to {
  opacity: 0;
}
.verticle-line {
  @apply absolute h-full border-l-2 border-red-700 top-2 left-6;
  border-color: #f1f2f6;
}
.post-profile {
  @apply pr-3 py-3 -mt-3;
  z-index: 20;
}
.post-profile img {
  min-width: 56px;
}
.reaction {
  @apply my-2 flex justify-between text-gray-1700 mr-16;
}
.reaction img {
  @apply inline-block w-4 mr-4;
  fill: #8e8e8e;
}
.retweet {
  @apply inline-block w-4 mr-4;
  fill: #8e8e8e;
}
.inner-body {
  height: 60%;
}
.min-w-115 {
  min-width: 438px;
}
.min-w-117 {
  min-width: 550px;
}
table thead tr {
  border-bottom: 2px solid #f1f2f6 !important;
  // border-top: 2px solid #f1f2f6 !important;
}
table tbody tr {
  border-bottom: 1px solid #f1f2f6 !important;
}
table tr th {
  @apply px-6 pl-6 pb-1.5 py-2 whitespace-nowrap;
}
table tr td {
  @apply px-6 pl-6 py-3.5 text-base whitespace-nowrap;
}
.detailsWrapper {
  @apply p-0;
}
.detailsWrapper table tr td:first-child {
  @apply px-0 pr-0 whitespace-nowrap;
}
.leftWrapper {
  // padding: 0px;
  // min-width: 256px;
  // vertical-align: baseline;
}
// .middleWrapper {
//   min-width: 256px;
//   vertical-align: baseline;
//   padding-left: 30px !important;
//   padding-right: 0px !important;
// }
// .rightWrapper {
//   @apply pb-6;
//   min-width: 256px;
//   vertical-align: baseline;
//   padding-right: 0px !important;
// }
// .rightWrapperInner {
//   max-height: 550px;
// }
.right-vertical {
  width: 2px;
  height: 552px;
}
.comment {
  // max-width: 345px;
}
.status {
  @apply text-ash-primary;
}

.toggle-checkbox {
  @apply w-3 h-3 md:w-4 md:h-4;
  min-width: 1rem;
  border: 0px;
  top: 2px;
  left: 2px;
  transition: all 0.5s ease-in-out;
  background-color: #ffffff;
  &:checked {
    @apply right-0;
    left: 18px;
    transition: all 0.5s ease-in-out;
    background-color: #a22a2a;
  }
  &:checked + .toggle-label {
    @apply bg-white;
    transition: all 0.5s ease-in-out;
    background-color: #ffffff;
  }
}
.action-icon {
  @apply pl-3 md:pl-5;
}
.scroll {
  overflow-x: auto;
  overflow-y: auto;
  scrollbar-color: #ffe0e0 #ffe0e080; /* Firefox 64 */
  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: #ffe0e0;
    border-radius: 3px;
  }
  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #ffe0e0;
  }
}
.full-wrapper {
  overflow-y: hidden !important;
}
.inner-body-wrapper {
  overflow-x: hidden !important;
}
.promissory-toggle {
  min-width: 36px;
}
#tooltipconcern {
  top: -3px !important;
}
.toggle-button {
  min-width: 2.25rem;
}
.table-fadeIn-enter-active,
.table-fadeIn-leave-active {
  transition: opacity 0.5s ease 0.5s;
}
.table-fadeIn-enter-from,
.table-fadeIn-leave-to {
  opacity: 0;
}

// .table-fadeIn-enter-active,
// .table-fadeIn-leave-active {
//   max-height: 100%;
//   transition:
//     opacity 0.5s ease-in-out,
//     height 0.5s ease-in-out;
//     transition-delay: 500;
// }
// .table-fadeIn-enter,
// .table-fadeIn-leave-to {
//   opacity: 0;
//   max-height: 0%;
//   transition-delay: 500;
// }
// .table-FadeIn-enter-active,
// .table-FadeIn-leave-active {
//   max-height: 100%;
//   transition:
//     opacity 0.5s ease-in-out,
//     height 0.5s ease-in-out;
// }
// .table-FadeIn-enter,
// .table-FadeIn-leave-to {
//   opacity: 0;
//   max-height: 0%;
// }
// .table-FadeIn-enter-active {
//   transition-delay: 0.5s;
// }
.table-group-fadeIn-enter-active,
.table-group-fadeIn-leave-active {
  max-height: 100%;
  transition:
    opacity 0.5s ease-in-out,
    height 0.5s ease-in-out;
}
.table-group-fadeIn-enter,
.table-group-fadeIn-leave-to {
  opacity: 0;
  max-height: 0%;
}
.table-group-fadeIn-enter-active {
  transition-delay: 0.5s;
}
@media (max-width: 767px) {
  .toggle-checkbox {
    min-width: 12px;
  }
  .min-w-115 {
    min-width: 327px;
  }
  .inner-body-wrapper {
    overflow-x: auto !important;
  }
  .right-vertical {
    height: 528px;
  }
  .toggle-checkbox {
    top: 1px;
    left: 1px;
    &:checked {
      left: 11px;
    }
    &:checked + .toggle-label {
    }
  }
  .toggle-button {
    min-width: 1.5rem;
  }
}
.activity-td {
  padding: 0px !important;
}
.bg-selected-content {
  background-color: #ffe0e0;
}
.text-clamp {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.activity_log {
  border-bottom: none !important;
}
</style>
