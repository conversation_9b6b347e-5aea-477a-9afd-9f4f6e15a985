<template>
  <div class="relative h-full flex flex-col">
    <div class="flex-grow chart-height px-6 border-b-2 border-[#F1F2F6] pb-4">
      <ClientOnly>
        <VueApexCharts
          id="scatter-chart"
          ref="chart"
          width="100%"
          height="100%"
          :options="options"
          :series="series"
        />
      </ClientOnly>
    </div>
    <div
      id="legend"
      class="flex justify-center w-full flex-wrap px-3 py-5 legend-bottom-margin"
    >
      <button
        v-if="summaryComp !== 'SummaryAllFlagsTable'"
        :disabled="!enableClick"
        class="flex items-center gap-2 px-3 text-[#D63C3C] text-base font-semibold"
        @click="bakeToPrevSummaryComp"
      >
        <SharedIconArrowUpLong class="w-5 -rotate-90" />
        <span>Go back</span>
      </button>
      <div
        v-if="summaryComp !== 'SummaryAllFlagsTable'"
        class="w-px h-full bg-[#E3E3E3]"
      />
      <div
        v-for="(singleSeries, index) in series"
        :key="singleSeries.name"
        class="flex items-center space-x-2 px-4 font-semibold cursor-pointer"
      >
        <label
          class="h-5 relative cursor-pointer flex items-center space-x-2"
          :for="singleSeries.name"
          @click.stop="updateSummaryComp"
        >
          <div class="h-5 relative cursor-pointer">
            <input
              :id="singleSeries.name"
              type="checkbox"
              checked
              :value="singleSeries.name"
              class="appearance-none w-4 h-4 border-2 rounded-sm toggle-check-1"
              :style="{ '--bgColor': options?.colors?.[index] }"
              @click.stop="toggleSeries(singleSeries.name, index)"
            />
            <fa
              class="w-3.5 h-3.5 absolute left-px top-0.5 font-normal cursor-pointer opacity-0 check-1"
              :icon="['fas', 'check']"
            />
          </div>
          <span class="feeds_name">{{ singleSeries.name }}</span>
        </label>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core'
import type { ApexOptions } from 'apexcharts'
import { storeToRefs } from 'pinia'
import VueApexCharts from 'vue3-apexcharts'
import { useStore } from 'vuex'
import rhombus from '~/assets/img/chart/rhombus.svg'
import square from '~/assets/img/chart/square.svg'
import triangle from '~/assets/img/chart/triangle.svg'
import { useSummaryComp } from '~/composables/feeds/useSummaryComp'
import { useAlert } from '~/stores/alert'

const excludedGoBackComps = [
  'SummaryAllFeedsTable',
  'SummaryGroupsTable',
  'SummaryAllFlagsORTable',
]

// Store setup
const store = useAlert()
const vuexStore = useStore()
const tailwindBreakpoints = useBreakpoints(breakpointsTailwind)

// Reactive references from store
const { series, currentDateRange, summaryComp, startDate, endDate } =
  storeToRefs(store)
const { setSummaryComp } = useSummaryComp()

// Reactive data
const countClick = ref(0)
const legendName = ref<string | null>(null)
const gettime = ref(0)
const chart = ref<ApexCharts | null>(null)
const initialDate = ref('')
const endingDate = ref('')
const enableClick = ref(true)

// Computed properties
const isDesktop = tailwindBreakpoints.greater('md')
const formatDate = computed(() => vuexStore.state.system.formatDate)
const formatTime = computed(() => vuexStore.state.system.formatTime)
const apexChartTimeFormat = computed(() => {
  if (formatTime.value === 'HH:mm') {
    return 'HH:mm'
  } else if (formatTime.value === 'hh:mm aaa') {
    return 'hh:mm tt'
  } else if (formatTime.value === 'hh:mm aa') {
    return 'hh:mm TT'
  } else {
    return 'hh:mm tt'
  }
})

const dayDiff = computed(() => {
  const a = new Date(initialDate.value)
  const b = new Date(endingDate.value)
  return Math.abs(b.getTime() - a.getTime()) / (1000 * 60 * 60 * 24)
})

const bakeToPrevSummaryComp = () => {
  enableClick.value = false
  store.backToPreviousSummaryComponent({
    summaryComp: store.previousSummaryComp[0],
    // previousSummaryComp: store.state.alert.summaryComp,
  })
  if (summaryComp.value === 'SummaryAllFlagsTable') {
    setTimeout(() => {
      enableClick.value = true
    }, 1000)
  } else {
    setTimeout(() => {
      enableClick.value = true
    }, 200)
  }
}

// Chart options
const options = reactive<ApexOptions>({
  chart: {
    type: 'scatter',
    id: 'vuechart-scatter-chart',
    zoom: { enabled: false },
    animations: { enabled: false },
    toolbar: {
      show: !isDesktop.value,
      tools: {
        zoom: true,
        zoomin: true,
        zoomout: true,
        pan: true,
        download: true,
        reset:
          '<img src="data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMzIgMzIiIHhtbDpzcGFjZT0icHJlc2VydmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHN0cm9rZS1taXRlcmxpbWl0PSIyIj48cGF0aCBkPSJNMjAuODUyIDYuNzQ0YzMuMDc0IDEuNzU2IDUuMTUgNS4xMjYgNS4xNSA4Ljk4NiAwIDUuNjY0LTQuNDc0IDEwLjI3NS0xMCAxMC4yNzVzLTEwLTQuNjExLTEwLTEwLjI3NWMwLTQuNTY2IDIuOTA2LTguNDQ2IDYuOTMtOS43ODFhMSAxIDAgMSAwLS42MjktMS44OThjLTQuODEgMS41OTUtOC4zMDEgNi4yMjEtOC4zMDEgMTEuNjc5IDAgNi43ODIgNS4zODQgMTIuMjc1IDEyIDEyLjI3NXMxMi01LjQ5MyAxMi0xMi4yNzVjMC00LjQzMS0yLjMtOC4zMTQtNS43NC0xMC40NzJsMS4yMTEtLjMyNGExIDEgMCAwIDAtLjUxNy0xLjkzMmwtMy44NjQgMS4wMzVhMSAxIDAgMCAwLS43MDcgMS4yMjVsMS4wMzUgMy44NjRhMSAxIDAgMCAwIDEuOTMyLS41MThsLS41LTEuODY0WiIgZmlsbD0iI2EyMmEyYSIgY2xhc3M9ImZpbGwtMDAwMDAwIj48L3BhdGg+PC9zdmc+" width="20">',
      },
    },
  },
  colors: ['#3B82F6', '#F59E0B', '#10B981'],
  markers: { size: [10, 10, 10], strokeWidth: 0 },
  fill: {
    type: 'image',
    opacity: 1,
    image: { src: [rhombus, square, triangle], width: 16, height: 16 },
  },
  legend: {
    show: false,
    showForSingleSeries: true,
    fontSize: '20px',
    fontFamily: 'Roboto',
    fontWeight: 700,
    labels: { useSeriesColors: true },
    markers: { size: 16 },
    itemMargin: { horizontal: 20, vertical: 20 },
  },
  grid: { show: false, borderColor: '#C3B7B9' },
  xaxis: {
    type: 'datetime',
    min: new Date('01 Jan 2011').getTime(),
    max: new Date('01 Jan 2012').getTime(),
    tickPlacement: 'on',
    title: {
      text: 'Score',
      style: {
        color: '#707070',
        fontSize: '14px',
        fontFamily: 'Roboto',
        fontWeight: '600',
      },
      offsetY: 0,
    },
    tickAmount: 10,
    axisBorder: { show: true, color: '#525252', offsetX: 0, offsetY: 0 },
    labels: {
      show: true,
      style: {
        fontSize: isDesktop.value ? '16px' : '12px',
        fontFamily: 'Roboto',
        fontWeight: 'normal',
      },
      datetimeFormatter: {
        year: 'MMM, yyyy',
        month: 'MMM',
        day: 'ddd',
        hour: 'HH:mm',
      },
    },
    axisTicks: {
      show: true,
      borderType: 'solid',
      color: '#525252',
      height: 6,
      offsetX: 0, // -25
      offsetY: 0,
    },
  },
  yaxis: {
    show: true,
    title: {
      text: 'Response Time (Sec)',
      style: {
        color: '#707070',
        fontSize: '14px',
        fontFamily: 'Roboto',
        fontWeight: '600',
      },
      offsetX: 0,
    },
    axisBorder: { show: true, color: '#525252', offsetX: 0, offsetY: 0 },
    labels: {
      show: true,
      style: {
        fontSize: isDesktop.value ? '16px' : '12px',
        fontFamily: 'Roboto',
        fontWeight: 'normal',
      },
      // formatter(y) {
      //   return y.toFixed(0)
      // },
    },
    axisTicks: {
      show: true,
      color: '#525252',
      width: 6,
      offsetX: 0,
      offsetY: 0,
    },
  },
})
// Methods
const updateSummaryComp = (event: Event) => {
  const target = event.currentTarget as HTMLLabelElement
  const seriesName = target.getAttribute('for')
  const clickTime = new Date().getTime() - gettime.value
  countClick.value = countClick.value + 1

  setTimeout(() => {
    countClick.value = 0
    legendName.value = null
    gettime.value = 0
  }, 250)

  if (legendName.value === seriesName) {
    legendName.value = seriesName
    console.log('double click', summaryComp.value)
    if (countClick.value === 2 && clickTime < 600) {
      console.log('double click', summaryComp.value)
      if (summaryComp.value === 'SummaryAllFlagsTable') {
        store.setSummaryComponent({
          summaryComp: 'SummaryAllFlagsComplianceTable',
          previousSummaryComp: store.summaryComp,
        })
      } else if (summaryComp.value === 'SummaryAllFlagsComplianceTable') {
        store.setSummaryComponent({
          summaryComp: 'SummaryAllFlagsHumanResourcesTable',
          previousSummaryComp: store.summaryComp,
        })
      } else if (summaryComp.value === 'SummaryAllFlagsHumanResourcesTable') {
        store.setSummaryComponent({
          summaryComp: 'SummaryAllFlagsCorporateSecurityTable',
          previousSummaryComp: store.summaryComp,
        })
      }
    }
  } else {
    legendName.value = seriesName
  }
  gettime.value = new Date().getTime()
}

const toggleSeries = (name: string, index: number) => {
  const allLegends = document.querySelectorAll<HTMLInputElement>(
    "#legend input[type='checkbox']",
  )
  let count = 0

  allLegends.forEach((item) => {
    if (item.checked) {
      count = count + 1
    }
  })

  if (count >= 1) {
    chart.value?.toggleSeries(name)
  } else if (!allLegends[index].checked) {
    allLegends[index].checked = true
  }
}

const currentDateGraph = (value1: string, value2: string) => {
  if (!chart.value) return
  chart.value?.zoomX(new Date(value1).getTime(), new Date(value2).getTime())
}

const checkDateString = (data: string) => {
  if (data === 'Current') {
    initialDate.value = '01 August 2012'
    endingDate.value = '02 August 2012'
  } else if (data === '1 Month') {
    initialDate.value = '01 July 2012'
    endingDate.value = '31 July 2012'
  } else if (data === '6 Months') {
    initialDate.value = '01 February 2012'
    endingDate.value = '31 July 2012'
  } else if (data === 'YTD') {
    initialDate.value = '01 January 2012'
    endingDate.value = '31 June 2012'
  } else if (data === '1 Year') {
    initialDate.value = '01 January 2012'
    endingDate.value = '31 December 2012'
  } else if (data === '3 Years') {
    initialDate.value = '01 January 2011'
    endingDate.value = '31 December 2013'
  } else if (data === '5 Years') {
    initialDate.value = '01 January 2012'
    endingDate.value = '31 December 2016'
  } else if (data === 'Max') {
    initialDate.value = '01 January 2012'
    endingDate.value = new Date().toDateString()
  } else if (data === 'Date Range' && startDate.value && endDate.value) {
    initialDate.value = startDate.value
    endingDate.value = endDate.value
  }
  currentDateGraph(initialDate.value, endingDate.value)
}
const updateDateTimeFormat = () => {
  if (!chart.value) return
  chart.value?.updateOptions({
    xaxis: {
      labels: {
        datetimeFormatter: {
          year: `${dayDiff.value >= 1096 ? 'yyyy' : 'yyyy MMM'}`,
          month: `${dayDiff.value >= 1096 ? '' : 'MMM'}`,
          day: `${dayDiff.value <= 2 ? 'dd MMM yyyy' : 'dd'}`,
          hour: `${apexChartTimeFormat.value}`,
        },
        rotate: -45,
        minHeight: dayDiff.value <= 2 ? 78 : 40,
        rotateAlways: dayDiff.value <= 2,
      },
    },
  })
}

const updateDateFormat = () => {
  if (!chart.value) return
  chart.value?.updateOptions({
    tooltip: {
      enabled: true,
      x: { show: true, format: `${formatDate.value}` },
    },
  })
}

watch(currentDateRange, (data) => {
  checkDateString(data)
})

watch(summaryComp, async () => {
  await nextTick()
  checkDateString(currentDateRange.value)
})

watch(startDate, (data) => {
  checkDateString(currentDateRange.value)
})

watch(endDate, (data) => {
  checkDateString(currentDateRange.value)
})

watch(formatDate, (data) => {
  updateDateFormat()
})

watch(apexChartTimeFormat, (data) => {
  updateDateTimeFormat()
})

// Lifecycle
onMounted(async () => {
  await nextTick()
  setTimeout(() => {
    checkDateString(currentDateRange.value)
    updateDateTimeFormat()
  })
  updateDateFormat()
})
</script>

<style lang="scss" scoped>
$bgColor: var(--bgColor);
$color: var(--color);
.toggle-check-1 {
  border-color: $bgColor;
  &:checked {
    background-color: $bgColor;
  }
  &:checked + .check-1 {
    @apply text-white opacity-100;
  }
}
.feeds_name {
  color: #525252;
}
@media (min-height: 600px) and (max-height: 800px) {
  // .legend-bottom-margin {
  //   margin-bottom: 24px;
  // }
  .chart-height {
    height: 500px;
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .chart-height {
    height: 500px;
  }
}
@media (max-width: 767px) {
  .chart-height {
    height: 400px;
  }
}
</style>
