<template>
  <div class="relative h-full flex flex-col">
    <div class="flex-grow chart-height px-6 border-b-2 border-[#F1F2F6]">
      <ClientOnly>
        <VueApexCharts
          id="chart"
          ref="chart"
          type="area"
          width="100%"
          height="100%"
          :options="options"
          :series="series"
        />
      </ClientOnly>
    </div>
    <div
      id="legend"
      class="flex w-full flex-wrap pt-0 legend-bottom-margin mt-4"
      :class="
        summaryComp === 'SummaryIndividualSocialFeedDetails'
          ? 'justify-start md:justify-center p-6'
          : 'justify-center p-6'
      "
    >
      <button
        v-if="!excludedGoBackComps.includes(summaryComp)"
        :disabled="!enableClick"
        class="flex items-center gap-2 px-3 text-[#D63C3C] text-base font-semibold"
        @click="bakeToPrevSummaryComp"
      >
        <SharedIconArrowUpLong class="w-5 -rotate-90" />
        <span>Go back</span>
      </button>
      <div
        v-if="!excludedGoBackComps.includes(summaryComp)"
        class="w-px h-full bg-[#E3E3E3]"
      />
      <div
        v-for="(singleSeries, index) in series"
        :key="singleSeries.name"
        class="flex items-center space-x-2 font-bold cursor-pointer px-3"
        :class="
          summaryComp === 'SummaryIndividualSocialFeedDetails' ? 'pd-l' : ''
        "
      >
        <label
          class="h-5 relative cursor-pointer flex items-center space-x-2 w-full"
          :for="singleSeries.name"
          @click.stop="updateSummaryComp(singleSeries.name, index)"
        >
          <div class="h-5 relative cursor-pointer">
            <input
              :id="singleSeries.name"
              type="checkbox"
              checked
              :value="singleSeries.name"
              class="appearance-none w-4 h-4 border-2 rounded-sm toggle-check-1"
              :style="{ '--bgColor': options.stroke?.colors?.[index] }"
              @click.stop="toggleSeries(singleSeries.name, index)"
            />
            <fa
              class="w-3.5 h-3.5 absolute left-px top-0.5 font-normal cursor-pointer opacity-0 check-1"
              :icon="['fas', 'check']"
            />
          </div>
          <span
            class="feeds_name"
            :class="
              summaryComp === 'SummaryIndividualSocialFeedDetails'
                ? 'mobile-inner-width mobile-clamp'
                : ''
            "
            >{{ singleSeries.name }}</span
          >
        </label>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core'
import type { ApexOptions } from 'apexcharts'
import { storeToRefs } from 'pinia'
import VueApexCharts from 'vue3-apexcharts'
import { useStore } from 'vuex'
import { useSummaryComp } from '~/composables/feeds/useSummaryComp'
import { useAlert } from '~/stores/alert'

const store = useAlert()
const { $toast } = useNuxtApp()
const vuexStore = useStore()
const isDesktop = useBreakpoints(breakpointsTailwind).greater('md')
const { series, currentDateRange, summaryComp, startDate, endDate } =
  storeToRefs(store)
const { setSummaryComp } = useSummaryComp()

const chart = ref<ApexCharts | null>(null)
const countClick = ref(0)
const areaClick = ref(0)
const legendName = ref('')
const gettime = ref(0)
const initialDate = ref('')
const endingDate = ref('')
const enableClick = ref(true)

const excludedGoBackComps = [
  'SummaryAllFeedsTable',
  'SummaryGroupsTable',
  'SummaryAllFlagsORTable',
]

// Computed properties
const formatDate = computed(() => vuexStore.state.system.formatDate)
const formatTime = computed(() => vuexStore.state.system.formatTime)

const dayDiff = computed(() => {
  const a = new Date(initialDate.value)
  const b = new Date(endingDate.value)
  return Math.abs(b.getTime() - a.getTime()) / (1000 * 60 * 60 * 24)
})

const apexChartTimeFormat = computed(() => {
  if (formatTime.value === 'HH:mm') {
    return 'HH:mm'
  } else if (formatTime.value === 'hh:mm aaa') {
    return 'hh:mm tt'
  } else if (formatTime.value === 'hh:mm aa') {
    return 'hh:mm TT'
  } else {
    return 'hh:mm tt'
  }
})

const updateSummaryCompArea = (oldSummaryCompFeed: string) => {
  if (isDesktop.value) {
    areaClick.value = areaClick.value + 1
    setTimeout(() => {
      areaClick.value = 0
    }, 300)
    if (areaClick.value === 2) {
      if (
        oldSummaryCompFeed === 'All Feeds' &&
        summaryComp.value === 'SummaryAllFeedsTable'
      ) {
        store.setSummaryComponent({
          summaryComp: 'SummaryIndividualFeedsTable',
          previousSummaryComp: store.summaryComp,
        })
      } else if (
        (oldSummaryCompFeed === 'Social Media' ||
          oldSummaryCompFeed === 'Text' ||
          oldSummaryCompFeed === 'IM and Collaboration' ||
          oldSummaryCompFeed === 'Email' ||
          oldSummaryCompFeed === 'Websites' ||
          oldSummaryCompFeed === 'Voice') &&
        summaryComp.value === 'SummaryIndividualFeedsTable'
      ) {
        store.setSummaryComponent({
          summaryComp: 'SummaryIndividualSocialFeedsTable',
          previousSummaryComp: store.summaryComp,
        })
      } else if (
        (oldSummaryCompFeed === 'Twitter' ||
          oldSummaryCompFeed === 'Facebook	' ||
          oldSummaryCompFeed === 'Instagram' ||
          oldSummaryCompFeed === 'Linkedin') &&
        summaryComp.value === 'SummaryIndividualSocialFeedsTable'
      ) {
        store.setSummaryComponent({
          summaryComp: 'SummaryIndividualSocialFeedDetails',
          previousSummaryComp: store.summaryComp,
        })
      } else if (summaryComp.value === 'SummaryAllFlagsORTable') {
        store.setSummaryComponent({
          summaryComp: 'SummaryFlagPersonsORTable',
          previousSummaryComp: store.summaryComp,
        })
      }
    }
  } else if (!isDesktop.value) {
    areaClick.value = areaClick.value + 1
    setTimeout(() => {
      areaClick.value = 0
    }, 300)
    if (areaClick.value === 4) {
      if (
        oldSummaryCompFeed === 'All Feeds' &&
        summaryComp.value === 'SummaryAllFeedsTable'
      ) {
        store.setSummaryComponent({
          summaryComp: 'SummaryIndividualFeedsTable',
          previousSummaryComp: store.summaryComp,
        })
      } else if (
        (oldSummaryCompFeed === 'Social Media' ||
          oldSummaryCompFeed === 'Text' ||
          oldSummaryCompFeed === 'IM and Collaboration' ||
          oldSummaryCompFeed === 'Email' ||
          oldSummaryCompFeed === 'Websites' ||
          oldSummaryCompFeed === 'Voice') &&
        summaryComp.value === 'SummaryIndividualFeedsTable'
      ) {
        store.setSummaryComponent({
          summaryComp: 'SummaryIndividualSocialFeedsTable',
          previousSummaryComp: store.summaryComp,
        })
      } else if (
        (oldSummaryCompFeed === 'Twitter' ||
          oldSummaryCompFeed === 'Facebook	' ||
          oldSummaryCompFeed === 'Instagram' ||
          oldSummaryCompFeed === 'Linkedin') &&
        summaryComp.value === 'SummaryIndividualSocialFeedsTable'
      ) {
        store.setSummaryComponent({
          summaryComp: 'SummaryIndividualSocialFeedDetails',
          previousSummaryComp: store.summaryComp,
        })
      } else if (summaryComp.value === 'SummaryAllFlagsORTable') {
        store.setSummaryComponent({
          summaryComp: 'SummaryFlagPersonsORTable',
          previousSummaryComp: store.summaryComp,
        })
      }
    }
  }
}

const updateSummaryComp = (name: string, index: number) => {
  const clickTime = new Date().getTime() - gettime.value
  countClick.value = countClick.value + 1
  setTimeout(() => {
    countClick.value = 0
    legendName.value = ''
    gettime.value = 0
  }, 250)
  if (legendName.value === name) {
    legendName.value = name
    if (countClick.value === 2 && clickTime < 600) {
      if (summaryComp.value === 'SummaryAllFeedsTable') {
        store.setSummaryComponent({
          summaryComp: 'SummaryIndividualFeedsTable',
          previousSummaryComp: store.summaryComp,
        })
      } else if (summaryComp.value === 'SummaryIndividualFeedsTable') {
        store.setSummaryComponent({
          summaryComp: 'SummaryIndividualSocialFeedsTable',
          previousSummaryComp: store.summaryComp,
        })
      } else if (summaryComp.value === 'SummaryIndividualSocialFeedsTable') {
        store.setSummaryComponent({
          summaryComp: 'SummaryIndividualSocialFeedDetails',
          previousSummaryComp: store.summaryComp,
        })
      } else if (summaryComp.value === 'SummaryAllFlagsORTable') {
        store.setSummaryComponent({
          summaryComp: 'SummaryFlagPersonsORTable',
          previousSummaryComp: store.summaryComp,
        })
      }
    }
  } else {
    legendName.value = name
  }
  gettime.value = new Date().getTime()
}

const bakeToPrevSummaryComp = () => {
  enableClick.value = false
  store.backToPreviousSummaryComponent({
    summaryComp: store.previousSummaryComp[0],
    // previousSummaryComp: store.state.alert.summaryComp,
  })
  if (
    summaryComp.value === 'SummaryAllFeedsTable' ||
    summaryComp.value === 'SummaryAllFlagsORTable'
  ) {
    setTimeout(() => {
      enableClick.value = true
    }, 1000)
  } else {
    setTimeout(() => {
      enableClick.value = true
    }, 200)
  }
}

// Chart options
const options = ref<ApexOptions>({
  chart: {
    type: 'area',
    stacked: true,
    toolbar: {
      show: !isDesktop.value,
      tools: {
        zoom: true,
        zoomin: true,
        zoomout: true,
        pan: '<img src="data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" width="20">',
        download: false,
        reset:
          '<img src="data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMzIgMzIiIHhtbDpzcGFjZT0icHJlc2VydmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHN0cm9rZS1taXRlcmxpbWl0PSIyIj48cGF0aCBkPSJNMjAuODUyIDYuNzQ0YzMuMDc0IDEuNzU2IDUuMTUgNS4xMjYgNS4xNSA4Ljk4NiAwIDUuNjY0LTQuNDc0IDEwLjI3NS0xMCAxMC4yNzVzLTEwLTQuNjExLTEwLTEwLjI3NWMwLTQuNTY2IDIuOTA2LTguNDQ2IDYuOTMtOS43ODFhMSAxIDAgMSAwLS42MjktMS44OThjLTQuODEgMS41OTUtOC4zMDEgNi4yMjEtOC4zMDEgMTEuNjc5IDAgNi43ODIgNS4zODQgMTIuMjc1IDEyIDEyLjI3NXMxMi01LjQ5MyAxMi0xMi4yNzVjMC00LjQzMS0yLjMtOC4zMTQtNS43NC0xMC40NzJsMS4yMTEtLjMyNGExIDEgMCAwIDAtLjUxNy0xLjkzMmwtMy44NjQgMS4wMzVhMSAxIDAgMCAwLS43MDcgMS4yMjVsMS4wMzUgMy44NjRhMSAxIDAgMCAwIDEuOTMyLS41MThsLS41LTEuODY0WiIgZmlsbD0iI2EyMmEyYSIgY2xhc3M9ImZpbGwtMDAwMDAwIj48L3BhdGg+PC9zdmc+" width="20">',
      },
    },
    zoom: {
      enabled: true,
      autoScaleYaxis: true,
    },
    events: {
      click: (event, chartContext, config) => {
        if (config.seriesIndex >= 0) {
          updateSummaryCompArea(config.globals.seriesNames[config.seriesIndex])
        }
      },
    },
  },
  tooltip: {
    enabled: true,
    x: {
      show: true,
      // format: 'MMMM dd, yyyy',
    },
  },
  grid: {
    show: false,
    borderColor: '#C3B7B9',
  },
  colors: [
    '#FFD3D3',
    '#9CACA7',
    '#A2C58E',
    '#86C2B4',
    '#A9CBB3',
    '#D9D1C3',
    '#D0E1FD',
    '#7EC3DC',
  ],
  dataLabels: {
    enabled: false,
  },
  stroke: {
    show: true,
    curve: 'smooth',
    lineCap: 'butt',
    width: 1.5,
    colors: [
      '#D63C3C',
      '#3B82F6',
      '#F59E0B',
      '#10B981',
      '#14A7B8',
      '#FACC15',
      '#6D28D9',
      '#E05252',
    ],
  },
  fill: {
    type: 'gradient',
    gradient: {
      opacityFrom: 1,
      opacityTo: 1,
      stops: [100, 100],
    },
  },
  legend: {
    show: false,
    showForSingleSeries: true,
    position: 'bottom',
    horizontalAlign: 'center',
    fontSize: '16px',
    fontWeight: 600,
    labels: {
      useSeriesColors: true,
    },
    markers: {
      size: 10,
    },
    itemMargin: {
      horizontal: 10,
      vertical: 15,
    },
    onItemHover: {
      highlightDataSeries: true,
    },
  },
  annotations: {
    xaxis: [
      {
        x: new Date('01 Jan 2013').getTime(),
        // borderColor: '#999',
        // yAxisIndex: 0,
        // label: {
        //   show: true,
        //   text: 'Rally',
        //   style: {
        //     color: '#fff',
        //     background: '#775DD0',
        //   },
        // },
      },
    ],
  },
  xaxis: {
    type: 'datetime',
    min: new Date('01 Jan 2011').getTime(),
    max: new Date('01 Jan 2012').getTime(),
    tickPlacement: 'on',
    axisBorder: {
      show: true,
      color: '#525252',
      offsetX: 0,
      offsetY: 0,
    },
    labels: {
      show: true,
      style: {
        fontSize: isDesktop.value ? '16px' : '12px',
        fontFamily: 'Roboto',
        fontWeight: 'normal',
      },
      datetimeFormatter: {
        year: 'MMM, yyyy',
        month: 'MMM',
        day: 'ddd',
        hour: 'HH:mm',
      },
    },
    axisTicks: {
      show: true,
      borderType: 'solid',
      color: '#525252',
      height: 6,
      offsetX: 0, // -25
      offsetY: 0,
    },
  },
  yaxis: {
    show: true,
    min: 0,
    // title: {
    //   show: false,
    //   text: 'Open Alerts',
    //   style: {
    //     color: '#505050',
    //     fontSize: '18px',
    //     fontFamily: 'Roboto, sans-serif',
    //     fontWeight: 'bold',
    //   },
    //   offsetX: 0,
    // },
    axisBorder: {
      show: true,
      color: '#525252',
      offsetX: 0,
      offsetY: 0,
    },
    labels: {
      show: true,
      style: {
        fontSize: isDesktop.value ? '16px' : '12px',
        fontFamily: 'Roboto',
        fontWeight: 'normal',
      },
      // formatter(y) {
      //   return y.toFixed(0)
      // },
    },
    axisTicks: {
      show: true,
      color: '#525252',
      width: 6,
      offsetX: 0,
      offsetY: 0,
    },
  },
})

// Methods
const zoomIn = (initial: string, ending: string) => {
  const a = new Date(initial)
  const b = new Date(ending)
  const timeDiff = Math.abs(b.getTime() - a.getTime()) / (1000 * 60 * 60 * 24)

  if (timeDiff <= 1) {
    $toast('clear')
    $toast('success', {
      message: 'Max Zoomed',
      className: 'toasted-bg-archive',
    })
  } else if (timeDiff > 1 && timeDiff < 10) {
    a.setDate(a.getDate() + 1)
  } else if (timeDiff >= 10 && timeDiff < 20) {
    a.setDate(a.getDate() + 3)
  } else if (timeDiff >= 20 && timeDiff < 100) {
    a.setDate(a.getDate() + Math.floor(timeDiff / 10))
  } else if (timeDiff >= 100) {
    a.setDate(a.getDate() + Math.floor(timeDiff / 30))
  }

  initialDate.value = a.toDateString()
  endingDate.value = b.toDateString()
  currentDateGraph(initialDate.value, endingDate.value)
}

const zoomOut = (initial: string, ending: string) => {
  const a = new Date(initial)
  const b = new Date(ending)
  const timeDiff = Math.abs(b.getTime() - a.getTime()) / (1000 * 60 * 60 * 24)

  if (timeDiff >= 1 && timeDiff < 10) {
    a.setDate(a.getDate() - 1)
  } else if (timeDiff >= 10 && timeDiff < 20) {
    a.setDate(a.getDate() - 3)
  } else if (timeDiff >= 20 && timeDiff < 100) {
    a.setDate(a.getDate() - Math.floor(timeDiff / 10))
  } else if (timeDiff >= 100) {
    a.setDate(a.getDate() - Math.floor(timeDiff / 30))
  }
  initialDate.value = a.toDateString()
  endingDate.value = b.toDateString()
  currentDateGraph(initialDate.value, endingDate.value)
}

const chartZoomByScroll = (event: WheelEvent) => {
  if (event.deltaY < 0) {
    zoomIn(initialDate.value, endingDate.value)
  } else {
    zoomOut(initialDate.value, endingDate.value)
  }
  event.preventDefault()
}

const addScroll = () => {
  const container = document.getElementById('chart')
  if (container) {
    container.addEventListener('wheel', chartZoomByScroll)
  }
}

const updateDateTimeFormat = () => {
  if (!chart.value) return
  chart.value?.updateOptions({
    xaxis: {
      labels: {
        datetimeFormatter: {
          year: `${dayDiff.value >= 1096 ? 'yyyy' : 'yyyy MMM'}`,
          month: `${dayDiff.value >= 1096 ? '' : 'MMM'}`,
          day: `${dayDiff.value <= 2 ? 'dd MMM yyyy' : 'dd'}`,
          hour: `${apexChartTimeFormat.value}`,
        },
        rotate: -45,
        minHeight: dayDiff.value <= 2 ? 78 : 40,
        rotateAlways: dayDiff.value <= 2,
      },
    },
  })
}

const updateDateFormat = () => {
  if (!chart.value) return
  chart.value?.updateOptions({
    tooltip: {
      enabled: true,
      x: {
        show: true,
        format: `${formatDate.value}`,
      },
    },
  })
}

const toggleSeries = (name: string, index: number) => {
  const allLegends = document.querySelectorAll<HTMLInputElement>(
    "#legend input[type='checkbox']",
  )
  let count = 0

  allLegends.forEach((item) => {
    if (item.checked) {
      count++
    }
  })

  if (count >= 1) {
    chart.value?.toggleSeries(name)
  } else if (!allLegends[index].checked) {
    allLegends[index].checked = true
  }
}

const currentDateGraph = (value1: string, value2: string) => {
  if (!chart.value) return
  chart.value?.zoomX(new Date(value1).getTime(), new Date(value2).getTime())
}

const checkDateString = (data: string) => {
  if (data === 'Current') {
    initialDate.value = '01 August 2012'
    endingDate.value = '02 August 2012'
  } else if (data === '1 Month') {
    initialDate.value = '01 July 2012'
    endingDate.value = '31 July 2012'
  } else if (data === '6 Months') {
    initialDate.value = '01 February 2012'
    endingDate.value = '31 July 2012'
  } else if (data === 'YTD') {
    initialDate.value = '01 January 2012'
    endingDate.value = '31 June 2012'
  } else if (data === '1 Year') {
    initialDate.value = '01 January 2012'
    endingDate.value = '31 December 2012'
  } else if (data === '3 Years') {
    initialDate.value = '01 January 2011'
    endingDate.value = '31 December 2013'
  } else if (data === '5 Years') {
    initialDate.value = '01 January 2012'
    endingDate.value = '31 December 2016'
  } else if (data === 'Max') {
    initialDate.value = '01 January 2012'
    endingDate.value = new Date().toDateString()
  } else if (data === 'Date Range' && startDate.value && endDate.value) {
    initialDate.value = startDate.value
    endingDate.value = endDate.value
  }
  currentDateGraph(initialDate.value, endingDate.value)
}

// Watchers
watch(currentDateRange, (data) => {
  checkDateString(data)
})

watch(summaryComp, async () => {
  await nextTick()
  checkDateString(currentDateRange.value)
})

watch(startDate, (data) => {
  checkDateString(currentDateRange.value)
})

watch(endDate, (data) => {
  checkDateString(currentDateRange.value)
})

watch(formatDate, (data) => {
  updateDateFormat()
})

watch(apexChartTimeFormat, (data) => {
  updateDateTimeFormat()
})

watch(dayDiff, (data) => {
  updateDateTimeFormat()
})

// Lifecycle
onMounted(async () => {
  await nextTick()
  setTimeout(() => {
    checkDateString(currentDateRange.value)
    updateDateTimeFormat()
  })
  updateDateFormat()
  addScroll()
})
</script>

<style lang="scss" scoped>
$bgColor: var(--bgColor);
$color: var(--color);

.toggle-check-1 {
  border-color: $bgColor;
  &:checked {
    background-color: $bgColor;
  }

  &:checked + .check-1 {
    @apply text-white opacity-100;
  }
}

.feeds_name {
  color: #525252;
  font-size: 16px;
  font-weight: 600;
}

@media (min-height: 600px) and (max-height: 800px) {
  .chart-height {
    height: 500px;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .chart-height {
    height: 500px;
  }
}

@media (max-width: 767px) {
  .mobile-inner-width {
    width: calc(100% - 30px);
  }

  .chart-height {
    height: 400px;
  }

  .mobile-clamp {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .pd-l {
    @apply pl-11 w-full;
  }
}
</style>
