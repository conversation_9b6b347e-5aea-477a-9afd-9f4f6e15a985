<template>
  <div class="text-[#333333] flex flex-col flex-grow">
    <div class="flex flex-col flex-grow">
      <div class="flex flex-col space-y-4">
        <div>Send To</div>
        <div ref="dropdown" class="dropdown-width text-left">
          <InputsSelectInput
            v-model="member"
            class="w-[265px] h-9"
            class-style-name="searchPageScrollStyle searchPageScrollWidth target-select-input-alert text-base"
            :options="members"
            place-holder="Select a send to"
            :place-holder-disabled="true"
            :color="member === 'Select a send to' ? '#707070' : '#FFFFFF'"
            :background="member === 'Select a send to' ? '#F1F2F6' : '#EF8914'"
            caret-bg="#F1F2F6"
            :caret-color="member === 'Select a send to' ? '#EF8914' : '#FFFFFF'"
            scroll-color="#9e7912"
          ></InputsSelectInput>
        </div>
      </div>
      <p class="!mt-3.5 text-[#525252]">Add Note</p>
      <textarea
        id="addNote"
        v-model="trueSendNote"
        name="addNote"
        cols="30"
        rows="3"
        class="w-full mt-3 mb-6 p-4 resize-none rounded-lg placeholder-[#707070] text-[#333333] bg-[#F1F2F6] outline-none border-none"
        placeholder="Type here..."
      >
      </textarea>
    </div>
    <div :class="alertContextChecked ? '' : 'mb-3.5'">
      <div class="flex flex-row justify-end md:space-x-5 space-x-2">
        <button
          class="focus:outline-none w-[120px] h-[39px] text-[#FFFFFF] rounded-full outline-none font-semibold md:text-base text-sm"
          :class="
            member === 'Select a send to' && !trueSendNote
              ? 'bg-[#C2C2C2] cursor-not-allowed'
              : 'bg-[#EF8914]'
          "
          @click="
            (member === 'Select a send to' && !trueSendNote
              ? ''
              : setDisposition(''),
            emit('unselect-send-directly', false))
          "
        >
          <span>Send</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAlert } from '~/stores/alert'

const props = defineProps({
  alertContextChecked: {
    type: Boolean,
    default: false,
  },
})

const store = useAlert()
const { setDisposition } = store

const emit = defineEmits(['unselect-send-directly'])
const members = [
  { id: 0, text: 'George Jones', value: 0 },
  { id: 1, text: 'Bob Rahman', value: 1 },
  { id: 2, text: 'Tommy Thompson', value: 2 },
  { id: 3, text: 'James Jamison', value: 3 },
]
const member = ref('Select a send to')
const trueSendNote = ref('')
</script>

<style lang="scss" scoped>
.dropdown-btn {
  //direction: ltr;
  background: red;
  line-height: 2.15rem !important;
  @apply text-ash-default bg-white h-10 w-60 px-3 text-lg flex justify-between items-center focus:outline-none;
}
.dropdown-width {
  width: 240px;
}
</style>
