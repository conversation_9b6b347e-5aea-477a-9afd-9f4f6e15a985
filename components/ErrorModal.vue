<template>
  <div class="relative w-full h-screen">
    <div
      class="fixed inset-0 transition-opacity"
      @click.stop="showErrorModal = false"
    >
      <div class="absolute inset-0 bg-gray-600 opacity-75"></div>
    </div>
    <div
      class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-9999 bg-dark-100 shadow-xl rounded-lg flex flex-col xl:w-2/5 lg:w-2/5 md:w-3/5 sm:w-4/6 w-[90%] p-5 h-[370px] md:h-[400px] lg:h-[420px]"
    >
      <ClientOnly>
        <fa
          class="xl:text-2xl md:text-xl text-2xl font-normal cursor-pointer text-orange-dark ml-auto"
          :icon="['fas', 'times']"
          @click.stop="showErrorModal = false"
        />
      </ClientOnly>
      <div class="w-full h-full pb-4 overflow-y-auto mt-8">
        <h2 class="text-orange-dark font-bold text-2xl">Errors</h2>
        <template v-if="ownerAccount && ownerAccount.length > 0">
          <ul
            class="text-white text-lg w-full"
            v-for="(account, index) in ownerAccount"
            :key="account.id"
          >
            <li class="flex space-x-4 mt-8 w-full">
              <span class="font-bold">{{ index + 1 }}.</span>
              <div class="flex flex-col space-y-4 w-full">
                <div class="flex space-x-4">
                  <p class="font-bold">{{ account.provider }}</p>
                  <p class="break-all">({{ account.username }})</p>
                </div>
                <div>
                  <h2 class="font-bold text-xl">Message:</h2>
                  <p>{{ account.status }}</p>
                </div>
                <button
                  class="w-40 h-10 bg-orange-dark text-white rounded-full mx-auto font-semibold"
                  @click="
                    authorize(
                      account.provider !== 'Instagram'
                        ? account.provider.toLowerCase()
                        : account.type === 'Business'
                          ? 'facebook'
                          : account.provider.toLowerCase(),
                    )
                  "
                >
                  Reconnect
                </button>
              </div>
            </li>
          </ul>
        </template>
        <h2
          v-if="guestAccount && guestAccount.length > 0"
          class="text-orange-dark text-xl font-bold mt-8"
        >
          Guest User Accounts
        </h2>
        <template v-if="guestAccount && guestAccount.length > 0">
          <ul
            class="text-white text-lg w-full"
            v-for="(account, index) in guestAccount"
            :key="account.id"
          >
            <li class="flex space-x-4 mt-4 w-full">
              <span class="font-bold">{{ index + 1 }}.</span>
              <div class="flex flex-col space-y-4 w-full">
                <div class="flex space-x-4">
                  <p class="break-all">{{ account.name }}</p>
                  <p class="font-bold">({{ account.provider }})</p>
                </div>
                <div>
                  <h2 class="font-bold text-xl">Message:</h2>
                  <p>{{ account.status }}</p>
                </div>
                <!-- <button
                  class="w-40 h-10 bg-orange-dark text-white rounded-full mx-auto font-semibold"
                  @click="
                    authorize(
                      account.provider !== 'Instagram'
                        ? account.provider.toLowerCase()
                        : account.type === 'Business'
                          ? 'facebook'
                          : account.provider.toLowerCase(),
                    )
                  "
                >
                  Reconnect
                </button> -->
                <div
                  key="2"
                  class="send-guest-email-wrapper w-full flex flex-col transition-all duration-1000 md:mt-6 mt-6 pr-4"
                >
                  <transition name="page" mode="out-in">
                    <div
                      v-if="!seletedId || seletedId !== account.id"
                      class="flex justify-center items-center"
                    >
                      <button
                        class="text-white text-xl mb-2 bg-orange-dark px-3 py-2 rounded-full font-semibold"
                        @click.stop="getSeletedId(account.id)"
                      >
                        Send Access Request
                      </button>
                    </div>
                    <!-- </transition>
                  <transition name="fadeIn"> -->
                    <div v-else-if="seletedId === account.id">
                      <input
                        v-model="guestEmail"
                        type="text"
                        placeholder="Enter email address"
                        autocomplete="off"
                        class="w-full rounded-full py-2 px-2.5 outline-none focus:outline-none bg-white text-gray-1200 placeholder-gray-1200 placeholder-opacity-50 text-center align-start"
                        :disabled="emailSendProcess"
                        @keyup="v$.guestEmail.$touch()"
                        @input="checkEmailIsVerified"
                      />
                      <template v-if="v$.guestEmail.$error">
                        <span
                          v-if="v$.guestEmail.email.$invalid"
                          class="text-red-400 text-xs mt-0 pl-2 w-full block text-center"
                        >
                          The Email is Invalid
                        </span>
                        <span
                          v-if="v$.guestEmail.required.$invalid"
                          class="text-red-400 text-xs mt-0 pl-2 w-full block text-center"
                        >
                          The field is required
                        </span>
                      </template>
                      <transition name="fadeIn">
                        <div v-if="emailVerified" class="text-center mt-4">
                          <button
                            type="submit"
                            class="w-44 py-1.5 text-white bg-orange-dark rounded-full border-none outline-none font-bold text-base setup relative"
                            :disabled="emailSendProcess"
                            @click="sendGuestEmail()"
                          >
                            <div
                              class="rounded-full relative flex items-center justify-around"
                            >
                              <span>Send</span>
                              <ClientOnly>
                                <fa
                                  v-if="emailSendProcess"
                                  class="absolute right-5 text-white font-bold animate-spin"
                                  :icon="['fas', 'spinner']"
                                />
                              </ClientOnly>
                            </div>
                          </button>
                        </div>
                      </transition>
                    </div>
                  </transition>
                </div>
              </div>
            </li>
          </ul>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useNuxtApp } from 'nuxt/app'
import { useStore } from 'vuex'
import { useVuelidate } from '@vuelidate/core'
import { required, email } from '@vuelidate/validators'
import { SEND_ACCESS_REQUEST } from '~/constants/urls'

const store = useStore()
const ownerAccount = ref([])
const guestAccount = ref([])
const allFeeds = computed(() => store.state.socialFeed.socialFeeds)
const {
  setAuthCookies,
  loggedIn,
  tokenCookie,
  userInfo,
  notifications,
  setAccountErrorsCookies,
  showErrorModal,
} = useAuth()
const guestEmail = ref('')
const rules = {
  guestEmail: {
    required,
    email,
  },
}
const v$ = useVuelidate(rules, { guestEmail })
onMounted(() => {
  if (allFeeds.value) {
    allFeeds.value.forEach((allFeed) => {
      if (allFeed.isOwner) {
        notifications.value.forEach((notification) => {
          if (allFeed.id === notification.id) {
            ownerAccount.value.push(allFeed)
          }
        })
      } else {
        notifications.value.forEach((notification) => {
          if (allFeed.id === notification.id) {
            guestAccount.value.push(allFeed)
          }
        })
      }
    })
  }
})
watch(
  () => allFeeds.value,
  (data) => {
    if (data) {
      data.forEach((allFeed) => {
        if (allFeed.isOwner) {
          notifications.value.forEach((notification) => {
            if (allFeed.id === notification.id) {
              ownerAccount.value.push(allFeed)
            }
          })
        } else {
          notifications.value.forEach((notification) => {
            if (allFeed.id === notification.id) {
              guestAccount.value.push(allFeed)
            }
          })
        }
      })
    }
  },
)
const nuxtApp = useNuxtApp()
const { fetch } = useFetched()
const authorize = async (provider) => {
  nuxtApp.$social.redirect(provider)
}
const emailSendProcess = ref(false)
const emailVerified = ref(false)
const checkEmailIsVerified = () => {
  if (!v$.value.$invalid) {
    emailVerified.value = true
  } else {
    emailVerified.value = false
  }
}
const sendGuestEmail = async () => {
  v$.value.$touch()
  nuxtApp.$toast('clear')
  if (!v$.value.$invalid) {
    try {
      emailSendProcess.value = true
      const res = await fetch(SEND_ACCESS_REQUEST, {
        method: 'POST',
        body: { email: guestEmail.value },
      })
      if (res.success) {
        nuxtApp.$toast('success', {
          message: res.message,
          className: 'toasted-bg-archive',
        })
        guestEmail.value = ''
        v$.value.$reset()
        seletedId.value = ''
        emailVerified.value = false
      } else {
        nuxtApp.$toast('error', {
          message: res.message,
          className: 'toasted-bg-alert',
        })
      }
      emailSendProcess.value = false
    } catch (error) {
      // eslint-disable-next-line no-console
      nuxtApp.$toast('error', {
        message: error,
        className: 'toasted-bg-alert',
      })
    }
  }
}
const seletedId = ref('')
const getSeletedId = (id) => {
  if (!seletedId.value) {
    seletedId.value = id
  } else if (seletedId.value !== id) {
    seletedId.value = id
    guestEmail.value = ''
    v$.value.$reset()
    emailVerified.value = false
  } else {
    seletedId.value = ''
    guestEmail.value = ''
    v$.value.$reset()
    emailVerified.value = false
  }
}
</script>
