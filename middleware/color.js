import { useStore } from 'vuex'

export default defineNuxtRouteMiddleware((to, from) => {
  const store = useStore()
  let color = 'dfgdf'
  // If "source" exists in the route name, set color to '#4A71D4'
  if (to.name?.includes('source')) {
    color = '#4A71D4'
  } else {
    switch (to.name) {
      case 'home':
        color = '#e4801d'
        break
      case 'archive':
        color = '#8db230'
        break
      case 'search':
        color = '#7d80bd'
        break
      case 'pricing-calculate':
        color = '#a22a2a'
        break
      case 'agent-book':
        color = '#5F9FC7'
        break
      case 'alert':
      case 'setting-archive':
      case 'setting-alert':
      case 'setting-hub':
        color = '#D63C3C'
        break
      case 'settings-services':
      case 'settings-organize':
      case 'settings-system':
      case 'settings-account':
      case 'help':
        color = '#e0ad1f'
        break
      default:
        color = '#e4801d'
        break
    }
  }
  store.commit('SET_GLOBAL_COLOR_PANEL', { backgroundColor: color })
})
