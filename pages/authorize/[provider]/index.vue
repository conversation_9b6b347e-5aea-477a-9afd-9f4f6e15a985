<template>
  <section
    class="absolute top-0 flex flex-col justify-center items-center h-full w-full z-9999"
    :class="downloadLoader ? '' : 'bg-ash-dark'"
  >
    <!-- <div class="loader">
      <span style="--i: 1"></span>
      <span style="--i: 2"></span>
      <span style="--i: 3"></span>
      <span style="--i: 4"></span>
      <span style="--i: 5"></span>
      <span style="--i: 6"></span>
      <span style="--i: 7"></span>
      <span style="--i: 8"></span>
      <span style="--i: 9"></span>
      <span style="--i: 10"></span>
      <span style="--i: 11"></span>
      <span style="--i: 12"></span>
      <span style="--i: 13"></span>
      <span style="--i: 14"></span>
      <span style="--i: 15"></span>
      <span style="--i: 16"></span>
      <span style="--i: 17"></span>
      <span style="--i: 18"></span>
      <span style="--i: 19"></span>
      <span style="--i: 20"></span>
    </div> -->
    <div
      :id="items[Math.floor(Math.random() * items.length)]"
      class="text-white text-lg loaders"
    ></div>
    <SharpArchiveSmallLogo></SharpArchiveSmallLogo>
    <p class="text-white text-xl font-medium">
      Connecting your account…We’ll be ready in just a moment.
    </p>
  </section>
</template>

<script>
import { mapState } from 'vuex'
import Logo from '~/components/SharpArchiveSmallLogo.vue'
import { useAuth } from '~/composables/useAuth'
import { useFetched } from '~/composables/useFetched'
import { SOCIAL_AUTH, TEXT_AUTH } from '~/constants/urls'
export default {
  name: 'AuthorizeProvider',
  components: {
    Logo,
  },
  // auth: false,
  setup() {
    definePageMeta({
      layout: 'guest',
    })
    const nuxtApp = useNuxtApp()
    const { fetch } = useFetched()
    const { tokenCookie, loggedIn, userInfo } = useAuth()
    const $config = useRuntimeConfig()
    useHead(() => ({
      script: [
        {
          src: `${$config.public.siteUrl}/loader/attachedRotation.js`,
          body: true,
        },
        {
          src: `${$config.public.siteUrl}/loader/blinking.js`,
          body: true,
        },
        // {
        //   src: `${$config.public.siteUrl}/loader/elementRotationInner.js`,
        //   body: true,
        // },
        {
          src: `${$config.public.siteUrl}/loader/elementRotationOuter.js`,
          body: true,
        },
        {
          src: `${$config.public.siteUrl}/loader/polygon.js`,
          body: true,
        },
        {
          src: `${$config.public.siteUrl}/loader/rotation.js`,
          body: true,
        },
      ],
    }))
    return { nuxtApp, fetch, tokenCookie, loggedIn }
  },
  data() {
    return {
      items: [
        'attachedRotation',
        'blinking',
        // 'elementRotationInner',
        'elementRotationOuter',
        'polygon',
        'rotation',
      ],
      provider:
        this.$route.params.provider === 'leadconnector'
          ? 'gohighlevel'
          : this.$route.params.provider,
      code: this.$route.query.code,
      state: this.$route.query.state,
      error: this.$route.query.error,
      errorMessage: this.$route.query.error_description,
      responseData: null,
    }
  },
  computed: {
    ...mapState('archive', ['downloadLoader']),
    // loggedIn() {
    //   return this.$store.state.auth.loggedIn
    // },
    user() {
      return this.$store.state.auth.user
    },
  },
  async mounted() {
    const provider =
      this.$route.params.provider === 'leadconnector'
        ? 'gohighlevel'
        : this.$route.params.provider
    // get if success
    if (this.$route.query.error || this.$route.query.denied) {
      this.nuxtApp.$toast('error', {
        message: `Feed is not archived, because you just denied the request.`,
        className: 'toasted-bg-alert',
      })
      this.redirect(this.loggedIn, provider)
    } else {
      this.nuxtApp.$toast('clear')
      if (provider === 'ringcentral' || provider === 'gohighlevel') {
        this.callAuthApi(TEXT_AUTH, provider)
      } else {
        this.callAuthApi(SOCIAL_AUTH, provider)
      }
    }
  },
  methods: {
    async callAuthApi(type, provider) {
      try {
        const code = this.$route.query.code
        const state = this.$route.query.state
        // const { code, state } = this.nuxtApp.$social.handleRedirect(provider)
        if (this.loggedIn) {
          this.state = ''
        } else {
          this.state = state
        }
        const { success, message, data } = await this.fetch(
          `${type}${provider}/`,
          {
            method: 'POST',
            body: {
              code,
              state: this.state,
            },
          },
        )
        if (success) {
          if (
            provider === 'twitter' ||
            provider === 'instagram' ||
            provider === 'google' ||
            provider === 'youtube' ||
            provider === 'pinterest' ||
            provider === 'reddit' ||
            provider === 'tiktok' ||
            provider === 'gohighlevel'
          ) {
            this.nuxtApp.$toast('success', {
              message: 'Feed Successfully Connected',
              className: 'toasted-bg-archive',
            })
          }
          this.responseData = data
        } else if (!success) {
          this.nuxtApp.$toast('error', {
            message: message,
            className: 'toasted-bg-alert',
          })
        } else if (this.error) {
          this.nuxtApp.$toast('error', {
            message: this.errorMessage,
            className: 'toasted-bg-alert',
          })
        } else {
          this.nuxtApp.$toast('error', {
            message: 'Failed to Connect Feed12',
            className: 'toasted-bg-alert',
          })
        }
      } catch (e) {
        this.nuxtApp.$toast('error', {
          message: 'Failed to Connect Feed34',
          className: 'toasted-bg-alert',
        })
      } finally {
        this.redirect(this.loggedIn, provider)
      }
    },
    redirect(loggedIn, provider) {
      if (loggedIn) {
        setTimeout(() => {
          this.$router.push('/home')
          if (
            (provider === 'facebook' ||
              provider === 'linkedin' ||
              provider === 'microsoft' ||
              provider === 'ringcentral') &&
            this.responseData
          ) {
            this.$store.commit('header/SET_FB_AUTH_DATA', this.responseData)
            this.$store.commit('header/SET_PROVIDER_NAME', provider)
            this.$store.dispatch('header/showUpdateAddFeed')
          } else if (provider === 'ringcentral' && this.responseData) {
            this.$store.commit('header/SET_FB_AUTH_DATA', this.responseData)
            this.$store.commit('header/SET_PROVIDER_NAME', provider)
            this.$store.commit('header/SET_TEXT_ACCOUNT_ONBOARDING', true)
          } else if (
            provider === 'twitter' ||
            provider === 'instagram' ||
            provider === 'google' ||
            provider === 'youtube' ||
            provider === 'pinterest' ||
            provider === 'reddit' ||
            provider === 'tiktok' ||
            provider === 'gohighlevel'
          ) {
            setTimeout(() => {
              this.$store.commit('socialFeed/SET_ARCHIVE_FEED', true)
              this.$store.commit('socialFeed/SHOW_ADD_FEED_MODAL', true)
              this.$store.commit('header/SHOW_ADD_FEED_FINISH_BTN')
              if (this.$route.name?.includes('source')) {
                this.$store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false)
                this.$store.commit('social/SET_MANAGER_ONBOARDING', true)
              }
            }, 100)
          } else {
            this.$store.commit('socialFeed/SET_ARCHIVE_FEED', true)
            this.$store.commit('socialFeed/SHOW_ADD_FEED_MODAL', true)
          }
        }, 500)
      } else {
        setTimeout(() => {
          this.$router.push('/guest/' + this.state)
          if (
            (provider === 'facebook' ||
              provider === 'linkedin' ||
              provider === 'microsoft' ||
              provider === 'ringcentral') &&
            this.responseData
          ) {
            this.$store.commit('header/SET_FB_AUTH_DATA', this.responseData)
            this.$store.commit('header/SET_PROVIDER_NAME', provider)
            this.$store.dispatch('header/showUpdateAddFeed')
          }
        }, 500)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
section {
  // animation: animateBG 10s linear infinite;
}
@keyframes animateBG {
  0% {
    filter: hue-rotate(0deg);
  }
  100% {
    filter: hue-rotate(360deg);
  }
}
.loader {
  animation: animateBG 10s linear infinite;
  position: relative;
  width: 100px;
  height: 100px;
}
.loader span {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform: rotate(calc(18deg * var(--i)));
}
.loader span::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 15px;
  height: 15px;
  background-image: linear-gradient(#8db230, #8db230, #8db230);
  border-radius: 50%;
  box-shadow:
    0 0 10px #8db230,
    0 0 20px #8db230,
    0 0 40px #8db230,
    0 0 60px #8db230,
    0 0 80px #8db230,
    0 0 100px #8db230;
  animation: animate 2s linear infinite;
  animation-delay: calc(0.1s * var(--i));
}
@keyframes animate {
  0% {
    transform: scale(1);
  }
  80%,
  100% {
    transform: scale(0);
  }
}
.loaders svg:nth-last-child(1) {
  display: none !important;
}
</style>
