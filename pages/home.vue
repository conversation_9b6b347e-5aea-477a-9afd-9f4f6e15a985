<template>
  <section :class="homeWrapper ? '' : 'h-full'">
    <div
      v-if="isDesktopDevice"
      class="hidden md:block p-4 md:pt-0 relative"
      :class="[
        homeWrapper ? 'demo_home' : 'home_page_height',
        home ? 'afterloading_home2' : '',
        widthDecrese ? 'afterlogout_home3' : '',
        slideLeft ? 'afterloading_home3' : '',
        slideRight ? 'afterlogout_home2' : '',
        circle ? 'increase_width' : '',
        slideFullRight ? 'afterlogout_home1' : '',
        homeSliderWrapper ? 'home_wrapper' : 'home_page_height',
        homeSlider ? 'home_wrapper_afterloading' : '',
        fullHomeSlider ? 'home_wrapper_afterloading2' : '',
        allSidebarText ? 'home_wrapper_afterloading3' : '',
      ]"
      @click="closeSidebar()"
    >
      <span
        class="background__circle bg-orange-dark"
        :class="[
          homeCircle || fullHomeSlider ? 'showCircle' : '',
          showSidebarMenu ? 'hideCircle' : '',
        ]"
      ></span>
      <div
        class="transition-all content duration-2000"
        :class="[
          showHomeContent || homePage ? 'opacity-0' : '',
          fadeInpageContent ? 'opacity-1' : 'opacity-0',
        ]"
      >
        <!-- grid lg:grid-cols-3 md:grid-cols-4 gap-2 mb-2 -->
        <div
          class="flex lg:flex-nowrap lg:justify-end justify-between header-wrapper"
        >
          <div
            class="feed-dropdown flex justify-between flex-nowrap flex-grow"
            :class="[
              squeeze && !skew ? 'feed-dropdown-squeeze' : '',
              isCustomSizeThree ? 'space-x-2' : 'space-x-4',
            ]"
          >
            <div class="relative h-full md:mt-4">
              <ArchiveSourcesDropdown
                :source="'home'"
                :active="false"
                :height="100"
                @expand="showSkew($event)"
                @show-edit-feed="showEditFeed = true"
                @change-feed="expandCollapseMenu()"
              />
            </div>
            <!-- betaVersion ? 'opacity-1' : 'opacity-0', -->
            <div
              class="flex flex-nowrap flex-grow transition-all duration-500 fw-bold post-message-none opacity-100 justify-end"
              :class="[
                skew && isDesktop ? 'margin_right' : '',
                squeeze && !skew ? 'post-message' : '',
                isCustomSizeThree ? 'space-x-2' : 'space-x-4',
              ]"
            >
              <incoming-outgoing
                v-if="currentComp === 'Google' || currentComp === 'Microsoft'"
                :expand-collapse="setExpanfCollapseMenu"
                :is-custom-size-two="isCustomSizeTwo"
                class="md:mt-4"
                @click="
                  isCustomSizeTwo && !setExpanfCollapseMenu
                    ? expandCollapseMenu()
                    : ''
                "
              ></incoming-outgoing>

              <voice-recorded
                v-if="currentComp === 'Calls'"
                class="md:mt-4"
              ></voice-recorded>
              <received-sent
                v-if="currentComp === 'Faxes'"
                class="md:mt-4"
              ></received-sent>
              <original-transcription
                v-if="currentComp === 'Faxes'"
                class="md:mt-4"
              ></original-transcription>
              <transition name="page" mode="out-in">
                <div
                  v-if="currentComp === 'Web' && webSearch.archiveDate"
                  class="w-10 h-10 rounded-full md:mt-4"
                  data-title="Clear"
                  @click="clearSearch()"
                >
                  <button
                    class="flex justify-center items-center w-8 h-8 bg-orange-dark rounded-full cursor-pointer p-5"
                  >
                    <ClearIcon class="absolute clear_button" />
                  </button>
                </div>
              </transition>
              <div
                v-if="
                  currentComp !== 'Web' &&
                  youtubeType !== 'Playlists' &&
                  showRealTimeView
                "
                class="w-10 h-10 rounded-full md:mt-4"
                data-title="Clear"
                @click="clearSearch()"
              >
                <button
                  class="flex justify-center items-center w-8 h-8 bg-orange-dark rounded-full cursor-pointer p-5"
                >
                  <ClearIcon class="absolute clear_button" />
                </button>
              </div>
              <!-- v-if="currentComp === 'Google' || currentComp === 'Microsoft'" -->
              <all-dates
                v-if="
                  currentComp !== 'Web' &&
                  youtubeType !== 'Playlists' &&
                  showRealTimeView
                "
                class="md:mt-4 flex-grow max-w-[390px]"
                :width="
                  isCustomSize &&
                  currentComp !== 'YouTube' &&
                  currentComp !== 'Web'
                    ? currentComp === 'Facebook'
                      ? isDesktop
                        ? '8.5rem'
                        : '7.2rem'
                      : '10rem'
                    : isCustomSizeThree &&
                        currentComp !== 'YouTube' &&
                        currentComp !== 'Web'
                      ? '7.2rem'
                      : isCustomSizeTwo &&
                          currentComp !== 'YouTube' &&
                          currentComp !== 'Web'
                        ? '8.5rem'
                        : currentComp === 'YouTube' || currentComp === 'Web'
                          ? '11.25rem'
                          : '8.5rem'
                "
              ></all-dates>
              <archive-dates
                v-if="currentComp === 'Web'"
                class="md:mt-4"
              ></archive-dates>
              <div
                v-if="
                  currentComp === 'Facebook' ||
                  currentComp === 'Twitter' ||
                  currentComp === 'LinkedIn' ||
                  currentComp === 'Instagram' ||
                  currentComp === 'Reddit' ||
                  currentComp === 'Pinterest' ||
                  currentComp === 'TikTok'
                "
                class="post_message flex flex-nowrap transition-all duration-500 overflow-hidden"
                :class="[
                  (!setExpanfCollapseMenu && isCustomSizeTwo) ||
                  (isCustomSizeOne &&
                    !setExpanfCollapseMenu &&
                    currentComp === 'Facebook')
                    ? 'w-10'
                    : currentComp === 'Facebook'
                      ? 'max-[1139px]:w-[302px] max-[1279px]:w-[366px] w-auto'
                      : 'max-[1279px]:w-[242px] w-auto',
                  isCustomSizeThree ? 'space-x-2' : 'space-x-4',
                ]"
              >
                <div
                  class="min-w-[40px] w-10 h-10 rounded-full cursor-pointer flex justify-center items-center md:mt-4 bg-orange-dark"
                  :class="
                    (!setExpanfCollapseMenu && isCustomSizeTwo) ||
                    (isCustomSizeOne &&
                      !setExpanfCollapseMenu &&
                      currentComp === 'Facebook')
                      ? 'block'
                      : 'hidden'
                  "
                  @click="
                    ;(isCustomSizeTwo && !setExpanfCollapseMenu) ||
                    (isCustomSizeOne &&
                      !setExpanfCollapseMenu &&
                      currentComp === 'Facebook')
                      ? expandCollapseMenu()
                      : ''
                  "
                >
                  <ClientOnly>
                    <fa
                      class="pointer-events-none text-3xl font-bold text-white transform -rotate-90"
                      :icon="['fas', 'caret-down']"
                    />
                  </ClientOnly>
                </div>
                <div
                  v-if="
                    currentComp === 'Twitter' ||
                    currentComp === 'LinkedIn' ||
                    currentComp === 'Instagram' ||
                    currentComp === 'Reddit' ||
                    currentComp === 'Pinterest' ||
                    currentComp === 'TikTok'
                  "
                  class="hover:text-white self-center hover:bg-yellow-primary rounded-3xl post-style font-bold md:mt-4 cursor-pointer"
                  :class="[
                    showRealTimeView
                      ? 'text-white bg-yellow-primary'
                      : 'text-yellow-primary bg-white',
                    isCustomSizeTwo ? '!ml-0' : '!ml-0',
                  ]"
                  @click="showRealTimeViewTable()"
                >
                  <h4 class="mt-2 text-center">Posts</h4>
                </div>
                <all-posts-reels
                  v-if="currentComp === 'Facebook'"
                  :expand-collapse="setExpanfCollapseMenu"
                  :is-custom-size-two="isCustomSizeOne"
                  class="md:mt-4 !ml-0"
                  @click="
                    isCustomSizeOne && !setExpanfCollapseMenu
                      ? [expandCollapseMenu()]
                      : !isDesktop
                        ? showRealTimeViewFacebookTable()
                        : ''
                  "
                ></all-posts-reels>
                <div
                  v-if="
                    currentComp === 'Facebook' ||
                    currentComp === 'Twitter' ||
                    currentComp === 'LinkedIn' ||
                    currentComp === 'Instagram' ||
                    currentComp === 'Reddit' ||
                    currentComp === 'Pinterest' ||
                    currentComp === 'TikTok'
                  "
                  class="hover:text-white hover:bg-yellow-primary xl:px-2.5 2xl:px-5 py-2 rounded-3xl font-bold md:mt-4 cursor-pointer"
                  :class="[
                    showMessage
                      ? 'text-white bg-yellow-primary'
                      : 'text-yellow-primary bg-white',
                    currentComp === 'Facebook'
                      ? 'message-style-facebook'
                      : 'message-style',
                  ]"
                  @click="showMessageTable()"
                >
                  <h4 class="mx-2 text-center">Messages</h4>
                </div>
              </div>
              <!-- <Options class="option-button md:mt-4" /> -->
              <!-- <Options class="option-button" /> -->
            </div>
          </div>
          <!-- lg:col-span-1 md:col-span-2 -->
          <SearchBar
            class="transition-all duration-500 h-10 md:mt-4"
            :class="[
              skew && isDesktop ? 'margin_left' : '',
              (isCustomSizeTwo && setExpandCollapseSearch) ||
              (isCustomSizeOne &&
                setExpandCollapseSearch &&
                currentComp === 'Facebook')
                ? 'search-bar-tab cursor-pointer'
                : 'search-bar overflow-hidden',
              currentComp === 'YouTube' || currentComp === 'Web'
                ? 'search-bar-youtube'
                : '',
            ]"
            :expand-collapse="
              currentComp !== 'YouTube' && currentComp !== 'Web'
                ? setExpandCollapseSearch
                : false
            "
            :is-custom-size-two="
              isCustomSizeOne &&
              setExpandCollapseSearch &&
              currentComp === 'Facebook'
                ? isCustomSizeOne
                : isCustomSizeTwo
            "
            @search="searchContent($event)"
            @clickSearch="goToSearch($event)"
            @click="
              ;(isCustomSizeTwo &&
                setExpandCollapseSearch &&
                currentComp !== 'YouTube' &&
                currentComp !== 'Web') ||
              (isCustomSizeOne &&
                setExpandCollapseSearch &&
                currentComp !== 'YouTube' &&
                currentComp !== 'Web' &&
                currentComp === 'Facebook')
                ? expandCollapseSearch()
                : ''
            "
          />
          <div
            class="flex space-x-4 transition-all duration-500 fw-bold tab-post-message flex-wrap"
          >
            <incoming-outgoing
              v-if="currentComp === 'Google' || currentComp === 'Microsoft'"
              class="md:mt-4"
              :expand-collapse="setExpanfCollapseMenu"
              :is-custom-size-two="isCustomSizeTwo"
            ></incoming-outgoing>

            <voice-recorded
              v-if="currentComp === 'Calls'"
              class="md:mt-4"
            ></voice-recorded>
            <received-sent
              v-if="currentComp === 'Faxes'"
              class="md:mt-4"
            ></received-sent>
            <original-transcription
              v-if="currentComp === 'Faxes'"
              class="md:mt-4"
            ></original-transcription>
            <transition name="page" mode="out-in">
              <div
                v-if="currentComp === 'Web' && webSearch.archiveDate"
                class="w-10 h-10 rounded-full md:mt-4"
                data-title="Clear"
                @click="clearSearch()"
              >
                <button
                  class="flex justify-center items-center w-8 h-8 bg-orange-dark rounded-full cursor-pointer p-5"
                >
                  <ClearIcon class="absolute clear_button" />
                </button>
              </div>
            </transition>
            <div
              v-if="
                currentComp !== 'Web' &&
                youtubeType !== 'Playlists' &&
                showRealTimeView
              "
              class="w-10 h-10 rounded-full md:mt-4"
              data-title="Clear"
              @click="clearSearch()"
            >
              <button
                class="flex justify-center items-center w-8 h-8 bg-orange-dark rounded-full cursor-pointer p-5"
              >
                <ClearIcon class="absolute clear_button" />
              </button>
            </div>
            <!-- v-if="currentComp === 'Google' || currentComp === 'Microsoft'" -->
            <all-dates
              v-if="
                currentComp !== 'Web' &&
                youtubeType !== 'Playlists' &&
                showRealTimeView
              "
              class="md:mt-4 flex-grow max-w-[390px]"
              :width="'10.2rem'"
            ></all-dates>
            <archive-dates
              v-if="currentComp === 'Web'"
              class="md:mt-4"
            ></archive-dates>
            <div
              v-if="
                currentComp === 'Twitter' ||
                currentComp === 'LinkedIn' ||
                currentComp === 'Instagram' ||
                currentComp === 'Reddit' ||
                currentComp === 'Pinterest' ||
                currentComp === 'TikTok'
              "
              class="hover:text-white self-center hover:bg-yellow-primary rounded-3xl post-style font-bold md:mt-4 cursor-pointer"
              :class="
                showRealTimeView
                  ? 'text-white bg-yellow-primary'
                  : 'text-yellow-primary bg-white'
              "
              @click="showRealTimeViewTable()"
            >
              <h4 class="mt-2 text-center">Posts</h4>
            </div>
            <all-posts-reels
              v-if="currentComp === 'Facebook'"
              class="md:mt-4"
              :expand-collapse="setExpanfCollapseMenu"
              :is-custom-size-two="isCustomSizeOne"
            ></all-posts-reels>
            <div
              v-if="
                currentComp === 'Facebook' ||
                currentComp === 'Twitter' ||
                currentComp === 'LinkedIn' ||
                currentComp === 'Instagram' ||
                currentComp === 'Reddit' ||
                currentComp === 'Pinterest' ||
                currentComp === 'TikTok'
              "
              class="hover:text-white hover:bg-yellow-primary xl:px-2.5 2xl:px-5 py-2 font-bold rounded-3xl message-style md:mt-4 cursor-pointer"
              :class="
                showMessage
                  ? 'text-white bg-yellow-primary'
                  : 'text-yellow-primary bg-white'
              "
              @click="showMessageTable()"
            >
              <h4 class="mx-2 text-center">Messages</h4>
            </div>
            <!-- <Options class="option-button mt-4" /> -->
            <!-- <Options class="option-button" /> -->
          </div>
        </div>
        <!-- betaVersion ? 'body-wrapper body-wrapper-new' : 'body-wrapper', -->

        <div
          class="body-wrapper body-wrapper-new h-full transition-all duration-3000"
          :class="[skew ? 'm-xl:ml-70' : '']"
        >
          <div class="h-full inner-body-wrapper">
            <div
              class="flex w-full h-full overflow-hidden rounded-2xl feed-latest-wrapper"
            >
              <!--start => real time feed rss-->
              <div
                v-if="isDesktop"
                id="resizeMeLeft"
                class="flex-grow flex left-main-wraper left-main-wrapper relative"
              >
                <!-- <transition name="fadeIn"> -->
                <!-- v-if="showRealTimeView" -->
                <div
                  class="transition-all duration-300 ease-in-out relative"
                  :class="[
                    showRealTimeView && !showMessage && !showFullMessage
                      ? 'delay-500 real-time-feed-full'
                      : 'real-time-feed',
                    !showRealTimeView && showMessage && showFullMessage
                      ? 'real-time-feed-zero'
                      : 'delay-500 real-time-feed',
                  ]"
                >
                  <RealTimeFeed
                    class="transition-all duration-300 ease-in-out"
                    :class="
                      !showRealTimeView && showMessage && showFullMessage
                        ? 'opacity-0'
                        : 'delay-500 opacity-100'
                    "
                  />
                </div>

                <!-- </transition> -->
                <!--end => real time feed rss-->
                <!-- <transition name="fadeIn"> -->
                <!-- v-if="showRealTimeView && showMessage" -->
                <div
                  id="resizeMeRight"
                  class="transitionWidthOpacity duration-300 ease-in-out relative bg-white rounded-3xl"
                  :class="[
                    showRealTimeView && showMessage && !showFullMessage
                      ? 'delay-500 opacity-100 MessageFeed-half md:ml-7'
                      : !showRealTimeView && showMessage && showFullMessage
                        ? 'opacity-100 FullMessageFeed md:ml-0'
                        : 'opacity-0 MessageFeed',
                  ]"
                >
                  <!-- <Transition name="page"> -->
                  <MessageFeed
                    v-if="showRealTimeView && showMessage && !showFullMessage"
                    class="transitionWidthOpacity duration-300 ease-in-out"
                    :class="
                      showRealTimeView && showMessage && !showFullMessage
                        ? 'delay-500 opacity-100'
                        : 'opacity-0 MessageFeed'
                    "
                  />
                  <FullMessageFeed
                    v-else-if="
                      !showRealTimeView && showMessage && showFullMessage
                    "
                    class="transitionWidthOpacity duration-300 ease-in-out"
                    :class="[
                      !showRealTimeView && showMessage && showFullMessage
                        ? 'delay-500 opacity-100'
                        : 'opacity-0',
                    ]"
                  />
                  <!-- </Transition> -->
                </div>
                <div
                  id="resizeIcon"
                  class="absolute -bottom-2 pr-2 pb-2 z-30 w-full hidden text-right"
                  :class="[
                    !showRealTimeView && showMessage && showFullMessage
                      ? 'right-0'
                      : '',
                  ]"
                >
                  <ClientOnly>
                    <fa
                      class="text-xl font-light text-yellow-primary"
                      :icon="['fas', 'arrows-alt-v']"
                    />
                  </ClientOnly>
                </div>
                <!-- </transition> -->
                <!-- <transition name="fadeIn"> -->
                <!-- v-if="!showRealTimeView && showMessage" -->
                <!-- <div
                  id="resizeMeFullMessage"
                  class="transitionWidthOpacity duration-300 ease-in-out relative"
                  :class="[
                    !showRealTimeView && showMessage && showFullMessage
                      ? 'delay-500 FullMessageFeed'
                      : 'FullMessageFeed-zero',
                  ]"
                >
                  <FullMessageFeed
                    class="transitionWidthOpacity duration-300 ease-in-out"
                    :class="[
                      !showRealTimeView && showMessage && showFullMessage
                        ? 'delay-500 opacity-100'
                        : 'opacity-0',
                    ]"
                  />
                </div> -->
                <!-- </transition> -->
              </div>

              <!-- Codes for resizing functionality -->

              <!-- id="resizeMeLeft" -->
              <!-- @mousedown="mouseDownHandler" @mouseup="mouseUpHandler"
              @touchstart="touchDownHandler" @touchend="touchUpHandler" -->

              <!-- <div
                  id="resizeIcon"
                  class="
                    absolute
                    -bottom-2
                    pr-2
                    pb-2
                    z-30
                    w-full
                    hidden
                    text-right
                    right-0
                  "
                >
                  <fa
                    class="text-xl font-light text-yellow-primary"
                    :icon="['fas', 'arrows-alt-v']"
                  />
                </div> -->

              <div
                v-else
                class="flex relative smallDesktop:mt-0 transition-all duration-500 ease-in-out"
                :class="feedIsExpanded ? 'left-main-wraper' : 'header-height'"
              >
                <div
                  class="transition-all duration-300 ease-in-out"
                  :class="[
                    showRealTimeView
                      ? 'delay-500 real-time-feed-full'
                      : 'real-time-feed-zero',
                  ]"
                >
                  <RealTimeFeed
                    class="transition-all duration-300 ease-in-out"
                    :class="
                      !showRealTimeView ? 'opacity-0' : 'delay-500 opacity-100'
                    "
                    :feed-is-expanded="feedIsExpanded"
                    @expand-height="feedHeightController"
                  />
                </div>
                <div
                  class="transition-all duration-300 ease-in-out"
                  :class="
                    showMessage
                      ? 'delay-500 opacity-100 MessageFeed-full'
                      : 'opacity-0 MessageFeed'
                  "
                >
                  <MessageFeed
                    class="transition-all duration-300 ease-in-out"
                    :class="showMessage ? 'delay-500 opacity-100' : 'opacity-0'"
                    :message-is-expanded="messageIsExpanded"
                    @show-full-height="messageHeightController"
                  />
                </div>
              </div>
              <div
                id="resizeMeAlso"
                class="hidden smallDesktop:block latest-archive"
              >
                <LatestArchive :search-item="searchItem" />
              </div>
              <div
                class="smallDesktop:hidden transition-all duration-500 ease-in-out"
                :class="archiveIsExpanded ? 'latest-archive' : 'header-height'"
              >
                <LatestArchive
                  :search-item="searchItem"
                  :archive-is-expanded="archiveIsExpanded"
                  @increase-height="latestArchiveHeightController"
                />
              </div>
              <!--end => latest archive-->
            </div>
          </div>
        </div>
      </div>
      <!-- <edit-feed
      :source="'home'"
      :edit-feed="showEditFeed"
      @close-edit-feed="showEditFeed = false"
    ></edit-feed> -->
    </div>

    <!-- Small Device -->
    <div
      v-else-if="!isDesktopDevice"
      class="p-2 md:hidden mobile-home_page_height"
    >
      <div
        class="transition-all content duration-2000"
        :class="[
          showHomeContent || homePage ? 'opacity-0' : '',
          fadeInpageContent ? 'opacity-1' : 'opacity-0',
          showMenu ? 'pointer-events-none' : '',
        ]"
      >
        <!-- showMenu ? 'filter blur-md pointer-events-none' : '', -->
        <div class="h-full transition-all body-wrapper duration-3000">
          <div class="relative h-full">
            <div class="w-full h-full overflow-hidden bg-white rounded-2xl">
              <div
                class="flex items-center justify-around w-full"
                :class="currentHead === 'MessageFeed' ? 'hidden' : 'block'"
              >
                <div
                  class="w-1/2 p-3 text-xl font-bold text-center cursor-pointer toggle-button"
                  :class="[
                    currentHead === 'RealTimeFeed'
                      ? 'active-feed-menu'
                      : 'bg-yellow-primary shadow-inner-site text-white',
                    currentSocialComponent.provider === 'Google' ||
                    currentSocialComponent.provider === 'Microsoft'
                      ? ''
                      : '',
                  ]"
                  @click="feedLatestMenu('RealTimeFeed')"
                >
                  <p>Viewer</p>
                  <button
                    v-if="
                      currentComp === 'Google' || currentComp === 'Microsoft'
                    "
                    id="download_icon"
                    class="w-5 h-5"
                  >
                    <ClientOnly>
                      <fa
                        class="mr-0.875 text-green-1100"
                        :icon="['fas', 'download']"
                      />
                    </ClientOnly>
                  </button>
                </div>
                <div
                  class="w-1/2 p-3 text-xl font-bold text-center cursor-pointer toggle-button"
                  :class="
                    currentHead === 'LatestArchive'
                      ? 'active-feed-menu'
                      : 'bg-yellow-primary shadow-inner-site text-white'
                  "
                  @click="feedLatestMenu('LatestArchive')"
                >
                  {{
                    webSearchResult && currentComp === 'Web'
                      ? 'Search Result'
                      : 'User Information'
                  }}
                </div>
              </div>
              <transition name="page" mode="out-in">
                <component :is="currentHead"> </component>
              </transition>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
//
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core'
import ArchiveSourcesDropdown from '~/components/home/<USER>'
import SearchBar from '~/components/home/<USER>'
import RealTimeFeed from '~/components/home/<USER>/RealTimeFeed'
import MessageFeed from '~/components/home/<USER>/MessageFeed'
import FullMessageFeed from '~/components/home/<USER>/FullMessageFeed'
import LatestArchive from '~/components/home/<USER>/LatestArchive'
import Options from '~/components/home/<USER>/Options'
import AllDates from '~/components/home/<USER>/AllDates'
import IncomingOutgoing from '~/components/home/<USER>/IncomingOutgoing.vue'
import VoiceRecorded from '~/components/home/<USER>/VoiceRecorded.vue'
import ReceivedSent from '~/components/home/<USER>/ReceivedSent.vue'
import OriginalTranscription from '~/components/home/<USER>/OriginalTranscription.vue'
import ClearIcon from '~/components/shared/icon/ClearIcon.vue'
import ArchiveDates from '~/components/home/<USER>/ArchiveDates.vue'
import AllPostsReels from '~/components/home/<USER>/AllPostsReels.vue'

// import EditFeed from '~/components/EditFeed.vue'
export default defineComponent({
  name: 'HomePage',
  components: {
    LatestArchive,
    RealTimeFeed,
    MessageFeed,
    // eslint-disable-next-line vue/no-unused-components
    FullMessageFeed,
    SearchBar,
    ArchiveSourcesDropdown,
    // eslint-disable-next-line vue/no-unused-components
    Options,
    // eslint-disable-next-line vue/no-unused-components
    AllDates,
    IncomingOutgoing,
    VoiceRecorded,
    ReceivedSent,
    OriginalTranscription,
    ClearIcon,
    ArchiveDates,
    AllPostsReels,
    // EditFeed,
  },
  // layout: 'dashboard',
  props: [
    'homeSliderWrapper',
    'homeSlider',
    'fullHomeSlider',
    'showSidebarMenu',
    'homePage',
    'blur',
    'allSidebarText',
  ],
  setup() {
    useHead(() => ({
      title: 'Sharp Archive',
      meta: [
        {
          hid: 'robots',
          name: 'robots',
          content: 'noindex, nofollow',
        },
      ],
    }))
    const nuxtApp = useNuxtApp()
    definePageMeta({
      layoutTransition: false,
      layout: 'dashboard',
      middleware: ['payment', 'auth', 'color'],
    })
    const breakpoints = useBreakpoints({
      largeDesktop: 1440,
      customSize: 1322,
      customSizeOne: 1376,
      customSizeTwo: 1280,
      customSizeThree: 1113,
      tablet: 1024,
    })
    const tailwindBreakpoints = useBreakpoints(breakpointsTailwind)
    return {
      isDesktop: breakpoints.greaterOrEqual('largeDesktop'),
      isDesktopDevice: tailwindBreakpoints.greaterOrEqual('md'),
      isCustomSize: breakpoints.greaterOrEqual('customSize'),
      isCustomSizeOne: breakpoints.between('tablet', 'customSizeOne'),
      isCustomSizeTwo: breakpoints.between('tablet', 'customSizeTwo'),
      isCustomSizeThree: breakpoints.between('tablet', 'customSizeThree'),
      nuxtApp,
    }
  },
  data() {
    return {
      archiveIsExpanded: false,
      feedIsExpanded: true,
      messageIsExpanded: true,
      skew: false,
      searchItem: '',
      fadeInpageContent: false,
      headerConfig: {
        barColor: 'text-orange-dark',
        icon: 'home',
        iconColor: 'text-orange-dark',
        text: 'Home',
        dropdownMenuColor: 'home',
        skew: false,
      },
      showEditFeed: false,
      showRealTimeView: true,
      showMessage: false,
      showFullMessage: false,
      slowShowMessage: false,
      yAxisPart1: 0,
      yAxisPart2: 0,
      hlPart1: 0,
      hrPart1: 0,
      hPart2: 0,
      initialUpperLeftHeight: '',
      initialUpperRightHeight: '',
      initailLowerHeight: '',
      closeArchiveDates: false,
      setExpandCollapseSearch: true,
      setExpanfCollapseMenu: true,
    }
  },

  computed: {
    ...mapState(['betaVersion']),
    // archiveDropHeight() {
    //   return this.betaVersion ? 330 : 248
    // },
    ...mapState('loginAnimation', {
      home: 'home',
      circle: 'circle',
      homeCircle: 'homeCircle',
      slideLeft: 'slideLeft',
      showHomeContent: 'showHomeContent',
      homeWrapper: 'homeWrapper',
      widthDecrese: 'widthDecrese',
      slideRight: 'slideRight',
      slideFullRight: 'slideFullRight',
    }),
    ...mapState('socialFeed', ['archiveFeed']),
    ...mapState('header', {
      squeeze: 'squeeze',
    }),
    ...mapState('socialFeed', ['archiveFeed']),
    ...mapState('feedsDropdown', ['isShowDropDown']),
    ...mapGetters('home', ['currentHead', 'currentHeader']),
    ...mapGetters('home', ['showMenu', 'showMobileMenu']),
    ...mapGetters('home', ['currentComp']),
    ...mapState('home', [
      'webSearch',
      'webSearchResult',
      'youtubeType',
      'currentSocialComponent',
    ]),
  },
  watch: {
    currentComp(data) {
      if (
        (data === 'Google' ||
          data === 'Microsoft' ||
          data === 'Faxes' ||
          data === 'Calls' ||
          data === 'Texts' ||
          data === 'Web' ||
          data === 'YouTube') &&
        this.showMessage
      ) {
        this.showMessage = false
        this.showFullMessage = false
        this.showRealTimeView = true
        // this.showMessageTable()
      }
    },
    isShowDropDown(data) {
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'))
      }, 400)
    },
    isDesktop(data) {
      if (this.showRealTimeView && !data && !this.showMessage) {
        this.$store.commit('home/SET_HOME_CURRENT_COMP', '')
      } else if (!this.showRealTimeView && !data && this.showMessage) {
        this.$store.commit('home/SET_HOME_CURRENT_COMP', '')
      } else if (this.showRealTimeView && data && !this.showMessage) {
        this.$store.commit('home/SET_HOME_CURRENT_COMP', 'FullMessageFeed')
      } else if (!this.showRealTimeView && data && this.showMessage) {
        this.$store.commit('home/SET_HOME_CURRENT_COMP', 'FullMessageFeed')
      }
    },
  },
  mounted() {
    this.fadeInpageContent = true
    this.setHeaderConfig()
    this.$store.dispatch('header/showMobileHeader')
    this.$store.dispatch('header/setHomePage')
    this.$store.commit('home/SET_SHOW_SEARCH_MESSAGE_FROM_SEARCH', false)
    this.$store.commit('socialFeed/SET_ARCHIVE_FEED', true)
  },
  methods: {
    expandCollapseSearch() {
      this.setExpandCollapseSearch = false
      this.setExpanfCollapseMenu = false
      // this.setExpandCollapseSearch = !this.setExpandCollapseSearch
    },
    expandCollapseMenu() {
      this.setExpanfCollapseMenu = true
      this.setExpandCollapseSearch = true
    },
    clearSearch() {
      this.$store.commit('home/RESET_WEB_SEARCH')
      this.nuxtApp.$bus.$emit('clearDatePicker')
    },
    latestArchiveHeightController() {
      this.archiveIsExpanded = !this.archiveIsExpanded
      this.feedIsExpanded = !this.feedIsExpanded
      this.messageIsExpanded = !this.messageIsExpanded
    },
    feedHeightController() {
      this.feedIsExpanded = !this.feedIsExpanded
      this.archiveIsExpanded = !this.archiveIsExpanded
      this.messageIsExpanded = !this.messageIsExpanded
    },
    messageHeightController() {
      this.messageIsExpanded = !this.messageIsExpanded
      this.archiveIsExpanded = !this.archiveIsExpanded
      this.feedIsExpanded = !this.feedIsExpanded
    },
    showRealTimeViewFacebookTable() {
      this.showRealTimeView = true
      this.showMessage = false
    },
    showRealTimeViewTable() {
      this.showRealTimeView = !this.showRealTimeView
      if (
        (!this.showMessage || !this.showFullMessage) &&
        !this.showRealTimeView
      ) {
        this.showMessage = true
        this.showFullMessage = true
      } else if (
        this.showMessage &&
        this.showFullMessage &&
        this.showRealTimeView &&
        this.isDesktop
      ) {
        this.showMessage = false
        this.showFullMessage = false
      }
      if (this.showRealTimeView && !this.isDesktop) {
        this.showMessage = false
        this.showFullMessage = false
      }
      if (
        !this.showRealTimeView &&
        this.showMessage &&
        this.showFullMessage &&
        this.isDesktop
      ) {
        this.$store.commit('home/SET_HOME_CURRENT_COMP', 'FullMessageFeed')
      } else {
        this.$store.commit('home/SET_HOME_CURRENT_COMP', '')
      }
    },
    showMessageTable() {
      if (this.isDesktop) {
        if (!this.showMessage && !this.showFullMessage) {
          this.showMessage = true
        } else if (this.showMessage && !this.showFullMessage) {
          this.showFullMessage = true
          this.showRealTimeView = false
        } else if (this.showMessage && this.showFullMessage) {
          this.showMessage = false
          this.showFullMessage = false
          this.showRealTimeView = true
        }
        if (
          !this.showRealTimeView &&
          this.showMessage &&
          this.showFullMessage
        ) {
          this.$store.commit('home/SET_HOME_CURRENT_COMP', 'FullMessageFeed')
        } else {
          this.$store.commit('home/SET_HOME_CURRENT_COMP', '')
        }
      } else {
        this.showMessage = !this.showMessage
        if (!this.showMessage && !this.showRealTimeView) {
          this.showRealTimeView = true
        }
        if (this.showMessage && !this.isDesktop) {
          this.showRealTimeView = false
        }
      }
      setTimeout(() => {
        if (this.showMessage) {
          this.$store.commit('home/SET_IS_SHOW_MESSAGE', true)
        } else {
          this.$store.commit('home/SET_IS_SHOW_MESSAGE', false)
        }
        setTimeout(() => {
          window.dispatchEvent(new Event('resize'))
        }, 400)
        if (this.isShowDropDown) {
          setTimeout(() => {
            window.dispatchEvent(new Event('resize'))
          }, 1500)
        }
      }, 500)
    },
    closeSidebar() {
      if (!this.archiveFeed) {
        this.$store.commit('archive/SET_BULK_DOWNLOAD_SIDEBAR', false)
        this.$store.commit('profile/SET_PROFILE_MODAL', false)
        this.$store.commit('search/SET_SAVE_SAERCH_MODAL', false)
        this.$store.commit('socialFeed/SHOW_SOCIAL_EDIT_FEED_MODAL', {
          open: false,
          data: {},
        })
        this.$store.commit('SET_SHOW_SIDE_BAR', false)
        this.$store.dispatch('header/removePayment')
        this.$store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false)
        this.$store.commit('home/SET_SELECT_ACCESS_TYPE', 'SelectedAccessType')
        this.$store.commit('notifications/SHOW_NOTIFICATION_MODAL', false)
      }
      setTimeout(() => {
        this.$store.commit('socialFeed/SET_ARCHIVE_FEED', false)
      }, 2000)
    },
    showSkew($event) {
      this.skew = $event
    },
    setHeaderConfig() {
      this.$store.dispatch('header/setHeaderConfig', this.headerConfig)
    },
    searchContent($event) {
      // this.searchItem = $event
    },
    goToSearch($event) {
      if ($event !== '') {
        this.$store.commit('search/SET_HOME_SEARCH', $event)
        this.$router.push('/search')
      }
    },
    feedLatestMenu(currentHead) {
      this.$store.commit('home/SET_CURRENT_HEADER', currentHead)
    },
  },
})
</script>

<style lang="scss" scoped>
.header-height {
  height: 40px;
}
@media (min-width: 1360px) {
  .header-height {
    height: 44px;
  }
}
.transitionWidthOpacity {
  transition-property: width, opacity, margin-left;
}
.post-style {
  // width: 150px;
  height: 40px;
  @apply min-[1382px]:w-[150px] min-[1362px]:w-[140px] min-[1342px]:w-[130px] min-[1298px]:w-[120px] w-[110px];
}
.message-style {
  @apply min-[1382px]:w-[150px] min-[1362px]:w-[140px] min-[1342px]:w-[130px] min-[1298px]:w-[120px] w-[110px];
  height: 40px;
  margin: 0 30px;
  margin-top: 16px;
}
.message-style-facebook {
  @apply min-[1396px]:w-[100px] min-[1440px]:w-[90px] min-[1646px]:w-[150px] min-[1362px]:w-[90px] min-[1342px]:w-[90px] min-[1280px]:w-[90px] w-[110px];
  height: 40px;
  margin: 0 30px;
  margin-top: 16px;
  padding-left: 0px;
  padding-right: 0px;
}
.option-button {
  width: 233px !important;
  height: 40px;
  margin-right: 30px;
}
.home_page_height {
  height: 100%;
}
.demo_home {
  height: 100%;
  margin-top: 60px !important;
  height: calc(100vh - 60px);
  z-index: 99;
  background-color: #393e46;
  position: absolute;
  width: 0%;
  right: calc(0% - 63px);
  transition:
    width 1.5s ease-in-out,
    width 1.5s ease-in-out,
    width 0.8s ease-in-out 0s;
}
.home_wrapper {
  height: calc(100vh - 60px);
  margin-top: 60px !important;
  height: calc(100vh - 60px);
  z-index: 99;
  background-color: #393e46;
  position: absolute;
  width: calc(100% - 100px);
  left: 101%;
  transition:
    left 0.8s ease-in-out,
    left 0.8s ease-in-out;
}
.afterloading_home2 {
  width: 288px;
}
.home_wrapper_afterloading {
  left: 50%;
}
.afterloading_home3 {
  width: -webkit-calc(100% - 137px);
  width: -moz-calc(100% - 137px);
  width: calc(100% - 137px);
}
.home_wrapper_afterloading2 {
  left: calc(200px + 0%);
}
.home_wrapper_afterloading3 {
  left: calc(100px + 0%);
}
.increase_width {
  width: 100%;
}
.background__circle {
  opacity: 1;
  width: 40px;
  height: 40px;
  position: absolute;
  top: 110px;
  left: -20px;
  z-index: 999999;
  @apply rounded-full hidden;
}
.showCircle {
  display: inline-block;
}

.hideCircle {
  display: none;
}

.afterlogout_home3 {
  height: calc(100vh - 60px);
  margin-top: 60px !important;
  height: calc(100vh - 60px);
  z-index: 99;
  background-color: #393e46;
  position: absolute;
  width: -webkit-calc(100% - 137px);
  width: -moz-calc(100% - 137px);
  width: calc(100% - 137px);
  right: calc(0% - 63px);
  transition:
    width 1.5s ease-in-out,
    width 1.5s ease-in-out,
    width 0.8s ease-in-out 0s;
}
.afterlogout_home2 {
  width: 288px;
}

.afterlogout_home1 {
  width: 0%;
}

.archive_search_bar_wrapper {
  @apply flex flex-col
        lg:flex-row
        justify-between
        gap-4
        mb-4
        lg:gap-12;
}
.home-search-wrapper {
  @apply flex flex-col lg:flex-row justify-between gap-4 mb-4 lg:gap-12;
}

.page-content {
  transition: margin 0.5s;
}

.searchbar {
  width: 32%;
}

.body-wrapper {
  @apply flex-grow mt-4 page-content overflow-y-auto md:relative;
}

.inner-body-wrapper {
  // height: calc(100% - 20px);
  height: 100%;
}

.main-content {
  @apply grid grid-cols-1 lg:grid-cols-3 gap-4 w-full h-full;
}

@media (max-width: 768px) {
  .searchbar {
    width: 56%;
  }
}
.mobile-home_page_height {
  height: 100%;
  position: relative;
  padding-top: 60px;
}
.shadow-inner-site {
  -moz-box-shadow: inset 0px 0px 6px 0px #575656;
  -webkit-box-shadow: inset 0px 0px 6px 0px #575656;
  box-shadow: inset 0px 0px 6px 0px #575656;
}
.active-feed-menu {
  @apply text-yellow-primary;
}
.active-feed-rss {
  @apply rounded-full bg-yellow-primary text-white;
}

@media (min-width: 1571px) {
  .header-wrapper {
    @apply space-x-7;
  }
  .feed-latest-wrapper {
    flex-direction: row;
    @apply space-x-7 space-y-0;
  }
  .feed-dropdown {
    // @apply w-2/3;
    width: 66.4%;
  }
  .margin_left {
    margin-left: 18.5rem !important;
  }
  .margin_right {
    margin-right: -16.9rem !important;
  }
  .search-bar {
    width: 33.6%;
  }
  .search-bar-youtube {
    width: 33.6%;
  }
  .left-main-wraper {
    @apply h-full;
  }
  .real-time-feed {
    @apply h-full w-1/2;
  }
  .real-time-feed-full {
    @apply h-full w-full;
  }
  .real-time-feed-zero {
    @apply h-full w-0;
  }
  .MessageFeed {
    @apply h-full w-0;
  }
  .MessageFeed-half {
    @apply h-full w-1/2;
  }
  .FullMessageFeed {
    @apply h-full w-full;
  }
  .FullMessageFeed-zero {
    @apply h-full w-0;
  }
  .latest-archive {
    @apply h-full w-1/3;
    width: 33%;
  }
  .tab-post-message {
    display: none;
  }
  .left-main-wrapper {
    width: 67%;
  }
}
@media (min-width: 2020px) {
  .feed-dropdown {
    width: 67%;
  }
  .search-bar {
    width: 33%;
  }
  .search-bar-youtube {
    width: 33%;
  }
  .margin_left {
    margin-left: 16.5rem !important;
  }
  .margin_right {
    margin-right: -14.9rem !important;
  }
}
@media (max-width: 1570px) and (min-width: 1368px) {
  .margin_left {
    margin-left: 18.5rem !important;
  }
  .margin_right {
    margin-right: -16.9rem !important;
  }
  // .post-message-none {
  //   display: none;
  // }
  .tab-post-message {
    display: none;
    // display: flex;
    // width: 100%;
    // justify-content: flex-end;
  }
  .header-wrapper {
    @apply space-x-7;
    // flex-wrap: wrap;
  }
  .feed-latest-wrapper {
    flex-direction: row;
    @apply space-x-7 space-y-0;
  }
  // .body-wrapper-new {
  //   margin-top: 4.25rem;
  // }
  .feed-dropdown {
    // @apply w-1/2 space-y-4;
    width: 69.4%;
  }
  .search-bar {
    // width: 33.2%;
    width: 27.8%;
  }
  .search-bar-youtube {
    width: 27.8%;
  }
  .left-main-wraper {
    @apply h-full;
  }
  .real-time-feed {
    @apply h-full w-1/2;
  }
  .real-time-feed-full {
    @apply h-full w-full;
  }
  .real-time-feed-zero {
    @apply h-full w-0;
  }
  .MessageFeed {
    @apply h-full w-0;
  }
  .MessageFeed-half {
    @apply h-full w-1/2;
  }
  .FullMessageFeed {
    @apply h-full w-full;
  }
  .FullMessageFeed-zero {
    @apply h-full w-0;
  }
  .latest-archive {
    @apply h-full w-1/3;
    width: 27.8%;
  }
  .feed-dropdown {
    // flex-wrap: wrap;
  }
  .header-margin_left {
    margin-left: 17rem;
  }
  .left-main-wrapper {
    width: 66.6%;
  }
}

@media (max-width: 1439px) {
  // .margin_left {
  //   margin-left: 21rem !important;
  // }
  #resizeIcon {
    cursor: row-resize;
    display: block;
  }
  // .margin_left {
  //   margin-left: 0rem !important;
  // }
  // .margin_right {
  //   // @apply min-[1114px]:!mr-4 !mr-2;
  //   margin-right: 1rem !important;
  // }
  .post-message-none {
    // display: none;
  }
  .tab-post-message {
    // display: flex;
    // width: 100%;
    // justify-content: flex-end;
    display: none;
  }
  .header-wrapper {
    @apply space-x-4;
    // flex-wrap: wrap;
  }
  .feed-latest-wrapper {
    flex-direction: column;
    @apply space-y-4 space-x-0;
  }
  // .body-wrapper-new {
  //   margin-top: 4.25rem;
  // }
  .feed-dropdown {
    // @apply w-1/2 space-y-4;
    width: 69.4%;
  }
  .search-bar {
    // @apply w-1/2;
    // width: 27.8%;
    // width: 308px;
    @apply min-[1366px]:w-[308px] min-[1294px]:w-[280px] xl:w-[264px];
  }
  .search-bar-youtube {
    width: 33.6%;
  }
  .left-main-wraper {
    // @apply h-1/2;
    height: calc(100% - 56px);
  }
  // .real-time-feed {
  //   @apply h-full w-full;
  // }
  .real-time-feed {
    @apply w-1/2;
  }
  .real-time-feed-full {
    @apply w-full;
    height: 100% !important;
  }
  .real-time-feed-zero {
    @apply h-full w-0;
  }
  .MessageFeed {
    @apply h-full w-0;
  }
  .MessageFeed-half {
    @apply h-full w-1/2;
  }
  .MessageFeed-full {
    @apply h-full w-full;
  }
  .FullMessageFeed {
    @apply h-full w-full;
  }
  .FullMessageFeed-zero {
    @apply h-full w-0;
  }
  .latest-archive {
    @apply w-full;
    height: calc(100% - 56px);
  }
  .feed-dropdown {
    // flex-wrap: wrap;
  }
  .header-margin_left {
    margin-left: 17rem;
  }
}
@media (max-width: 1113px) {
  .header-wrapper {
    @apply space-x-2;
    // flex-wrap: wrap;
  }
  // .margin_left{
  //   margin-left: 0.5rem !important;
  // }
  // .margin_right {
  //   // @apply min-[1114px]:!mr-4 !mr-2;
  //   margin-right: 0.5rem !important;
  // }
}
.search-bar-tab {
  width: 40px;
  overflow: hidden;
}
@media (min-width: 1024px) and (max-width: 1279px) {
  .search-bar-youtube {
    width: 33.6%;
  }
}
@media (max-width: 1023px) and (min-width: 768px) {
  .header-margin_left {
    margin-left: 0rem;
  }
  .header-wrapper {
    flex-wrap: wrap;
  }
  .feed-dropdown {
    width: auto;
  }
  .search-bar {
    width: 50%;
  }
  .search-bar-youtube {
    width: 50%;
  }
  .tab-post-message {
    display: flex;
    width: 100%;
    justify-content: flex-end;
    flex-wrap: wrap;
    // display: none;
  }
  .tab-post-message {
    // flex-wrap: wrap;
  }
  .post-message-none {
    display: none;
  }
  // .body-wrapper-new {
  //   margin-top: 8.25rem;
  // }
}
@media (max-width: 767px) {
  .body-wrapper {
    margin-top: 1rem;
  }
}

@media (max-width: 392px) {
  .toggle-button {
    @apply h-20 flex justify-center items-center;
  }
}

.clear_button {
  left: 3px;
}
</style>
