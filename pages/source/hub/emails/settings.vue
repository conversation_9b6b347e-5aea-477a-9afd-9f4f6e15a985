<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const route = useRoute()

const previousPath = computed(() => store.state.emails.previousPath)
const accountProvider = computed(() => store.state.social.accountItem.provider)
const dynamicEmailModal = computed(() => store.state.emails.dynamicEmailModal)
const showSignatureModal = computed(() => store.state.emails.showSignatureModal)
const editSignatureName = computed(() => store.state.emails.editSignatureName)
const deleteFilterModal = computed(() => store.state.emails.deleteFilterModal)
const showImportMailModal = computed(
  () => store.state.emails.showImportMailModal,
)
const showAddAccountModal = computed(
  () => store.state.emails.showAddAccountModal,
)
const showAnotherEmailModal = computed(
  () => store.state.emails.showAnotherEmailModal,
)
const showGrantAccountModal = computed(
  () => store.state.emails.showGrantAccountModal,
)
const showGrantAccountDeleteModal = computed(
  () => store.state.emails.showGrantAccountDeleteModal,
)
const forwardingAddressModal = computed(
  () => store.state.emails.forwardingAddressModal,
)
const unblockAddressModal = computed(
  () => store.state.emails.unblockAddressModal,
)
const importOutlookEmailModal = computed(
  () => store.state.outlook.importEmailModal,
)
const removeEmailModal = computed(
  () => store.state.emails.removeEmailModal.isOpen,
)
const createNewFilterModal = computed(
  () => store.state.emails.createNewFilterModal,
)
const isOutlookSettingChanged = computed(
  () => store.state.outlook.isSettingsChanged,
)

const currentPath = computed(() => {
  const currentPathArray = route.path.split('/')
  const current = currentPathArray[currentPathArray.length - 1]
  return current
})

const isSettingLinkDisable = computed(() => {
  return isOutlookSettingChanged.value && accountProvider.value === 'Microsoft'
})

const handleShowWarningModal = (path: string) => {
  if (isSettingLinkDisable.value && path !== currentPath.value) {
    store.commit('outlook/SET_SHOW_WARNING_MODAL', true)
  }
}
const showNewCategoryModal = computed(
  () => store.state.emails.showNewCategoryModal,
)
const settingsMenus = computed(() => {
  if (accountProvider.value === 'Google') {
    return [
      {
        id: 1,
        title: 'General',
        path: 'general',
      },
      {
        id: 2,
        title: 'Labels',
        path: 'labels',
      },
      {
        id: 3,
        title: 'Inbox',
        path: 'inbox',
      },
      {
        id: 4,
        title: 'Accounts and Import',
        path: 'accounts',
      },
      {
        id: 5,
        title: 'Filters and Blocked Addresses',
        path: 'filters',
      },
      {
        id: 6,
        title: 'Forwarding and POP/IMAP',
        path: 'forwardandpop',
      },
    ]
  } else if (accountProvider.value === 'Microsoft') {
    return [
      {
        id: 1,
        title: 'Account',
        path: 'account',
      },
      {
        id: 2,
        title: 'File',
        path: 'file',
      },
      {
        id: 3,
        title: 'General',
        path: 'general',
      },
      {
        id: 4,
        title: 'Mail',
        path: 'mail',
      },
      {
        id: 5,
        title: 'People',
        path: 'people',
      },
    ]
  }
})

onMounted(() => {
  document.addEventListener('click', () => {
    store.commit('emails/SET_CLOSE_ALL_MENUS', false)
  })
})
onUnmounted(() => {
  document.removeEventListener('click', () => {
    store.commit('emails/SET_CLOSE_ALL_MENUS', false)
  })
})
</script>

<template>
  <div
    class="w-full h-full overflow-hidden bg-white absolute top-0 left-0 rounded-2xl"
  >
    <div class="border-b-[3px] border-[#F1F2F6]">
      <div class="px-6 py-4">
        <div class="flex justify-between items-center">
          <p class="text-[#505050] text-lg font-semibold">Settings</p>
          <NuxtLink
            :to="isSettingLinkDisable ? '' : previousPath"
            class="cursor-pointer"
            @click="handleShowWarningModal('inbox')"
          >
            <SharedIconHubEmailsCrossIcon class="w-4 h-4" />
          </NuxtLink>
        </div>
        <ul class="flex space-x-4 mt-6">
          <li
            class="text-sm font-semibold text-[#707070]"
            v-for="settingsMenu in settingsMenus"
            :key="settingsMenu.id"
          >
            <NuxtLink
              :to="
                isSettingLinkDisable && currentPath !== settingsMenu.path
                  ? ''
                  : settingsMenu.path
              "
              class="py-2 rounded-full cursor-pointer"
              :class="[accountProvider === 'Google' ? 'px-4' : 'px-6']"
              @click="handleShowWarningModal(settingsMenu.path)"
              >{{ settingsMenu.title }}</NuxtLink
            >
          </li>
        </ul>
      </div>
    </div>
    <NuxtPage class="h-[calc(100%-107px)] custom-scroll" />
    <Transition name="page" mode="out-in">
      <div v-if="showSignatureModal || editSignatureName">
        <SourceHubEmailsSettingsSignatureModal />
        <div
          @click.stop="store.commit('emails/SHOW_HIDE_EMAIL_SIGNATURE', false)"
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
    <Transition name="page" mode="out-in">
      <div v-if="dynamicEmailModal">
        <SourceHubEmailsSettingsPreviewDynamicEmailsModal />
        <div
          @click.stop="store.commit('emails/SHOW_DYNAMIC_EMAILS_MODAL', false)"
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
    <Transition name="page" mode="out-in">
      <div v-if="createNewFilterModal.isOpen">
        <SourceHubEmailsSettingsCreateNewFilterModal />
        <div
          @click.stop="
            store.commit('emails/SET_CREATE_NEW_FILTER_MODAL', {
              isOpen: false,
              type: '',
              for: '',
            })
          "
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
    <Transition name="page" mode="out-in">
      <div v-if="deleteFilterModal.isOpen">
        <SourceHubEmailsSettingsDeleteFilterModal />
        <div
          @click.stop="
            store.commit('emails/SET_DELETE_FILTER_MODAL', {
              isOpen: false,
              type: '',
            })
          "
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
    <Transition name="page" mode="out-in">
      <div v-if="importOutlookEmailModal">
        <SourceHubMicrosoftModalImportOutlookEmail />
        <div
          @click.stop="store.commit('outlook/SET_IMPORT_EMAIL_MODAL', false)"
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
    <Transition name="page" mode="out-in">
      <div v-if="unblockAddressModal.isOpen">
        <SourceHubEmailsSettingsUnblockAddressModal />
        <div
          @click.stop="
            store.commit('emails/SET_UNBLOCK_ADDRESS_MODAL', {
              isOpen: false,
              type: '',
            })
          "
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
    <Transition name="page" mode="out-in">
      <div v-if="removeEmailModal">
        <SourceHubEmailsSettingsRemoveEmailModal />
        <div
          @click.stop="
            store.commit('emails/SET_REMOVE_EMAIL_MODAL', {
              isOpen: false,
              type: '',
              email: null,
            })
          "
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
    <Transition name="page" mode="out-in">
      <div v-if="forwardingAddressModal">
        <SourceHubEmailsSettingsForwardingAddressModal />
        <div
          @click.stop="
            store.commit('emails/SET_FORWARDING_ADDRESS_MODAL', false)
          "
          class="fixed inset-0 transition-opacity z-[10]"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
    <Transition name="page" mode="out-in">
      <div v-if="showImportMailModal">
        <SourceHubEmailsSettingsAccountsImportMailModal />
        <div
          @click.stop="
            store.commit('emails/SET_SHOW_IMPORT_MAIL_MODAL', {
              show: false,
              email: '',
            })
          "
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
    <Transition name="page" mode="out-in">
      <div v-if="showAddAccountModal">
        <SourceHubEmailsSettingsAccountsAddEmailAccountModal />
        <div
          @click.stop="
            store.commit('emails/SET_SHOW_ADD_ACCOUNT_MODAL', {
              show: false,
              email: '',
            })
          "
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
    <Transition name="page" mode="out-in">
      <div v-if="showAnotherEmailModal">
        <SourceHubEmailsSettingsAccountsAnotherEmailModal />
        <div
          @click.stop="
            store.commit('emails/SET_SHOW_ANOTHER_EMAIL_MODAL', {
              show: false,
              email: '',
            })
          "
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
    <Transition name="page" mode="out-in">
      <div v-if="showGrantAccountModal">
        <SourceHubEmailsSettingsAccountsGrantAccountModal />
        <div
          @click.stop="
            store.commit('emails/SHOW_GRANT_ACCOUNT_MODAL', {
              show: false,
              email: '',
            })
          "
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
    <Transition name="page" mode="out-in">
      <div v-if="showGrantAccountDeleteModal">
        <SourceHubEmailsSettingsAccountsGrantAccountDeleteModal />
        <div
          @click.stop="
            store.commit('emails/SHOW_GRANT_ACCOUNT_DELETE_MODAL', {
              show: false,
              grantAccount: null,
              id: '',
            })
          "
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
    <Transition name="page" mode="out-in">
      <div v-if="showNewCategoryModal">
        <SourceHubMicrosoftSettingsMailRulesCreateNewCategory />
        <div
          @click.stop="
            store.commit('emails/SET_SHOW_NEW_CATEGORY_MODAL', false)
          "
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<style scoped>
.router-link-exact-active,
.router-link-active {
  background: #4a71d4;
  color: #ffffff;
}
</style>
