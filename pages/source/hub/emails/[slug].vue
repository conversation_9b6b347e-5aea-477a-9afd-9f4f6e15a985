<script setup lang="ts">
import { useStore } from 'vuex'

// definePageMeta({
//   pageTransition: false,
// })
const store = useStore()
onMounted(() => {
  document.addEventListener('click', () => {
    store.commit('emails/SET_CLOSE_ALL_MENUS', false)
  })
})
onUnmounted(() => {
  document.removeEventListener('click', () => {
    store.commit('emails/SET_CLOSE_ALL_MENUS', false)
  })
})
</script>

<template>
  <div class="w-full h-full flex flex-col overflow-hidden relative">
    <SourceHubSocialsTheHeader />
    <NuxtPage />
  </div>
</template>
