<script setup lang="ts">
import { Editor, EditorContent } from '@tiptap/vue-3'
import { useStore } from 'vuex'

interface Props {
  editor?: Editor | null
}

const props = withDefaults(defineProps<Props>(), {
  editor: null,
})

const store = useStore()

const accountItem = computed(() => {
  return store.state.social.accountItem
})
</script>

<template>
  <div class="w-full h-auto">
    <SourceHubEmailsSingleMessage v-if="accountItem.provider === 'Google'" />
    <SourceHubMicrosoftSingleMessage
      v-else-if="accountItem.provider === 'Microsoft'"
      :editor="editor"
    />
  </div>
</template>

<style scoped></style>
