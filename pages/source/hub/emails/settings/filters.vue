<script setup lang="ts">
import { useStore } from 'vuex'
import BlockedAddress from '~/components/source/hub/emails/settings/filters/BlockedAddress.vue'
import ImportFilters from '~/components/source/hub/emails/settings/filters/ImportFilters.vue'
import SingleFilter from '~/components/source/hub/emails/settings/filters/SingleFilter.vue'

const store = useStore()

const filters = computed(() => store.state.emails.filters)

const showImports = ref(false)

const isExportDeleteEnabled = computed(() => {
  return filters.value.some((filter: any) => filter.isChecked)
})

const handleOpenCreateNewFilterModal = () => {
  store.commit('emails/SET_CREATE_NEW_FILTER_MODAL', {
    isOpen: true,
    type: 'create',
    for: 'settings',
  })
}

const handleOpenEditFilterModal = (filter: any) => {
  store.commit('emails/SET_CREATE_NEW_FILTER_MODAL', {
    isOpen: true,
    type: 'edit',
    for: 'settings',
  })
  store.commit('emails/SET_SELECTED_FILTER', filter)
}

const selectAllFilters = () => {
  filters.value.forEach((filter: any) => {
    filter.isChecked = true
  })
}
const deSelectAllFilters = () => {
  filters.value.forEach((filter: any) => {
    filter.isChecked = false
  })
}

const openDeleteFilterModal = (item: { type: string; id?: any }) => {
  store.commit('emails/SET_DELETE_FILTER_MODAL', {
    isOpen: true,
    ...item,
  })
}
</script>

<template>
  <div class="w-full flex flex-col px-6 pb-4">
    <div class="border-b-[2px] border-[#F1F2F6]">
      <p class="text-base font-semibold text-[#333333] leading-[21px] pt-3.5">
        The following filters are applied to all incoming mail:
      </p>
      <div
        v-if="filters && filters.length > 0"
        class="flex flex-col border-t mt-[22px]"
      >
        <div
          v-for="filter in filters"
          :key="filter.id"
          class="flex items-center justify-between border-b py-[15px] space-x-4"
        >
          <div class="flex items-center space-x-4">
            <InputsCheckBoxInput
              :id="filter.id"
              v-model="filter.isChecked"
              checkColor="#4A71D4"
            />
            <SingleFilter :filter="filter" />
          </div>
          <div class="flex items-center space-x-2">
            <button
              @click="handleOpenEditFilterModal(filter)"
              class="text-[#3964D0]"
            >
              Edit
            </button>
            <button
              @click="openDeleteFilterModal({ type: 'single', id: filter.id })"
              class="text-[#3964D0]"
            >
              Delete
            </button>
          </div>
        </div>
      </div>

      <div class="w-full mt-[30px]">
        <div class="flex items-center text-base leading-[21px]">
          <span class="text-[#333333] mr-1">Select: </span>
          <button @click="selectAllFilters" class="text-[#3964D0]">All</button>
          <span>,</span>
          <button @click="deSelectAllFilters" class="text-[#3964D0] ml-1">
            None
          </button>
        </div>
        <div class="flex items-center space-x-2 mt-2">
          <button
            :disabled="!isExportDeleteEnabled"
            class="h-[33px] w-[104px] rounded-full text-[#333333] bg-[#F1F2F6] text-center hover:bg-[#d8d9dd]/50"
            :class="{ 'opacity-60': !isExportDeleteEnabled }"
          >
            Export
          </button>
          <button
            :disabled="!isExportDeleteEnabled"
            @click="openDeleteFilterModal({ type: 'multiple', id: '' })"
            class="h-[33px] w-[104px] rounded-full text-[#333333] bg-[#F1F2F6] text-center hover:bg-[#d8d9dd]/50"
            :class="{ 'opacity-60': !isExportDeleteEnabled }"
          >
            Delete
          </button>
        </div>
      </div>
      <div class="flex items-center justify-center space-x-4 mt-[22px] mb-4">
        <button
          @click="handleOpenCreateNewFilterModal"
          class="text-[#3964D0] text-base leading-[21px]"
        >
          Create a new filter
        </button>
        <button
          @click="showImports = true"
          class="text-[#3964D0] text-base leading-[21px]"
        >
          Import filters
        </button>
      </div>
      <ImportFilters v-model="showImports" />
    </div>
    <BlockedAddress />
  </div>
</template>

<style scoped></style>
