<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const systemLabelsOptions = computed(
  () => store.state.emails.systemLabelsOptions,
)
const categoriesLabelsOptions = computed(
  () => store.state.emails.categoriesLabelsOptions,
)
const categoriesMessageOptions = computed(
  () => store.state.emails.categoriesMessageOptions,
)
const createdLabelOptions = computed(
  () => store.state.emails.createdLabelOptions,
)
const createdLabelMessageOptions = computed(
  () => store.state.emails.createdLabelMessageOptions,
)
const createdLabelActionOptions = computed(
  () => store.state.emails.createdLabelActionOptions,
)
const labelsItems = computed(() => store.state.emails.labelsItems)
const showEditLabelModal = computed(() => store.state.emails.showEditLabelModal)
const changeSelectedText = (id: number, selectedText: string) => {
  store.commit('emails/SET_CHANGE_SELECTED_TEXT', { id, selectedText })
}
</script>

<template>
  <div class="px-6">
    <div class="py-4 px-4 border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[250px_1fr]">
        <ul>
          <li class="text-[#333333] font-semibold">System labels</li>
          <li class="mt-[18px]">
            <ul class="flex flex-col space-y-[22px]">
              <li
                v-for="systemLabelsOption in systemLabelsOptions"
                :key="systemLabelsOption.id"
                class="text-[#333333]"
              >
                {{ systemLabelsOption.text }}
              </li>
            </ul>
          </li>
        </ul>
        <ul>
          <li class="text-[#333333] font-semibold">Show in label list</li>
          <li class="mt-[18px]">
            <ul class="flex flex-col space-y-[22px]">
              <li
                v-for="systemLabelsOption in systemLabelsOptions"
                :key="systemLabelsOption.id"
                class="text-[#333333] h-6"
              >
                <div v-if="systemLabelsOption.id !== 1" class="flex space-x-4">
                  <button
                    :class="
                      systemLabelsOption.selected === 'show'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      store.commit('emails/SET_MENU_ITEMS_SHOW_HIDE', {
                        text: systemLabelsOption.text,
                        selected: 'show',
                      }),
                        changeSelectedText(systemLabelsOption.id, 'show')
                    "
                  >
                    {{ systemLabelsOption.showText }}
                  </button>
                  <button
                    :class="
                      systemLabelsOption.selected === 'hide'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      store.commit('emails/SET_MENU_ITEMS_SHOW_HIDE', {
                        text: systemLabelsOption.text,
                        selected: 'hide',
                      }),
                        changeSelectedText(systemLabelsOption.id, 'hide')
                    "
                  >
                    {{ systemLabelsOption.hideText }}
                  </button>
                  <button
                    v-if="systemLabelsOption.unreadText"
                    :class="
                      systemLabelsOption.selected === 'show if unread'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      store.commit('emails/SET_MENU_ITEMS_SHOW_HIDE', {
                        text: systemLabelsOption.text,
                        selected: 'show if unread',
                      }),
                        changeSelectedText(
                          systemLabelsOption.id,
                          'show if unread',
                        )
                    "
                  >
                    {{ systemLabelsOption.unreadText }}
                  </button>
                </div>
              </li>
            </ul>
          </li>
        </ul>
      </div>
    </div>
    <div class="py-4 px-4 border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[250px_300px_1fr]">
        <ul>
          <li class="text-[#333333] font-semibold">Categories</li>
          <li class="mt-[18px]">
            <ul class="flex flex-col space-y-[22px]">
              <li
                v-for="categoriesLabelsOption in categoriesLabelsOptions"
                :key="categoriesLabelsOption.id"
                class="text-[#333333]"
              >
                {{ categoriesLabelsOption.text }}
              </li>
            </ul>
          </li>
        </ul>
        <ul>
          <li class="text-[#333333] font-semibold">Show in label list</li>
          <li class="mt-[18px]">
            <ul class="flex flex-col space-y-[22px]">
              <li
                v-for="categoriesLabelsOption in categoriesLabelsOptions"
                :key="categoriesLabelsOption.id"
                class="text-[#333333] h-6"
              >
                <div class="flex space-x-4">
                  <button
                    :class="
                      categoriesLabelsOption.selected === 'show'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      changeSelectedText(categoriesLabelsOption.id, 'show')
                    "
                  >
                    {{ categoriesLabelsOption.showText }}
                  </button>
                  <button
                    :class="
                      categoriesLabelsOption.selected === 'hide'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      changeSelectedText(categoriesLabelsOption.id, 'hide')
                    "
                  >
                    {{ categoriesLabelsOption.hideText }}
                  </button>
                </div>
              </li>
            </ul>
          </li>
        </ul>
        <ul>
          <li class="text-[#333333] font-semibold">Show in message list</li>
          <li class="mt-[18px]">
            <ul class="flex flex-col space-y-[22px]">
              <li
                v-for="categoriesMessageOption in categoriesMessageOptions"
                :key="categoriesMessageOption.id"
                class="text-[#333333] h-6"
              >
                <div
                  v-if="categoriesMessageOption.id !== 1"
                  class="flex space-x-4"
                >
                  <button
                    :class="
                      categoriesMessageOption.selected === 'show'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      changeSelectedText(categoriesMessageOption.id, 'show')
                    "
                  >
                    {{ categoriesMessageOption.showText }}
                  </button>
                  <button
                    :class="
                      categoriesMessageOption.selected === 'hide'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      changeSelectedText(categoriesMessageOption.id, 'hide')
                    "
                  >
                    {{ categoriesMessageOption.hideText }}
                  </button>
                </div>
              </li>
            </ul>
          </li>
        </ul>
      </div>
    </div>
    <!-- <div class="py-4 px-4 border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[250px_300px_300px_1fr]">
        <ul>
          <li class="text-[#333333] font-semibold">
            <p>Labels</p>
            <button
              class="mt-2 flex justify-center items-center bg-[#F1F2F6] border-[1px] border-[#C2C2C2] w-[142px] h-[33px] rounded-full text-[#525252] text-sm"
              @click.stop="store.commit('emails/SET_NEW_LABEL_MODAL', true)"
            >
              Create new labels
            </button>
          </li>
          <li
            v-if="createdLabelOptions && createdLabelOptions.length > 0"
            class="mt-[18px]"
          >
            <ul class="flex flex-col space-y-[22px]">
              <li
                v-for="createdLabelOption in createdLabelOptions"
                :key="createdLabelOption.id"
                class="text-[#333333]"
              >
                {{ createdLabelOption.text }}
              </li>
            </ul>
          </li>
        </ul>
        <ul>
          <li class="text-[#333333] font-semibold">
            <p>Show in label list</p>
            <div class="mt-2 w-[142px] h-[33px]"></div>
          </li>
          <li
            v-if="createdLabelOptions && createdLabelOptions.length > 0"
            class="mt-[18px]"
          >
            <ul class="flex flex-col space-y-[22px]">
              <li
                v-for="createdLabelOption in createdLabelOptions"
                :key="createdLabelOption.id"
                class="text-[#333333] h-6"
              >
                <div class="flex space-x-4">
                  <button
                    :class="
                      createdLabelOption.selected === 'show'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      store.commit('emails/SET_LABEL_ITEMS_SHOW_HIDE', {
                        text: createdLabelOption.text,
                        selected: 'show',
                      }),
                        store.commit('emails/SET_LABEL_SELECTED_TEXT', {
                          id: createdLabelOption.id,
                          selectedText: 'show',
                        })
                    "
                  >
                    {{ createdLabelOption.showText }}
                  </button>
                  <button
                    :class="
                      createdLabelOption.selected === 'hide'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      store.commit('emails/SET_LABEL_ITEMS_SHOW_HIDE', {
                        text: createdLabelOption.text,
                        selected: 'hide',
                      }),
                        store.commit('emails/SET_LABEL_SELECTED_TEXT', {
                          id: createdLabelOption.id,
                          selectedText: 'hide',
                        })
                    "
                  >
                    {{ createdLabelOption.hideText }}
                  </button>
                </div>
              </li>
            </ul>
          </li>
        </ul>
        <ul>
          <li class="text-[#333333] font-semibold">
            <p>Show in message list</p>
            <div class="mt-2 w-[142px] h-[33px]"></div>
          </li>
          <li
            v-if="
              createdLabelMessageOptions &&
              createdLabelMessageOptions.length > 0
            "
            class="mt-[18px]"
          >
            <ul class="flex flex-col space-y-[22px]">
              <li
                v-for="createdLabelMessageOption in createdLabelMessageOptions"
                :key="createdLabelMessageOption.id"
                class="text-[#333333] h-6"
              >
                <div class="flex space-x-4">
                  <button
                    :class="
                      createdLabelMessageOption.selected === 'show'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      store.commit('emails/SET__MESSAGE_LABEL_SELECTED_TEXT', {
                        id: createdLabelMessageOption.id,
                        selectedText: 'hide',
                      })
                    "
                  >
                    {{ createdLabelMessageOption.showText }}
                  </button>
                  <button
                    :class="
                      createdLabelMessageOption.selected === 'hide'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      store.commit('emails/SET__MESSAGE_LABEL_SELECTED_TEXT', {
                        id: createdLabelMessageOption.id,
                        selectedText: 'hide',
                      })
                    "
                  >
                    {{ createdLabelMessageOption.hideText }}
                  </button>
                </div>
              </li>
            </ul>
          </li>
        </ul>
        <ul>
          <li class="text-[#333333] font-semibold">
            <p>Actions</p>
            <div class="mt-2 w-[142px] h-[33px]"></div>
          </li>
          <li
            v-if="
              createdLabelActionOptions && createdLabelActionOptions.length > 0
            "
            class="mt-[18px]"
          >
            <ul class="flex flex-col space-y-[22px]">
              <li
                v-for="createdLabelActionOption in createdLabelActionOptions"
                :key="createdLabelActionOption.id"
                class="text-[#333333] h-6"
              >
                <div class="flex space-x-4">
                  <button
                    :class="
                      createdLabelActionOption.selected === 'remove'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      store.commit('emails/SET_NEW_LABEL', {
                        id: createdLabelActionOption.id,
                        text: createdLabelActionOption.text,
                      }),
                        store.commit('emails/SET_ACTION_LABEL_SELECTED_TEXT', {
                          id: createdLabelActionOption.id,
                          selectedText: 'remove',
                        }),
                        store.commit(
                          'emails/SET_REMOVE_CONFIRMATION_MODAL',
                          true,
                        )
                    "
                  >
                    {{ createdLabelActionOption.showText }}
                  </button>
                  <button
                    :class="
                      createdLabelActionOption.selected === 'edit'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      store.commit('emails/SET_NEW_LABEL', {
                        id: createdLabelActionOption.id,
                        text: createdLabelActionOption.text,
                      }),
                        store.commit('emails/SET_ACTION_LABEL_SELECTED_TEXT', {
                          id: createdLabelActionOption.id,
                          selectedText: 'edit',
                        }),
                        store.commit('emails/SET_EDIT_LABEL_MODAL', true)
                    "
                  >
                    {{ createdLabelActionOption.hideText }}
                  </button>
                </div>
              </li>
            </ul>
          </li>
        </ul>
      </div>
      <div class="grid grid-cols-[250px_300px_300px_1fr]">
        <ul>
          <li class="text-[#333333] font-semibold">
            <p>Labels</p>
            <button
              class="mt-2 flex justify-center items-center bg-[#F1F2F6] border-[1px] border-[#C2C2C2] w-[142px] h-[33px] rounded-full text-[#525252] text-sm"
              @click.stop="store.commit('emails/SET_NEW_LABEL_MODAL', true)"
            >
              Create new labels
            </button>
          </li>
          <li
            v-if="createdLabelOptions && createdLabelOptions.length > 0"
            class="mt-[18px]"
          >
            <ul class="flex flex-col space-y-[22px]">
              <li
                v-for="createdLabelOption in createdLabelOptions"
                :key="createdLabelOption.id"
                class="text-[#333333]"
              >
                <p>{{ createdLabelOption.text }}</p>
                <ul
                  v-if="
                    createdLabelOption.nested &&
                    createdLabelOption.nested.length > 0
                  "
                  class="flex flex-col space-y-[22px]"
                >
                  <li
                    v-for="nested in createdLabelOption.nested"
                    :key="nested.id"
                    class="text-[#333333]"
                  >
                    {{ nested.text }}
                  </li>
                </ul>
              </li>
            </ul>
          </li>
        </ul>
        <ul>
          <li class="text-[#333333] font-semibold">
            <p>Show in label list</p>
            <div class="mt-2 w-[142px] h-[33px]"></div>
          </li>
          <li
            v-if="createdLabelOptions && createdLabelOptions.length > 0"
            class="mt-[18px]"
          >
            <ul class="flex flex-col space-y-[22px] mt-[22px]">
              <li
                v-for="createdLabelOption in createdLabelOptions"
                :key="createdLabelOption.id"
                class="text-[#333333] h-6"
              >
                <div class="flex space-x-4">
                  <button
                    :class="
                      createdLabelOption.selected === 'show'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      store.commit('emails/SET_LABEL_ITEMS_SHOW_HIDE', {
                        text: createdLabelOption.text,
                        selected: 'show',
                      }),
                        store.commit('emails/SET_LABEL_SELECTED_TEXT', {
                          id: createdLabelOption.id,
                          selectedText: 'show',
                        })
                    "
                  >
                    {{ createdLabelOption.showText }}
                  </button>
                  <button
                    :class="
                      createdLabelOption.selected === 'hide'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      store.commit('emails/SET_LABEL_ITEMS_SHOW_HIDE', {
                        text: createdLabelOption.text,
                        selected: 'hide',
                      }),
                        store.commit('emails/SET_LABEL_SELECTED_TEXT', {
                          id: createdLabelOption.id,
                          selectedText: 'hide',
                        })
                    "
                  >
                    {{ createdLabelOption.hideText }}
                  </button>
                </div>
              </li>
            </ul>
          </li>
        </ul>
        <ul>
          <li class="text-[#333333] font-semibold">
            <p>Show in message list</p>
            <div class="mt-2 w-[142px] h-[33px]"></div>
          </li>
          <li
            v-if="
              createdLabelMessageOptions &&
              createdLabelMessageOptions.length > 0
            "
            class="mt-[18px]"
          >
            <ul class="flex flex-col space-y-[22px]">
              <li
                v-for="createdLabelMessageOption in createdLabelMessageOptions"
                :key="createdLabelMessageOption.id"
                class="text-[#333333] h-6"
              >
                <div class="flex space-x-4">
                  <button
                    :class="
                      createdLabelMessageOption.selected === 'show'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      store.commit('emails/SET__MESSAGE_LABEL_SELECTED_TEXT', {
                        id: createdLabelMessageOption.id,
                        selectedText: 'hide',
                      })
                    "
                  >
                    {{ createdLabelMessageOption.showText }}
                  </button>
                  <button
                    :class="
                      createdLabelMessageOption.selected === 'hide'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      store.commit('emails/SET__MESSAGE_LABEL_SELECTED_TEXT', {
                        id: createdLabelMessageOption.id,
                        selectedText: 'hide',
                      })
                    "
                  >
                    {{ createdLabelMessageOption.hideText }}
                  </button>
                </div>
                <ul
                  v-if="
                    createdLabelMessageOption.nested &&
                    createdLabelMessageOption.nested.length > 0
                  "
                  class="flex flex-col space-y-[22px]"
                >
                  <li
                    v-for="nested in createdLabelMessageOption.nested"
                    :key="nested.id"
                    class="text-[#333333] h-6"
                  >
                    <div class="flex space-x-4">
                      <button
                        :class="
                          nested.selected === 'show'
                            ? 'text-[#333333] font-semibold'
                            : 'text-[#3964D0]'
                        "
                        @click="
                          store.commit(
                            'emails/SET__MESSAGE_LABEL_SELECTED_TEXT',
                            {
                              id: nested.id,
                              selectedText: 'hide',
                            },
                          )
                        "
                      >
                        {{ nested.showText }}
                      </button>
                      <button
                        :class="
                          nested.selected === 'hide'
                            ? 'text-[#333333] font-semibold'
                            : 'text-[#3964D0]'
                        "
                        @click="
                          store.commit(
                            'emails/SET__MESSAGE_LABEL_SELECTED_TEXT',
                            {
                              id: nested.id,
                              selectedText: 'hide',
                            },
                          )
                        "
                      >
                        {{ nested.hideText }}
                      </button>
                    </div>
                  </li>
                </ul>
              </li>
            </ul>
          </li>
        </ul>
        <ul>
          <li class="text-[#333333] font-semibold">
            <p>Actions</p>
            <div class="mt-2 w-[142px] h-[33px]"></div>
          </li>
          <li
            v-if="
              createdLabelActionOptions && createdLabelActionOptions.length > 0
            "
            class="mt-[18px]"
          >
            <ul class="flex flex-col space-y-[22px]">
              <li
                v-for="createdLabelActionOption in createdLabelActionOptions"
                :key="createdLabelActionOption.id"
                class="text-[#333333] h-6"
              >
                <div class="flex space-x-4">
                  <button
                    :class="
                      createdLabelActionOption.selected === 'remove'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      store.commit('emails/SET_NEW_LABEL', {
                        id: createdLabelActionOption.id,
                        text: createdLabelActionOption.text,
                      }),
                        store.commit('emails/SET_ACTION_LABEL_SELECTED_TEXT', {
                          id: createdLabelActionOption.id,
                          selectedText: 'remove',
                        }),
                        store.commit(
                          'emails/SET_REMOVE_CONFIRMATION_MODAL',
                          true,
                        )
                    "
                  >
                    {{ createdLabelActionOption.showText }}
                  </button>
                  <button
                    :class="
                      createdLabelActionOption.selected === 'edit'
                        ? 'text-[#333333] font-semibold'
                        : 'text-[#3964D0]'
                    "
                    @click="
                      store.commit('emails/SET_NEW_LABEL', {
                        id: createdLabelActionOption.id,
                        text: createdLabelActionOption.text,
                      }),
                        store.commit('emails/SET_ACTION_LABEL_SELECTED_TEXT', {
                          id: createdLabelActionOption.id,
                          selectedText: 'edit',
                        }),
                        store.commit('emails/SET_EDIT_LABEL_MODAL', true)
                    "
                  >
                    {{ createdLabelActionOption.hideText }}
                  </button>
                </div>
                <ul
                  v-if="
                    createdLabelActionOption.nested &&
                    createdLabelActionOption.nested.length > 0
                  "
                  class="flex flex-col space-y-[22px]"
                >
                  <li
                    v-for="nested in createdLabelActionOption.nested"
                    :key="nested.id"
                    class="text-[#333333] h-6"
                  >
                    <div class="flex space-x-4">
                      <button
                        :class="
                          nested.selected === 'remove'
                            ? 'text-[#333333] font-semibold'
                            : 'text-[#3964D0]'
                        "
                        @click="
                          store.commit('emails/SET_NEW_LABEL', {
                            id: nested.id,
                            text: nested.text,
                          }),
                            store.commit(
                              'emails/SET_ACTION_LABEL_SELECTED_TEXT',
                              {
                                id: nested.id,
                                selectedText: 'remove',
                              },
                            ),
                            store.commit(
                              'emails/SET_REMOVE_CONFIRMATION_MODAL',
                              true,
                            )
                        "
                      >
                        {{ nested.showText }}
                      </button>
                      <button
                        :class="
                          nested.selected === 'edit'
                            ? 'text-[#333333] font-semibold'
                            : 'text-[#3964D0]'
                        "
                        @click="
                          store.commit('emails/SET_NEW_LABEL', {
                            id: nested.id,
                            text: nested.text,
                          }),
                            store.commit(
                              'emails/SET_ACTION_LABEL_SELECTED_TEXT',
                              {
                                id: nested.id,
                                selectedText: 'edit',
                              },
                            ),
                            store.commit('emails/SET_EDIT_LABEL_MODAL', true)
                        "
                      >
                        {{ nested.hideText }}
                      </button>
                    </div>
                  </li>
                </ul>
              </li>
            </ul>
          </li>
        </ul>
      </div>
    </div> -->
    <SourceHubEmailsSettingsLabelsLabelList />
  </div>
</template>

<style scoped></style>
