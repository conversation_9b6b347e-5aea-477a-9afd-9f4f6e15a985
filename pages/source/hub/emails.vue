<script setup lang="ts">
import { useStore } from 'vuex'
// definePageMeta({
//   pageTransition: false,
// })
const store = useStore()
const route = useRoute()
const showNewLabelModal = computed(() => store.state.emails.showNewLabelModal)
const showEditLabelModal = computed(() => store.state.emails.showEditLabelModal)
const showRemoveConfirmationModal = computed(
  () => store.state.emails.showRemoveConfirmationModal,
)
</script>

<template>
  <div
    class="w-full h-full overflow-hidden"
    :class="route.name.includes('source-hub-emails-settings') ? '' : 'relative'"
  >
    <NuxtPage />
    <SourceHubEmailsSettingsReportSpamModal />
    <Transition name="page" mode="out-in">
      <div
        v-if="
          showNewLabelModal || showEditLabelModal || showRemoveConfirmationModal
        "
      >
        <SourceHubEmailsSettingsLabelsNewEditLabelModal />
        <div
          @click.stop="
            store.commit('emails/SET_NEW_LABEL_MODAL', false),
              store.commit('emails/SET_EDIT_LABEL_MODAL', false)
          "
          class="fixed inset-0 transition-opacity z-[11]"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
  </div>
</template>
