<script setup lang="ts">
import { useStore } from 'vuex'
import PublishPostModal from '~/components/source/create-post/PublishPostModal.vue'

const store = useStore()
const currentTab = computed<string>(() => store.state.createPost.currentTab)

onBeforeUnmount(() => {
  store.commit('createPost/REVOKE_OBJECT_URLS')
})
</script>

<template>
  <div class="w-full h-full pt-6 overflow-hidden">
    <div
      class="w-full h-full flex flex-col xl:flex-row gap-4 rounded-2xl overflow-hidden"
    >
      <div
        class="w-full xl:w-[68%] h-full rounded-2xl flex flex-col overflow-hidden shadow-[3px_3px_6px_#2E2B2B4D]"
      >
        <div
          class="w-full h-[35px] bg-blue-200 flex justify-center items-center text-base text-white font-bold"
        >
          {{ currentTab === 'Text' ? 'Generate Post' : 'Generate Script' }}
        </div>
        <div
          class="w-full h-[calc(100%-35px)] flex flex-row flex-grow bg-white"
        >
          <SourceCreatePostSidebar />
          <Transition name="page" mode="out-in">
            <!-- <KeepAlive> -->
            <SourceCreatePostText v-if="currentTab === 'Text'" />
            <SourceCreatePostVideo v-else />
            <!-- </KeepAlive> -->
          </Transition>
        </div>
      </div>

      <div
        class="w-full xl:w-[32%] h-full rounded-2xl flex flex-col overflow-hidden shadow-[3px_3px_6px_#2E2B2B4D]"
      >
        <div
          class="w-full h-[35px] bg-blue-200 flex justify-center items-center text-base text-white font-bold"
        >
          Preview
        </div>
        <div class="w-full h-[calc(100%-35px)] flex-grow bg-[#F1F2F6]">
          <SourceCreatePostPreview />
        </div>
      </div>
    </div>
    <PublishPostModal />
  </div>
</template>
