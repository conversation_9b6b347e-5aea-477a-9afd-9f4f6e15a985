<template>
  <section id="mainBody" class="h-full">
    <ClientOnly>
      <div
        v-if="isDesktop"
        class="md:flex md:flex-col hidden card-wrapper px-2"
      >
        <div
          id="userInfoLarge"
          class="card transition-all duration-500 ease-in-out"
          :class="
            !expandCollapseFunctionality
              ? user.userPermission === 'Administrator'
                ? 'h-1/2'
                : 'h-full'
              : userInfoIsExpanded
                ? config.public.workflow !== 'dev' ||
                  config.public.workflow !== 'live' ||
                  user.userPermission !== 'Administrator'
                  ? 'h-full'
                  : 'new-expand-height'
                : 'collapse-height'
          "
        >
          <div
            class="card-header flex justify-center items-center px-4"
            :class="expandCollapseFunctionality ? 'cursor-pointer' : ''"
            @click="expandCollapseFunctionality ? toggleExpand('UserInfo') : ''"
          >
            <h3 class="card-title flex-grow pl-5">User and Information</h3>
            <span
              v-if="
                expandCollapseFunctionality &&
                user.userPermission === 'Administrator'
              "
              class="toggle-icon transition-all duration-500 transform"
              :class="userInfoIsExpanded ? 'rotate-180' : 'rotate-0'"
            >
              <ClientOnly>
                <fa :icon="['fas', 'caret-down']" />
              </ClientOnly>
            </span>
          </div>
          <div
            class="inner-body-height transition-all duration-300 ease-in-out"
          >
            <div class="card-body web-user_info scroll">
              <table class="table-wrapper min-w-full">
                <!--start => table header section-->
                <thead>
                  <tr class="bg-yellow-moreLight sticky top-0 z-1 border-b">
                    <th class="table-th">
                      <strong>Name</strong>
                    </th>
                    <th class="table-th">
                      <strong>Email</strong>
                    </th>
                    <th class="table-th">
                      <strong>Phone</strong>
                    </th>
                    <th class="table-th">
                      <strong>User Permission</strong>
                    </th>
                    <!-- <th class="table-th">
                      <strong>Alerts Permission</strong>
                    </th> -->
                    <th v-if="user.userPermission !== 'User'" class="table-th">
                      <strong>Last Login</strong>
                    </th>
                    <th v-if="user.userPermission !== 'User'" class="table-th">
                      <strong>Is Expired</strong>
                    </th>
                  </tr>
                </thead>
                <!--end => table header section-->

                <!--start => table body section-->
                <tbody v-if="owner" class="body">
                  <tr class="cursor-pointer bg-gray-default">
                    <td class="table-tr">
                      <span v-if="owner.firstName && owner.lastName">
                        {{ owner.firstName + ' ' + owner.lastName }}
                      </span>
                    </td>
                    <td class="table-tr">
                      <!-- <span>{{ alluser.email | strLimit }}</span> -->
                      <span v-if="owner.email">{{ owner.email }}</span>
                    </td>
                    <td class="table-tr">
                      <span v-if="owner.phone">{{ owner.phone }}</span>
                    </td>
                    <td class="table-tr">
                      <span v-if="owner.userPermission">{{
                        owner.userPermission
                      }}</span>
                    </td>
                    <!-- <td class="table-tr">
                      <span v-if="owner.alertPermission">{{
                        owner.alertPermission
                      }}</span>
                    </td> -->
                    <td v-if="user.userPermission !== 'User'" class="table-tr">
                      <span v-if="owner.lastLogin">{{ owner.lastLogin }}</span>
                    </td>
                    <td
                      v-if="user.userPermission !== 'User'"
                      class="table-tr"
                      @click.stop=""
                    >
                      <button
                        v-if="owner.isExpired"
                        class="footer-btn !py-[0.1rem] !px-[12px] md:!text-base"
                        :disabled="addProcess"
                      >
                        Renew
                      </button>
                    </td>
                  </tr>
                </tbody>
                <tbody class="body">
                  <tr
                    v-for="(alluser, index) in allUsers"
                    :key="`${index}-user-info`"
                    :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-default'"
                    class="cursor-pointer"
                    @click="
                      user.userPermission !== 'User'
                        ? showEditProfile(allUsers[index], $event)
                        : ''
                    "
                  >
                    <td class="table-tr">
                      <span>
                        {{ alluser.firstName + ' ' + alluser.lastName }}
                      </span>
                    </td>
                    <td class="table-tr">
                      <!-- <span>{{ alluser.email | strLimit }}</span> -->
                      <span>{{ alluser.email }}</span>
                    </td>
                    <td class="table-tr">
                      <span>{{ alluser.phone }}</span>
                    </td>
                    <td class="table-tr">
                      <span>{{ alluser.userPermission }}</span>
                    </td>
                    <!-- <td class="table-tr">
                      <span>{{ alluser.alertPermission }}</span>
                    </td> -->
                    <td v-if="user.userPermission !== 'User'" class="table-tr">
                      <span>{{ alluser.lastLogin }}</span>
                    </td>
                    <td
                      v-if="user.userPermission !== 'User'"
                      class="table-tr"
                      @click.stop=""
                    >
                      <button
                        v-if="alluser.isExpired"
                        class="footer-btn !py-[0.1rem] !px-[12px] md:!text-base"
                        :disabled="addProcess"
                        @click.stop="renewToken(allUsers[index])"
                      >
                        Renew
                      </button>
                    </td>
                  </tr>
                </tbody>
                <!--end => table body section-->
              </table>
            </div>
            <div
              v-if="user.userPermission !== 'User'"
              class="flex items-center justify-end p-4 py-0 my-4"
            >
              <button class="footer-btn" @click="addNewUser($event)">
                Add New User
              </button>
            </div>
            <!-- <div
            id="resizeIcon"
            class="absolute pr-2 bottom-0 z-30 w-full text-right block"
            @mousedown="mouseDownHandler"
            @mouseup="mouseUpHandler"
            @touchstart="touchDownHandler"
            @touchend="touchUpHandler"
          >
            <fa
              class="text-xl font-light text-yellow-primary"
              :icon="['fas', 'arrows-alt-v']"
            />
          </div>-->
          </div>
        </div>
        <div
          v-if="user.userPermission === 'Administrator'"
          class="w-full flex-grow transition-all duration-500 ease-in-out"
          :class="
            userInfoIsExpanded
              ? user.userPermission !== 'Administrator'
                ? 'h-[44px]'
                : 'h-108'
              : 'h-1/2'
          "
        >
          <div
            class="flex w-full h-full"
            :class="
              expandCollapseFunctionality
                ? 'flex-col space-y-5 justify-end'
                : 'flex-row space-x-4'
            "
          >
            <account-billing-info
              :expand-collapse-functionality="expandCollapseFunctionality"
              :update-billing-info="updateBillingInfo"
              :user-info-is-expanded="userInfoIsExpanded"
              :download-process="downloadProcess"
              :billing-info-expanded="billingInfoExpanded"
              :next-billing-date="nextBillingDate"
              :card-delete-process="cardDeleteProcess"
              @toggle-expand="toggleExpand($event)"
              @close-account="closeAccount()"
              @change-billing="changeBilling($event)"
              @show-invoice-modal="showInvoiceModal($event)"
              @show-change-billing-info="showChangeBillingInfo($event)"
              @delete-card="deleteCard($event)"
            ></account-billing-info>
            <account-archive-cost
              :expand-collapse-functionality="expandCollapseFunctionality"
              :update-billing-info="updateBillingInfo"
              :user-info-is-expanded="userInfoIsExpanded"
              :download-process="downloadProcess"
              :archive-cost-expanded="archiveCostExpanded"
              @toggle-expand="toggleExpand($event)"
              @close-account="closeAccount()"
              @change-billing="changeBilling($event)"
              @show-invoice-modal="showInvoiceModal($event)"
              @show-change-billing-info="showChangeBillingInfo($event)"
              @delete-card="deleteCard($event)"
              @next-billing-date="setNextBillingDate($event)"
            ></account-archive-cost>
          </div>
        </div>
      </div>

      <!-- Small Device -->
      <div v-else class="md:hidden flex flex-col mobile-card-wrapper">
        <div
          class="card transition-all duration-500 ease-in-out"
          :class="
            userInfoIsExpanded
              ? config.public.workflow !== 'dev' ||
                config.public.workflow !== 'live' ||
                user.userPermission !== 'Administrator'
                ? 'h-full'
                : 'new-expand-height'
              : 'collapse-height'
          "
        >
          <div
            class="card-header flex justify-center items-center px-4"
            @click="toggleExpand('UserInfo')"
          >
            <h3 class="card-title pl-5 flex-grow">User and Information</h3>
            <span
              v-if="user.userPermission === 'Administrator'"
              class="toggle-icon transition-all duration-500 transform"
              :class="userInfoIsExpanded ? 'rotate-180' : 'rotate-0'"
            >
              <ClientOnly>
                <fa :icon="['fas', 'caret-down']" />
              </ClientOnly>
            </span>
          </div>
          <div
            class="inner-body-height transition-all duration-300 ease-in-out"
            :class="userInfoIsExpanded ? 'opacity-100' : 'opacity-0'"
          >
            <div class="card-body scroll userinfo">
              <table class="table-wrapper min-w-full">
                <!--start => table header section-->
                <thead>
                  <tr class="bg-yellow-moreLight sticky top-0 z-1 border-b">
                    <th class="table-th">
                      <span>Name</span>
                    </th>
                    <th class="table-th">
                      <span>Email</span>
                    </th>
                    <th class="table-th">
                      <span>Phone</span>
                    </th>
                    <th class="table-th">
                      <span>User Permission</span>
                    </th>
                    <!-- <th class="table-th">
                      <span>Alerts Permission</span>
                    </th> -->
                    <th v-if="user.userPermission !== 'User'" class="table-th">
                      <strong>Last Login</strong>
                    </th>
                    <th v-if="user.userPermission !== 'User'" class="table-th">
                      <strong>Is Expired</strong>
                    </th>
                  </tr>
                </thead>
                <!--end => table header section-->

                <!--start => table body section-->
                <tbody v-if="owner" class="body">
                  <tr class="cursor-pointer bg-gray-default">
                    <td class="table-tr">
                      <span v-if="owner.firstName && owner.lastName">
                        {{ owner.firstName + ' ' + owner.lastName }}
                      </span>
                    </td>
                    <td class="table-tr">
                      <span v-if="owner.email">{{ owner.email }}</span>
                    </td>
                    <td class="table-tr">
                      <span v-if="owner.phone">{{ owner.phone }}</span>
                    </td>
                    <td class="table-tr">
                      <span v-if="owner.userPermission">{{
                        owner.userPermission
                      }}</span>
                    </td>
                    <!-- <td class="table-tr">
                      <span v-if="owner.alertPermission">{{
                        owner.alertPermission
                      }}</span>
                    </td> -->
                    <td v-if="user.userPermission !== 'User'" class="table-tr">
                      <span v-if="owner.lastLogin">{{ owner.lastLogin }}</span>
                    </td>
                    <td v-if="user.userPermission !== 'User'" class="table-tr">
                      <button
                        v-if="owner.isExpired"
                        class="footer-btn !py-[0.1rem] !px-[12px] md:!text-base"
                        :disabled="addProcess"
                        @click.stop="renewToken(owner[index])"
                      >
                        Renew
                      </button>
                    </td>
                  </tr>
                </tbody>
                <tbody class="body">
                  <tr
                    v-for="(alluser, alluserIndex) in allUsers"
                    :key="`${alluserIndex}-small-user-info`"
                    :class="
                      alluserIndex % 2 === 0 ? 'bg-white' : 'bg-gray-default'
                    "
                    class="cursor-pointer"
                    @click="
                      user.userPermission !== 'User'
                        ? showEditProfile(allUsers[alluserIndex], $event)
                        : ''
                    "
                  >
                    <td class="table-tr">
                      <span>
                        {{ alluser.firstName + ' ' + alluser.lastName }}
                      </span>
                    </td>
                    <td class="table-tr">
                      <span>{{ alluser.email }}</span>
                    </td>
                    <td class="table-tr">
                      <span>{{ alluser.phone }}</span>
                    </td>
                    <td class="table-tr">
                      <span>{{ alluser.userPermission }}</span>
                    </td>
                    <!-- <td class="table-tr">
                      <span>{{ alluser.alertPermission }}</span>
                    </td> -->
                    <td v-if="user.userPermission !== 'User'" class="table-tr">
                      <span>{{ alluser.lastLogin }}</span>
                    </td>
                    <td
                      v-if="user.userPermission !== 'User'"
                      class="table-tr"
                      @click.stop=""
                    >
                      <button
                        v-if="alluser.isExpired"
                        class="footer-btn !py-[0.1rem] !px-[12px] md:!text-base"
                        :disabled="addProcess"
                        @click.stop="renewToken(allUsers[index])"
                      >
                        Renew
                      </button>
                    </td>
                  </tr>
                </tbody>
                <!--end => table body section-->
              </table>
            </div>
            <div
              v-if="user.userPermission !== 'User'"
              class="flex items-center justify-end p-4 py-0 my-4"
            >
              <button class="footer-btn" @click="addNewUser">
                Add New User
              </button>
            </div>
            <!-- <div
          id="resizeIcon"
          class="absolute pr-2 bottom-0 z-30 w-full text-right block"
          @mousedown="mouseDownHandler"
          @mouseup="mouseUpHandler"
          @touchstart="touchDownHandler"
          @touchend="touchUpHandler"
        >
          <fa
            class="text-xl font-light text-yellow-primary"
            :icon="['fas', 'arrows-alt-v']"
          />
          </div>-->
          </div>
        </div>
        <div
          v-if="user.userPermission === 'Administrator'"
          class="w-full flex-grow transition-all duration-500 ease-in-out"
          :class="
            userInfoIsExpanded
              ? user.userPermission !== 'Administrator'
                ? 'h-[44px]'
                : 'h-108'
              : 'h-1/2'
          "
        >
          <div
            class="flex w-full h-full"
            :class="
              expandCollapseFunctionality
                ? 'flex-col md:space-y-5 space-y-2 justify-end'
                : 'flex-row space-x-4'
            "
          >
            <account-billing-info
              :expand-collapse-functionality="expandCollapseFunctionality"
              :update-billing-info="updateBillingInfo"
              :user-info-is-expanded="userInfoIsExpanded"
              :download-process="downloadProcess"
              :billing-info-expanded="billingInfoExpanded"
              :next-billing-date="nextBillingDate"
              :card-delete-process="cardDeleteProcess"
              @toggle-expand="toggleExpand($event)"
              @close-account="closeAccount()"
              @change-billing="changeBilling($event)"
              @show-invoice-modal="showInvoiceModal($event)"
              @show-change-billing-info="showChangeBillingInfo($event)"
              @delete-card="deleteCard($event)"
            ></account-billing-info>
            <account-archive-cost
              :expand-collapse-functionality="expandCollapseFunctionality"
              :update-billing-info="updateBillingInfo"
              :user-info-is-expanded="userInfoIsExpanded"
              :download-process="downloadProcess"
              :archive-cost-expanded="archiveCostExpanded"
              @toggle-expand="toggleExpand($event)"
              @close-account="closeAccount()"
              @change-billing="changeBilling($event)"
              @show-invoice-modal="showInvoiceModal($event)"
              @show-change-billing-info="showChangeBillingInfo($event)"
              @delete-card="deleteCard($event)"
              @next-billing-date="setNextBillingDate($event)"
            ></account-archive-cost>
          </div>
        </div>
      </div>
    </ClientOnly>
    <!-- Modals -->
    <alert-confirm-modal
      :processing="cardDeleteProcess"
      :show="showDeleteAlert"
      @cancel="deleteCancel"
      @delete="deleteConfirm"
    ></alert-confirm-modal>

    <alert-confirm-modal
      title="Are you sure you want to close your account?"
      message="This action cannot be undone, you will be unable to recover your account, and your archive will be deleted from our system."
      confirm-btn-text="Close Account"
      cancel-btn-text="Exit"
      :processing="downloadProcess"
      :show="closeAccountConfirmModal"
      @cancel="closeAccountDeleteCancel"
      @delete="closeAccountDeleteConfirm"
    ></alert-confirm-modal>

    <password-confirmation-modal
      v-if="showPasswordConfirmModal"
      @isValidPassword="isValidPassword"
      @close="closePasswordConfirmModal"
    />
  </section>
</template>

<script setup lang="ts">
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core'
import { PAYMENT_METHOD, NEW_USER, USER_PROFILE } from '~/constants/urls'
import AlertConfirmModal from '~/components/AlertConfirmModal.vue'
import PasswordConfirmationModal from '~/components/PasswordConfirmationModal.vue'
import EditIcon from '~/components/shared/icon/EditIcon.vue'
import { useFetched } from '~/composables/useFetched'
import { useAuth } from '~/composables/useAuth'
import AccountBillingInfo from '~/components/settings/account/AccountBillingInfo.vue'
import AccountArchiveCost from '~/components/settings/account/AccountArchiveCost.vue'
import { useStore } from 'vuex'
import { useNuxtApp } from '#app'

interface UserType {
  alertPermission: string
  avatar: string
  city: string
  country: string
  device: {}
  email: string
  firstName: string
  id: number
  isExpired: boolean
  lastLogin: string
  lastName: string
  phone: string
  state: string
  streetAddress: string
  userPermission: string
  zipCode: string
}
interface $eventType {
  isTrusted: boolean
  _vts: number
  _sentryCaptured: boolean
  altKey: boolean
  altitudeAngle: number
  azimuthAngle: number
  bubbles: boolean
  button: number
  buttons: number
  cancelBubble: false
  cancelable: boolean
  clientX: number
  clientY: number
}
const props = defineProps({
  createdNewUserData: {
    type: Object,
    default: null,
  },
  updateBillingInfo: {
    type: Object,
    default: null,
  },
  newCardAdded: {
    type: Boolean,
    default: false,
  },
  updatedProfileInfo: {
    type: Object,
    default: null,
  },
  deleteNewUser: {
    type: Number,
    default: null,
  },
})
interface AllUsersType {
  friends: [UserType]
  owner: UserType
}

const allUsers = ref<AllUsersType[]>([])
const owner = ref<null>(null)
const downloadProcess = ref<boolean>(false)
const showDeleteAlert = ref<boolean>(false)
const deletedCardId = ref<string>('')
const closeAccountConfirmModal = ref<boolean>(false)
const cardDeleteProcess = ref<boolean>(false)
const showPasswordConfirmModal = ref<boolean>(false)

const nextBillingDate = ref<string>('')
const addProcess = ref<boolean>(false)
const userInfoIsExpanded = ref<boolean>(true)
const billingInfoExpanded = ref<boolean>(false)
const archiveCostExpanded = ref<boolean>(false)
const bodyHeight = ref<number>(0)
const expandCollapseFunctionality = ref<boolean>(true)

const emit = defineEmits<{
  (event: 'change-billing-Info', value: billingInfoType): void
  (event: 'showAddNewUserModal', value: UserType): void
}>()
const route = useRoute()
const router = useRouter()
const store = useStore()
const nuxtApp = useNuxtApp()
const config = useRuntimeConfig()
const breakpoints = useBreakpoints(breakpointsTailwind)
const { fetch } = useFetched()
const { logout, resetAllValue } = useAuth()
const isDesktop = breakpoints.greaterOrEqual('md')

// computed
const showBillingInfo = computed(() => store.state.setting.showBillingInfo)
const isVerified = computed(() => store.state.account.isVerified)
const billingInfo = computed(() => store.state.account.billingInfo)
const cardAdded = computed(() => store.getters['header/getCardInfo'])
const user = computed(() => store.state.auth.user)

// watch
watch(
  () => isVerified.value,
  (data) => {
    if (data) {
      showChangeBillingInfo(billingInfo.value)
    }
  },
)
watch(
  () => showBillingInfo.value,
  (data) => {
    if (data) {
      userInfoIsExpanded.value = false
    }
  },
)

watch(
  () => createdNewUserData.value,
  (data) => {
    allUsers.value.unshift(data)
  },
)

watch(
  () => props.updateBillingInfo,
  (data) => {
    // this.billingInfo = data
    getUserBillingInfo()
  },
)
watch(
  () => cardAdded.value,
  (data) => {
    if (data === true) {
      getUserBillingInfo()
    }
  },
)
watch(
  () => props.updatedProfileInfo,
  (data) => {
    if (data) {
      allUsers.value = allUsers.value.map((item) => {
        if (Number(item.id) === Number(data.id)) {
          return (item = data)
        } else {
          return item
        }
      })
    }
  },
)

watch(
  () => props.deleteNewUser,
  (id) => {
    allUsers.value = allUsers.value.filter((item) => {
      return Number(id) !== Number(item.id)
    })
  },
)
// life cycle

onMounted(async () => {
  // this.getUserBillingInfo()
  if (route.fullPath.includes('settings/account/?success')) {
    nuxtApp.$toast('success', {
      message: 'Payment Success',
      className: 'toasted-bg-archive',
    })
    router.push('/settings/account')
  } else if (route.fullPath.includes('settings/account/?cancel')) {
    nuxtApp.$toast('success', {
      message: 'Payment Canceled',
      className: 'toasted-bg-alert',
    })

    router.push('/settings/account')
  } else if (
    route.fullPath.includes('settings/account/?failed&success=false')
  ) {
    nuxtApp.$toast('success', {
      message: 'This PayPal account is already in use by another user',
      className: 'toasted-bg-alert',
    })
    router.push('/settings/account')
  }
  getAllUsers()
  resizeWindow()
  window.addEventListener('resize', resizeWindow)
  // await store.dispatch('account/fetchUserPermissons')
  // await store.dispatch('account/fetchAlertPermissons')
})
onUnmounted(() => {
  setTimeout(() => {
    window.removeEventListener('resize', resizeWindow)
  }, 2000)
})

// methods
const toggleExpand = (value: string) => {
  if (config.public.workflow === 'dev' || config.public.workflow === 'live') {
    if (value === 'UserInfo' && !userInfoIsExpanded.value) {
      userInfoIsExpanded.value = true
      billingInfoExpanded.value = false
      archiveCostExpanded.value = false
    } else if (value === 'BillingInfo' && !billingInfoExpanded.value) {
      userInfoIsExpanded.value = false
      billingInfoExpanded.value = true
      archiveCostExpanded.value = false
    } else if (value === 'ArchiveCost' && !archiveCostExpanded.value) {
      userInfoIsExpanded.value = false
      billingInfoExpanded.value = false
      archiveCostExpanded.value = true
    }
  } else {
    userInfoIsExpanded.value = !userInfoIsExpanded.value
  }
}

const setNextBillingDate = (date: string) => {
  nextBillingDate.value = date
}

const resizeWindow = () => {
  bodyHeight.value = window.innerHeight
  if (bodyHeight.value > 841 && window.innerWidth > 817) {
    expandCollapseFunctionality.value = false
  } else {
    expandCollapseFunctionality.value = true
  }
}
// Horizontal Draggable Functionality End

const showInvoiceModal = ($event: $eventType) => {
  store.commit('setting/SET_SHOW_INVOICE', true)
  $event.stopPropagation()
}
interface billingInfoType {
  addressCity: string
  addressCountry: string
  addressLine1: string
  addressLine2: string
  addressState: string
  addressZip: string
  brand: string
  expMonth: number
  expYear: number
  funding: string
  id: number
  isDefault: false
  last4: string
  methodId: string
  name: string
}
const showChangeBillingInfo = (billingInfo: billingInfoType) => {
  store.commit('profile/SET_PROFILE_MODAL', false)
  store.commit('notifications/SHOW_NOTIFICATION_MODAL', false)
  store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false)
  store.commit('setting/SET_SHOW_PROFILE', false)
  store.commit('setting/SET_CHANGE_BILLING_INFO', true)

  emit('change-billing-Info', billingInfo)
}
const changeBilling = ($event: $eventType) => {
  store.dispatch('header/setPayment')
  $event.stopPropagation()
}
const showEditProfile = (user: UserType, $event: $eventType) => {
  store.commit('profile/SET_PROFILE_MODAL', false)
  store.commit('notifications/SHOW_NOTIFICATION_MODAL', false)
  store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false)
  store.commit('setting/SET_CHANGE_BILLING_INFO', false)
  store.commit('setting/SET_SHOW_INVOICE', false)
  store.dispatch('header/removePayment')
  store.commit('setting/SET_SHOW_PROFILE', true)
  $event.stopPropagation()
  emit('showAddNewUserModal', user)
}

const addNewUser = ($event: $eventType) => {
  store.commit('setting/SET_SHOW_PROFILE', true)
  $event.stopPropagation()
  emit('showAddNewUserModal', { data: null })
}
const getUserBillingInfo = async () => {}
const closeAccountDeleteCancel = () => {
  closeAccountConfirmModal.value = false
}

const closeAccount = () => {
  showPasswordConfirmModal.value = true
  store.commit('confirm/SET_FROM_CLOSE_ACCOUNT', true)
}
const closePasswordConfirmModal = () => {
  showPasswordConfirmModal.value = false
}
const isValidPassword = () => {
  closePasswordConfirmModal()
  closeAccountConfirmModal.value = true
}
const closeAccountDeleteConfirm = async () => {
  downloadProcess.value = true
  try {
    const response = await fetch(USER_PROFILE, {
      method: 'DELETE',
      body: {
        id: user.value.id,
      },
    })
    nuxtApp.$toast('success', {
      message: 'Your account closed successfully!',
      className: 'toasted-bg-archive',
    })
    closeAccountConfirmModal.value = false

    profileLogout()
  } catch (err) {
    if (err.response.status === 411) {
      nuxtApp.$toast('success', {
        message: 'Your account closed successfully!',
        className: 'toasted-bg-archive',
      })
      store.commit('socialFeed/SET_REFUND_AMOUNT', err.response.refund)
      store.commit('socialFeed/SHOW_REFUND_MODAL', true)
    }
    console.log(err)
  } finally {
    downloadProcess.value = false
    closeAccountConfirmModal.value = false
    showPasswordConfirmModal.value = false
  }
}
const profileLogout = () => {
  show_login(true)
  nuxtApp.$toast('clear')
  store.commit('SET_LANDING_LOADER', false)
  router.push('/home')
  setTimeout(async () => {
    show_home_content(true)
    resetAllValue()
    router.push('/')

    store.commit('socialFeed/SET_REFUND_AMOUNT', {})
    store.commit('socialFeed/SHOW_REFUND_MODAL', false)
    localStorage.removeItem('lockScreen')
    store.commit('SET_LOCK_SCREEN', false)
    store.commit('header/BLUR_ACTIVE_FEED_MODAL', false)
    store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false)
    landing_content(true)
    expand_starter_modal(false)
    collapse_starter_modal(false)
    maximize_starter_modal(false)
    starter_account_maximized(false)
    updateSetupContent('SetupStarterButton')
    setTimeout(() => {
      home_wrapper(true)
    }, 300)

    setTimeout(() => {
      show_home(false)
      show_logo_text(true)
      home_menu_text(true)
      setTimeout(() => {
        all_side_menu(false)
        setTimeout(() => {
          home_side_menu(false)
          home_menu_text(false)
          setTimeout(() => {
            show_logo(false)
            setTimeout(() => {
              home_circle(true)
              sidebar_menu(false)
              sidebar_circle(false)
              width_decrese(true)
              setTimeout(() => {
                home_modal(false)
                slide_left(false)
                slide_right(true)
                login_circle(true)
                setTimeout(() => {
                  home_header(false)
                  slide_full_right(true)
                  home_circle(false)
                  home_sidebar(false)
                  setTimeout(() => {
                    after_logout(true)
                    setTimeout(() => {
                      text_loading(true)
                      setTimeout(() => {
                        successfully(false)
                        notsuccessfully(true)
                        setTimeout(() => {
                          setTimeout(() => {
                            login_button_transition(false)
                            submit_button_transition(false)
                            login_form_transition(false)
                            setTimeout(() => {
                              after_logout(false)
                              text_loading(false)
                              after_loading(false)
                              width_increase(false)
                              full_width(false)
                              header_text(false)
                              loading_text(false)
                              width_decrese(false)
                              slide_right(false)
                              slide_full_right(false)
                            }, 1000)
                          }, 300)
                        }, 500)
                      }, 1800)
                    }, 800)
                  }, 800)
                }, 800)
              }, 600)
            }, 500)
          }, 0)
        }, 0)
      }, 800)
    }, 200)
  }, 600)

  set_header_width(false)
  setIsSticky(false)
}
interface getAllUsersType {
  data: { friends: [UserType]; owner: UserType }
  success: boolean
}
const getAllUsers = async () => {
  try {
    const { success, data } = (await fetch(NEW_USER)) as getAllUsersType
    if (success) {
      allUsers.value = data.friends
      owner.value = data.owner
    }
  } catch (err) {
    console.log(err)
  }
}

const renewToken = async (renewUser: $eventType) => {
  const formData = new FormData()
  Object.keys(renewUser).forEach((key) => {
    formData.append(key, renewUser[key])
  })
  if (formData != null) {
    nuxtApp.$toast('clear')
    try {
      addProcess.value = true
      const { success, message, data } = await fetch(NEW_USER, {
        method: 'POST',
        body: formData,
      })
      if (success) {
        nuxtApp.$toast('success', {
          message: message,
          className: 'toasted-bg-archive',
        })
        allUsers.value.forEach((item) => {
          if (item.id === data.id) {
            item.isExpired = data.isExpired
          }
        })
      } else {
        nuxtApp.$toast('error', {
          message: message,
          className: 'toasted-bg-alert',
        })
      }
    } catch (err) {
      nuxtApp.$toast('err', {
        message: message,
        className: 'toasted-bg-alert',
      })
    } finally {
      addProcess.value = false
    }
  }
}

const deleteCard = (cardId: number) => {
  showDeleteAlert.value = true
  deletedCardId.value = cardId
}
const deleteCancel = () => {
  showDeleteAlert.value = false
  deletedCardId.value = ''
}
const deleteConfirm = async () => {
  nuxtApp.$toast('clear')
  try {
    cardDeleteProcess.value = true
    const response = await fetch(PAYMENT_METHOD, {
      method: 'DELETE',
      body: {
        id: deletedCardId.value,
      },
    })
    if (response.success) {
      nuxtApp.$toast('success', {
        message: response.message,
        className: 'toasted-bg-archive',
      })
    } else {
      nuxtApp.$toast('error', {
        message: response.message,
        className: 'toasted-bg-alert',
      })
    }
  } catch (error) {
    console.log(error)
  } finally {
    showDeleteAlert.value = false
    deletedCardId.value = ''
    cardDeleteProcess.value = false
  }
}
const setIsSticky = (value: boolean) => store.dispatch('set_sticky', value)
const set_header_width = (value: boolean) =>
  store.dispatch('set_header_width', value)
const expand_starter_modal = (value: boolean) =>
  store.dispatch('expand_starter_modal', value)
const collapse_starter_modal = (value: boolean) =>
  store.dispatch('collapse_starter_modal', value)
const show_sign_up = (value: boolean) => store.dispatch('show_sign_up', value)
const maximize_starter_modal = (value: boolean) =>
  store.dispatch('maximize_starter_modal', value)
const starter_account_maximized = (value: boolean) =>
  store.dispatch('starter_account_maximized', value)
const updateSetupContent = (value: string) =>
  store.dispatch('updateSetupContent', value)

const login_button_transition = (value: boolean) =>
  store.dispatch('loginAnimation/login_button_transition', value)
const submit_button_transition = (value: boolean) =>
  store.dispatch('loginAnimation/submit_button_transition', value)
const login_form_transition = (value: boolean) =>
  store.dispatch('loginAnimation/login_form_transition', value)
const successfully = (value: boolean) =>
  store.dispatch('loginAnimation/successfully', value)
const notsuccessfully = (value: boolean) =>
  store.dispatch('loginAnimation/notsuccessfully', value)
const after_loading = (value: boolean) =>
  store.dispatch('loginAnimation/after_loading', value)
const home_modal = (value: boolean) =>
  store.dispatch('loginAnimation/home', value)
const sidebar_menu = (value: boolean) =>
  store.dispatch('loginAnimation/sidebar_menu', value)
const sidebar_circle = (value: boolean) =>
  store.dispatch('loginAnimation/circle', value)
const home_sidebar = (value: boolean) =>
  store.dispatch('loginAnimation/sidebar', value)
const home_circle = (value: boolean) =>
  store.dispatch('loginAnimation/home_circle', value)
const login_circle = (value: boolean) =>
  store.dispatch('loginAnimation/login_circle', value)
const slide_left = (value: boolean) =>
  store.dispatch('loginAnimation/slide_left', value)
const show_logo = (value: boolean) =>
  store.dispatch('loginAnimation/show_logo', value)
const home_header = (value: boolean) =>
  store.dispatch('loginAnimation/header', value)
const width_increase = (value: boolean) =>
  store.dispatch('loginAnimation/width_increase', value)
const full_width = (value: boolean) =>
  store.dispatch('loginAnimation/full_width', value)
const home_side_menu = (value: boolean) =>
  store.dispatch('loginAnimation/home_side_menu', value)
const all_side_menu = (value: boolean) =>
  store.dispatch('loginAnimation/all_side_menu', value)
const show_home = (value: boolean) =>
  store.dispatch('loginAnimation/show_home', value)
const home_menu_text = (value: boolean) =>
  store.dispatch('loginAnimation/home_menu_text', value)
const show_home_content = (value: boolean) =>
  store.dispatch('loginAnimation/show_home_content', value)
const home_wrapper = (value: boolean) =>
  store.dispatch('loginAnimation/home_wrapper', value)
const show_logo_text = (value: boolean) =>
  store.dispatch('loginAnimation/show_logo_text', value)
const header_text = (value: boolean) =>
  store.dispatch('loginAnimation/header_text', value)
const loading_text = (value: boolean) =>
  store.dispatch('loginAnimation/loading_text', value)

const width_decrese = (value: boolean) =>
  store.dispatch('loginAnimation/width_decrese', value)
const slide_right = (value: boolean) =>
  store.dispatch('loginAnimation/slide_right', value)
const slide_full_right = (value: boolean) =>
  store.dispatch('loginAnimation/slide_full_right', value)
const show_login = (value: boolean) =>
  store.dispatch('loginAnimation/show_login', value)
const landing_content = (value: boolean) =>
  store.dispatch('loginAnimation/landing_content', value)
const after_logout = (value: boolean) =>
  store.dispatch('loginAnimation/after_logout', value)
const text_loading = (value: boolean) =>
  store.dispatch('loginAnimation/text_loading', value)
</script>

<style lang="scss" scoped>
.toggle-icon {
  color: #ffffff;
  svg {
    @apply md:text-3xl text-2xl;
  }
}
#resizeIcon {
  cursor: row-resize;
}
.card-wrapper {
  @apply overflow-y-auto scroll md:pt-4 relative md:space-y-5 space-y-2 h-full;
  // min-height: 900px;
}
.mobile-card-wrapper {
  @apply overflow-y-auto scroll md:pt-4 relative md:space-y-8 space-y-2 h-full;
  // min-height: 700px;
}
.card {
  @apply bg-white rounded-3xl overflow-hidden flex flex-col;
}
.card-header {
  @apply bg-yellow-midlight text-center h-11 py-1.5;
  min-height: 44px;
}
.card-title {
  @apply md:text-white text-offwhite-200 font-bold md:text-xl text-md;
}
.inner-body-height {
  height: calc(100% - 44px);
  @apply flex flex-col;
}
.card-body {
  @apply overflow-auto scroll flex-grow;
}
.userinfo {
  height: calc(100% - 52px);
}
.web-user_info {
  height: calc(100% - 72px);
}
.web-billing_height {
  height: calc(100% - 42px);
}
.web-billing_info {
  height: calc(100% - 72px);
}
.billing_info_height {
  height: calc(100% - 44px);
}
.billing_info {
  height: calc(100% - 84px);
}
.expnad-height {
  height: calc(100% - 44px);
}
.new-expand-height {
  height: calc(100% - 128px);
}
.collapse-height {
  height: 44px;
}
.table-th {
  @apply md:px-6 px-6 md:py-2 py-2 md:font-bold md:text-gray-1200 text-yellow-midlight text-left md:text-lg text-md pr-10 whitespace-nowrap;
}
.table-body {
  @apply flex items-center justify-start md:space-x-5 w-full md:py-1 py-0.5;
}
.table-tr {
  @apply md:px-6 px-6 md:py-2 py-1.5 text-left whitespace-nowrap;
}
.action-icon {
  @apply pl-5;
}
.table-tr span {
  @apply text-gray-light xl:text-xl md:text-lg text-md;
}
.card-footer {
  @apply text-right lg:pb-6 pb-2 md:mr-2;
}
.footer-btn {
  @apply focus:outline-none rounded-full
  border-2 border-yellow-midlight py-1 lg:px-5 md:px-2 px-2 text-center text-white
  bg-yellow-midlight font-bold md:text-lg
  text-sm;
}
//Small Device
.mobile-card-body {
  @apply overflow-auto scroll bg-white rounded-2xl py-2 px-2 h-full;
}
.mobile-table-th {
  @apply px-3 py-1 text-left text-yellow-midlight tracking-wider whitespace-nowrap text-xs;
}
.mobile-table-body {
  @apply w-full py-0.5;
}
.mobile-table-td {
  @apply px-3 pl-0 text-gray-900 whitespace-nowrap text-sm;
}
.mb-bill-table-th {
  @apply font-bold text-yellow-midlight text-left w-3/12 break-all text-md;
}
.mb-bill-table-tr {
  @apply text-gray-600 w-3/12 break-all text-xs;
}
.mobile-text-size {
  font-size: 8px;
  text-align: center;
}
.min-width-6 {
  min-width: 6rem;
}
.min-width-8 {
  min-width: 9rem;
}
.min-width-10 {
  min-width: 10rem;
}
.min-width-12 {
  min-width: 12rem;
}
.min-width-16 {
  min-width: 16rem;
}
.min-width-20 {
  min-width: 20rem;
}
.min-width-41 {
  min-width: 55rem;
}
.min-width-55 {
  min-width: 67rem;
}
.min-width-34 {
  min-width: 34rem;
}
.tooltip {
  @apply invisible 
  break-all
  whitespace-normal 
  absolute 
  -top-5 
  left-4 
  z-50 
  bg-yellow-midlight 
  text-white 
  rounded 
  py-1 px-2 
  shadow-lg md:w-40 w-32;
}

.tooltip1 {
  @apply invisible 
  break-all
  whitespace-normal 
  -top-5 
  left-5 
  z-50 
  absolute 
  bg-yellow-midlight 
  text-white 
  rounded 
  py-1 px-2 
  shadow-lg md:w-44 w-32;
}

.has-tooltip:hover .tooltip {
  @apply visible;
}

.has-tooltip1:hover .tooltip1 {
  @apply visible;
}
.scroll {
  scrollbar-color: #e0ad1f #ececec; /* Firefox 64 */

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: #e0ad1f;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #e0ad1f;
  }
}

[data-title]:after {
  color: #bb8b28;
  left: 100%;
  z-index: 9999999999;
  line-height: 18px;
}
[data-title].delete:after {
  line-height: 18px;
  color: red;
  left: 100%;
  z-index: 9999999999;
}
.marqueeStyle {
  display: inline-block;
  /* Apply animation to this element */
  -webkit-animation: scrolling-left1 20s linear infinite;
  animation: scrolling-left1 20s linear infinite;
}

@keyframes scrolling-left1 {
  0% {
    transform: translateX(100%);
    -webkit-transform: translateX(100%);
  }
  100% {
    transform: translateX(-110%);
    -webkit-transform: translateX(-110%);
  }
}

@-webkit-keyframes scrolling-left1 {
  0% {
    -webkit-transform: translateX(100%);
  }
  100% {
    -webkit-transform: translateX(-110%);
  }
}

@media (min-height: 470px) and (max-height: 750px) {
  .card-wrapper {
    // min-height: 900px;
  }
}
@media (max-height: 667px) {
  .billing_info {
    height: calc(100% - 84px);
  }
}
.h-108 {
  height: 108px;
}
@media (max-width: 768px) {
  .expnad-height {
    height: calc(100% - 44px);
  }
  .new-expand-height {
    height: calc(100% - 96px);
  }
  .collapse-height {
    height: 44px;
  }
  .h-108 {
    height: 96px;
  }
}
</style>
