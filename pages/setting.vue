<script setup lang="ts">
import { useStore } from 'vuex'

definePageMeta({
  layoutTransition: false,
  layout: 'dashboard',
  middleware: ['payment', 'auth', 'color'],
})

const store = useStore()
const showHomeContent = computed(
  () => store.state.loginAnimation.showHomeContent,
)
const skew = ref(false)
const showEditFeed = ref(false)
const currentComponent = ref('Alert')
const showSkew = ($event: boolean) => {
  skew.value = $event
}
const currentTabComponent = (currentTab: string) => {
  currentComponent.value = currentTab
}
const closeSidebar = () => {
  store.commit('archive/SET_BULK_DOWNLOAD_SIDEBAR', false)
  store.commit('profile/SET_PROFILE_MODAL', false)
  store.commit('search/SET_SAVE_SAERCH_MODAL', false)
  store.commit('socialFeed/SHOW_SOCIAL_EDIT_FEED_MODAL', {
    open: false,
    data: {},
  })
  store.commit('setting/SET_SHOW_CREATE_GROUP', false)
  store.commit('setting/SET_SHOW_EDIT_GROUP', false)
  store.dispatch('showArchiveSettings', false)
  store.commit('setting/SET_SHOW_PROFILE', false)
  store.dispatch('header/removePayment')
  store.commit('socialFeed/SHOW_ADD_FEED_MODAL', false)
  store.commit('home/SET_SELECT_ACCESS_TYPE', 'SelectedAccessType')
  store.commit('notifications/SHOW_NOTIFICATION_MODAL', false)
  store.commit('setting/SET_CHANGE_BILLING_INFO', false)
  store.commit('setting/SET_SHOW_INVOICE', false)
  store.commit('SET_SHOW_SIDE_BAR', false)
  store.commit('account/SET_ACH_BANK_VERIFICATION', false)
}
</script>

<template>
  <div class="md:block hidden py-4 px-2 h-full" @click.stop="closeSidebar()">
    <div
      class="content flex flex-col transition-all duration-2000"
      :class="[showHomeContent ? 'opacity-0' : '']"
    >
      <div
        class="flex lg:flex-row flex-col lg:justify-between lg:items-center px-2 lg:space-x-4"
      >
        <!-- <div class="w-64 main_dropdown">
          <ArchiveSourcesDropdown
            :active="false"
            :height="100"
            :source="'setting'"
            @expand="showSkew($event)"
            @show-edit-feed="showEditFeed = true"
          />
        </div> -->
        <div
          class="min-width-[450px] max-width-[450px] text-center lg:mt-0 md:mt-4"
        >
          <AlertHeaderTabArchiveAlertHubTab></AlertHeaderTabArchiveAlertHubTab>
        </div>
        <div
          class="min-width-[450px] max-width-[450px] text-center lg:mt-0 md:mt-4"
        >
          <AlertHeaderTabAppearanceUserBilling></AlertHeaderTabAppearanceUserBilling>
        </div>
      </div>
      <div
        class="flex-grow page-content overflow-hidden h-full mt-6"
        :class="[skew ? 'm-xl:ml-70' : '']"
      >
        <NuxtPage class="h-full" />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
