<template>
  <div v-if="isDesktop" class="md:block hidden p-4 h-full relative">
    <div
      class="content flex flex-col transition-all duration-2000"
      :class="[showHomeContent ? 'opacity-0' : '']"
    >
      <Transition name="page" mode="out-in">
        <SourceCreatePostHeader
          @toggle-skew="showSkew($event)"
          v-if="route.name === 'source-create-post'"
        />
        <SourceHeader v-else @toggle-skew="showSkew($event)" />
      </Transition>
      <div
        class="page-content overflow-hidden h-[calc(100%-0px)] transition-all duration-[500ms]"
        :class="[skew ? 'm-xl:ml-70' : '']"
      >
        <NuxtPage />
      </div>
    </div>
    <ClientOnly>
      <TransitionGroup name="fade">
        <TeleprompterModal
          v-if="isTeleprompterOpen"
          class="absolute -top-10 left-[46%] -translate-x-[46%]"
        />
        <ImageEditorModal v-if="isImageEditorOpen" />
      </TransitionGroup>
    </ClientOnly>
   
  </div>
</template>

<script setup lang="ts">
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core'
import { useStore } from 'vuex'
import TeleprompterModal from '~/components/source/create-post/TeleprompterModal.vue'
import ImageEditorModal from '~/components/source/image-editor/ImageEditorModal.vue'

useHead(() => ({
  title: 'Sharp Archive',
  meta: [
    {
      hid: 'robots',
      name: 'robots',
      content: 'noindex, nofollow',
    },
  ],
}))
definePageMeta({
  layoutTransition: false,
  layout: 'dashboard',
  middleware: ['payment', 'auth', 'color'],
})
const store = useStore()
const route = useRoute()
onMounted(() => {
  console.log(route, 'route')
})
const breakpoints = useBreakpoints(breakpointsTailwind)
const isDesktop = breakpoints.greaterOrEqual('md')
// store state
const showHomeContent = computed(
  () => store.state.loginAnimation.showHomeContent,
)
const skew = ref<boolean>(false)
const showSkew = ($event: boolean) => {
  skew.value = $event
}

const isTeleprompterOpen = computed<boolean>(
  () => store.state.createPost.teleprompter.isOpen,
)
const isImageEditorOpen = computed<boolean>(
  () => store.state.createPost.imageEditor.isOpen,
)
</script>

<style lang="scss" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s ease-in-out;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  scale: 0.98;
}
.fade-enter-to,
.fade-leave-from {
  opacity: 1;
  scale: 1;
}
</style>
