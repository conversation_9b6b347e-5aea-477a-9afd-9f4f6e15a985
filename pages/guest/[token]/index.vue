<template>
  <section
    class="bg-ash-default overflow-y-auto scroll md:overflow-hidden w-full relative md:fixed guest"
  >
    <a
      class="w-full h-17 md:h-20"
      href="https://sharparchive.com/"
      target="_blank"
      rel="noopener noreferrer"
    >
      <GuestArchiveLogo
        class="relative"
        :hide-feed-connect="hideFeedConnect"
      ></GuestArchiveLogo>
    </a>
    <div
      class="w-full flex flex-col justify-center items-center relative z-9999 body-wrapper"
    >
      <div class="w-full h-full flex justify-center items-center flex-grow">
        <div class="message_wrapper p-4 rounded-xl text-center md:w-103 pt-13">
          <div>
            <p class="text-white text-lg">
              {{ sender ? sender.businessName : null }} has requested access to
              your accounts to archive the content.
              <span
                class="bulk-download_button cursor-pointer"
                @click="showGuestModal = true"
              >
                Click here to learn more about what this means.
              </span>
            </p>
          </div>

          <!-- <SelectedAccessType /> -->
          <div
            class="connect-account-wrapper flex flex-col justify-start items-center mt-4"
          >
            <Transition name="fadeIn" mode="out-in">
              <keep-alive>
                <component
                  :is="getSelectAccessType"
                  :hide-option="true"
                  :google-provider="googleProvider"
                  :microsoft-provider="microsoftProvider"
                  :facebook-provider="facebookProvider"
                  :twitter-provider="twitterProvider"
                  :instagram-provider="instagramProvider"
                  :pinterest-provider="pinterestProvider"
                  :youtube-provider="youtubeProvider"
                  :linked-in-provider="linkedInProvider"
                  :reddit-provider="redditProvider"
                  :tiktok-provider="tiktokProvider"
                  :ringcentral-provider="ringcentralProvider"
                  class="mt-4"
                  @showInstagramModal="instagramModal"
                  @selectCurrentAccess="authorize"
                >
                </component>
              </keep-alive>
            </Transition>
            <transition name="fadeInBtn" mode="out-in">
              <button
                v-if="showButton"
                class="bg-orange-dark text-white w-44 h-10 flex justify-center items-center rounded-full font-bold"
                :class="
                  getSelectAccessType === 'AccessWithEmail' ? 'mt-4' : 'mt-8'
                "
                @click="backToAccessType()"
              >
                <p>Back</p>
              </button>
            </transition>
          </div>
        </div>

        <div
          v-if="authorizationProviders.length > 0"
          class="fixed bg-ash-dark top-0 right-0 h-full profile md:rounded-l-2xl md:shadow-2xl md:py-10 md:pt-4 md:px-21 p-4 transition-all duration-500 ease-in-out scroll"
          :class="
            hideFeedConnect
              ? 'show'
              : 'hide rounded-t-2xl shadow-2xl md:pt-4 pt-1.5'
          "
        >
          <div
            class="w-full h-6 flex md:justify-start items-center justify-center"
          >
            <ClientOnly>
              <fa
                v-if="right"
                class="arrow-icon text-yellow-primary xl:text-2xl md:text-xl md:font-bold text-2xl cursor-pointer text-left md:block hidden"
                :icon="['fas', 'chevron-right']"
                @click="slideRight()"
              />
              <fa
                v-if="left"
                class="arrow-icon text-yellow-primary xl:text-2xl md:text-xl md:font-bold text-2xl cursor-pointer text-left md:block hidden"
                :icon="['fas', 'chevron-left']"
                @click="slideLeft()"
              />
              <fa
                v-if="right"
                class="arrow-icon text-yellow-primary xl:text-2xl md:text-xl md:font-bold text-2xl cursor-pointer md:hidden"
                :icon="['fas', 'chevron-down']"
                @click="slideRight()"
              />
              <fa
                v-if="left"
                class="arrow-icon text-yellow-primary xl:text-2xl md:text-xl md:font-bold text-2xl cursor-pointer md:hidden"
                :icon="['fas', 'chevron-up']"
                @click="slideLeft()"
              />
            </ClientOnly>
          </div>
          <h5
            class="text-white text-lg py-2 pt-11 transition-all duration-500 ease-in-out"
            :class="hideFeedConnect ? 'opacity-1' : 'opacity-0'"
          >
            {{ sender ? sender.businessName : null }} Company has requested
            access to your accounts to archive the content.
            <span
              class="bulk-download_button cursor-pointer"
              @click="showGuestModal = true"
            >
              Click here to learn more about what this means.
            </span>
          </h5>

          <div
            class="w-full pt-5 transition-all duration-500 ease-in-out"
            :class="hideFeedConnect ? 'opacity-1' : 'opacity-0'"
          >
            <div
              v-for="(provider, index) in authorizationProviders"
              :key="index"
              class="grid grid-cols-12 my-4"
            >
              <div class="col-span-7 flex space-x-2 justify-start items-center">
                <div>
                  <a href="javascript:void(0);">
                    <LazySharedIconFacebookIcon
                      v-if="provider.provider.toLowerCase() === 'facebook'"
                      class="w-10 h-10 rounded-full social-img"
                    />
                    <!-- InstagramIcon -->
                    <LazySharedIconInstagramPngIcon
                      v-if="provider.provider.toLowerCase() === 'instagram'"
                      class="w-10 h-10 rounded-full social-img"
                    />
                    <LazySharedIconLinkedinIcon
                      v-if="provider.provider.toLowerCase() === 'linkedin'"
                      class="w-10 h-10 rounded-full social-img"
                    />
                    <LazySharedIconTwitterIcon
                      v-if="provider.provider.toLowerCase() === 'twitter'"
                      class="w-10 h-10 rounded-full social-img"
                    />
                    <LazySharedIconMicrosoftIcon
                      v-if="provider.provider.toLowerCase() === 'microsoft'"
                      class="w-10 h-10 rounded-full social-img"
                    />
                    <img
                      v-if="
                        provider.provider.toLowerCase() === 'google' ||
                        provider.provider.toLowerCase() === 'youtube'
                      "
                      :src="getSocialIcon(provider.provider)"
                      :alt="`${provider.provider} logo`"
                      class="w-10 h-10 rounded-full social-img"
                    />
                    <LazySharedIconPinterestIcon
                      v-if="provider.provider.toLowerCase() === 'pinterest'"
                      class="w-10 h-10 rounded-full social-img"
                    />
                    <LazySharedIconRedditIcon
                      v-if="provider.provider.toLowerCase() === 'reddit'"
                      class="w-10 h-10 rounded-full social-img"
                    />
                    <LazySharedIconTiktokIcon
                      v-if="provider.provider.toLowerCase() === 'tiktok'"
                      class="w-10 h-10 rounded-full social-img"
                    />
                  </a>
                </div>
                <div>
                  <a href="javascript:void(0);">
                    <img
                      :src="
                        provider.profilePic
                          ? provider.profilePic
                          : '/images/people-say/images.png'
                      "
                      :alt="`${provider.username}'s ${provider.provider} profile picture`"
                      class="w-10 h-10 rounded-full profile-img"
                    />
                  </a>
                </div>
                <div class="text-white">
                  <div class="flex flex-col relative has-tooltip">
                    <h4
                      class="md:text-xl text-sm whitespace-nowrap hidden md:block"
                    >
                      {{
                        provider.name
                          ? provider.name
                          : $strLimit(provider.username, 15)
                      }}
                    </h4>
                    <h5
                      v-if="
                        provider.name
                          ? provider.name.length > 15
                          : provider.username.length > 15
                      "
                      class="tooltip hidden md:block"
                    >
                      {{ provider.name ? provider.name : provider.username }}
                    </h5>

                    <h4 class="md:text-xl text-sm whitespace-nowrap md:hidden">
                      {{ $strLimit(provider.username, 12) }}
                    </h4>
                    <h5
                      v-if="provider.username.length > 12"
                      class="tooltip md:hidden"
                    >
                      {{ provider.username }}
                    </h5>
                  </div>

                  <p
                    class="text-gray-400 md:text-sm text-xs whitespace-nowrap hidden md:block"
                  >
                    @{{ $strLimit(provider.username, 20) }}
                  </p>

                  <p
                    class="text-gray-400 md:text-sm text-xs whitespace-nowrap md:hidden"
                  >
                    @{{ $strLimit(provider.username, 14) }}
                  </p>
                </div>
              </div>
              <div class="col-span-5 flex justify-end items-center">
                <button
                  class="md:px-8 px-6 py-1 border-2 border-yellow-primary text-yellow-primary text-base font-semibold rounded-full"
                  @click="deleteSocial(provider.id)"
                >
                  Disconnect
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="w-full flex flex-wrap mb-13 justify-center items-center text-left sm:mt-0 md:space-x-6"
      >
        <div class="lg:text-left min-width-12 mx-2">
          <p class="text-yellow-primary text-lg py-1">
            &copy; Sharp Archive {{ year }}
          </p>
        </div>
        <div class="lg:text-left text-center min-width-12 mx-2">
          <NuxtLink
            to="/terms-and-conditions"
            target="_blank"
            rel="noopener noreferrer"
          >
            <p class="text-yellow-primary text-lg py-1">Terms & Conditions</p>
          </NuxtLink>
        </div>
        <div class="lg:text-left text-center min-width-12 mx-2">
          <NuxtLink
            to="/subuser-terms-of-service"
            target="_blank"
            rel="noopener noreferrer"
          >
            <p class="text-yellow-primary text-lg py-1">Terms of Service</p>
          </NuxtLink>
        </div>
        <div class="lg:text-left text-center min-width-12 mx-2">
          <NuxtLink
            to="/subuser-privacypolicy"
            target="_blank"
            rel="noopener noreferrer"
          >
            <p class="text-yellow-primary text-lg py-1">Privacy Policy</p>
          </NuxtLink>
        </div>
      </div>
    </div>

    <GuestModal
      :show-guest-modal="showGuestModal"
      @closeGuestModal="showGuestModal = false"
    >
    </GuestModal>
    <div v-if="!acceptTerm" class="guest-overlay"></div>
    <GuestConfirmModal
      :accept-term="acceptTerm"
      @checked-checkbox="acceptTerm = true"
    ></GuestConfirmModal>
    <GuestInstagramModal
      v-if="showGuestInstagramModal"
      class="guest-overlay"
    ></GuestInstagramModal>
  </section>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
import { useNuxtApp } from '#app'
import { randomString } from '~/utils'
import { CHECK_GUEST_REQUEST, SOCIAL_FEEDS } from '~/constants/urls'
import youtubeIcon from '~/assets/img/png/youtube_social_circle_white.png'
import googleSvg from '~/assets/img/svg/Google__G__Logo.svg'

definePageMeta({
  layout: 'guest',
})
const nuxtApp = useNuxtApp()
const { $toast } = useNuxtApp()
// Access the route
const route = useRoute()
const store = useStore()

// Define types
type AuthorizationProvider = {
  id: number
  provider: string
  profilePic: string
  username: string
  name: string
}
type Sender = {
  businessName: string
  email: string
  firstName: string
  lastName: string
  phone: string
}
// Define the reactive properties
const showGuestModal = ref<boolean>(false)
const token = ref<string | undefined>(route.params.token as string | undefined)
const youtubeCircle = ref<string>(youtubeIcon)
const google = ref<string>(googleSvg)
const authorizationProviders = ref<AuthorizationProvider[]>([])
const sender = ref<Sender | null>(null)
const acceptTerm = ref<boolean>(true)
const right = ref<boolean>(true)
const left = ref<boolean>(false)
const hideFeedConnect = ref<boolean>(true)
const showButton = ref<boolean>(false)

// computed
// Destructure state and getters using vuex-composition-helpers or directly
const showGuestInstagramModal = computed(
  () => store.state.guest.showGuestInstagramModal,
)
const getSelectAccessType = computed(
  () => store.getters['home/getSelectAccessType'],
)

// Define `computed` properties
const checkRequest = computed(() => store.state.guest.checkRequest)
const year = computed(() => new Date().getFullYear())

// Assuming `authorizationProviders` is a reactive property (ref or reactive array)
const googleProvider = computed(() => {
  return authorizationProviders.value.length > 0
    ? authorizationProviders.value.filter((provider: AuthorizationProvider) => {
        return provider.provider.toLowerCase() === 'google'
      })
    : []
})

const microsoftProvider = computed(() => {
  return authorizationProviders.value.length > 0
    ? authorizationProviders.value.filter((provider: AuthorizationProvider) => {
        return provider.provider.toLowerCase() === 'microsoft'
      })
    : []
})

const facebookProvider = computed(() => {
  return authorizationProviders.value.length > 0
    ? authorizationProviders.value.filter((provider: AuthorizationProvider) => {
        return provider.provider.toLowerCase() === 'facebook'
      })
    : []
})

const twitterProvider = computed(() => {
  return authorizationProviders.value.length > 0
    ? authorizationProviders.value.filter((provider: AuthorizationProvider) => {
        return provider.provider.toLowerCase() === 'twitter'
      })
    : []
})

const linkedInProvider = computed(() => {
  return authorizationProviders.value.length > 0
    ? authorizationProviders.value.filter((provider: AuthorizationProvider) => {
        return provider.provider.toLowerCase() === 'linkedin'
      })
    : []
})

const instagramProvider = computed(() => {
  return authorizationProviders.value.length > 0
    ? authorizationProviders.value.filter((provider: AuthorizationProvider) => {
        return provider.provider.toLowerCase() === 'instagram'
      })
    : []
})

const pinterestProvider = computed(() => {
  return authorizationProviders.value.length > 0
    ? authorizationProviders.value.filter((provider: AuthorizationProvider) => {
        return provider.provider.toLowerCase() === 'pinterest'
      })
    : []
})

const redditProvider = computed(() => {
  return authorizationProviders.value.length > 0
    ? authorizationProviders.value.filter((provider: AuthorizationProvider) => {
        return provider.provider.toLowerCase() === 'reddit'
      })
    : []
})

const tiktokProvider = computed(() => {
  return authorizationProviders.value.length > 0
    ? authorizationProviders.value.filter((provider: AuthorizationProvider) => {
        return provider.provider.toLowerCase() === 'tiktok'
      })
    : []
})

const youtubeProvider = computed(() => {
  return authorizationProviders.value.length > 0
    ? authorizationProviders.value.filter((provider: AuthorizationProvider) => {
        return provider.provider.toLowerCase() === 'youtube'
      })
    : []
})

const ringcentralProvider = computed(() => {
  return authorizationProviders.value.length > 0
    ? authorizationProviders.value.filter((provider: AuthorizationProvider) => {
        return provider.provider.toLowerCase() === 'ringcentral'
      })
    : []
})

// Watch `checkRequest`
watch(checkRequest, (data: boolean) => {
  if (data) {
    checkGuestRequest()
  }
})

// Watch `getSelectAccessType`
watch(getSelectAccessType, (data: string) => {
  if (data !== 'SelectedAccessType') {
    setTimeout(() => {
      showButton.value = true
    }, 500)
  } else {
    showButton.value = false
  }
})

onMounted(async () => {
  await checkGuestRequest()
})
const backToAccessType = () => {
  showButton.value = false
  store.commit('home/SET_SELECT_ACCESS_TYPE', 'SelectedAccessType')
}
const instagramModal = () => {
  store.commit('guest/SET_GUEST_INSTAGRAM_MODAL', true)
}

const slideRight = () => {
  hideFeedConnect.value = false
  setTimeout(() => {
    right.value = false
    setTimeout(() => {
      left.value = true
    })
  }, 500)
}
const slideLeft = () => {
  hideFeedConnect.value = true
  setTimeout(() => {
    left.value = false
    setTimeout(() => {
      right.value = true
    })
  }, 500)
}

type Feeds = {
  feeds: AuthorizationProvider[]
  sender: Sender
  acceptTerm: boolean
}
type CheckGuestRequest = {
  success: boolean
  data: Feeds
  message: string
}
const checkGuestRequest = async () => {
  const res = await $fetch<CheckGuestRequest>(CHECK_GUEST_REQUEST, {
    method: 'POST',
    body: {
      token: token.value,
    },
  })
  if (res.success) {
    authorizationProviders.value = res.data.feeds
    sender.value = res.data.sender
    acceptTerm.value = res.data.acceptTerm
    store.commit('guest/SET_CHECK_REQUEST', false)
  } else {
    $toast('error', {
      message: `${res.message}. Please back to landing page by clicking the Sharp Archive logo`,
      className: 'toasted-bg-alert',
    })
    store.commit('guest/SET_CHECK_REQUEST', false)
  }
}
type DeleteSocial = {
  success: boolean
  message: string
}
const deleteSocial = async (id: number) => {
  if (confirm('Are you sure you want to delete this feed?')) {
    $toast('clear')
    try {
      const response = await $fetch<DeleteSocial>(SOCIAL_FEEDS, {
        method: 'DELETE',
        body: {
          id,
          token: token.value,
        },
      })
      if (response.success) {
        $toast('success', {
          message: response.message,
          className: 'toasted-bg-archive',
        })
        authorizationProviders.value = authorizationProviders.value.filter(
          (item: AuthorizationProvider) => {
            return Number(item.id) !== Number(id)
          },
        )
      } else {
        $toast('error', {
          message: response.message,
          className: 'toasted-bg-alert',
        })
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error)
    }
  }
}

const authorize = async (provider: string) => {
  console.log(token.value)
  const state = token.value || randomString(10)
  await nuxtApp.$social.redirect(provider, state)
}

const getSocialIcon = (item: string) => {
  let socialIcon = ''
  if (item.toLowerCase() === 'google') {
    socialIcon = google.value
  } else if (item.toLowerCase() === 'youtube') {
    socialIcon = youtubeCircle.value
  }
  return socialIcon
}

const hideText = (workflow: string) => {
  if (workflow === 'live') {
    return false
  } else {
    return true
  }
}
</script>

<style lang="scss" scoped>
.tooltip {
  @apply absolute
  bg-offwhite-200
  text-yellow-primary
  z-100
  left-0
  -top-5
  text-left
  invisible
  p-1.5
  px-4
  rounded-xl 
  shadow-lg md:w-34 w-32
  whitespace-normal break-all;
}
.has-tooltip:hover .tooltip {
  @apply visible;
  transition: all 0.3s linear;
}
.email-template li img {
  @apply px-2  mb-5 mt-2;
  // w-20 h-16
}
.social-media li img {
  @apply w-20 h-20;
  min-width: 60px;
  max-width: 60px;
  min-height: 60px;
  max-height: 60px;
}
.social-img,
.profile-img {
  @apply w-10 h-10;
  max-width: 40px;
}
/* .message_wrapper {
  width: 558px;
} */
.bulk-download_button {
  @apply lg:w-44 md:w-36
  h-10
  text-orange-600
  mx-1
  md:my-1 lg:mb-2 lg:mt-0
  border-none
  outline-none text-md;
}

.guest {
  height: 100vh;
}
.profile {
  width: 500px;
}
.guest-overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: rgba(255, 255, 255, 0.1);
  opacity: 1;
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  pointer-events: all;
}
.show {
  width: 500px;
}
.hide {
  right: -430px;
}

.svg-inline--fa.fa-w-14 {
  width: 100%;
}
.scroll {
  overflow-y: auto;
  overflow-x: hidden;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-width: thin;
  scrollbar-color: #ff8308 #ececec; /* Firefox 64 */
  &::-webkit-scrollbar {
    width: 6px;
    height: 10px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    // box-shadow: inset 0 0 5px #ECECEC;
    border-radius: 3px;
    background: #ececec;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: #ff8308;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #ff8308;
  }
}
.button-style {
  @apply h-10 w-38 p-3 leading-5 whitespace-nowrap bg-white text-orange-600 font-bold rounded-full hover:text-white hover:bg-orange-600;
}
@media (min-width: 768px) {
  .body-wrapper {
    margin-top: 30px;
    @apply h-full;
  }
  @media (min-height: 520px) {
    .body-wrapper {
      margin-top: 50px;
      height: calc(100% - 150px);
    }
  }
}
@media (max-width: 767px) {
  .profile {
    width: 100%;
  }
  .show {
    top: 0px;
    right: 0px;
  }
  .hide {
    top: 95%;
    right: 0px;
  }
  .scroll {
    &::-webkit-scrollbar {
      width: 3px;
      height: 3px;
    }
  }
  .body-wrapper {
    margin-top: 30px;
    @apply h-full;
  }
  @media (max-height: 799px) {
    .body-wrapper {
      margin-top: 50px;
      height: calc(100% - 10px);
    }
  }
}

// @media (max-width: 430px) {
//   .email-template {
//     @apply space-x-0 space-y-4 flex-wrap;
//   }
// }
.youtube_icon {
  height: 44px;
}

.connect-account-wrapper {
  min-height: 336px;
  // height: 300px;
}

.fadeIn-enter-active,
.fadeIn-leave-active {
  transition: opacity 0.5s;
}
.fadeIn-enter-from,
.fadeIn-leave-to {
  opacity: 0;
}

.fadeInBtn-enter-active,
.fadeInBtn-leave-active {
  transition: opacity 0.5s;
}
.fadeInBtn-enter-from,
.fadeInBtn-leave-to {
  opacity: 0;
}
</style>
