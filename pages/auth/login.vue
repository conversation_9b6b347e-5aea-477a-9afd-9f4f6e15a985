<script setup lang="ts">
import { useStore } from 'vuex'
const store = useStore()

definePageMeta({
  layoutTransition: false,
  layout: 'auth',
  middleware: ['signup'],
  // middleware: ['payment', 'auth', 'color'],
})
const loggedIn = computed(() => store.state.auth.loggedIn)
</script>
<template>
  <section class="bg-[#373e46] w-full h-screen">
    <div class="landing_container md:pb-[88px] pb-26">
      <div class="main_body">
        <div class="body">
          <div id="hero_section" class="intro">
            <div class="intro_body lg:w-1/2 w-full min-h-[500px]">
              <h1>Compliance without complexity.</h1>
              <p>
                See how Sharp Archive can help you easily <br />monitor,
                capture, and store your emails and <br />communications with our
                data backup services.
              </p>
              <NuxtLink
                v-if="!loggedIn"
                class="orange_button"
                to="/appointment"
                aria-label="Book a Demo"
              >
                Book a Demo
              </NuxtLink>
              <p v-if="!loggedIn" class="demo_text">
                * 30-Day Free Trial allows a maximum of five connections.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <HomePageLoadingAnimation />
  </section>
</template>

<style lang="scss" scoped>
.landing_container {
  .main_body {
    @apply w-full h-full;
    .body {
      @apply w-full h-full;
      .intro {
        @apply w-full h-screen relative flex flex-col min-[1010px]:flex-row min-[1010px]:flex-nowrap lg:justify-between items-center xl:items-start 2xl:px-44 xl:px-26 md:px-20 px-0 2xl:pt-44 pt-64;
        background: url(~/assets/img/landing/landing_bg_img_logo1.webp);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-color: #222831;
        border-radius: 100% / 17%;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        .intro_body {
          @apply flex flex-col lg:items-start items-center text-white text-center lg:text-left px-7 md:px-0 pt-6 md:pt-32;
          h1 {
            @apply text-3xl md:text-5xl font-medium pb-3.5;
          }
          p {
            @apply text-base md:text-xl lg:text-2xl pb-7;
          }
          .demo_text {
            @apply pt-3.5 text-lg text-gray-1100 italic;
          }
        }
      }
    }
  }
}
</style>
