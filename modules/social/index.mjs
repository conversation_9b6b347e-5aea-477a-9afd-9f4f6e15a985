import { addPlugin, defineNuxtModule } from 'nuxt/kit'
import path from 'path'
import { fileURLToPath } from 'url'

export default defineNuxtModule({
  meta: {
    name: 'social'
  },
  // const { nuxt, addPlugin } = this
  setup(moduleOptions, nuxt) {
    const __filename = fileURLToPath(import.meta.url)
    const __dirname = path.dirname(__filename)
    // Combine options
    const options = {
      ...nuxt.options.social,
      ...moduleOptions,
    }

    console.log(__filename, __dirname)

    // Register plugin
    addPlugin({
      src: path.resolve(__dirname, 'plugin.js'),
      fileName: 'social.js',
      options,
    })
  },
})