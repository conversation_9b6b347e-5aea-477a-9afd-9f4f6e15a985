import { defineStore } from 'pinia'

export interface ChartDataPoint {
  name: string
  data: [number, number][]
}

export type SummeryComponent =
  | ''
  | 'SummaryAllFeedsTable'
  | 'SummaryIndividualFeedsTable'
  | 'SummaryIndividualSocialFeedsTable'
  | 'SummaryIndividualSocialFeedDetails'
  | 'SummaryGroupsTable'
  | 'SummaryAllFlagsORTable'
  | 'SummaryFlagPersonsORTable'
  | 'SummaryAllFlagsCRTable'
  | 'SummaryTeamCRTable'
  | 'SummaryPersonCRTable'
  | 'SummaryAllFlagsTable'
  | 'SummaryAllFlagsComplianceTable'
  | 'SummaryAllFlagsHumanResourcesTable'
  | 'SummaryAllFlagsCorporateSecurityTable'

export interface AlertState {
  isDesktopView: boolean
  type: string
  newAlert: Record<string, any>
  isGraphExpanded: boolean
  currentComp: string
  previousCurrentComp: string[]
  graphCurrentComp: string
  previousGraphCurrentComp: string[]
  currentGraph: "AreaGraph" | "LineGraph" | "ScatterGraph"
  summaryComp: SummeryComponent
  previousSummaryComp: SummeryComponent[]
  feedsGroupsResponseTab: string
  summaryAlertTab: string
  listSeveritySourceGroupTab: string
  mobileCurrentTab: string
  disposition: string
  addNoteComp: string
  openClose: 'Open' | 'Close'
  showNewAlert: boolean
  activityLog: boolean
  currentInputTab: string
  trendLine: boolean
  compareSeries: boolean
  multiRangeStartingValue: number
  multiRangeEndingValue: number
  singleRangeEndingValue: number
  changePosition: boolean
  minGap: number
  slidingStop: boolean
  compareSelectInput: string
  currentDateRange: string
  startDate: string
  endDate: string
  startValue: string
  endValue: string
  oldSeries: ChartDataPoint[]
  series: ChartDataPoint[]
  showHideFullSummaryTable: boolean
  hideGraphTable: boolean
  showSummaryTableData: boolean
  addFeedsSeries: ChartDataPoint[]
  allFlagsSeries: ChartDataPoint[]
  pieSeries: number[]
  pieLabels: string[]
  IndividualFeedsPieSeries: number[]
  IndividualFeedsPieLabels: string[]
  // scatterSeries: ChartDataPoint[]
  allFlagsScatterSeries: ChartDataPoint[]
  complianceScatterSeries: ChartDataPoint[]
  trendLineSeries: ChartDataPoint[]
  allFlagLineSeries: ChartDataPoint[]
  humanResourcesScatterSeries: ChartDataPoint[]
  corporateSecurityScatterSeries: ChartDataPoint[]
  individualPersonSeries: ChartDataPoint[]
  IndividualFeedsSeries: ChartDataPoint[]
  socialMediaSeries: ChartDataPoint[]
  socialMediaPieSeries: number[]
  socialMediaPieLabels: string[]
  IndividualAccountPieSeries: number[]
  IndividualAccountPieLabels: string[]
  groupsSeries: ChartDataPoint[]
  groupsPieSeries: number[]
  groupsPieLabels: string[]
  individualTeamLineSeries: ChartDataPoint[]
  IndividualAccountSeries: ChartDataPoint[]
  IndividualFlagPersonsSeries: ChartDataPoint[]
  previousSeries: ChartDataPoint
  teamsSeries: ChartDataPoint
  individualsSeries: ChartDataPoint
}

const generateData = ({
  chance = 0.2, // 20%
  min = 50,
  max = 60,
}: { chance?: number; min?: number; max?: number } = {}) => {
  const results: [number, number][] = []
  const start = new Date('2011-01-01T00:00:00Z').getTime()
  const end = new Date('2014-01-01T00:00:00Z').getTime()

  const dayMs = 24 * 60 * 60 * 1000

  for (let time = start; time <= end; time += dayMs) {
    if (Math.random() < chance) {
      const timestamp = time
      const randomValue = Math.floor(Math.random() * (max - min + 1)) + min
      results.push([timestamp, randomValue])
    }
  }

  return results
}

export const useAlert = defineStore('alert', {
  state: (): AlertState => ({
    isDesktopView: true,
    type: 'Alerts Library',
    newAlert: {},
    isGraphExpanded: true,
    currentComp: 'AlertOpenEdit',
    previousCurrentComp: [],
    graphCurrentComp: 'AllFeedsOpenArea',
    previousGraphCurrentComp: [],
    currentGraph: 'AreaGraph',
    summaryComp: 'SummaryAllFeedsTable',
    previousSummaryComp: [],
    feedsGroupsResponseTab: 'SummaryAllFeedsTable',
    summaryAlertTab: 'AlertOpenEdit',
    listSeveritySourceGroupTab: 'AlertOpenEdit',
    mobileCurrentTab: 'AllFeedsOpenArea',
    disposition: '',
    addNoteComp: '',
    openClose: 'Open',
    showNewAlert: false,
    activityLog: false,
    currentInputTab: 'Email',
    trendLine: false,
    compareSeries: false,
    multiRangeStartingValue: 0.3,
    multiRangeEndingValue: 0.98,
    singleRangeEndingValue: 0.98,
    changePosition: false,
    minGap: 1,
    slidingStop: false,
    compareSelectInput: 'Previous',
    currentDateRange: '1 Year',
    startDate: '',
    endDate: '',
    startValue: '50',
    endValue: '100',
    oldSeries: [],
    series: [],
    showHideFullSummaryTable: false,
    hideGraphTable: false,
    showSummaryTableData: false,
    addFeedsSeries: [
      {
        name: 'All Feeds',
        data: generateData(),
      },
    ],
    IndividualFeedsSeries: [
      {
        name: 'Social Media',
        data: generateData(),
      },
      {
        name: 'Text',
        data: generateData(),
      },
      {
        name: 'IM and Collaboration',
        data: generateData(),
      },
      {
        name: 'Email',
        data: generateData(),
      },
      {
        name: 'Website',
        data: generateData(),
      },
      {
        name: 'Voice',
        data: generateData(),
      },
    ],
    socialMediaSeries: [
      {
        name: 'Twitter',
        data: generateData(),
      },
      {
        name: 'Facebook',
        data: generateData(),
      },
      {
        name: 'Instagram',
        data: generateData(),
      },
      {
        name: 'LinkedIn',
        data: generateData(),
      },
    ],
    IndividualAccountSeries: [
      {
        name: '@whale-song (Jim Jones)',
        data: generateData(),
      },
      {
        name: '@dog-smile (Betsy Bop)',
        data: generateData(),
      },
      {
        name: '@pony-phenomenon (Steven Stevensos)',
        data: generateData(),
      },
      {
        name: '@tiger-floss (Suzy Johnson)',
        data: generateData(),
      },
      {
        name: '@mouse-massage (James Joyce)',
        data: generateData(),
      },
      {
        name: '@albatros-alchemy (Oleg Olyphant)',
        data: generateData(),
      },
      {
        name: '@whale-song (Jim Jones1)',
        data: generateData(),
      },
      {
        name: '@dog-smile (Betsy Bop3)',
        data: generateData(),
      },
    ],

    // Groups
    groupsSeries: [
      {
        name: 'Bob Rahman',
        data: generateData(),
      },
      {
        name: 'Tommy Thompson',
        data: generateData(),
      },
      {
        name: 'James Jamison',
        data: generateData(),
      },
      {
        name: 'Ryan Rjiani',
        data: generateData(),
      },
    ],

    // Response
    allFlagsSeries: [
      {
        name: 'All Flags',
        data: generateData(),
      },
    ],
    IndividualFlagPersonsSeries: [
      {
        name: 'Jane Smith',
        data: generateData(),
      },
      {
        name: 'George Jones',
        data: generateData(),
      },
      {
        name: 'Suzanne Burns',
        data: generateData(),
      },
    ],

    // pieChart
    pieSeries: [],
    pieLabels: [],
    IndividualFeedsPieSeries: [61, 18, 51, 31, 3, 7],
    IndividualFeedsPieLabels: [
      'Social Media',
      'Text',
      'IM and Collaboration',
      'Email',
      'Websites',
      'Voice',
    ],
    socialMediaPieSeries: [16, 15, 10, 20],
    socialMediaPieLabels: ['Twitter', 'Facebook', 'Instagram', 'Linkedin'],
    IndividualAccountPieSeries: [16, 15, 10, 20, 20, 20, 20, 20],
    IndividualAccountPieLabels: [
      '@whale-song (Jim Jones)',
      '@dog-smile (Betsy Bop)',
      '@pony-phenomenon (Ste...',
      '@tiger-floss (Suzy Johnso...',
      '@mouse-massage (Jame...',
      '@albatros-alchemy (Oleg...',
      '@elephant-tail (Bob Bury)',
      '@donkey-delerium (Kendr...',
    ],

    // pieChart for groups
    groupsPieSeries: [61, 18, 51, 31],
    groupsPieLabels: [
      'Bob Rahman',
      'Tommy Thompson',
      'James Jamison',
      'Ryan Rjiani',
    ],

    // Scatter chart
    // scatterSeries: [
    //   {
    //     name: 'All Flags',
    //     data: generateDates(),
    //   },
    // ],
    allFlagsScatterSeries: [
      {
        name: 'All Flags',
        data: generateData({ chance: 0.1, min: 0, max: 100 }),
      },
    ],
    complianceScatterSeries: [
      {
        name: 'Jane Smith',
        data: generateData({ chance: 0.1, min: 0, max: 100 }),
      },
      {
        name: 'George Jones',
        data: generateData({ chance: 0.1, min: 0, max: 100 }),
      },
      {
        name: 'Suzanne Burns',
        data: generateData({ chance: 0.1, min: 0, max: 100 }),
      },
    ],
    humanResourcesScatterSeries: [
      {
        name: 'Jane Smith 2',
        data: generateData({ chance: 0.1, min: 0, max: 100 }),
      },
      {
        name: 'George Jones 2',
        data: generateData({ chance: 0.1, min: 0, max: 100 }),
      },
      {
        name: 'Suzanne Burns 2',
        data: generateData({ chance: 0.1, min: 0, max: 100 }),
      },
    ],
    corporateSecurityScatterSeries: [
      {
        name: 'Jane Smith 3',
        data: generateData({ chance: 0.1, min: 0, max: 100 }),
      },
      {
        name: 'George Jones 3',
        data: generateData({ chance: 0.1, min: 0, max: 100 }),
      },
      {
        name: 'Suzanne Burns 3',
        data: generateData({ chance: 0.1, min: 0, max: 100 }),
      },
    ],

    // Line series
    previousSeries: {
      name: 'Previous',
      data: [
        [1327359600000, 500],
        [1327446000000, 502],
        [1327532400000, 503],
        [1327618800000, 504],
        [1327878000000, 508],
        [1327964400000, 510],
        [1328050800000, 512],
        [1328137200000, 514],
        [1328223600000, 515],
        [1328482800000, 516],
        [1328569200000, 520],
        [1328655600000, 522],
        [1328742000000, 525],
        [1328828400000, 527],
        [1329087600000, 528],
        [1329174000000, 530],
        [1329260400000, 528],
        [1329346800000, 525],
        [1329433200000, 524],
        [1329778800000, 523],
        [1329865200000, 522],
        [1329951600000, 520],
        [1330038000000, 519],
        [1330297200000, 518],
        [1330383600000, 516],
        [1330470000000, 520],
        [1330556400000, 522],
        [1330642800000, 524],
        [1330902000000, 526],
        [1330988400000, 528],
        [1331074800000, 530],
        [1331161200000, 532],
        [1331247600000, 533],
        [1331506800000, 535],
        [1331593200000, 540],
        [1331679600000, 545],
        [1331766000000, 546],
        [1331852400000, 550],
        [1332111600000, 551],
        [1332198000000, 555],
        [1332284400000, 557],
        [1332370800000, 560],
        [1332457200000, 580],
        [1332712800000, 582],
        [1332799200000, 583],
        [1332885600000, 585],
        [1332972000000, 586],
        [1333058400000, 588],
        [1333317600000, 589],
        [1333404000000, 590],
        [1333490400000, 580],
        [1333576800000, 579],
        [1333922400000, 575],
        [1334008800000, 574],
        [1334095200000, 577],
        [1334181600000, 570],
        [1334268000000, 569],
        [1334527200000, 568],
        [1334613600000, 565],
        [1334700000000, 565],
        [1334786400000, 560],
        [1334872800000, 561],
        [1335132000000, 570],
        [1335218400000, 572],
        [1335304800000, 575],
        [1335391200000, 580],
        [1335477600000, 585],
        [1335736800000, 590],
        [1335823200000, 591],
        [1335909600000, 592],
        [1335996000000, 600],
        [1336082400000, 602],
        [1336341600000, 604],
        [1336428000000, 603],
        [1336514400000, 605],
        [1336600800000, 606],
        [1336687200000, 608],
        [1336946400000, 610],
        [1337032800000, 611],
        [1337119200000, 602],
        [1337205600000, 600],
        [1337292000000, 601],
        [1337551200000, 602],
        [1337637600000, 603],
        [1337724000000, 550],
        [1337810400000, 549],
        [1337896800000, 545],
        [1338242400000, 543],
        [1338328800000, 541],
        [1338415200000, 540],
        [1338501600000, 538],
        [1338760800000, 535],
        [1338847200000, 532],
        [1338933600000, 530],
        [1339020000000, 520],
        [1339106400000, 520],
        [1339365600000, 521],
        [1339452000000, 522],
        [1339538400000, 521],
        [1339624800000, 522],
        [1339711200000, 515],
        [1339970400000, 517],
        [1340056800000, 510],
        [1340143200000, 520],
        [1340229600000, 522],
        [1340316000000, 525],
        [1340575200000, 526],
        [1340661600000, 536],
        [1340748000000, 535],
        [1340834400000, 537],
        [1340920800000, 538],
        [1341180000000, 540],
        [1341266400000, 542],
        [1341439200000, 550],
        [1341525600000, 580],
        [1341784800000, 579],
        [1341871200000, 578],
        [1341957600000, 576],
        [1342044000000, 575],
        [1342130400000, 574],
        [1342389600000, 572],
        [1342476000000, 560],
        [1342562400000, 562],
        [1342648800000, 565],
        [1342735200000, 568],
        [1342994400000, 570],
        [1343080800000, 571],
        [1343167200000, 555],
        [1343253600000, 550],
        [1343340000000, 540],
        [1343599200000, 530],
        [1343685600000, 533],
        [1343772000000, 534],
        [1343858400000, 535],
        [1343944800000, 538],
        [1344204000000, 538],
        [1344290400000, 540],
        [1344376800000, 540],
        [1344463200000, 545],
        [1344549600000, 547],
        [1344808800000, 548],
        [1344895200000, 548],
        [1344981600000, 548],
        [1345068000000, 549],
        [1345154400000, 550],
        [1345413600000, 551],
        [1345500000000, 552],
        [1345586400000, 553],
        [1345672800000, 551],
        [1345759200000, 555],
        [1346018400000, 551],
        [1346104800000, 553],
        [1346191200000, 523],
        [1346277600000, 524],
        [1346364000000, 526],
        [1346709600000, 527],
        [1347400800000, 500],
        [1347487200000, 502],
        [1347573600000, 504],
        [1347832800000, 505],
        [1347919200000, 506],
        [1348005600000, 509],
        [1348092000000, 512],
        [1348178400000, 514],
        [1348437600000, 518],
        [1348524000000, 520],
        [1348610400000, 525],
        [1348696800000, 526],
        [1348783200000, 512],
        [1349042400000, 514],
        [1349128800000, 516],
        [1349215200000, 517],
        [1349301600000, 520],
        [1349388000000, 521],
        [1349647200000, 522],
        [1349733600000, 523],
        [1349820000000, 525],
        [1349906400000, 530],
        [1349992800000, 570],
        [1350252000000, 571],
        [1350338400000, 572],
        [1350424800000, 572],
        [1350511200000, 573],
        [1350597600000, 575],
        [1350856800000, 577],
        [1350943200000, 578],
        [1351029600000, 578],
        [1351116000000, 580],
        [1351202400000, 581],
        [1351638000000, 582],
        [1351724400000, 584],
        [1351810800000, 580],
        [1352070000000, 580],
        [1352156400000, 575],
        [1352242800000, 560],
        [1352329200000, 550],
        [1352415600000, 552],
        [1352674800000, 554],
        [1352761200000, 557],
        [1352847600000, 550],
        [1352934000000, 560],
        [1353020400000, 564],
        [1353279600000, 567],
        [1353366000000, 569],
        [1353452400000, 572],
        [1353625200000, 575],
        [1353884400000, 578],
        [1353970800000, 580],
        [1354057200000, 580],
        [1354143600000, 583],
        [1354230000000, 585],
        [1354489200000, 589],
        [1354575600000, 590],
        [1354662000000, 600],
        [1354748400000, 590],
        [1354834800000, 585],
        [1355094000000, 580],
        [1355180400000, 565],
        [1355266800000, 570],
        [1355353200000, 571],
        [1355439600000, 560],
        [1355698800000, 565],
        [1355785200000, 550],
        [1355871600000, 540],
        [1355958000000, 535],
        [1356044400000, 530],
        [1356303600000, 520],
        [1356476400000, 515],
        [1356562800000, 517],
        [1356649200000, 518],
        [1356908400000, 520],
        [1357081200000, 523],
        [1357167600000, 525],
        [1357254000000, 527],
        [1357254000000, 530],
        [1357254000000, 532],
        [1357254000000, 535],
        [1357254000000, 530],
        [1357254000000, 540],
        [1357254000000, 543],
        [1357254000000, 544],
        [1357254000000, 545],
        [1357254000000, 550],
        [1357254000000, 560],
        [1357254000000, 563],
        [1357254000000, 560],
        [1357254000000, 562],
        [1357254000000, 568],
        [1357254000000, 580],
        [1357254000000, 585],
        [1357254000000, 590],
        [1357254000000, 600],
        [1357254000000, 610],
        [1357254000000, 615],
        [1357254000000, 620],
        [1357513200000, 628],
        [1357599600000, 621],
        [1357686000000, 623],
        [1357772400000, 620],
        [1357858800000, 620],
        [1358118000000, 621],
        [1358204400000, 620],
        [1358290800000, 625],
        [1358377200000, 630],
        [1358463600000, 632],
        [1358809200000, 635],
        [1358895600000, 640],
        [1358982000000, 642],
        [1359068400000, 645],
        [1359327600000, 649],
        [1359414000000, 648],
        [1359500400000, 647],
        [1359586800000, 645],
        [1359673200000, 644],
        [1359932400000, 643],
        [1360018800000, 644],
        [1360105200000, 641],
        [1360191600000, 640],
        [1360278000000, 634],
        [1360537200000, 640],
        [1360623600000, 642],
        [1360710000000, 640],
        [1360796400000, 620],
        [1360882800000, 620],
        [1361228400000, 620],
        [1361314800000, 620],
        [1361401200000, 620],
        [1361487600000, 620],
        [1361746800000, 610],
        [1361833200000, 600],
      ],
    },
    teamsSeries: {
      name: 'Teams',
      data: [
        [1327359600000, 500],
        [1327446000000, 502],
        [1327532400000, 503],
        [1327618800000, 504],
        [1327878000000, 508],
        [1327964400000, 510],
        [1328050800000, 512],
        [1328137200000, 514],
        [1328223600000, 515],
        [1328482800000, 516],
        [1328569200000, 520],
        [1328655600000, 522],
        [1328742000000, 525],
        [1328828400000, 527],
        [1329087600000, 528],
        [1329174000000, 530],
        [1329260400000, 528],
        [1329346800000, 525],
        [1329433200000, 524],
        [1329778800000, 523],
        [1329865200000, 522],
        [1329951600000, 520],
        [1330038000000, 519],
        [1330297200000, 518],
        [1330383600000, 516],
        [1330470000000, 520],
        [1330556400000, 522],
        [1330642800000, 524],
        [1330902000000, 526],
        [1330988400000, 528],
        [1331074800000, 530],
        [1331161200000, 532],
        [1331247600000, 533],
        [1331506800000, 535],
        [1331593200000, 540],
        [1331679600000, 545],
        [1331766000000, 546],
        [1331852400000, 550],
        [1332111600000, 551],
        [1332198000000, 555],
        [1332284400000, 557],
        [1332370800000, 560],
        [1332457200000, 580],
        [1332712800000, 582],
        [1332799200000, 583],
        [1332885600000, 585],
        [1332972000000, 586],
        [1333058400000, 588],
        [1333317600000, 589],
        [1333404000000, 590],
        [1333490400000, 580],
        [1333576800000, 579],
        [1333922400000, 575],
        [1334008800000, 574],
        [1334095200000, 577],
        [1334181600000, 570],
        [1334268000000, 569],
        [1334527200000, 568],
        [1334613600000, 565],
        [1334700000000, 565],
        [1334786400000, 560],
        [1334872800000, 561],
        [1335132000000, 570],
        [1335218400000, 572],
        [1335304800000, 575],
        [1335391200000, 580],
        [1335477600000, 585],
        [1335736800000, 590],
        [1335823200000, 591],
        [1335909600000, 592],
        [1335996000000, 600],
        [1336082400000, 602],
        [1336341600000, 604],
        [1336428000000, 603],
        [1336514400000, 605],
        [1336600800000, 606],
        [1336687200000, 608],
        [1336946400000, 610],
        [1337032800000, 611],
        [1337119200000, 602],
        [1337205600000, 600],
        [1337292000000, 601],
        [1337551200000, 602],
        [1337637600000, 603],
        [1337724000000, 550],
        [1337810400000, 549],
        [1337896800000, 545],
        [1338242400000, 543],
        [1338328800000, 541],
        [1338415200000, 540],
        [1338501600000, 538],
        [1338760800000, 535],
        [1338847200000, 532],
        [1338933600000, 530],
        [1339020000000, 520],
        [1339106400000, 520],
        [1339365600000, 521],
        [1339452000000, 522],
        [1339538400000, 521],
        [1339624800000, 522],
        [1339711200000, 515],
        [1339970400000, 517],
        [1340056800000, 510],
        [1340143200000, 520],
        [1340229600000, 522],
        [1340316000000, 525],
        [1340575200000, 526],
        [1340661600000, 536],
        [1340748000000, 535],
        [1340834400000, 537],
        [1340920800000, 538],
        [1341180000000, 540],
        [1341266400000, 542],
        [1341439200000, 550],
        [1341525600000, 580],
        [1341784800000, 579],
        [1341871200000, 578],
        [1341957600000, 576],
        [1342044000000, 575],
        [1342130400000, 574],
        [1342389600000, 572],
        [1342476000000, 560],
        [1342562400000, 562],
        [1342648800000, 565],
        [1342735200000, 568],
        [1342994400000, 570],
        [1343080800000, 571],
        [1343167200000, 555],
        [1343253600000, 550],
        [1343340000000, 540],
        [1343599200000, 530],
        [1343685600000, 533],
        [1343772000000, 534],
        [1343858400000, 535],
        [1343944800000, 538],
        [1344204000000, 538],
        [1344290400000, 540],
        [1344376800000, 540],
        [1344463200000, 545],
        [1344549600000, 547],
        [1344808800000, 548],
        [1344895200000, 548],
        [1344981600000, 548],
        [1345068000000, 549],
        [1345154400000, 550],
        [1345413600000, 551],
        [1345500000000, 552],
        [1345586400000, 553],
        [1345672800000, 551],
        [1345759200000, 555],
        [1346018400000, 551],
        [1346104800000, 553],
        [1346191200000, 523],
        [1346277600000, 524],
        [1346364000000, 526],
        [1346709600000, 527],
        [1347400800000, 500],
        [1347487200000, 502],
        [1347573600000, 504],
        [1347832800000, 505],
        [1347919200000, 506],
        [1348005600000, 509],
        [1348092000000, 512],
        [1348178400000, 514],
        [1348437600000, 518],
        [1348524000000, 520],
        [1348610400000, 525],
        [1348696800000, 526],
        [1348783200000, 512],
        [1349042400000, 514],
        [1349128800000, 516],
        [1349215200000, 517],
        [1349301600000, 520],
        [1349388000000, 521],
        [1349647200000, 522],
        [1349733600000, 523],
        [1349820000000, 525],
        [1349906400000, 530],
        [1349992800000, 570],
        [1350252000000, 571],
        [1350338400000, 572],
        [1350424800000, 572],
        [1350511200000, 573],
        [1350597600000, 575],
        [1350856800000, 577],
        [1350943200000, 578],
        [1351029600000, 578],
        [1351116000000, 580],
        [1351202400000, 581],
        [1351638000000, 582],
        [1351724400000, 584],
        [1351810800000, 580],
        [1352070000000, 580],
        [1352156400000, 575],
        [1352242800000, 560],
        [1352329200000, 550],
        [1352415600000, 552],
        [1352674800000, 554],
        [1352761200000, 557],
        [1352847600000, 550],
        [1352934000000, 560],
        [1353020400000, 564],
        [1353279600000, 567],
        [1353366000000, 569],
        [1353452400000, 572],
        [1353625200000, 575],
        [1353884400000, 578],
        [1353970800000, 580],
        [1354057200000, 580],
        [1354143600000, 583],
        [1354230000000, 585],
        [1354489200000, 589],
        [1354575600000, 590],
        [1354662000000, 600],
        [1354748400000, 590],
        [1354834800000, 585],
        [1355094000000, 580],
        [1355180400000, 565],
        [1355266800000, 570],
        [1355353200000, 571],
        [1355439600000, 560],
        [1355698800000, 565],
        [1355785200000, 550],
        [1355871600000, 540],
        [1355958000000, 535],
        [1356044400000, 530],
        [1356303600000, 520],
        [1356476400000, 515],
        [1356562800000, 517],
        [1356649200000, 518],
        [1356908400000, 520],
        [1357081200000, 523],
        [1357167600000, 525],
        [1357254000000, 527],
        [1357254000000, 530],
        [1357254000000, 532],
        [1357254000000, 535],
        [1357254000000, 530],
        [1357254000000, 540],
        [1357254000000, 543],
        [1357254000000, 544],
        [1357254000000, 545],
        [1357254000000, 550],
        [1357254000000, 560],
        [1357254000000, 563],
        [1357254000000, 560],
        [1357254000000, 562],
        [1357254000000, 568],
        [1357254000000, 580],
        [1357254000000, 585],
        [1357254000000, 590],
        [1357254000000, 600],
        [1357254000000, 610],
        [1357254000000, 615],
        [1357254000000, 620],
        [1357513200000, 628],
        [1357599600000, 621],
        [1357686000000, 623],
        [1357772400000, 620],
        [1357858800000, 620],
        [1358118000000, 621],
        [1358204400000, 620],
        [1358290800000, 625],
        [1358377200000, 630],
        [1358463600000, 632],
        [1358809200000, 635],
        [1358895600000, 640],
        [1358982000000, 642],
        [1359068400000, 645],
        [1359327600000, 649],
        [1359414000000, 648],
        [1359500400000, 647],
        [1359586800000, 645],
        [1359673200000, 644],
        [1359932400000, 643],
        [1360018800000, 644],
        [1360105200000, 641],
        [1360191600000, 640],
        [1360278000000, 634],
        [1360537200000, 640],
        [1360623600000, 642],
        [1360710000000, 640],
        [1360796400000, 620],
        [1360882800000, 620],
        [1361228400000, 620],
        [1361314800000, 620],
        [1361401200000, 620],
        [1361487600000, 620],
        [1361746800000, 610],
        [1361833200000, 600],
      ],
    },
    individualsSeries: {
      name: 'Individuals',
      data: [
        [1327359600000, 500],
        [1327446000000, 502],
        [1327532400000, 503],
        [1327618800000, 504],
        [1327878000000, 508],
        [1327964400000, 510],
        [1328050800000, 512],
        [1328137200000, 514],
        [1328223600000, 515],
        [1328482800000, 516],
        [1328569200000, 520],
        [1328655600000, 522],
        [1328742000000, 525],
        [1328828400000, 527],
        [1329087600000, 528],
        [1329174000000, 530],
        [1329260400000, 528],
        [1329346800000, 525],
        [1329433200000, 524],
        [1329778800000, 523],
        [1329865200000, 522],
        [1329951600000, 520],
        [1330038000000, 519],
        [1330297200000, 518],
        [1330383600000, 516],
        [1330470000000, 520],
        [1330556400000, 522],
        [1330642800000, 524],
        [1330902000000, 526],
        [1330988400000, 528],
        [1331074800000, 530],
        [1331161200000, 532],
        [1331247600000, 533],
        [1331506800000, 535],
        [1331593200000, 540],
        [1331679600000, 545],
        [1331766000000, 546],
        [1331852400000, 550],
        [1332111600000, 551],
        [1332198000000, 555],
        [1332284400000, 557],
        [1332370800000, 560],
        [1332457200000, 580],
        [1332712800000, 582],
        [1332799200000, 583],
        [1332885600000, 585],
        [1332972000000, 586],
        [1333058400000, 588],
        [1333317600000, 589],
        [1333404000000, 590],
        [1333490400000, 580],
        [1333576800000, 579],
        [1333922400000, 575],
        [1334008800000, 574],
        [1334095200000, 577],
        [1334181600000, 570],
        [1334268000000, 569],
        [1334527200000, 568],
        [1334613600000, 565],
        [1334700000000, 565],
        [1334786400000, 560],
        [1334872800000, 561],
        [1335132000000, 570],
        [1335218400000, 572],
        [1335304800000, 575],
        [1335391200000, 580],
        [1335477600000, 585],
        [1335736800000, 590],
        [1335823200000, 591],
        [1335909600000, 592],
        [1335996000000, 600],
        [1336082400000, 602],
        [1336341600000, 604],
        [1336428000000, 603],
        [1336514400000, 605],
        [1336600800000, 606],
        [1336687200000, 608],
        [1336946400000, 610],
        [1337032800000, 611],
        [1337119200000, 602],
        [1337205600000, 600],
        [1337292000000, 601],
        [1337551200000, 602],
        [1337637600000, 603],
        [1337724000000, 550],
        [1337810400000, 549],
        [1337896800000, 545],
        [1338242400000, 543],
        [1338328800000, 541],
        [1338415200000, 540],
        [1338501600000, 538],
        [1338760800000, 535],
        [1338847200000, 532],
        [1338933600000, 530],
        [1339020000000, 520],
        [1339106400000, 520],
        [1339365600000, 521],
        [1339452000000, 522],
        [1339538400000, 521],
        [1339624800000, 522],
        [1339711200000, 515],
        [1339970400000, 517],
        [1340056800000, 510],
        [1340143200000, 520],
        [1340229600000, 522],
        [1340316000000, 525],
        [1340575200000, 526],
        [1340661600000, 536],
        [1340748000000, 535],
        [1340834400000, 537],
        [1340920800000, 538],
        [1341180000000, 540],
        [1341266400000, 542],
        [1341439200000, 550],
        [1341525600000, 580],
        [1341784800000, 579],
        [1341871200000, 578],
        [1341957600000, 576],
        [1342044000000, 575],
        [1342130400000, 574],
        [1342389600000, 572],
        [1342476000000, 560],
        [1342562400000, 562],
        [1342648800000, 565],
        [1342735200000, 568],
        [1342994400000, 570],
        [1343080800000, 571],
        [1343167200000, 555],
        [1343253600000, 550],
        [1343340000000, 540],
        [1343599200000, 530],
        [1343685600000, 533],
        [1343772000000, 534],
        [1343858400000, 535],
        [1343944800000, 538],
        [1344204000000, 538],
        [1344290400000, 540],
        [1344376800000, 540],
        [1344463200000, 545],
        [1344549600000, 547],
        [1344808800000, 548],
        [1344895200000, 548],
        [1344981600000, 548],
        [1345068000000, 549],
        [1345154400000, 550],
        [1345413600000, 551],
        [1345500000000, 552],
        [1345586400000, 553],
        [1345672800000, 551],
        [1345759200000, 555],
        [1346018400000, 551],
        [1346104800000, 553],
        [1346191200000, 523],
        [1346277600000, 524],
        [1346364000000, 526],
        [1346709600000, 527],
        [1347400800000, 500],
        [1347487200000, 502],
        [1347573600000, 504],
        [1347832800000, 505],
        [1347919200000, 506],
        [1348005600000, 509],
        [1348092000000, 512],
        [1348178400000, 514],
        [1348437600000, 518],
        [1348524000000, 520],
        [1348610400000, 525],
        [1348696800000, 526],
        [1348783200000, 512],
        [1349042400000, 514],
        [1349128800000, 516],
        [1349215200000, 517],
        [1349301600000, 520],
        [1349388000000, 521],
        [1349647200000, 522],
        [1349733600000, 523],
        [1349820000000, 525],
        [1349906400000, 530],
        [1349992800000, 570],
        [1350252000000, 571],
        [1350338400000, 572],
        [1350424800000, 572],
        [1350511200000, 573],
        [1350597600000, 575],
        [1350856800000, 577],
        [1350943200000, 578],
        [1351029600000, 578],
        [1351116000000, 580],
        [1351202400000, 581],
        [1351638000000, 582],
        [1351724400000, 584],
        [1351810800000, 580],
        [1352070000000, 580],
        [1352156400000, 575],
        [1352242800000, 560],
        [1352329200000, 550],
        [1352415600000, 552],
        [1352674800000, 554],
        [1352761200000, 557],
        [1352847600000, 550],
        [1352934000000, 560],
        [1353020400000, 564],
        [1353279600000, 567],
        [1353366000000, 569],
        [1353452400000, 572],
        [1353625200000, 575],
        [1353884400000, 578],
        [1353970800000, 580],
        [1354057200000, 580],
        [1354143600000, 583],
        [1354230000000, 585],
        [1354489200000, 589],
        [1354575600000, 590],
        [1354662000000, 600],
        [1354748400000, 590],
        [1354834800000, 585],
        [1355094000000, 580],
        [1355180400000, 565],
        [1355266800000, 570],
        [1355353200000, 571],
        [1355439600000, 560],
        [1355698800000, 565],
        [1355785200000, 550],
        [1355871600000, 540],
        [1355958000000, 535],
        [1356044400000, 530],
        [1356303600000, 520],
        [1356476400000, 515],
        [1356562800000, 517],
        [1356649200000, 518],
        [1356908400000, 520],
        [1357081200000, 523],
        [1357167600000, 525],
        [1357254000000, 527],
        [1357254000000, 530],
        [1357254000000, 532],
        [1357254000000, 535],
        [1357254000000, 530],
        [1357254000000, 540],
        [1357254000000, 543],
        [1357254000000, 544],
        [1357254000000, 545],
        [1357254000000, 550],
        [1357254000000, 560],
        [1357254000000, 563],
        [1357254000000, 560],
        [1357254000000, 562],
        [1357254000000, 568],
        [1357254000000, 580],
        [1357254000000, 585],
        [1357254000000, 590],
        [1357254000000, 600],
        [1357254000000, 610],
        [1357254000000, 615],
        [1357254000000, 620],
        [1357513200000, 628],
        [1357599600000, 621],
        [1357686000000, 623],
        [1357772400000, 620],
        [1357858800000, 620],
        [1358118000000, 621],
        [1358204400000, 620],
        [1358290800000, 625],
        [1358377200000, 630],
        [1358463600000, 632],
        [1358809200000, 635],
        [1358895600000, 640],
        [1358982000000, 642],
        [1359068400000, 645],
        [1359327600000, 649],
        [1359414000000, 648],
        [1359500400000, 647],
        [1359586800000, 645],
        [1359673200000, 644],
        [1359932400000, 643],
        [1360018800000, 644],
        [1360105200000, 641],
        [1360191600000, 640],
        [1360278000000, 634],
        [1360537200000, 640],
        [1360623600000, 642],
        [1360710000000, 640],
        [1360796400000, 620],
        [1360882800000, 620],
        [1361228400000, 620],
        [1361314800000, 620],
        [1361401200000, 620],
        [1361487600000, 620],
        [1361746800000, 610],
        [1361833200000, 600],
      ],
    },

    trendLineSeries: [
      {
        name: 'Trendline',
        data: [
          [1327359600000, 450],
          [1337551200000, 440],
          [1339106400000, 430],
          [1340920800000, 440],
          [1348610400000, 430],
          [1350597600000, 420],
          [1355266800000, 410],
          [1355958000000, 400],
          [1357772400000, 390],
          [1359068400000, 380],
          [1361487600000, 370],
          [1361833200000, 360],
        ],
      },
    ],

    allFlagLineSeries: [
      {
        name: 'All Flags',
        data: [
          [1327359600000, 400],
          [1327446000000, 402],
          [1327532400000, 403],
          [1327618800000, 404],
          [1327878000000, 408],
          [1327964400000, 410],
          [1328050800000, 412],
          [1328137200000, 414],
          [1328223600000, 415],
          [1328482800000, 416],
          [1328569200000, 420],
          [1328655600000, 418],
          [1328742000000, 415],
          [1328828400000, 416],
          [1329087600000, 410],
          [1329174000000, 412],
          [1329260400000, 415],
          [1329346800000, 410],
          [1329433200000, 408],
          [1329778800000, 405],
          [1329865200000, 400],
          [1329951600000, 401],
          [1330038000000, 395],
          [1330297200000, 392],
          [1330383600000, 390],
          [1330470000000, 385],
          [1330556400000, 382],
          [1330642800000, 380],
          [1330902000000, 378],
          [1330988400000, 375],
          [1331074800000, 370],
          [1331161200000, 371],
          [1331247600000, 372],
          [1331506800000, 375],
          [1331593200000, 377],
          [1331679600000, 370],
          [1331766000000, 367],
          [1331852400000, 365],
          [1332111600000, 364],
          [1332198000000, 360],
          [1332284400000, 355],
          [1332370800000, 352],
          [1332457200000, 355],
          [1332712800000, 353],
          [1332799200000, 350],
          [1332885600000, 347],
          [1332972000000, 340],
          [1333058400000, 339],
          [1333317600000, 340],
          [1333404000000, 320],
          [1333490400000, 310],
          [1333576800000, 300],
          [1333922400000, 297],
          [1334008800000, 298],
          [1334095200000, 299],
          [1334181600000, 295],
          [1334268000000, 280],
          [1334527200000, 275],
          [1334613600000, 273],
          [1334700000000, 270],
          [1334786400000, 280],
          [1334872800000, 282],
          [1335132000000, 283],
          [1335218400000, 290],
          [1335304800000, 292],
          [1335391200000, 280],
          [1335477600000, 270],
          [1335736800000, 260],
          [1335823200000, 250],
          [1335909600000, 255],
          [1335996000000, 240],
          [1336082400000, 220],
          [1336341600000, 222],
          [1336428000000, 225],
          [1336514400000, 230],
          [1336600800000, 232],
          [1336687200000, 235],
          [1336946400000, 240],
          [1337032800000, 245],
          [1337119200000, 250],
          [1337205600000, 260],
          [1337292000000, 270],
          [1337551200000, 280],
          [1337637600000, 300],
          [1337724000000, 310],
          [1337810400000, 330],
          [1337896800000, 325],
          [1338242400000, 324],
          [1338328800000, 320],
          [1338415200000, 318],
          [1338501600000, 316],
          [1338760800000, 315],
          [1338847200000, 310],
          [1338933600000, 305],
          [1339020000000, 303],
          [1339106400000, 300],
          [1339365600000, 298],
          [1339452000000, 297],
          [1339538400000, 295],
          [1339624800000, 290],
          [1339711200000, 293],
          [1339970400000, 298],
          [1340056800000, 300],
          [1340143200000, 305],
          [1340229600000, 304],
          [1340316000000, 310],
          [1340575200000, 315],
          [1340661600000, 320],
          [1340748000000, 330],
          [1340834400000, 337],
          [1340920800000, 338],
          [1341180000000, 340],
          [1341266400000, 342],
          [1341439200000, 350],
          [1341525600000, 350],
          [1341784800000, 359],
          [1341871200000, 358],
          [1341957600000, 366],
          [1342044000000, 365],
          [1342130400000, 364],
          [1342389600000, 362],
          [1342476000000, 370],
          [1342562400000, 372],
          [1342648800000, 375],
          [1342735200000, 378],
          [1342994400000, 370],
          [1343080800000, 371],
          [1343167200000, 375],
          [1343253600000, 380],
          [1343340000000, 380],
          [1343599200000, 390],
          [1343685600000, 393],
          [1343772000000, 394],
          [1343858400000, 395],
          [1343944800000, 398],
          [1344204000000, 398],
          [1344290400000, 400],
          [1344376800000, 390],
          [1344463200000, 395],
          [1344549600000, 397],
          [1344808800000, 398],
          [1344895200000, 388],
          [1344981600000, 388],
          [1345068000000, 389],
          [1345154400000, 370],
          [1345413600000, 361],
          [1345500000000, 352],
          [1345586400000, 353],
          [1345672800000, 345],
          [1345759200000, 343],
          [1346018400000, 341],
          [1346104800000, 320],
          [1346191200000, 325],
          [1346277600000, 326],
          [1346364000000, 328],
          [1346709600000, 330],
          [1347400800000, 333],
          [1347487200000, 340],
          [1347573600000, 330],
          [1347832800000, 328],
          [1347919200000, 325],
          [1348005600000, 320],
          [1348092000000, 321],
          [1348178400000, 318],
          [1348437600000, 315],
          [1348524000000, 313],
          [1348610400000, 310],
          [1348696800000, 308],
          [1348783200000, 305],
          [1349042400000, 300],
          [1349128800000, 295],
          [1349215200000, 294],
          [1349301600000, 293],
          [1349388000000, 280],
          [1349647200000, 270],
          [1349733600000, 260],
          [1349820000000, 250],
          [1349906400000, 260],
          [1349992800000, 262],
          [1350252000000, 267],
          [1350338400000, 270],
          [1350424800000, 272],
          [1350511200000, 273],
          [1350597600000, 275],
          [1350856800000, 277],
          [1350943200000, 278],
          [1351029600000, 278],
          [1351116000000, 280],
          [1351202400000, 281],
          [1351638000000, 282],
          [1351724400000, 284],
          [1351810800000, 290],
          [1352070000000, 290],
          [1352156400000, 295],
          [1352242800000, 300],
          [1352329200000, 310],
          [1352415600000, 312],
          [1352674800000, 314],
          [1352761200000, 317],
          [1352847600000, 320],
          [1352934000000, 320],
          [1353020400000, 324],
          [1353279600000, 327],
          [1353366000000, 329],
          [1353452400000, 332],
          [1353625200000, 335],
          [1353884400000, 338],
          [1353970800000, 350],
          [1354057200000, 360],
          [1354143600000, 353],
          [1354230000000, 355],
          [1354489200000, 359],
          [1354575600000, 350],
          [1354662000000, 360],
          [1354748400000, 360],
          [1354834800000, 365],
          [1355094000000, 370],
          [1355180400000, 375],
          [1355266800000, 380],
          [1355353200000, 381],
          [1355439600000, 380],
          [1355698800000, 385],
          [1355785200000, 380],
          [1355871600000, 380],
          [1355958000000, 385],
          [1356044400000, 390],
          [1356303600000, 400],
          [1356476400000, 405],
          [1356562800000, 407],
          [1356649200000, 418],
          [1356908400000, 420],
          [1357081200000, 423],
          [1357167600000, 425],
          [1357254000000, 427],
          [1357254000000, 430],
          [1357254000000, 432],
          [1357254000000, 435],
          [1357254000000, 430],
          [1357254000000, 420],
          [1357254000000, 410],
          [1357254000000, 400],
          [1357254000000, 395],
          [1357254000000, 390],
          [1357254000000, 380],
          [1357254000000, 373],
          [1357254000000, 360],
          [1357254000000, 352],
          [1357254000000, 358],
          [1357254000000, 350],
          [1357254000000, 355],
          [1357254000000, 340],
          [1357254000000, 330],
          [1357254000000, 320],
          [1357254000000, 325],
          [1357254000000, 320],
          [1357513200000, 328],
          [1357599600000, 321],
          [1357686000000, 323],
          [1357772400000, 330],
          [1357858800000, 340],
          [1358118000000, 310],
          [1358204400000, 300],
          [1358290800000, 300],
          [1358377200000, 290],
          [1358463600000, 280],
          [1358809200000, 270],
          [1358895600000, 260],
          [1358982000000, 250],
          [1359068400000, 260],
          [1359327600000, 262],
          [1359414000000, 267],
          [1359500400000, 270],
          [1359586800000, 272],
          [1359673200000, 275],
          [1359932400000, 280],
          [1360018800000, 282],
          [1360105200000, 285],
          [1360191600000, 290],
          [1360278000000, 295],
          [1360537200000, 300],
          [1360623600000, 305],
          [1360710000000, 300],
          [1360796400000, 305],
          [1360882800000, 306],
          [1361228400000, 307],
          [1361314800000, 310],
          [1361401200000, 310],
          [1361487600000, 312],
          [1361746800000, 315],
          [1361833200000, 320],
        ],
      },
    ],
    individualTeamLineSeries: [
      {
        name: 'Term #1',
        data: [
          [1327359600000, 100],
          [1327446000000, 103],
          [1327532400000, 103],
          [1327618800000, 103],
          [1327878000000, 103],
          [1327964400000, 100],
          [1328050800000, 103],
          [1328137200000, 103],
          [1328223600000, 103],
          [1328482800000, 103],
          [1328569200000, 105],
          [1328655600000, 105],
          [1328742000000, 105],
          [1328828400000, 105],
          [1329087600000, 105],
          [1329174000000, 105],
          [1329260400000, 105],
          [1329346800000, 105],
          [1329433200000, 105],
          [1329778800000, 105],
          [1329865200000, 105],
          [1329951600000, 105],
          [1330038000000, 110],
          [1330297200000, 110],
          [1330383600000, 110],
          [1330470000000, 105],
          [1330556400000, 110],
          [1330642800000, 110],
          [1330902000000, 110],
          [1330988400000, 103],
          [1331074800000, 105],
          [1331161200000, 110],
          [1331247600000, 110],
          [1331506800000, 110],
          [1331593200000, 115],
          [1331679600000, 110],
          [1331766000000, 115],
          [1331852400000, 110],
          [1332111600000, 115],
          [1332198000000, 110],
          [1332284400000, 110],
          [1332370800000, 110],
          [1332457200000, 110],
          [1332712800000, 115],
          [1332799200000, 115],
          [1332885600000, 115],
          [1332972000000, 115],
          [1333058400000, 115],
          [1333317600000, 115],
          [1333404000000, 115],
          [1333490400000, 110],
          [1333576800000, 110],
          [1333922400000, 110],
          [1334008800000, 105],
          [1334095200000, 110],
          [1334181600000, 110],
          [1334268000000, 110],
          [1334527200000, 105],
          [1334613600000, 110],
          [1334700000000, 110],
          [1334786400000, 105],
          [1334872800000, 110],
          [1335132000000, 105],
          [1335218400000, 105],
          [1335304800000, 110],
          [1335391200000, 110],
          [1335477600000, 110],
          [1335736800000, 110],
          [1335823200000, 110],
          [1335909600000, 110],
          [1335996000000, 110],
          [1336082400000, 105],
          [1336341600000, 105],
          [1336428000000, 105],
          [1336514400000, 105],
          [1336600800000, 103],
          [1336687200000, 105],
          [1336946400000, 105],
          [1337032800000, 105],
          [1337119200000, 105],
          [1337205600000, 105],
          [1337292000000, 103],
          [1337551200000, 105],
          [1337637600000, 105],
          [1337724000000, 105],
          [1337810400000, 103],
          [1337896800000, 103],
          [1338242400000, 105],
          [1338328800000, 103],
          [1338415200000, 103],
          [1338501600000, 110],
          [1338760800000, 100],
          [1338847200000, 100],
          [1338933600000, 103],
          [1339020000000, 103],
          [1339106400000, 103],
          [1339365600000, 103],
          [1339452000000, 103],
          [1339538400000, 103],
          [1339624800000, 103],
          [1339711200000, 105],
          [1339970400000, 105],
          [1340056800000, 105],
          [1340143200000, 103],
          [1340229600000, 103],
          [1340316000000, 103],
          [1340575200000, 100],
          [1340661600000, 103],
          [1340748000000, 103],
          [1340834400000, 103],
          [1340920800000, 105],
          [1341180000000, 105],
          [1341266400000, 105],
          [1341439200000, 105],
          [1341525600000, 103],
          [1341784800000, 100],
          [1341871200000, 100],
          [1341957600000, 100],
          [1342044000000, 100],
          [1342130400000, 100],
          [1342389600000, 100],
          [1342476000000, 100],
          [1342562400000, 103],
          [1342648800000, 103],
          [1342735200000, 103],
          [1342994400000, 100],
          [1343080800000, 100],
          [1343167200000, 100],
          [1343253600000, 100],
          [1343340000000, 103],
          [1343599200000, 103],
          [1343685600000, 100],
          [1343772000000, 100],
          [1343858400000, 100],
          [1343944800000, 105],
          [1344204000000, 105],
          [1344290400000, 105],
          [1344376800000, 105],
          [1344463200000, 105],
          [1344549600000, 105],
          [1344808800000, 105],
          [1344895200000, 105],
          [1344981600000, 105],
          [1345068000000, 110],
          [1345154400000, 110],
          [1345413600000, 110],
          [1345500000000, 110],
          [1345586400000, 110],
          [1345672800000, 110],
          [1345759200000, 105],
          [1346018400000, 105],
          [1346104800000, 105],
          [1346191200000, 103],
          [1346277600000, 103],
          [1346364000000, 103],
          [1346709600000, 103],
          [1346796000000, 105],
          [1346882400000, 105],
          [1346968800000, 105],
          [1347228000000, 105],
          [1347314400000, 105],
          [1347400800000, 105],
          [1347487200000, 105],
          [1347573600000, 110],
          [1347832800000, 110],
          [1347919200000, 105],
          [1348005600000, 110],
          [1348092000000, 115],
          [1348178400000, 110],
          [1348437600000, 110],
          [1348524000000, 105],
          [1348610400000, 105],
          [1348696800000, 105],
          [1348783200000, 105],
          [1349042400000, 105],
          [1349128800000, 105],
          [1349215200000, 105],
          [1349301600000, 105],
          [1349388000000, 105],
          [1349647200000, 105],
          [1349733600000, 103],
          [1349820000000, 103],
          [1349906400000, 103],
          [1349992800000, 103],
          [1350252000000, 105],
          [1350338400000, 110],
          [1350424800000, 110],
          [1350511200000, 110],
          [1350597600000, 110],
          [1350856800000, 110],
          [1350943200000, 110],
          [1351029600000, 110],
          [1351116000000, 110],
          [1351202400000, 115],
          [1351638000000, 115],
          [1351724400000, 115],
          [1351810800000, 115],
          [1352070000000, 115],
          [1352156400000, 115],
          [1352242800000, 110],
          [1352329200000, 105],
          [1352415600000, 105],
          [1352674800000, 105],
          [1352761200000, 105],
          [1352847600000, 105],
          [1352934000000, 105],
          [1353020400000, 105],
          [1353279600000, 105],
          [1353366000000, 105],
          [1353452400000, 105],
          [1353625200000, 110],
          [1353884400000, 110],
          [1353970800000, 110],
          [1354057200000, 110],
          [1354143600000, 115],
          [1354230000000, 115],
          [1354489200000, 115],
          [1354575600000, 110],
          [1354662000000, 110],
          [1354748400000, 110],
          [1354834800000, 110],
          [1355094000000, 110],
          [1355180400000, 110],
          [1355266800000, 110],
          [1355353200000, 110],
          [1355439600000, 107],
          [1355698800000, 107],
          [1355785200000, 107],
          [1355871600000, 107],
          [1355958000000, 107],
          [1356044400000, 107],
          [1356303600000, 107],
          [1356476400000, 107],
          [1356562800000, 107],
          [1356649200000, 105],
          [1356908400000, 105],
          [1357081200000, 104],
          [1357167600000, 104],
          [1357254000000, 104],
          [1357513200000, 103],
          [1357599600000, 102],
          [1357686000000, 102],
          [1357772400000, 102],
          [1357858800000, 102],
          [1358118000000, 102],
          [1358204400000, 102],
          [1358290800000, 102],
          [1358377200000, 102],
          [1358463600000, 102],
          [1358809200000, 102],
          [1358895600000, 102],
          [1358982000000, 102],
          [1359068400000, 102],
          [1359327600000, 102],
          [1359414000000, 102],
          [1359500400000, 100],
          [1359586800000, 100],
          [1359673200000, 100],
          [1359932400000, 100],
          [1360018800000, 100],
          [1360105200000, 100],
          [1360191600000, 100],
          [1360278000000, 99],
          [1360537200000, 99],
          [1360623600000, 102],
          [1360710000000, 102],
          [1360796400000, 102],
          [1360882800000, 102],
          [1361228400000, 102],
          [1361314800000, 102],
          [1361401200000, 102],
          [1361487600000, 102],
          [1361746800000, 102],
          [1361833200000, 102],
          [1361919600000, 99],
        ],
      },
      {
        name: 'Term #2',
        data: [
          [1327359600000, 60],
          [1327446000000, 70],
          [1327532400000, 70],
          [1327618800000, 70],
          [1327878000000, 70],
          [1327964400000, 60],
          [1328050800000, 70],
          [1328137200000, 70],
          [1328223600000, 70],
          [1328482800000, 70],
          [1328569200000, 72],
          [1328655600000, 72],
          [1328742000000, 72],
          [1328828400000, 72],
          [1329087600000, 72],
          [1329174000000, 72],
          [1329260400000, 72],
          [1329346800000, 72],
          [1329433200000, 72],
          [1329778800000, 72],
          [1329865200000, 72],
          [1329951600000, 72],
          [1330038000000, 67],
          [1330297200000, 67],
          [1330383600000, 67],
          [1330470000000, 72],
          [1330556400000, 67],
          [1330642800000, 67],
          [1330902000000, 67],
          [1330988400000, 70],
          [1331074800000, 72],
          [1331161200000, 67],
          [1331247600000, 67],
          [1331506800000, 67],
          [1331593200000, 64],
          [1331679600000, 67],
          [1331766000000, 64],
          [1331852400000, 67],
          [1332111600000, 64],
          [1332198000000, 67],
          [1332284400000, 67],
          [1332370800000, 67],
          [1332457200000, 67],
          [1332712800000, 64],
          [1332799200000, 64],
          [1332885600000, 64],
          [1332972000000, 64],
          [1333058400000, 64],
          [1333317600000, 64],
          [1333404000000, 64],
          [1333490400000, 67],
          [1333576800000, 67],
          [1333922400000, 67],
          [1334008800000, 72],
          [1334095200000, 67],
          [1334181600000, 67],
          [1334268000000, 67],
          [1334527200000, 72],
          [1334613600000, 67],
          [1334700000000, 67],
          [1334786400000, 72],
          [1334872800000, 67],
          [1335132000000, 72],
          [1335218400000, 72],
          [1335304800000, 67],
          [1335391200000, 67],
          [1335477600000, 67],
          [1335736800000, 67],
          [1335823200000, 67],
          [1335909600000, 67],
          [1335996000000, 67],
          [1336082400000, 72],
          [1336341600000, 72],
          [1336428000000, 72],
          [1336514400000, 72],
          [1336600800000, 70],
          [1336687200000, 72],
          [1336946400000, 72],
          [1337032800000, 72],
          [1337119200000, 72],
          [1337205600000, 72],
          [1337292000000, 70],
          [1337551200000, 72],
          [1337637600000, 72],
          [1337724000000, 72],
          [1337810400000, 70],
          [1337896800000, 70],
          [1338242400000, 72],
          [1338328800000, 70],
          [1338415200000, 70],
          [1338501600000, 60],
          [1338760800000, 60],
          [1338847200000, 60],
          [1338933600000, 70],
          [1339020000000, 70],
          [1339106400000, 70],
          [1339365600000, 70],
          [1339452000000, 70],
          [1339538400000, 70],
          [1339624800000, 70],
          [1339711200000, 72],
          [1339970400000, 72],
          [1340056800000, 72],
          [1340143200000, 70],
          [1340229600000, 70],
          [1340316000000, 70],
          [1340575200000, 60],
          [1340661600000, 70],
          [1340748000000, 70],
          [1340834400000, 70],
          [1340920800000, 69],
          [1341180000000, 69],
          [1341266400000, 68],
          [1341439200000, 68],
          [1341525600000, 68],
          [1341784800000, 67],
          [1341871200000, 67],
          [1341957600000, 67],
          [1342044000000, 67],
          [1342130400000, 67],
          [1342389600000, 65],
          [1342476000000, 65],
          [1342562400000, 65],
          [1342648800000, 65],
          [1342735200000, 63],
          [1342994400000, 63],
          [1343080800000, 63],
          [1343167200000, 63],
          [1343253600000, 63],
          [1343340000000, 60],
          [1343599200000, 60],
          [1343685600000, 60],
          [1343772000000, 60],
          [1343858400000, 60],
          [1343944800000, 57],
          [1344204000000, 57],
          [1344290400000, 57],
          [1344376800000, 58],
          [1344463200000, 58],
          [1344549600000, 58],
          [1344808800000, 59],
          [1344895200000, 59],
          [1344981600000, 59],
          [1345068000000, 61],
          [1345154400000, 61],
          [1345413600000, 61],
          [1345500000000, 61],
          [1345586400000, 62],
          [1345672800000, 62],
          [1345759200000, 62],
          [1346018400000, 62],
          [1346104800000, 65],
          [1346191200000, 65],
          [1346277600000, 65],
          [1346364000000, 65],
          [1346709600000, 66],
          [1346796000000, 66],
          [1346882400000, 66],
          [1346968800000, 66],
          [1347228000000, 67],
          [1347314400000, 67],
          [1347400800000, 67],
          [1347487200000, 67],
          [1347573600000, 67],
          [1347832800000, 67],
          [1347919200000, 72],
          [1348005600000, 67],
          [1348092000000, 64],
          [1348178400000, 67],
          [1348437600000, 67],
          [1348524000000, 72],
          [1348610400000, 72],
          [1348696800000, 72],
          [1348783200000, 72],
          [1349042400000, 72],
          [1349128800000, 72],
          [1349215200000, 72],
          [1349301600000, 72],
          [1349388000000, 72],
          [1349647200000, 72],
          [1349733600000, 70],
          [1349820000000, 70],
          [1349906400000, 70],
          [1349992800000, 70],
          [1350252000000, 72],
          [1350338400000, 67],
          [1350424800000, 67],
          [1350511200000, 67],
          [1350597600000, 67],
          [1350856800000, 67],
          [1350943200000, 67],
          [1351029600000, 67],
          [1351116000000, 67],
          [1351202400000, 64],
          [1351638000000, 64],
          [1351724400000, 64],
          [1351810800000, 64],
          [1352070000000, 64],
          [1352156400000, 64],
          [1352242800000, 67],
          [1352329200000, 72],
          [1352415600000, 72],
          [1352674800000, 72],
          [1352761200000, 72],
          [1352847600000, 72],
          [1352934000000, 72],
          [1353020400000, 72],
          [1353279600000, 72],
          [1353366000000, 72],
          [1353452400000, 72],
          [1353625200000, 67],
          [1353884400000, 67],
          [1353970800000, 67],
          [1354057200000, 67],
          [1354143600000, 64],
          [1354230000000, 64],
          [1354489200000, 64],
          [1354575600000, 60],
          [1354662000000, 60],
          [1354748400000, 60],
          [1354834800000, 60],
          [1355094000000, 60],
          [1355180400000, 60],
          [1355266800000, 60],
          [1355353200000, 60],
          [1355439600000, 54],
          [1355698800000, 54],
          [1355785200000, 54],
          [1355871600000, 55],
          [1355958000000, 54],
          [1356044400000, 54],
          [1356303600000, 54],
          [1356476400000, 54],
          [1356562800000, 54],
          [1356649200000, 60],
          [1356908400000, 54],
          [1357081200000, 55],
          [1357167600000, 54],
          [1357254000000, 55],
          [1357513200000, 54],
          [1357599600000, 55],
          [1357686000000, 55],
          [1357772400000, 55],
          [1357858800000, 55],
          [1358118000000, 55],
          [1358204400000, 55],
          [1358290800000, 54],
          [1358377200000, 54],
          [1358463600000, 54],
          [1358809200000, 54],
          [1358895600000, 55],
          [1358982000000, 55],
          [1359068400000, 55],
          [1359327600000, 55],
          [1359414000000, 55],
          [1359500400000, 54],
          [1359586800000, 54],
          [1359673200000, 55],
          [1359932400000, 55],
          [1360018800000, 55],
          [1360105200000, 55],
          [1360191600000, 55],
          [1360278000000, 54],
          [1360537200000, 55],
          [1360623600000, 55],
          [1360710000000, 55],
          [1360796400000, 55],
          [1360882800000, 55],
          [1361228400000, 55],
          [1361314800000, 55],
          [1361401200000, 55],
          [1361487600000, 55],
          [1361746800000, 55],
          [1361833200000, 55],
          [1361919600000, 54],
        ],
      },
      {
        name: 'Term #3',
        data: [
          [1327359600000, 30],
          [1327446000000, 31],
          [1327532400000, 31],
          [1327618800000, 31],
          [1327878000000, 31],
          [1327964400000, 30],
          [1328050800000, 31],
          [1328137200000, 31],
          [1328223600000, 31],
          [1328482800000, 31],
          [1328569200000, 32],
          [1328655600000, 32],
          [1328742000000, 32],
          [1328828400000, 32],
          [1329087600000, 32],
          [1329174000000, 32],
          [1329260400000, 32],
          [1329346800000, 32],
          [1329433200000, 32],
          [1329778800000, 32],
          [1329865200000, 32],
          [1329951600000, 32],
          [1330038000000, 33],
          [1330297200000, 33],
          [1330383600000, 33],
          [1330470000000, 32],
          [1330556400000, 33],
          [1330642800000, 33],
          [1330902000000, 33],
          [1330988400000, 31],
          [1331074800000, 32],
          [1331161200000, 33],
          [1331247600000, 33],
          [1331506800000, 33],
          [1331593200000, 34],
          [1331679600000, 33],
          [1331766000000, 34],
          [1331852400000, 33],
          [1332111600000, 34],
          [1332198000000, 33],
          [1332284400000, 33],
          [1332370800000, 33],
          [1332457200000, 33],
          [1332712800000, 34],
          [1332799200000, 34],
          [1332885600000, 34],
          [1332972000000, 34],
          [1333058400000, 34],
          [1333317600000, 34],
          [1333404000000, 34],
          [1333490400000, 33],
          [1333576800000, 33],
          [1333922400000, 33],
          [1334008800000, 32],
          [1334095200000, 33],
          [1334181600000, 33],
          [1334268000000, 33],
          [1334527200000, 32],
          [1334613600000, 33],
          [1334700000000, 33],
          [1334786400000, 32],
          [1334872800000, 33],
          [1335132000000, 32],
          [1335218400000, 32],
          [1335304800000, 33],
          [1335391200000, 33],
          [1335477600000, 33],
          [1335736800000, 33],
          [1335823200000, 33],
          [1335909600000, 33],
          [1335996000000, 33],
          [1336082400000, 32],
          [1336341600000, 32],
          [1336428000000, 32],
          [1336514400000, 32],
          [1336600800000, 31],
          [1336687200000, 32],
          [1336946400000, 32],
          [1337032800000, 32],
          [1337119200000, 32],
          [1337205600000, 32],
          [1337292000000, 31],
          [1337551200000, 32],
          [1337637600000, 32],
          [1337724000000, 32],
          [1337810400000, 31],
          [1337896800000, 31],
          [1338242400000, 32],
          [1338328800000, 31],
          [1338415200000, 31],
          [1338501600000, 29],
          [1338760800000, 30],
          [1338847200000, 30],
          [1338933600000, 31],
          [1339020000000, 31],
          [1339106400000, 31],
          [1339365600000, 31],
          [1339452000000, 31],
          [1339538400000, 31],
          [1339624800000, 31],
          [1339711200000, 32],
          [1339970400000, 32],
          [1340056800000, 32],
          [1340143200000, 31],
          [1340229600000, 31],
          [1340316000000, 31],
          [1340575200000, 30],
          [1340661600000, 31],
          [1340748000000, 31],
          [1340834400000, 31],
          [1340920800000, 32],
          [1341180000000, 32],
          [1341266400000, 32],
          [1341439200000, 32],
          [1341525600000, 31],
          [1341784800000, 30],
          [1341871200000, 30],
          [1341957600000, 30],
          [1342044000000, 30],
          [1342130400000, 30],
          [1342389600000, 30],
          [1342476000000, 30],
          [1342562400000, 31],
          [1342648800000, 31],
          [1342735200000, 31],
          [1342994400000, 30],
          [1343080800000, 30],
          [1343167200000, 30],
          [1343253600000, 30],
          [1343340000000, 31],
          [1343599200000, 31],
          [1343685600000, 30],
          [1343772000000, 30],
          [1343858400000, 30],
          [1343944800000, 32],
          [1344204000000, 32],
          [1344290400000, 32],
          [1344376800000, 32],
          [1344463200000, 32],
          [1344549600000, 32],
          [1344808800000, 32],
          [1344895200000, 32],
          [1344981600000, 32],
          [1345068000000, 33],
          [1345154400000, 33],
          [1345413600000, 33],
          [1345500000000, 33],
          [1345586400000, 33],
          [1345672800000, 33],
          [1345759200000, 32],
          [1346018400000, 32],
          [1346104800000, 32],
          [1346191200000, 31],
          [1346277600000, 31],
          [1346364000000, 31],
          [1346709600000, 31],
          [1346796000000, 32],
          [1346882400000, 32],
          [1346968800000, 32],
          [1347228000000, 32],
          [1347314400000, 32],
          [1347400800000, 32],
          [1347487200000, 32],
          [1347573600000, 33],
          [1347832800000, 33],
          [1347919200000, 32],
          [1348005600000, 33],
          [1348092000000, 34],
          [1348178400000, 33],
          [1348437600000, 33],
          [1348524000000, 32],
          [1348610400000, 32],
          [1348696800000, 32],
          [1348783200000, 32],
          [1349042400000, 32],
          [1349128800000, 32],
          [1349215200000, 32],
          [1349301600000, 32],
          [1349388000000, 32],
          [1349647200000, 32],
          [1349733600000, 31],
          [1349820000000, 31],
          [1349906400000, 31],
          [1349992800000, 31],
          [1350252000000, 32],
          [1350338400000, 33],
          [1350424800000, 33],
          [1350511200000, 33],
          [1350597600000, 33],
          [1350856800000, 33],
          [1350943200000, 33],
          [1351029600000, 33],
          [1351116000000, 33],
          [1351202400000, 34],
          [1351638000000, 34],
          [1351724400000, 34],
          [1351810800000, 34],
          [1352070000000, 34],
          [1352156400000, 34],
          [1352242800000, 33],
          [1352329200000, 32],
          [1352415600000, 32],
          [1352674800000, 32],
          [1352761200000, 32],
          [1352847600000, 32],
          [1352934000000, 32],
          [1353020400000, 32],
          [1353279600000, 32],
          [1353366000000, 32],
          [1353452400000, 32],
          [1353625200000, 33],
          [1353884400000, 33],
          [1353970800000, 33],
          [1354057200000, 33],
          [1354143600000, 34],
          [1354230000000, 34],
          [1354489200000, 34],
          [1354575600000, 35],
          [1354662000000, 35],
          [1354748400000, 35],
          [1354834800000, 35],
          [1355094000000, 35],
          [1355180400000, 35],
          [1355266800000, 35],
          [1355353200000, 35],
          [1355439600000, 37],
          [1355698800000, 37],
          [1355785200000, 37],
          [1355871600000, 38],
          [1355958000000, 37],
          [1356044400000, 37],
          [1356303600000, 37],
          [1356476400000, 37],
          [1356562800000, 37],
          [1356649200000, 36],
          [1356908400000, 37],
          [1357081200000, 38],
          [1357167600000, 37],
          [1357254000000, 38],
          [1357513200000, 37],
          [1357599600000, 38],
          [1357686000000, 38],
          [1357772400000, 38],
          [1357858800000, 38],
          [1358118000000, 38],
          [1358204400000, 38],
          [1358290800000, 37],
          [1358377200000, 37],
          [1358463600000, 37],
          [1358809200000, 37],
          [1358895600000, 38],
          [1358982000000, 38],
          [1359068400000, 38],
          [1359327600000, 38],
          [1359414000000, 38],
          [1359500400000, 37],
          [1359586800000, 37],
          [1359673200000, 38],
          [1359932400000, 38],
          [1360018800000, 38],
          [1360105200000, 38],
          [1360191600000, 38],
          [1360278000000, 39],
          [1360537200000, 38],
          [1360623600000, 38],
          [1360710000000, 38],
          [1360796400000, 38],
          [1360882800000, 38],
          [1361228400000, 38],
          [1361314800000, 38],
          [1361401200000, 38],
          [1361487600000, 38],
          [1361746800000, 38],
          [1361833200000, 38],
        ],
      },
    ],
    individualPersonSeries: [
      {
        name: 'Jane Smith',
        data: [
          [1327359600000, 30],
          [1327446000000, 31],
          [1327532400000, 31],
          [1327618800000, 31],
          [1327878000000, 31],
          [1327964400000, 30],
          [1328050800000, 31],
          [1328137200000, 31],
          [1328223600000, 31],
          [1328482800000, 31],
          [1328569200000, 32],
          [1328655600000, 32],
          [1328742000000, 32],
          [1328828400000, 32],
          [1329087600000, 32],
          [1329174000000, 32],
          [1329260400000, 32],
          [1329346800000, 32],
          [1329433200000, 32],
          [1329778800000, 32],
          [1329865200000, 32],
          [1329951600000, 32],
          [1330038000000, 33],
          [1330297200000, 33],
          [1330383600000, 33],
          [1330470000000, 32],
          [1330556400000, 33],
          [1330642800000, 33],
          [1330902000000, 33],
          [1330988400000, 31],
          [1331074800000, 32],
          [1331161200000, 33],
          [1331247600000, 33],
          [1331506800000, 33],
          [1331593200000, 34],
          [1331679600000, 33],
          [1331766000000, 34],
          [1331852400000, 33],
          [1332111600000, 34],
          [1332198000000, 33],
          [1332284400000, 33],
          [1332370800000, 33],
          [1332457200000, 33],
          [1332712800000, 34],
          [1332799200000, 34],
          [1332885600000, 34],
          [1332972000000, 34],
          [1333058400000, 34],
          [1333317600000, 34],
          [1333404000000, 34],
          [1333490400000, 33],
          [1333576800000, 33],
          [1333922400000, 33],
          [1334008800000, 32],
          [1334095200000, 33],
          [1334181600000, 33],
          [1334268000000, 33],
          [1334527200000, 32],
          [1334613600000, 33],
          [1334700000000, 33],
          [1334786400000, 32],
          [1334872800000, 33],
          [1335132000000, 32],
          [1335218400000, 32],
          [1335304800000, 33],
          [1335391200000, 33],
          [1335477600000, 33],
          [1335736800000, 33],
          [1335823200000, 33],
          [1335909600000, 33],
          [1335996000000, 33],
          [1336082400000, 32],
          [1336341600000, 32],
          [1336428000000, 32],
          [1336514400000, 32],
          [1336600800000, 31],
          [1336687200000, 32],
          [1336946400000, 32],
          [1337032800000, 32],
          [1337119200000, 32],
          [1337205600000, 32],
          [1337292000000, 31],
          [1337551200000, 32],
          [1337637600000, 32],
          [1337724000000, 32],
          [1337810400000, 31],
          [1337896800000, 31],
          [1338242400000, 32],
          [1338328800000, 31],
          [1338415200000, 31],
          [1338501600000, 29],
          [1338760800000, 30],
          [1338847200000, 30],
          [1338933600000, 31],
          [1339020000000, 31],
          [1339106400000, 31],
          [1339365600000, 31],
          [1339452000000, 31],
          [1339538400000, 31],
          [1339624800000, 31],
          [1339711200000, 32],
          [1339970400000, 32],
          [1340056800000, 32],
          [1340143200000, 31],
          [1340229600000, 31],
          [1340316000000, 31],
          [1340575200000, 30],
          [1340661600000, 31],
          [1340748000000, 31],
          [1340834400000, 31],
          [1340920800000, 32],
          [1341180000000, 32],
          [1341266400000, 32],
          [1341439200000, 32],
          [1341525600000, 31],
          [1341784800000, 30],
          [1341871200000, 30],
          [1341957600000, 30],
          [1342044000000, 30],
          [1342130400000, 30],
          [1342389600000, 30],
          [1342476000000, 30],
          [1342562400000, 31],
          [1342648800000, 31],
          [1342735200000, 31],
          [1342994400000, 30],
          [1343080800000, 30],
          [1343167200000, 30],
          [1343253600000, 30],
          [1343340000000, 31],
          [1343599200000, 31],
          [1343685600000, 30],
          [1343772000000, 30],
          [1343858400000, 30],
          [1343944800000, 32],
          [1344204000000, 32],
          [1344290400000, 32],
          [1344376800000, 32],
          [1344463200000, 32],
          [1344549600000, 32],
          [1344808800000, 32],
          [1344895200000, 32],
          [1344981600000, 32],
          [1345068000000, 33],
          [1345154400000, 33],
          [1345413600000, 33],
          [1345500000000, 33],
          [1345586400000, 33],
          [1345672800000, 33],
          [1345759200000, 32],
          [1346018400000, 32],
          [1346104800000, 32],
          [1346191200000, 31],
          [1346277600000, 31],
          [1346364000000, 31],
          [1346709600000, 31],
          [1346796000000, 32],
          [1346882400000, 32],
          [1346968800000, 32],
          [1347228000000, 32],
          [1347314400000, 32],
          [1347400800000, 32],
          [1347487200000, 32],
          [1347573600000, 33],
          [1347832800000, 33],
          [1347919200000, 32],
          [1348005600000, 33],
          [1348092000000, 34],
          [1348178400000, 33],
          [1348437600000, 33],
          [1348524000000, 32],
          [1348610400000, 32],
          [1348696800000, 32],
          [1348783200000, 32],
          [1349042400000, 32],
          [1349128800000, 32],
          [1349215200000, 32],
          [1349301600000, 32],
          [1349388000000, 32],
          [1349647200000, 32],
          [1349733600000, 31],
          [1349820000000, 31],
          [1349906400000, 31],
          [1349992800000, 31],
          [1350252000000, 32],
          [1350338400000, 33],
          [1350424800000, 33],
          [1350511200000, 33],
          [1350597600000, 33],
          [1350856800000, 33],
          [1350943200000, 33],
          [1351029600000, 33],
          [1351116000000, 33],
          [1351202400000, 34],
          [1351638000000, 34],
          [1351724400000, 34],
          [1351810800000, 34],
          [1352070000000, 34],
          [1352156400000, 34],
          [1352242800000, 33],
          [1352329200000, 32],
          [1352415600000, 32],
          [1352674800000, 32],
          [1352761200000, 32],
          [1352847600000, 32],
          [1352934000000, 32],
          [1353020400000, 32],
          [1353279600000, 32],
          [1353366000000, 32],
          [1353452400000, 32],
          [1353625200000, 33],
          [1353884400000, 33],
          [1353970800000, 33],
          [1354057200000, 33],
          [1354143600000, 34],
          [1354230000000, 34],
          [1354489200000, 34],
          [1354575600000, 35],
          [1354662000000, 35],
          [1354748400000, 35],
          [1354834800000, 35],
          [1355094000000, 35],
          [1355180400000, 35],
          [1355266800000, 35],
          [1355353200000, 35],
          [1355439600000, 37],
          [1355698800000, 37],
          [1355785200000, 37],
          [1355871600000, 38],
          [1355958000000, 37],
          [1356044400000, 37],
          [1356303600000, 37],
          [1356476400000, 37],
          [1356562800000, 37],
          [1356649200000, 36],
          [1356908400000, 37],
          [1357081200000, 38],
          [1357167600000, 37],
          [1357254000000, 38],
          [1357513200000, 37],
          [1357599600000, 38],
          [1357686000000, 38],
          [1357772400000, 38],
          [1357858800000, 38],
          [1358118000000, 38],
          [1358204400000, 38],
          [1358290800000, 37],
          [1358377200000, 37],
          [1358463600000, 37],
          [1358809200000, 37],
          [1358895600000, 38],
          [1358982000000, 38],
          [1359068400000, 38],
          [1359327600000, 38],
          [1359414000000, 38],
          [1359500400000, 37],
          [1359586800000, 37],
          [1359673200000, 38],
          [1359932400000, 38],
          [1360018800000, 38],
          [1360105200000, 38],
          [1360191600000, 38],
          [1360278000000, 39],
          [1360537200000, 38],
          [1360623600000, 38],
          [1360710000000, 38],
          [1360796400000, 38],
          [1360882800000, 38],
          [1361228400000, 38],
          [1361314800000, 38],
          [1361401200000, 38],
          [1361487600000, 38],
          [1361746800000, 38],
          [1361833200000, 38],
        ],
      },
      {
        name: 'George Jones',
        data: [
          [1327359600000, 100],
          [1327446000000, 103],
          [1327532400000, 103],
          [1327618800000, 103],
          [1327878000000, 103],
          [1327964400000, 100],
          [1328050800000, 103],
          [1328137200000, 103],
          [1328223600000, 103],
          [1328482800000, 103],
          [1328569200000, 105],
          [1328655600000, 105],
          [1328742000000, 105],
          [1328828400000, 105],
          [1329087600000, 105],
          [1329174000000, 105],
          [1329260400000, 105],
          [1329346800000, 105],
          [1329433200000, 105],
          [1329778800000, 105],
          [1329865200000, 105],
          [1329951600000, 105],
          [1330038000000, 110],
          [1330297200000, 110],
          [1330383600000, 110],
          [1330470000000, 105],
          [1330556400000, 110],
          [1330642800000, 110],
          [1330902000000, 110],
          [1330988400000, 103],
          [1331074800000, 105],
          [1331161200000, 110],
          [1331247600000, 110],
          [1331506800000, 110],
          [1331593200000, 115],
          [1331679600000, 110],
          [1331766000000, 115],
          [1331852400000, 110],
          [1332111600000, 115],
          [1332198000000, 110],
          [1332284400000, 110],
          [1332370800000, 110],
          [1332457200000, 110],
          [1332712800000, 115],
          [1332799200000, 115],
          [1332885600000, 115],
          [1332972000000, 115],
          [1333058400000, 115],
          [1333317600000, 115],
          [1333404000000, 115],
          [1333490400000, 110],
          [1333576800000, 110],
          [1333922400000, 110],
          [1334008800000, 105],
          [1334095200000, 110],
          [1334181600000, 110],
          [1334268000000, 110],
          [1334527200000, 105],
          [1334613600000, 110],
          [1334700000000, 110],
          [1334786400000, 105],
          [1334872800000, 110],
          [1335132000000, 105],
          [1335218400000, 105],
          [1335304800000, 110],
          [1335391200000, 110],
          [1335477600000, 110],
          [1335736800000, 110],
          [1335823200000, 110],
          [1335909600000, 110],
          [1335996000000, 110],
          [1336082400000, 105],
          [1336341600000, 105],
          [1336428000000, 105],
          [1336514400000, 105],
          [1336600800000, 103],
          [1336687200000, 105],
          [1336946400000, 105],
          [1337032800000, 105],
          [1337119200000, 105],
          [1337205600000, 105],
          [1337292000000, 103],
          [1337551200000, 105],
          [1337637600000, 105],
          [1337724000000, 105],
          [1337810400000, 103],
          [1337896800000, 103],
          [1338242400000, 105],
          [1338328800000, 103],
          [1338415200000, 103],
          [1338501600000, 110],
          [1338760800000, 100],
          [1338847200000, 100],
          [1338933600000, 103],
          [1339020000000, 103],
          [1339106400000, 103],
          [1339365600000, 103],
          [1339452000000, 103],
          [1339538400000, 103],
          [1339624800000, 103],
          [1339711200000, 105],
          [1339970400000, 105],
          [1340056800000, 105],
          [1340143200000, 103],
          [1340229600000, 103],
          [1340316000000, 103],
          [1340575200000, 100],
          [1340661600000, 103],
          [1340748000000, 103],
          [1340834400000, 103],
          [1340920800000, 105],
          [1341180000000, 105],
          [1341266400000, 105],
          [1341439200000, 105],
          [1341525600000, 103],
          [1341784800000, 100],
          [1341871200000, 100],
          [1341957600000, 100],
          [1342044000000, 100],
          [1342130400000, 100],
          [1342389600000, 100],
          [1342476000000, 100],
          [1342562400000, 103],
          [1342648800000, 103],
          [1342735200000, 103],
          [1342994400000, 100],
          [1343080800000, 100],
          [1343167200000, 100],
          [1343253600000, 100],
          [1343340000000, 103],
          [1343599200000, 103],
          [1343685600000, 100],
          [1343772000000, 100],
          [1343858400000, 100],
          [1343944800000, 105],
          [1344204000000, 105],
          [1344290400000, 105],
          [1344376800000, 105],
          [1344463200000, 105],
          [1344549600000, 105],
          [1344808800000, 105],
          [1344895200000, 105],
          [1344981600000, 105],
          [1345068000000, 110],
          [1345154400000, 110],
          [1345413600000, 110],
          [1345500000000, 110],
          [1345586400000, 110],
          [1345672800000, 110],
          [1345759200000, 105],
          [1346018400000, 105],
          [1346104800000, 105],
          [1346191200000, 103],
          [1346277600000, 103],
          [1346364000000, 103],
          [1346709600000, 103],
          [1346796000000, 105],
          [1346882400000, 105],
          [1346968800000, 105],
          [1347228000000, 105],
          [1347314400000, 105],
          [1347400800000, 105],
          [1347487200000, 105],
          [1347573600000, 110],
          [1347832800000, 110],
          [1347919200000, 105],
          [1348005600000, 110],
          [1348092000000, 115],
          [1348178400000, 110],
          [1348437600000, 110],
          [1348524000000, 105],
          [1348610400000, 105],
          [1348696800000, 105],
          [1348783200000, 105],
          [1349042400000, 105],
          [1349128800000, 105],
          [1349215200000, 105],
          [1349301600000, 105],
          [1349388000000, 105],
          [1349647200000, 105],
          [1349733600000, 103],
          [1349820000000, 103],
          [1349906400000, 103],
          [1349992800000, 103],
          [1350252000000, 105],
          [1350338400000, 110],
          [1350424800000, 110],
          [1350511200000, 110],
          [1350597600000, 110],
          [1350856800000, 110],
          [1350943200000, 110],
          [1351029600000, 110],
          [1351116000000, 110],
          [1351202400000, 115],
          [1351638000000, 115],
          [1351724400000, 115],
          [1351810800000, 115],
          [1352070000000, 115],
          [1352156400000, 115],
          [1352242800000, 110],
          [1352329200000, 105],
          [1352415600000, 105],
          [1352674800000, 105],
          [1352761200000, 105],
          [1352847600000, 105],
          [1352934000000, 105],
          [1353020400000, 105],
          [1353279600000, 105],
          [1353366000000, 105],
          [1353452400000, 105],
          [1353625200000, 110],
          [1353884400000, 110],
          [1353970800000, 110],
          [1354057200000, 110],
          [1354143600000, 115],
          [1354230000000, 115],
          [1354489200000, 115],
          [1354575600000, 110],
          [1354662000000, 110],
          [1354748400000, 110],
          [1354834800000, 110],
          [1355094000000, 110],
          [1355180400000, 110],
          [1355266800000, 110],
          [1355353200000, 110],
          [1355439600000, 107],
          [1355698800000, 107],
          [1355785200000, 107],
          [1355871600000, 107],
          [1355958000000, 107],
          [1356044400000, 107],
          [1356303600000, 107],
          [1356476400000, 107],
          [1356562800000, 107],
          [1356649200000, 105],
          [1356908400000, 105],
          [1357081200000, 104],
          [1357167600000, 104],
          [1357254000000, 104],
          [1357513200000, 103],
          [1357599600000, 102],
          [1357686000000, 102],
          [1357772400000, 102],
          [1357858800000, 102],
          [1358118000000, 102],
          [1358204400000, 102],
          [1358290800000, 102],
          [1358377200000, 102],
          [1358463600000, 102],
          [1358809200000, 102],
          [1358895600000, 102],
          [1358982000000, 102],
          [1359068400000, 102],
          [1359327600000, 102],
          [1359414000000, 102],
          [1359500400000, 100],
          [1359586800000, 100],
          [1359673200000, 100],
          [1359932400000, 100],
          [1360018800000, 100],
          [1360105200000, 100],
          [1360191600000, 100],
          [1360278000000, 99],
          [1360537200000, 99],
          [1360623600000, 102],
          [1360710000000, 102],
          [1360796400000, 102],
          [1360882800000, 102],
          [1361228400000, 102],
          [1361314800000, 102],
          [1361401200000, 102],
          [1361487600000, 102],
          [1361746800000, 102],
          [1361833200000, 102],
          [1361919600000, 99],
        ],
      },
      {
        name: 'Suzanne Burns',
        data: [
          [1327359600000, 16],
          [1327446000000, 14],
          [1327532400000, 14],
          [1327618800000, 14],
          [1327878000000, 14],
          [1327964400000, 16],
          [1328050800000, 14],
          [1328137200000, 14],
          [1328223600000, 14],
          [1328482800000, 14],
          [1328569200000, 11],
          [1328655600000, 11],
          [1328742000000, 11],
          [1328828400000, 11],
          [1329087600000, 11],
          [1329174000000, 11],
          [1329260400000, 11],
          [1329346800000, 11],
          [1329433200000, 11],
          [1329778800000, 11],
          [1329865200000, 11],
          [1329951600000, 11],
          [1330038000000, 9],
          [1330297200000, 9],
          [1330383600000, 9],
          [1330470000000, 11],
          [1330556400000, 9],
          [1330642800000, 9],
          [1330902000000, 9],
          [1330988400000, 14],
          [1331074800000, 11],
          [1331161200000, 9],
          [1331247600000, 9],
          [1331506800000, 9],
          [1331593200000, 8],
          [1331679600000, 9],
          [1331766000000, 8],
          [1331852400000, 9],
          [1332111600000, 8],
          [1332198000000, 9],
          [1332284400000, 9],
          [1332370800000, 9],
          [1332457200000, 9],
          [1332712800000, 8],
          [1332799200000, 8],
          [1332885600000, 8],
          [1332972000000, 8],
          [1333058400000, 8],
          [1333317600000, 8],
          [1333404000000, 8],
          [1333490400000, 9],
          [1333576800000, 9],
          [1333922400000, 9],
          [1334008800000, 11],
          [1334095200000, 9],
          [1334181600000, 9],
          [1334268000000, 9],
          [1334527200000, 11],
          [1334613600000, 9],
          [1334700000000, 9],
          [1334786400000, 11],
          [1334872800000, 9],
          [1335132000000, 11],
          [1335218400000, 11],
          [1335304800000, 9],
          [1335391200000, 9],
          [1335477600000, 9],
          [1335736800000, 9],
          [1335823200000, 9],
          [1335909600000, 9],
          [1335996000000, 9],
          [1336082400000, 11],
          [1336341600000, 11],
          [1336428000000, 11],
          [1336514400000, 11],
          [1336600800000, 14],
          [1336687200000, 11],
          [1336946400000, 11],
          [1337032800000, 11],
          [1337119200000, 11],
          [1337205600000, 11],
          [1337292000000, 14],
          [1337551200000, 11],
          [1337637600000, 11],
          [1337724000000, 11],
          [1337810400000, 14],
          [1337896800000, 14],
          [1338242400000, 11],
          [1338328800000, 14],
          [1338415200000, 14],
          [1338501600000, 12],
          [1338760800000, 16],
          [1338847200000, 16],
          [1338933600000, 14],
          [1339020000000, 14],
          [1339106400000, 14],
          [1339365600000, 14],
          [1339452000000, 14],
          [1339538400000, 14],
          [1339624800000, 14],
          [1339711200000, 11],
          [1339970400000, 11],
          [1340056800000, 11],
          [1340143200000, 14],
          [1340229600000, 14],
          [1340316000000, 14],
          [1340575200000, 16],
          [1340661600000, 14],
          [1340748000000, 14],
          [1340834400000, 14],
          [1340920800000, 11],
          [1341180000000, 11],
          [1341266400000, 11],
          [1341439200000, 11],
          [1341525600000, 14],
          [1341784800000, 16],
          [1341871200000, 16],
          [1341957600000, 16],
          [1342044000000, 16],
          [1342130400000, 16],
          [1342389600000, 16],
          [1342476000000, 16],
          [1342562400000, 14],
          [1342648800000, 14],
          [1342735200000, 14],
          [1342994400000, 16],
          [1343080800000, 16],
          [1343167200000, 16],
          [1343253600000, 16],
          [1343340000000, 14],
          [1343599200000, 14],
          [1343685600000, 16],
          [1343772000000, 16],
          [1343858400000, 16],
          [1343944800000, 11],
          [1344204000000, 11],
          [1344290400000, 11],
          [1344376800000, 11],
          [1344463200000, 11],
          [1344549600000, 11],
          [1344808800000, 11],
          [1344895200000, 11],
          [1344981600000, 11],
          [1345068000000, 9],
          [1345154400000, 9],
          [1345413600000, 9],
          [1345500000000, 9],
          [1345586400000, 9],
          [1345672800000, 9],
          [1345759200000, 11],
          [1346018400000, 11],
          [1346104800000, 11],
          [1346191200000, 14],
          [1346277600000, 14],
          [1346364000000, 14],
          [1346709600000, 14],
          [1346796000000, 11],
          [1346882400000, 11],
          [1346968800000, 11],
          [1347228000000, 11],
          [1347314400000, 11],
          [1347400800000, 11],
          [1347487200000, 11],
          [1347573600000, 9],
          [1347832800000, 9],
          [1347919200000, 11],
          [1348005600000, 9],
          [1348092000000, 8],
          [1348178400000, 9],
          [1348437600000, 9],
          [1348524000000, 11],
          [1348610400000, 11],
          [1348696800000, 11],
          [1348783200000, 11],
          [1349042400000, 11],
          [1349128800000, 11],
          [1349215200000, 11],
          [1349301600000, 11],
          [1349388000000, 11],
          [1349647200000, 11],
          [1349733600000, 14],
          [1349820000000, 14],
          [1349906400000, 14],
          [1349992800000, 14],
          [1350252000000, 11],
          [1350338400000, 9],
          [1350424800000, 9],
          [1350511200000, 9],
          [1350597600000, 9],
          [1350856800000, 9],
          [1350943200000, 9],
          [1351029600000, 9],
          [1351116000000, 9],
          [1351202400000, 8],
          [1351638000000, 8],
          [1351724400000, 8],
          [1351810800000, 8],
          [1352070000000, 8],
          [1352156400000, 8],
          [1352242800000, 9],
          [1352329200000, 11],
          [1352415600000, 11],
          [1352674800000, 11],
          [1352761200000, 11],
          [1352847600000, 11],
          [1352934000000, 11],
          [1353020400000, 11],
          [1353279600000, 11],
          [1353366000000, 11],
          [1353452400000, 11],
          [1353625200000, 9],
          [1353884400000, 9],
          [1353970800000, 9],
          [1354057200000, 9],
          [1354143600000, 8],
          [1354230000000, 8],
          [1354489200000, 8],
          [1354575600000, 6],
          [1354662000000, 6],
          [1354748400000, 6],
          [1354834800000, 6],
          [1355094000000, 6],
          [1355180400000, 6],
          [1355266800000, 6],
          [1355353200000, 6],
          [1355439600000, 4],
          [1355698800000, 4],
          [1355785200000, 4],
          [1355871600000, 3],
          [1355958000000, 4],
          [1356044400000, 4],
          [1356303600000, 4],
          [1356476400000, 4],
          [1356562800000, 4],
          [1356649200000, 5],
          [1356908400000, 4],
          [1357081200000, 3],
          [1357167600000, 4],
          [1357254000000, 3],
          [1357513200000, 4],
          [1357599600000, 3],
          [1357686000000, 3],
          [1357772400000, 3],
          [1357858800000, 3],
          [1358118000000, 3],
          [1358204400000, 3],
          [1358290800000, 4],
          [1358377200000, 4],
          [1358463600000, 4],
          [1358809200000, 4],
          [1358895600000, 3],
          [1358982000000, 3],
          [1359068400000, 3],
          [1359327600000, 3],
          [1359414000000, 3],
          [1359500400000, 4],
          [1359586800000, 4],
          [1359673200000, 3],
          [1359932400000, 3],
          [1360018800000, 3],
          [1360105200000, 3],
          [1360191600000, 3],
          [1360278000000, 2],
          [1360537200000, 3],
          [1360623600000, 3],
          [1360710000000, 3],
          [1360796400000, 3],
          [1360882800000, 3],
          [1361228400000, 3],
          [1361314800000, 3],
          [1361401200000, 3],
          [1361487600000, 3],
          [1361746800000, 3],
          [1361833200000, 3],
          [1361919600000, 2],
        ],
      },
    ],
  }),
  // getters,
  actions: {
    updateActivityLog() {
      this.activityLog ? (this.activityLog = false) : (this.activityLog = true)
    },
    setDisposition(payload) {
      this.disposition = payload
    },

    setAddNoteComp(payload) {
      this.addNoteComp = payload
    },

    setCurrentComp(payload) {
      this.currentComp = payload.currentComp
      this.previousCurrentComp.splice(0, 1)
      this.previousCurrentComp.unshift(payload.previousCurrentComp)
    },

    backToCurrentComp(payload) {
      this.currentComp = payload
      this.previousCurrentComp.splice(0, 1)
    },

    setSummaryAlertTab(payload) {
      this.summaryAlertTab = payload
    },

    setListSeveritySourceGroupTab(payload) {
      this.listSeveritySourceGroupTab = payload
    },

    setCurrentGraph(payload: "AreaGraph" | "LineGraph" | "ScatterGraph") {
      this.currentGraph = payload
    },

    setSummaryComponent(payload: {
      summaryComp: SummeryComponent
      previousSummaryComp: SummeryComponent
    }) {
      this.summaryComp = payload.summaryComp
      if (!this.isDesktopView) {
        if (this.mobileCurrentTab === 'AllFeedsOpenArea') {
          this.graphCurrentComp = this.mobileCurrentTab
        } else {
          this.graphCurrentComp = payload.summaryComp
        }
      }
      if (payload.previousSummaryComp) {
        this.previousSummaryComp.unshift(payload.previousSummaryComp)
      }
      if (this.summaryComp === 'SummaryAllFeedsTable') {
        this.pieSeries = []
        this.series = this.addFeedsSeries
        this.pieLabels = []
      } else if (this.summaryComp === 'SummaryIndividualFeedsTable') {
        this.pieSeries = this.IndividualFeedsPieSeries
        this.series = this.IndividualFeedsSeries
        this.pieLabels = this.IndividualFeedsPieLabels
      } else if (this.summaryComp === 'SummaryIndividualSocialFeedsTable') {
        this.pieSeries = this.socialMediaPieSeries
        this.series = this.socialMediaSeries
        this.pieLabels = this.socialMediaPieLabels
      } else if (this.summaryComp === 'SummaryIndividualSocialFeedDetails') {
        this.pieSeries = this.IndividualAccountPieSeries
        this.series = this.IndividualAccountSeries
        this.pieLabels = this.IndividualAccountPieLabels
      } else if (this.summaryComp === 'SummaryAllFlagsORTable') {
        this.series = this.allFlagsSeries
      } else if (this.summaryComp === 'SummaryFlagPersonsORTable') {
        this.series = this.IndividualFlagPersonsSeries
      } else if (this.summaryComp === 'SummaryAllFlagsCRTable') {
        this.series = JSON.parse(JSON.stringify(this.allFlagLineSeries))
        this.oldSeries = JSON.parse(JSON.stringify(this.allFlagLineSeries))
        // this.series = this.allFlagsScatterSeries
        this.pieSeries = []
        this.pieLabels = []
      } else if (this.summaryComp === 'SummaryTeamCRTable') {
        this.series = JSON.parse(JSON.stringify(this.individualTeamLineSeries))
        this.oldSeries = JSON.parse(
          JSON.stringify(this.individualTeamLineSeries),
        )
        // this.series = this.complianceScatterSeries
      } else if (this.summaryComp === 'SummaryPersonCRTable') {
        this.series = JSON.parse(JSON.stringify(this.individualPersonSeries))
        this.oldSeries = JSON.parse(JSON.stringify(this.individualPersonSeries))
        // this.series = this.humanResourcesScatterSeries
      } else if (this.summaryComp === 'SummaryAllFlagsTable') {
        this.series = JSON.parse(JSON.stringify(this.allFlagsScatterSeries))
        this.oldSeries = []
      } else if (this.summaryComp === 'SummaryAllFlagsComplianceTable') {
        this.series = JSON.parse(JSON.stringify(this.complianceScatterSeries))
        this.oldSeries = JSON.parse(JSON.stringify(this.allFlagsScatterSeries))
      } else if (this.summaryComp === 'SummaryAllFlagsHumanResourcesTable') {
        this.series = JSON.parse(
          JSON.stringify(this.humanResourcesScatterSeries),
        )
        this.oldSeries = JSON.parse(
          JSON.stringify(this.complianceScatterSeries),
        )
      } else if (this.summaryComp === 'SummaryAllFlagsCorporateSecurityTable') {
        this.series = JSON.parse(
          JSON.stringify(this.corporateSecurityScatterSeries),
        )
        this.oldSeries = JSON.parse(
          JSON.stringify(this.humanResourcesScatterSeries),
        )
      }
      console.log(this.summaryComp, 'this.summaryComp')
    },

    backToPreviousSummaryComponent(payload: { summaryComp: SummeryComponent }) {
      this.summaryComp = payload.summaryComp
      if (!this.isDesktopView) {
        this.graphCurrentComp = payload.summaryComp
        this.graphCurrentComp = 'AllFeedsOpenArea'
      }
      this.previousSummaryComp.splice(0, 1)
      if (this.summaryComp === 'SummaryAllFeedsTable') {
        this.pieSeries = []
        this.series = this.addFeedsSeries
        this.pieLabels = []
      } else if (this.summaryComp === 'SummaryIndividualFeedsTable') {
        this.pieSeries = this.IndividualFeedsPieSeries
        this.series = this.IndividualFeedsSeries
        this.pieLabels = this.IndividualFeedsPieLabels
      } else if (this.summaryComp === 'SummaryIndividualSocialFeedsTable') {
        this.pieSeries = this.socialMediaPieSeries
        this.series = this.socialMediaSeries
        this.pieLabels = this.socialMediaPieLabels
      } else if (this.summaryComp === 'SummaryIndividualSocialFeedDetails') {
        this.pieSeries = this.IndividualAccountPieSeries
        this.series = this.IndividualAccountSeries
        this.pieLabels = this.IndividualAccountPieLabels
      } else if (this.summaryComp === 'SummaryAllFlagsORTable') {
        this.pieLabels = []
        this.pieSeries = []
        this.series = this.allFlagsSeries
      } else if (this.summaryComp === 'SummaryFlagPersonsORTable') {
        this.series = this.IndividualFlagPersonsSeries
      } else if (this.summaryComp === 'SummaryAllFlagsCRTable') {
        this.series = JSON.parse(JSON.stringify(this.allFlagLineSeries))
        this.oldSeries = JSON.parse(JSON.stringify(this.allFlagLineSeries))
        // this.series = this.allFlagsScatterSeries
        this.pieSeries = []
        this.pieLabels = []
      } else if (this.summaryComp === 'SummaryTeamCRTable') {
        this.series = JSON.parse(JSON.stringify(this.individualTeamLineSeries))
        this.oldSeries = JSON.parse(
          JSON.stringify(this.individualTeamLineSeries),
        )
        // this.series = this.complianceScatterSeries
      } else if (this.summaryComp === 'SummaryPersonCRTable') {
        this.series = JSON.parse(JSON.stringify(this.individualPersonSeries))
        this.oldSeries = JSON.parse(JSON.stringify(this.individualPersonSeries))
        // this.series = this.humanResourcesScatterSeries
      } else if (this.summaryComp === 'SummaryAllFlagsTable') {
        this.series = JSON.parse(JSON.stringify(this.allFlagsScatterSeries))
        this.oldSeries = []
      } else if (this.summaryComp === 'SummaryAllFlagsComplianceTable') {
        this.series = JSON.parse(JSON.stringify(this.complianceScatterSeries))
        this.oldSeries = JSON.parse(JSON.stringify(this.allFlagsScatterSeries))
      } else if (this.summaryComp === 'SummaryAllFlagsHumanResourcesTable') {
        this.series = JSON.parse(
          JSON.stringify(this.humanResourcesScatterSeries),
        )
        this.oldSeries = JSON.parse(
          JSON.stringify(this.complianceScatterSeries),
        )
      }
    },

    setNewSummaryComponent(payload: SummeryComponent) {
      this.previousSummaryComp = []
      this.summaryComp = payload
      if (!this.isDesktopView) {
        this.graphCurrentComp = payload
        this.graphCurrentComp = 'AllFeedsOpenArea'
      }
      if (this.summaryComp === 'SummaryAllFeedsTable') {
        this.pieSeries = []
        this.series = this.addFeedsSeries
        this.pieLabels = []
      } else if (this.summaryComp === 'SummaryGroupsTable') {
        this.series = this.groupsSeries
        this.pieSeries = this.groupsPieSeries
        this.pieLabels = this.groupsPieLabels
      } else if (this.summaryComp === 'SummaryAllFlagsORTable') {
        this.series = this.allFlagsSeries
        this.pieSeries = []
        this.pieLabels = []
      } else if (this.summaryComp === 'SummaryAllFlagsCRTable') {
        this.series = JSON.parse(JSON.stringify(this.allFlagLineSeries))
        this.oldSeries = JSON.parse(JSON.stringify(this.allFlagLineSeries))
        // this.series = this.allFlagsScatterSeries
        this.pieSeries = []
        this.pieLabels = []
      } else if (this.summaryComp === 'SummaryAllFlagsTable') {
        this.series = JSON.parse(JSON.stringify(this.allFlagsScatterSeries))
        this.oldSeries = []
        this.pieSeries = []
        this.pieLabels = []
      }
    },

    setPreviousSummaryComponent(payload: SummeryComponent) {
      this.previousSummaryComp.unshift(payload)
    },

    setFeedsGroupsResponseTab(payload: SummeryComponent) {
      this.feedsGroupsResponseTab = payload
    },

    setOpenClose(payload: 'Open' | 'Close') {
      this.openClose = payload
    },

    setCurrentInputTab(payload) {
      this.currentInputTab = payload
    },

    showNewAlertComp(payload) {
      this.showNewAlert = payload
    },

    setCurrentDateRange(payload) {
      this.currentDateRange = payload
    },

    setGraphCurrentCom(payload) {
      this.graphCurrentComp = payload
      this.mobileCurrentTab = payload
    },

    setIsDesktop(payload) {
      this.isDesktopView = payload
    },

    setShowFullSummaryTable(payload) {
      this.showHideFullSummaryTable = true
      this.hideGraphTable = true
      setTimeout(() => {
        if (this.summaryComp !== 'SummaryAllFeedsTable') {
          this.previousSummaryComp.unshift(this.summaryComp)
          this.summaryComp = 'SummaryAllFeedsTable'
        }
      }, 500)
      setTimeout(() => {
        this.showSummaryTableData = true
        // this.setCurrentComp({

        // })
      }, 800)
    },

    setHideFullSummaryTable(payload: any) {
      this.showSummaryTableData = false
      setTimeout(() => {
        if (this.previousSummaryComp.length > 0) {
          this.summaryComp = this.previousSummaryComp.shift()!
          if (this.summaryComp === 'SummaryAllFeedsTable') {
            this.pieSeries = []
            this.series = this.addFeedsSeries
            this.pieLabels = []
          } else if (this.summaryComp === 'SummaryGroupsTable') {
            this.series = this.groupsSeries
            this.pieSeries = this.groupsPieSeries
            this.pieLabels = this.groupsPieLabels
          } else if (this.summaryComp === 'SummaryAllFlagsORTable') {
            this.series = this.allFlagsSeries
            this.pieSeries = []
            this.pieLabels = []
          } else if (this.summaryComp === 'SummaryAllFlagsCRTable') {
            this.series = JSON.parse(JSON.stringify(this.allFlagLineSeries))
            this.oldSeries = JSON.parse(JSON.stringify(this.allFlagLineSeries))
            // this.series = this.allFlagsScatterSeries
            this.pieSeries = []
            this.pieLabels = []
          }
        }
      }, 100)
      setTimeout(() => {
        this.showHideFullSummaryTable = false
        setTimeout(() => {
          this.hideGraphTable = false
        }, 300)
      }, 400)
    },
    setMultiRangeEndingValue(end) {
      // this.multiRangeStartingValue = start
      this.multiRangeEndingValue = end
    },

    setSingleRangeEndingValue(end) {
      // this.multiRangeStartingValue = start
      this.singleRangeEndingValue = end
    },
    setMultiRangeStartingValue(start) {
      this.multiRangeStartingValue = start
    },
    setPositionChange(payload) {
      this.changePosition = payload
    },
  },
})
